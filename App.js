import React, { Component } from 'react';
import { AlertPro, AppState, BackHandler, NetInfo, View } from './src/components';
import { Provider } from 'mobx-react';
// eslint-disable-next-line import/no-extraneous-dependencies
import Reactotron from 'reactotron-react-native';
import ToastComponent from './src/components/toastManager';
import GlobalLoading from './src/components/loading/globalLoading';
import stores from './src/store';
import chatAction from './src/store/actions/chatAction';
import NavigationService from './src/navigationService';
import App from './src';
import { initApplication } from './src/initApp';
import PushService from './src/pushService';
import I18n from './src/i18n';
import constant from './src/store/constant';
import { StyleSheet } from 'react-native';
import DeepLinkService from './src/deepLinkService';
import imWebSocket from './src/api/imWebSocket';
import CallComponent from './src/pages/call/callComponent';
import GlobalAlert from './src/components/alert/globalAlert';
import TimerUtil from './src/util/timerUtil';
import DateUtil from './src/util/dateUtil';
import KeyboardUtil from './src/util/KeyboardUtil';
import GlobalLoadingModal from './src/components/loading/globalLoadingModal';

DateUtil.init();

if (__DEV__) {
  Reactotron.configure() // controls connection & communication settings
    .useReactNative() // add all built-in react native plugins
    .connect(); // let's connect!
}

if (!__DEV__) {
  global.console = {
    info: () => {},
    log: () => {},
    assert: () => {},
    warn: () => {},
    debug: () => {},
    error: () => {},
    time: () => {},
    timeEnd: () => {},
  };
}

export default class NimApp extends Component {
  constructor(props) {
    super(props);
    // 是否已启动
    global.isAppStarted = false;
    console.debug('push isAppStarted false');
    this.state = {
      appState: AppState.currentState,
      showAlert: false,
    };
    KeyboardUtil.init();
    initApplication();
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.unsubscribeNetInfoListener = NetInfo.addEventListener(this.handleConnectivityChange);
  }

  componentDidMount() {
    TimerUtil.start();
    this.appStateChangeListener = AppState.addEventListener('change', this._handleAppStateChange);
    PushService.init();
    PushService.registerListeners();
    global.emitter.on(constant.event.navigationStateChange, this.onNavigationStateChange);
    DeepLinkService.addListener();
    chatAction.autoLoginIM();
    imWebSocket.init();
  }

  componentWillUnmount() {
    KeyboardUtil.stop();
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.appStateChangeListener?.remove();
    PushService.removeListeners();
    TimerUtil.stop();
    this.unsubscribeNetInfoListener();
    global.emitter.off(constant.event.navigationStateChange, this.onNavigationStateChange);
    DeepLinkService.removeListener();
    imWebSocket.close();
  }

  onNavigationStateChange = ({ prevScreen, currentScreen }) => {};

  onBackButtonPressAndroid = () => true;

  _handleAppStateChange = (nextAppState) => {
    if (this.state.appState.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App has come to the foreground!');
      // this.handleConnectivityChange();
      PushService.clearBadge();
    } else {
      console.log('App has come to the background!');
    }
    this.setState({ appState: nextAppState });
    imWebSocket.startPing(nextAppState);
  };

  handleConnectivityChange = (state) => {
    if (state.isConnected) {
      this.closeNetworkError();
      global.emitter.emit('refreshList', true);
    } else {
      this.showNetworkError();
      global.emitter.emit('resetStatus', true);
    }
  };

  isShowNetworkErrorAlert = false;

  showNetworkError() {
    if (this.isShowNetworkErrorAlert) {
      return;
    }
    this.isShowNetworkErrorAlert = true;
    this.setState({ showAlert: true });
  }

  closeNetworkError() {
    this.setState({ showAlert: false });
    this.isShowNetworkErrorAlert = false;
  }

  onAlertConfirm = () => {
    this.closeNetworkError();
  };

  render() {
    return (
      <View style={styles.container}>
        <Provider stores={stores} store={stores} {...stores}>
          <View style={styles.container}>
            <App
              ref={NavigationService.setTopLevelNavigator}
              onNavigationStateChange={NavigationService.onNavigationStateChange}
            />
            <CallComponent />
            <ToastComponent />
            <GlobalLoading />
            <GlobalLoadingModal />
            <GlobalAlert />
            <AlertPro
              visible={this.state.showAlert}
              title={I18n.t('page_resume_alt_manager_title')}
              message={I18n.t('msg_network_connect_fail')}
              textConfirm={I18n.t('page_resume_annex_alert_confirm')}
              textCancel={I18n.t('page_resume_annex_alert_cancel')}
              onConfirm={this.onAlertConfirm}
              textConfirmStyle={styles.alertTextConfirmStyle}
              showCancel={false}
            />
          </View>
        </Provider>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  alertTextConfirmStyle: { color: '#fff' },
});
