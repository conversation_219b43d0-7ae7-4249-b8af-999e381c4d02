{"name": "camhr", "version": "1.2.9", "private": true, "scripts": {"postinstall": "patch-package", "start": "node scripts/build.js start", "test": "jest", "ios": "react-native run-ios", "android": "node scripts/build.js env-dev run-android", "android-qa": "node scripts/build.js env-qa run-android-qa", "android-prod": "node scripts/build.js env-prod run-android-prod", "connect": "adb -s emulator-5554 reverse tcp:8081 tcp:8081", "check-i18n": "node scripts/check-i18n.js ", "check-res": "node scripts/check-res.js ", "build": "node scripts/build.js ", "build-prod": "node scripts/build.js buildProd", "update-version": "node scripts/build.js versionName-1.70.5 versionCode-655", "apk-dev": "node scripts/build.js env-dev apk-Dev clean", "apk-prod": "node scripts/build.js env-prod apk-Prod clean", "apk-prod-bundle": "node scripts/build.js env-prod apk-Prod abi-AllAbi bundle", "clean-android": "cd android & gradlew.bat clean", "clean-android-mac": "cd android && ./gradlew clean", "lint": "eslint .", "prettier": "prettier --write ./src/**/**/**/*.js"}, "dependencies": {"@baronha/react-native-multiple-image-picker": "1.1.9", "@qeepsake/react-native-file-utils": "1.3.5", "@react-native-async-storage/async-storage": "1.15.5", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/art": "1.2.0", "@react-native-camera-roll/camera-roll": "7.10.0", "@react-native-community/geolocation": "2.0.2", "@react-native-community/masked-view": "0.1.10", "@react-native-community/netinfo": "5.9.5", "@react-native-community/toolbar-android": "0.2.1", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "1.16.3", "@react-navigation/native": "3.8.0", "@wuye/react-native-subsampling-scale-image": "1.0.15", "aliyun-oss-react-native": "1.0.0-alpha.7", "axios": "0.27.2", "big.js": "6.0.3", "buffer": "5.7.1", "crypto-js": "3.1.9-1", "currency-formatter": "1.5.3", "debounce-decorator": "1.0.6", "jcore-react-native": "2.1.3", "jpush-react-native": "3.0.4", "js-base64": "2.5.1", "lodash": "4.17.15", "lottie-react-native": "7.0.0", "memoize-one": "6.0.0", "mitt": "1.1.3", "mobx": "5.13.0", "mobx-react": "5.4.3", "moment": "2.24.0", "prop-types": "15.7.2", "qs": "6.7.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.3", "react-native-actionsheet": "git+https://gitee.com/rayslei/react-native-actionsheet#v2.4.3", "react-native-agora": "3.7.0", "react-native-alert-pro": "1.0.1", "react-native-audio-recorder-player": "3.6.10", "react-native-background-timer": "2.1.1", "react-native-beautiful-video-recorder": "git+https://gitee.com/sxw_sxw/react-native-beautiful-video-recorder", "react-native-camera": "3.35.0", "react-native-compressor": "1.8.3", "react-native-create-thumbnail": "1.4.1", "react-native-device-info": "11.1.0", "react-native-doc-viewer": "https://github.com/Rays15/react-native-doc-viewer#v2.7.9", "react-native-document-picker": "8.1.0", "react-native-draggable-flatlist": "4.0.2", "react-native-easy-toast": "1.2.0", "react-native-elements": "2.1.0", "react-native-fast-image": "8.6.3", "react-native-fbsdk-next": "12.1.4", "react-native-file-viewer": "2.1.5", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.25.0", "react-native-gifted-chat": "0.16.3", "react-native-google-signin": "2.1.1", "react-native-hyperlink": "0.0.16", "react-native-i18n": "git+https://gitee.com/rayslei/react-native-i18n#v2.0.16", "react-native-image-crop-picker": "0.40.0", "react-native-image-pan-zoom": "2.1.11", "react-native-image-size": "1.1.6", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-keychain": "6.1.1", "react-native-linear-gradient": "2.5.6", "react-native-linkedin": "2.1.1", "react-native-loader": "1.3.1", "react-native-localize": "2.0.1", "react-native-maps": "0.27.1", "react-native-modal": "9.0.0", "react-native-modalbox": "git+https://gitee.com/rayslei/react-native-modalbox#v2.0.2", "react-native-permissions": "3.3.1", "react-native-picker": "git+https://gitee.com/rayslei/react-native-picker", "react-native-popover-view": "5.1.7", "react-native-progress": "5.0.0", "react-native-reanimated": "3.10.0", "react-native-safe-area-context": "4.14.1", "react-native-screens": "3.37.0", "react-native-scrollable-tab-view": "1.0.0", "react-native-share": "10.2.1", "react-native-sound": "0.11.2", "react-native-splash-screen": "3.2.0", "react-native-sqlite-storage": "6.0.1", "react-native-storage": "1.0.1", "react-native-svg": "13.7.0", "react-native-swipe-list-view": "3.2.9", "react-native-swipeout": "2.3.6", "react-native-swiper": "git+https://gitee.com/rayslei/react-native-swiper#v1.6.1", "react-native-tab-navigator": "git+https://gitee.com/rayslei/react-native-tab-navigator", "react-native-vector-icons": "8.1.0", "react-native-video": "5.2.0", "react-native-webview": "13.10.5", "react-native-wechat-lib": "1.1.25", "react-navigation": "4.4.0", "react-navigation-stack": "2.8.2", "rn-expandable-text": "1.0.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"@babel/core": "7.25.2", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.24.7", "@babel/plugin-proposal-unicode-property-regex": "7.18.6", "@babel/plugin-syntax-typescript": "7.24.7", "@babel/plugin-transform-flow-strip-types": "7.25.2", "@babel/preset-env": "7.25.2", "@babel/runtime": "7.25.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "18.3.3", "@types/react-test-renderer": "18.3.0", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-transform-remove-console": "6.9.4", "cz-conventional-changelog": "1.0.0", "deprecated-react-native-prop-types": "5.0.0", "eslint": "8.57.0", "jest": "29.7.0", "patch-package": "8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "reactotron-react-native": "4.2.0", "shelljs": "0.8.4", "typescript": "5.0.4"}, "resolutions": {"react-native-webview": "13.10.5", "react-native-safe-area-view": "2.0.0", "@react-navigation/native": "3.8.0"}, "jest": {"preset": "react-native", "transformIgnorePatterns": ["node_modules/(?!react-native|react)/"], "setupFiles": ["<rootDir>/jest-setup.js"], "globals": {"__DEV__": true}, "testEnvironment": "node"}, "engines": {"node": ">=18"}}