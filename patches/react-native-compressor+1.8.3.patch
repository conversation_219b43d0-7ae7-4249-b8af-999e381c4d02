diff --git a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Image/ImageMain.kt b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Image/ImageMain.kt
index 7f46bf1..99a3276 100644
--- a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Image/ImageMain.kt
+++ b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Image/ImageMain.kt
@@ -25,6 +25,7 @@ class ImageMain(private val reactContext: ReactApplicationContext) {
       }
       MediaCache.removeCompletedImagePath(imagePath)
     } catch (ex: Exception) {
+      ex.printStackTrace()
       promise.reject(ex)
     }
   }
diff --git a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Utils/Utils.kt b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Utils/Utils.kt
index 83b0b51..d1ccf96 100644
--- a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Utils/Utils.kt
+++ b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Utils/Utils.kt
@@ -48,6 +48,7 @@ object Utils {
 
             override fun onSuccess(index: Int, size: Long, path: String?) {
               val fileUrl = "file://$destinationPath"
+              Log.d("compression", "onSuccess fileUrl:$fileUrl")
               //convert finish,result(true is success,false is fail)
               promise.resolve(fileUrl)
               MediaCache.removeCompletedImagePath(fileUrl)
@@ -56,12 +57,13 @@ object Utils {
             }
 
             override fun onFailure(index: Int, failureMessage: String) {
-              Log.wtf("failureMessage", failureMessage)
+              Log.w("compression", "failureMessage msg:$failureMessage")
               currentVideoCompression[0] = 0
+              promise.reject("0", failureMessage)
             }
 
             override fun onCancelled(index: Int) {
-              Log.wtf("TAG", "compression has been cancelled")
+              Log.d("compression", "compression has been cancelled")
               // make UI changes, cleanup, etc
               currentVideoCompression[0] = 0
             }
diff --git a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Video/AutoVideoCompression.kt b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Video/AutoVideoCompression.kt
index 062ea00..e10cf47 100644
--- a/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Video/AutoVideoCompression.kt
+++ b/node_modules/react-native-compressor/android/src/main/java/com/reactnativecompressor/Video/AutoVideoCompression.kt
@@ -2,11 +2,13 @@ package com.reactnativecompressor.Video
 
 import android.media.MediaMetadataRetriever
 import android.net.Uri
+import android.util.Log
 import com.facebook.react.bridge.Promise
 import com.facebook.react.bridge.ReactApplicationContext
 import com.reactnativecompressor.Utils.Utils.compressVideo
 import com.reactnativecompressor.Utils.Utils.generateCacheFilePath
 import java.io.File
+import java.net.URLEncoder
 
 object AutoVideoCompression {
     fun createCompressionSettings(fileUrl: String?, options: VideoCompressorHelper, promise: Promise, reactContext: ReactApplicationContext?) {
@@ -14,14 +16,15 @@ object AutoVideoCompression {
         val minimumFileSizeForCompress = options.minimumFileSizeForCompress
         try {
             val uri = Uri.parse(fileUrl)
-            val srcPath = uri.path
-            val metaRetriever = MediaMetadataRetriever()
-            metaRetriever.setDataSource(srcPath)
-            val file = File(srcPath)
+            Log.d("compression", "createCompressionSettings 1: scheme:${uri.scheme} path:${uri.path} encodedPath:${uri.encodedPath} minimumFileSizeForCompress:$minimumFileSizeForCompress")
+            val file = File(uri.path)
             val sizeInBytes = file.length().toFloat()
             val sizeInMb = sizeInBytes / (1024 * 1024)
+            Log.d("compression", "createCompressionSettings 2: exists:${file.exists()} sizeInBytes:$sizeInBytes sizeInMb:$sizeInMb")
             if (sizeInMb > minimumFileSizeForCompress) {
                 val destinationPath = generateCacheFilePath("mp4", reactContext!!)
+                val metaRetriever = MediaMetadataRetriever()
+                metaRetriever.setDataSource(reactContext, uri)
                 val actualHeight = metaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)!!.toInt()
                 val actualWidth = metaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)!!.toInt()
                 val bitrate = metaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)!!.toInt()
@@ -33,12 +36,13 @@ object AutoVideoCompression {
                   bitrate,
                   resultHeight, resultWidth
                 ).toFloat()
-                compressVideo(srcPath!!, destinationPath, resultWidth, resultHeight, videoBitRate, options.uuid!!,options.progressDivider!!, promise, reactContext)
+                compressVideo(fileUrl!!, destinationPath, resultWidth, resultHeight, videoBitRate, options.uuid!!,options.progressDivider!!, promise, reactContext)
             } else {
-                promise.resolve(fileUrl)
+                promise.resolve(uri.path)
             }
         } catch (ex: Exception) {
             promise.reject(ex)
+            Log.w("compression", "createCompressionSettings error", ex)
         }
     }
 
diff --git a/node_modules/react-native-compressor/ios/Image/ImageCompressor.swift b/node_modules/react-native-compressor/ios/Image/ImageCompressor.swift
index eb059e0..9330928 100644
--- a/node_modules/react-native-compressor/ios/Image/ImageCompressor.swift
+++ b/node_modules/react-native-compressor/ios/Image/ImageCompressor.swift
@@ -232,8 +232,8 @@ class ImageCompressor {
 
             var actualHeight = image.size.height
             var actualWidth = image.size.width
-            let maxHeight: CGFloat = 1280.0
-            let maxWidth: CGFloat = 1280.0
+            let maxHeight: CGFloat = CGFloat(options.maxHeight) ?? 1280.0
+            let maxWidth: CGFloat = CGFloat(options.maxWidth) ?? 1280.0
             var imgRatio = actualWidth / actualHeight
             let maxRatio = maxWidth / maxHeight
             let compressionQuality: CGFloat = 0.8
