diff --git a/node_modules/react-native-elements/src/buttons/Button.js b/node_modules/react-native-elements/src/buttons/Button.js
index 1d8183f..cc9eec6 100644
--- a/node_modules/react-native-elements/src/buttons/Button.js
+++ b/node_modules/react-native-elements/src/buttons/Button.js
@@ -234,8 +234,8 @@ const styles = {
     color: type === 'solid' ? 'white' : theme.colors.primary,
     fontSize: 16,
     textAlign: 'center',
-    paddingTop: 2,
-    paddingBottom: 1,
+    // paddingTop: 2,
+    paddingBottom: 2,
     ...Platform.select({
       android: {
         fontFamily: 'sans-serif-medium',
diff --git a/node_modules/react-native-elements/src/input/Input.js b/node_modules/react-native-elements/src/input/Input.js
index 7eda00d..e79b0a7 100644
--- a/node_modules/react-native-elements/src/input/Input.js
+++ b/node_modules/react-native-elements/src/input/Input.js
@@ -205,7 +205,7 @@ const styles = {
     flexDirection: 'row',
     borderBottomWidth: 1,
     alignItems: 'center',
-    borderColor: theme.colors.grey3,
+    borderColor: '#f1f1f1',
   }),
   iconContainer: {
     height: 40,
