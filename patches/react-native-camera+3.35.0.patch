diff --git a/node_modules/react-native-camera/android/build.gradle b/node_modules/react-native-camera/android/build.gradle
index 3d217a8..58d204f 100644
--- a/node_modules/react-native-camera/android/build.gradle
+++ b/node_modules/react-native-camera/android/build.gradle
@@ -2,23 +2,6 @@ def safeExtGet(prop, fallback) {
     rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
 }
 
-buildscript {
-  // The Android Gradle plugin is only required when opening the android folder stand-alone.
-  // This avoids unnecessary downloads and potential conflicts when the library is included as a
-  // module dependency in an application project.
-  if (project == rootProject) {
-    repositories {
-      google()
-      jcenter()
-    }
-
-    dependencies {
-      //noinspection GradleDependency
-      classpath("com.android.tools.build:gradle:3.6.3")
-    }
-  }
-}
-
 apply plugin: 'com.android.library'
 
 android {
@@ -52,23 +35,12 @@ android {
   }
 }
 
-repositories {
-  google()
-  jcenter()
-  mavenCentral()
-  maven { url "https://jitpack.io" }
-  maven {
-    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
-    url "$rootDir/../node_modules/react-native/android"
-  }
-}
-
 dependencies {
   def googlePlayServicesVisionVersion = safeExtGet('googlePlayServicesVisionVersion', safeExtGet('googlePlayServicesVersion', '17.0.2'))
 
   //noinspection GradleDynamicVersion
-  implementation 'com.facebook.react:react-native:+'  // From node_modules
-  implementation "com.google.zxing:core:3.3.3"
+  implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"  // From node_modules
+  implementation "com.google.zxing:core:3.5.1"
   implementation "com.drewnoakes:metadata-extractor:2.11.0"
   generalImplementation "com.google.android.gms:play-services-vision:$googlePlayServicesVisionVersion"
   implementation "androidx.exifinterface:exifinterface:1.0.0"
