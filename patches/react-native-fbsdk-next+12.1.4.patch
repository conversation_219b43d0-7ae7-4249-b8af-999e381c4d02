diff --git a/node_modules/react-native-fbsdk-next/android/src/main/java/com/facebook/reactnative/androidsdk/FBProfileModule.java b/node_modules/react-native-fbsdk-next/android/src/main/java/com/facebook/reactnative/androidsdk/FBProfileModule.java
index 71ceda5..1608b9f 100644
--- a/node_modules/react-native-fbsdk-next/android/src/main/java/com/facebook/reactnative/androidsdk/FBProfileModule.java
+++ b/node_modules/react-native-fbsdk-next/android/src/main/java/com/facebook/reactnative/androidsdk/FBProfileModule.java
@@ -7,6 +7,7 @@ import com.facebook.react.bridge.NativeModule;
 import com.facebook.react.bridge.ReactContextBaseJavaModule;
 import com.facebook.react.bridge.ReactMethod;
 import com.facebook.react.bridge.ReactApplicationContext;
+import com.facebook.react.bridge.WritableMap;
 import com.facebook.react.module.annotations.ReactModule;

 import androidx.annotation.NonNull;
@@ -47,7 +48,8 @@ public class FBProfileModule extends ReactContextBaseJavaModule {
               @Override
               public void run() {
                 timer.cancel();
-                callback.invoke(null);
+                WritableMap map = null;
+                callback.invoke(map);
               }
             },
             30000
