diff --git a/node_modules/react-native-sqlite-storage/platforms/android-native/build.gradle b/node_modules/react-native-sqlite-storage/platforms/android-native/build.gradle
index 94a8675..3a78f77 100644
--- a/node_modules/react-native-sqlite-storage/platforms/android-native/build.gradle
+++ b/node_modules/react-native-sqlite-storage/platforms/android-native/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-    repositories {
-        google()
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:3.1.4'
-    }
-}
-
 apply plugin: 'com.android.library'
 
 def safeExtGet(prop, fallback) {
diff --git a/node_modules/react-native-sqlite-storage/platforms/android/build.gradle b/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
index ff79b10..d96904a 100644
--- a/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
+++ b/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-    repositories {
-        google()
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:3.1.4'
-    }
-}
-
 apply plugin: 'com.android.library'
 
 def safeExtGet(prop, fallback) {
