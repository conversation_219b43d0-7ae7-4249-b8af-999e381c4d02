diff --git a/node_modules/@react-native-community/masked-view/android/build.gradle b/node_modules/@react-native-community/masked-view/android/build.gradle
index c4ee933..e402d9d 100644
--- a/node_modules/@react-native-community/masked-view/android/build.gradle
+++ b/node_modules/@react-native-community/masked-view/android/build.gradle
@@ -2,19 +2,6 @@ def safeExtGet(prop, fallback) {
     rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
 }
 
-buildscript {
-  if (project == rootProject) {
-    repositories {
-      google()
-      jcenter()
-    }
-
-    dependencies {
-      classpath 'com.android.tools.build:gradle:3.3.1'
-    }
-  }
-}
-
 apply plugin: 'com.android.library'
 
 android {
@@ -42,16 +29,6 @@ android {
     }
 }
 
-repositories {
-  google()
-  jcenter()
-  maven { url "https://jitpack.io" }
-  maven {
-    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
-    url "$rootDir/../node_modules/react-native/android"
-  }
-}
-
 dependencies {
-  implementation 'com.facebook.react:react-native:+'
+  implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"
 }
