diff --git a/node_modules/react-native-gifted-chat/lib/Message.js b/node_modules/react-native-gifted-chat/lib/Message.js
index 2490081..f845cf8 100644
--- a/node_modules/react-native-gifted-chat/lib/Message.js
+++ b/node_modules/react-native-gifted-chat/lib/Message.js
@@ -91,22 +91,34 @@ export default class Message extends React.Component {
         const { containerStyle, ...props } = this.props;
         return <Avatar {...props}/>;
     }
+
+    renderContent() {
+        const { currentMessage, nextMessage, position, containerStyle, isMultiSelect, renderMultiSelect } = this.props;
+        if (currentMessage.system) {
+            return this.renderSystemMessage();
+        }
+        const sameUser = isSameUser(currentMessage, nextMessage);
+        const content = (<View style={[
+            styles[position].container,
+            { marginBottom: sameUser ? 2 : 10 },
+            !this.props.inverted && { marginBottom: 2 },
+            containerStyle && containerStyle[position],
+        ]}>
+            {this.props.position === 'left' ? this.renderAvatar() : null}
+            {this.renderBubble()}
+            {this.props.position === 'right' ? this.renderAvatar() : null}
+        </View>);
+        if (isMultiSelect) {
+            return renderMultiSelect(this.props, content);
+        }
+        return content;
+    }
+
     render() {
-        const { currentMessage, nextMessage, position, containerStyle } = this.props;
-        if (currentMessage) {
-            const sameUser = isSameUser(currentMessage, nextMessage);
+        if (this.props.currentMessage) {
             return (<View>
           {this.renderDay()}
-          {currentMessage.system ? (this.renderSystemMessage()) : (<View style={[
-                styles[position].container,
-                { marginBottom: sameUser ? 2 : 10 },
-                !this.props.inverted && { marginBottom: 2 },
-                containerStyle && containerStyle[position],
-            ]}>
-              {this.props.position === 'left' ? this.renderAvatar() : null}
-              {this.renderBubble()}
-              {this.props.position === 'right' ? this.renderAvatar() : null}
-            </View>)}
+          {this.renderContent()}
         </View>);
         }
         return null;
diff --git a/node_modules/react-native-gifted-chat/lib/MessageContainer.js b/node_modules/react-native-gifted-chat/lib/MessageContainer.js
index 193772a..0a9d091 100644
--- a/node_modules/react-native-gifted-chat/lib/MessageContainer.js
+++ b/node_modules/react-native-gifted-chat/lib/MessageContainer.js
@@ -55,18 +55,17 @@ export default class MessageContainer extends React.PureComponent {
         this.attachKeyboardListeners = () => {
             const { invertibleScrollViewProps: invertibleProps } = this.props;
             if (invertibleProps) {
-                Keyboard.addListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
-                Keyboard.addListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
-                Keyboard.addListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
-                Keyboard.addListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
+                this.keyboardWillShowSubscription = Keyboard.addListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
+                this.keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
+                this.keyboardWillHideSubscription = Keyboard.addListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
+                this.keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
             }
         };
         this.detachKeyboardListeners = () => {
-            const { invertibleScrollViewProps: invertibleProps } = this.props;
-            Keyboard.removeListener('keyboardWillShow', invertibleProps.onKeyboardWillShow);
-            Keyboard.removeListener('keyboardDidShow', invertibleProps.onKeyboardDidShow);
-            Keyboard.removeListener('keyboardWillHide', invertibleProps.onKeyboardWillHide);
-            Keyboard.removeListener('keyboardDidHide', invertibleProps.onKeyboardDidHide);
+            this.keyboardWillShowSubscription && this.keyboardWillShowSubscription.remove();
+            this.keyboardDidShowSubscription && this.keyboardDidShowSubscription.remove();
+            this.keyboardWillHideSubscription && this.keyboardWillHideSubscription.remove();
+            this.keyboardDidHideSubscription && this.keyboardDidHideSubscription.remove();
         };
         this.renderTypingIndicator = () => {
             if (Platform.OS === 'web') {
@@ -172,7 +171,7 @@ export default class MessageContainer extends React.PureComponent {
         this.onEndReached = ({ distanceFromEnd }) => {
             const { loadEarlier, onLoadEarlier, infiniteScroll, isLoadingEarlier, } = this.props;
             if (infiniteScroll &&
-                distanceFromEnd > 0 &&
+                // distanceFromEnd > 0 &&
                 distanceFromEnd <= 100 &&
                 loadEarlier &&
                 onLoadEarlier &&
@@ -269,7 +268,7 @@ MessageContainer.propTypes = {
     inverted: PropTypes.bool,
     loadEarlier: PropTypes.bool,
     invertibleScrollViewProps: PropTypes.object,
-    extraData: PropTypes.array,
+    extraData: PropTypes.object,
     scrollToBottom: PropTypes.bool,
     scrollToBottomOffset: PropTypes.number,
     scrollToBottomComponent: PropTypes.func,
diff --git a/node_modules/react-native-gifted-chat/lib/MessageText.js b/node_modules/react-native-gifted-chat/lib/MessageText.js
index 6f03145..ef24a34 100644
--- a/node_modules/react-native-gifted-chat/lib/MessageText.js
+++ b/node_modules/react-native-gifted-chat/lib/MessageText.js
@@ -43,6 +43,7 @@ export default class MessageText extends React.Component {
     constructor() {
         super(...arguments);
         this.onUrlPress = (url) => {
+            if (this.props.onPressInterceptor && this.props.onPressInterceptor(this.props)) return;
             // When someone sends a message that includes a website address beginning with "www." (omitting the scheme),
             // react-native-parsed-text recognizes it as a valid url, but Linking fails to open due to the missing scheme.
             if (WWW_URL_PATTERN.test(url)) {
@@ -60,6 +61,7 @@ export default class MessageText extends React.Component {
             }
         };
         this.onPhonePress = (phone) => {
+            if (this.props.onPressInterceptor && this.props.onPressInterceptor(this.props)) return;
             const { optionTitles } = this.props;
             const options = optionTitles && optionTitles.length > 0
                 ? optionTitles.slice(0, 3)
@@ -81,7 +83,10 @@ export default class MessageText extends React.Component {
                 }
             });
         };
-        this.onEmailPress = (email) => Communications.email([email], null, null, null, null);
+        this.onEmailPress = (email) => {
+            if (this.props.onPressInterceptor && this.props.onPressInterceptor(this.props)) return;
+            Communications.email([email], null, null, null, null);
+        }
     }
     shouldComponentUpdate(nextProps) {
         return (!!this.props.currentMessage &&
@@ -103,7 +108,7 @@ export default class MessageText extends React.Component {
             this.props.textStyle && this.props.textStyle[this.props.position],
             this.props.customTextStyle,
         ]} parse={[
-            ...this.props.parsePatterns(linkStyle),
+            ...this.props.parsePatterns(linkStyle, this.props),
             { type: 'url', style: linkStyle, onPress: this.onUrlPress },
             { type: 'phone', style: linkStyle, onPress: this.onPhonePress },
             { type: 'email', style: linkStyle, onPress: this.onEmailPress },
