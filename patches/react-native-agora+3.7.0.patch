diff --git a/node_modules/react-native-agora/android/build.gradle b/node_modules/react-native-agora/android/build.gradle
index 5e66c96..1679fda 100644
--- a/node_modules/react-native-agora/android/build.gradle
+++ b/node_modules/react-native-agora/android/build.gradle
@@ -1,19 +1,3 @@
-buildscript {
-  // Buildscript is evaluated before everything else so we can't use getExtOrDefault
-  def kotlin_version = rootProject.ext.has('kotlinVersion') ? rootProject.ext.get('kotlinVersion') : project.properties['Agora_kotlinVersion']
-
-  repositories {
-    mavenCentral()
-    google()
-  }
-
-  dependencies {
-    classpath 'com.android.tools.build:gradle:3.2.1'
-    // noinspection DifferentKotlinGradleVersion
-    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
-  }
-}
-
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
 
@@ -29,10 +13,8 @@ android {
   compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
   buildToolsVersion getExtOrDefault('buildToolsVersion')
   defaultConfig {
-    minSdkVersion 16
+    minSdkVersion getExtOrIntegerDefault('minSdkVersion')
     targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
-    versionCode 1
-    versionName "1.0"
 
     consumerProguardFiles 'consumer-rules.pro'
   }
@@ -51,82 +33,11 @@ android {
   }
 }
 
-repositories {
-  mavenCentral()
-  google()
-  maven { url 'https://www.jitpack.io' }
-
-  def found = false
-  def defaultDir = null
-  def androidSourcesName = 'React Native sources'
-
-  if (rootProject.ext.has('reactNativeAndroidRoot')) {
-    defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
-  } else {
-    defaultDir = new File(
-      projectDir,
-      '/../../../node_modules/react-native/android'
-    )
-  }
-
-  if (defaultDir.exists()) {
-    maven {
-      url defaultDir.toString()
-      name androidSourcesName
-    }
-
-    logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
-    found = true
-  } else {
-    def parentDir = rootProject.projectDir
-
-    1.upto(5, {
-      if (found) return true
-      parentDir = parentDir.parentFile
-
-      def androidSourcesDir = new File(
-        parentDir,
-        'node_modules/react-native'
-      )
-
-      def androidPrebuiltBinaryDir = new File(
-        parentDir,
-        'node_modules/react-native/android'
-      )
-
-      if (androidPrebuiltBinaryDir.exists()) {
-        maven {
-          url androidPrebuiltBinaryDir.toString()
-          name androidSourcesName
-        }
-
-        logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
-        found = true
-      } else if (androidSourcesDir.exists()) {
-        maven {
-          url androidSourcesDir.toString()
-          name androidSourcesName
-        }
-
-        logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
-        found = true
-      }
-    })
-  }
-
-  if (!found) {
-    throw new GradleException(
-      "${project.name}: unable to locate React Native android sources. " +
-        "Ensure you have you installed React Native as a dependency in your project and try again."
-    )
-  }
-}
-
 def kotlin_version = getExtOrDefault('kotlinVersion')
 
 dependencies {
   // noinspection GradleDynamicVersion
-  api 'com.facebook.react:react-native:+'
+  implementation "com.facebook.react:react-native:${getExtOrDefault('reactNativeVersion')}"
   api 'io.agora.rtc:full-sdk:3.7.0'
   implementation 'io.agora.rtc:full-screen-sharing:3.7.0'
 
diff --git a/node_modules/react-native-agora/android/src/main/AndroidManifest.xml b/node_modules/react-native-agora/android/src/main/AndroidManifest.xml
index 2ef5500..e6e5ceb 100644
--- a/node_modules/react-native-agora/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-agora/android/src/main/AndroidManifest.xml
@@ -15,4 +15,15 @@
     android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
     tools:ignore="ProtectedPermissions" />
 
+  <application>
+    <service
+      android:name="io.agora.rtc.ss.impl.LocalScreenSharingService"
+      android:foregroundServiceType="mediaProjection"
+      android:exported="true"
+      tools:node="replace">
+      <intent-filter>
+        <action android:name="android.intent.action.screenshare" />
+      </intent-filter>
+    </service>
+  </application>
 </manifest>
