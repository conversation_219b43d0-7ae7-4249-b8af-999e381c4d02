diff --git a/node_modules/@react-native-community/art/android/build.gradle b/node_modules/@react-native-community/art/android/build.gradle
index 949bc00..3afa3b8 100644
--- a/node_modules/@react-native-community/art/android/build.gradle
+++ b/node_modules/@react-native-community/art/android/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-  repositories {
-    google()
-    jcenter()
-  }
-
-  dependencies {
-    classpath 'com.android.tools.build:gradle:3.2.1'
-  }
-}
-
 def getExtOrDefault(name) {
   return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeART_' + name]
 }
@@ -17,6 +6,10 @@ def getExtOrIntegerDefault(name) {
   return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeART_' + name]).toInteger()
 }
 
+def safeExtGet(prop, fallback) {
+    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
+}
+
 apply plugin: 'com.android.library'
 
 android {
@@ -29,24 +22,7 @@ android {
   }
 }
 
-repositories {
-  google()
-  jcenter()
-  mavenCentral()
-}
-
-allprojects {
-    repositories {
-        mavenLocal()
-        google()
-        jcenter()
-        maven {
-            url "$rootDir/../node_modules/react-native/android"
-        }
-    }
-}
-
 dependencies {
     //noinspection GradleDynamicVersion
-    api 'com.facebook.react:react-native:+'
+    implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"
 }
diff --git a/node_modules/@react-native-community/art/android/src/main/java/com/reactnativecommunity/art/ARTShapeShadowNode.java b/node_modules/@react-native-community/art/android/src/main/java/com/reactnativecommunity/art/ARTShapeShadowNode.java
index cf57bbb..c2e7f77 100644
--- a/node_modules/@react-native-community/art/android/src/main/java/com/reactnativecommunity/art/ARTShapeShadowNode.java
+++ b/node_modules/@react-native-community/art/android/src/main/java/com/reactnativecommunity/art/ARTShapeShadowNode.java
@@ -22,8 +22,6 @@ import com.facebook.react.common.ReactConstants;
 import com.facebook.react.uimanager.annotations.ReactProp;
 import javax.annotation.Nullable;
 
-import static com.facebook.react.common.ArrayUtils.copyArray;
-
 /**
  * Shadow node for virtual ARTShape view
  */
