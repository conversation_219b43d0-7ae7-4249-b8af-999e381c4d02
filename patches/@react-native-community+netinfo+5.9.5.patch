diff --git a/node_modules/@react-native-community/netinfo/android/build.gradle b/node_modules/@react-native-community/netinfo/android/build.gradle
index be364b1..1a461f4 100644
--- a/node_modules/@react-native-community/netinfo/android/build.gradle
+++ b/node_modules/@react-native-community/netinfo/android/build.gradle
@@ -1,19 +1,3 @@
-buildscript {
-  // The Android Gradle plugin is only required when opening the android folder stand-alone.
-  // This avoids unnecessary downloads and potential conflicts when the library is included as a
-  // module dependency in an application project.
-  if (project == rootProject) {
-    repositories {
-      google()
-      jcenter()
-    }
-
-    dependencies {
-      classpath("com.android.tools.build:gradle:3.6.3")
-    }
-  }
-}
-
 def getExtOrInitialValue(name, initialValue) {
   return rootProject.ext.has(name) ? rootProject.ext.get(name) : initialValue
 }
@@ -26,6 +10,10 @@ def getExtOrIntegerDefault(name) {
   return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeNetInfo_' + name]).toInteger()
 }

+def safeExtGet(prop, fallback) {
+  rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
+}
+
 apply plugin: 'com.android.library'

 android {
@@ -40,19 +28,9 @@ android {
   }
 }

-repositories {
-  maven {
-    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
-    url "$rootDir/../node_modules/react-native/android"
-  }
-  google()
-  jcenter()
-  mavenCentral()
-}
-
 dependencies {
   //noinspection GradleDynamicVersion
-  implementation 'com.facebook.react:react-native:+'
+  implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"

   def supportLibVersion = getExtOrInitialValue('supportLibVersion', getExtOrInitialValue('supportVersion', null))
   def androidXVersion = getExtOrInitialValue('androidXVersion', null)
diff --git a/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/AmazonFireDeviceConnectivityPoller.java b/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/AmazonFireDeviceConnectivityPoller.java
index d58c798..7e26fdd 100644
--- a/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/AmazonFireDeviceConnectivityPoller.java
+++ b/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/AmazonFireDeviceConnectivityPoller.java
@@ -90,7 +90,11 @@ public class AmazonFireDeviceConnectivityPoller {
         IntentFilter filter = new IntentFilter();
         filter.addAction(ACTION_INTERNET_DOWN);
         filter.addAction(ACTION_INTERNET_UP);
-        context.registerReceiver(receiver, filter);
+        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
+            context.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED);
+        } else {
+            context.registerReceiver(receiver, filter);
+        }

         receiver.registered = true;
     }
diff --git a/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/BroadcastReceiverConnectivityReceiver.java b/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/BroadcastReceiverConnectivityReceiver.java
index 99ad861..86142f6 100644
--- a/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/BroadcastReceiverConnectivityReceiver.java
+++ b/node_modules/@react-native-community/netinfo/android/src/main/java/com/reactnativecommunity/netinfo/BroadcastReceiverConnectivityReceiver.java
@@ -13,6 +13,8 @@ import android.content.Intent;
 import android.content.IntentFilter;
 import android.net.ConnectivityManager;
 import android.net.NetworkInfo;
+import android.os.Build;
+
 import com.facebook.react.bridge.ReactApplicationContext;
 import com.reactnativecommunity.netinfo.types.CellularGeneration;
 import com.reactnativecommunity.netinfo.types.ConnectionType;
@@ -38,7 +40,11 @@ public class BroadcastReceiverConnectivityReceiver extends ConnectivityReceiver
     public void register() {
         IntentFilter filter = new IntentFilter();
         filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
-        getReactContext().registerReceiver(mConnectivityBroadcastReceiver, filter);
+        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
+            getReactContext().registerReceiver(mConnectivityBroadcastReceiver, filter, Context.RECEIVER_EXPORTED);
+        } else {
+            getReactContext().registerReceiver(mConnectivityBroadcastReceiver, filter);
+        }
         mConnectivityBroadcastReceiver.setRegistered(true);
         updateAndSendConnectionType();
     }
