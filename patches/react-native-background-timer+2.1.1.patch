diff --git a/node_modules/react-native-background-timer/index.js b/node_modules/react-native-background-timer/index.js
index 545aaa1..33d5b11 100644
--- a/node_modules/react-native-background-timer/index.js
+++ b/node_modules/react-native-background-timer/index.js
@@ -17,13 +17,13 @@ class BackgroundTimer {
     Emitter.addListener('backgroundTimer.timeout', (id) => {
       if (this.callbacks[id]) {
         const callbackById = this.callbacks[id];
-        const { callback } = callbackById;
+        const { callback, args } = callbackById;
         if (!this.callbacks[id].interval) {
           delete this.callbacks[id];
         } else {
           RNBackgroundTimer.setTimeout(id, this.callbacks[id].timeout);
         }
-        callback();
+        callback(...args);
       }
     });
   }
@@ -63,13 +63,14 @@ class BackgroundTimer {
   }
 
   // New API, allowing for multiple timers
-  setTimeout(callback, timeout) {
+  setTimeout(callback, timeout, ...args) {
     this.uniqueId += 1;
     const timeoutId = this.uniqueId;
     this.callbacks[timeoutId] = {
       callback,
       interval: false,
       timeout,
+      args,
     };
     RNBackgroundTimer.setTimeout(timeoutId, timeout);
     return timeoutId;
@@ -82,13 +83,14 @@ class BackgroundTimer {
     }
   }
 
-  setInterval(callback, timeout) {
+  setInterval(callback, timeout, ...args) {
     this.uniqueId += 1;
     const intervalId = this.uniqueId;
     this.callbacks[intervalId] = {
       callback,
       interval: true,
       timeout,
+      args,
     };
     RNBackgroundTimer.setTimeout(intervalId, timeout);
     return intervalId;
