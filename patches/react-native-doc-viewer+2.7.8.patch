diff --git a/node_modules/react-native-doc-viewer/android/build.gradle b/node_modules/react-native-doc-viewer/android/build.gradle
index ccd521a..af24405 100644
--- a/node_modules/react-native-doc-viewer/android/build.gradle
+++ b/node_modules/react-native-doc-viewer/android/build.gradle
@@ -1,16 +1,9 @@
+apply plugin: 'com.android.library'
 
-buildscript {
-    repositories {
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:1.3.1'
-    }
+def getExtOrDefault(name) {
+    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeCameraRoll_' + name]
 }
 
-apply plugin: 'com.android.library'
-
 android {
     compileSdkVersion 25
     buildToolsVersion "25.0.0"
@@ -31,6 +24,6 @@ repositories {
 }
 
 dependencies {
-    compile 'com.facebook.react:react-native:+'
+    implementation "com.facebook.react:react-native:${getExtOrDefault('reactNativeVersion')}"
 }
 
