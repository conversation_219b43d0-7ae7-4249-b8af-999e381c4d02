diff --git a/node_modules/react-native-keychain/android/build.gradle b/node_modules/react-native-keychain/android/build.gradle
index ff2d8b1..d18c27a 100755
--- a/node_modules/react-native-keychain/android/build.gradle
+++ b/node_modules/react-native-keychain/android/build.gradle
@@ -1,16 +1,4 @@
-buildscript {
-  repositories {
-    maven {
-      url 'https://plugins.gradle.org/m2/'
-    }
-  }
-  dependencies {
-    classpath 'com.adarshr:gradle-test-logger-plugin:2.0.0'
-  }
-}
-
 apply plugin: 'com.android.library'
-apply plugin: "com.adarshr.test-logger"

 def safeExtGet(prop, fallback) {
   rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
@@ -23,8 +11,6 @@ android {
   defaultConfig {
     minSdkVersion safeExtGet('minSdkVersion', 19)
     targetSdkVersion safeExtGet('targetSdkVersion', 29)
-    versionCode 1
-    versionName "1.0"
   }

   lintOptions {
@@ -35,16 +21,6 @@ android {
     sourceCompatibility JavaVersion.VERSION_1_8
     targetCompatibility JavaVersion.VERSION_1_8
   }
-
-  testOptions {
-    unitTests {
-      includeAndroidResources = true
-    }
-  }
-}
-
-repositories {
-  mavenCentral()
 }

 dependencies {
@@ -60,27 +36,4 @@ dependencies {
   /* version higher 1.1.3 has problems with included soloader packages,
       https://github.com/facebook/conceal/releases */
   implementation "com.facebook.conceal:conceal:1.1.3@aar"
-
-  /* Unit Testing Frameworks */
-  testImplementation 'junit:junit:4.13'
-
-  /* Mockito, https://mvnrepository.com/artifact/org.mockito/mockito-inline */
-  testImplementation 'org.mockito:mockito-inline:3.2.4'
-
-  /* https://mvnrepository.com/artifact/org.hamcrest/hamcrest/2.1 */
-  testImplementation 'org.hamcrest:hamcrest:2.2'
-
-  /* http://robolectric.org/getting-started/ */
-  testImplementation("org.robolectric:robolectric:4.3.1")
-
-  /* https://mvnrepository.com/artifact/androidx.test.ext/junit */
-  testImplementation "androidx.test.ext:junit:1.1.2-alpha03"
-
-  /* https://mvnrepository.com/artifact/androidx.test/core */
-  testImplementation 'androidx.test:core:1.3.0-alpha03'
-  testImplementation "androidx.test:runner:1.3.0-alpha03"
-  testImplementation "androidx.test:monitor:1.3.0-alpha03"
-
-  // Uncomment for including JRE implementation of crypto api (that is used in Robolectric tests)
-  // testImplementation fileTree(dir: "${System.properties.get("java.home")}/lib", include: ["jce.jar"])
 }
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeCipherSpi.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeCipherSpi.java
deleted file mode 100644
index a4d37e8..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeCipherSpi.java
+++ /dev/null
@@ -1,6 +0,0 @@
-package com.oblador.keychain;
-
-import javax.crypto.CipherSpi;
-
-public abstract class FakeCipherSpi extends CipherSpi {
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyFactorySpi.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyFactorySpi.java
deleted file mode 100644
index d0038bd..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyFactorySpi.java
+++ /dev/null
@@ -1,15 +0,0 @@
-package com.oblador.keychain;
-
-import java.security.Key;
-import java.security.KeyFactorySpi;
-import java.security.spec.InvalidKeySpecException;
-import java.security.spec.KeySpec;
-
-public abstract class FakeKeyFactorySpi extends KeyFactorySpi {
-  @Override
-  protected <T extends KeySpec> T engineGetKeySpec(Key key, Class<T> keySpec) throws InvalidKeySpecException {
-    return doEngineGetKeySpec(key, keySpec);
-  }
-
-  public abstract <T extends KeySpec> T doEngineGetKeySpec(Key key, Class<T> keySpec);
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyGeneratorSpi.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyGeneratorSpi.java
deleted file mode 100644
index 371f32f..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyGeneratorSpi.java
+++ /dev/null
@@ -1,13 +0,0 @@
-package com.oblador.keychain;
-
-import javax.crypto.KeyGeneratorSpi;
-import javax.crypto.SecretKey;
-
-public abstract class FakeKeyGeneratorSpi extends KeyGeneratorSpi {
-  @Override
-  protected SecretKey engineGenerateKey() {
-    return doEngineGenerateKey();
-  }
-
-  public abstract SecretKey doEngineGenerateKey();
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyStoreSpi.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyStoreSpi.java
deleted file mode 100644
index 1ae6773..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeKeyStoreSpi.java
+++ /dev/null
@@ -1,97 +0,0 @@
-package com.oblador.keychain;
-
-import java.io.IOException;
-import java.io.InputStream;
-import java.io.OutputStream;
-import java.security.Key;
-import java.security.KeyStoreException;
-import java.security.KeyStoreSpi;
-import java.security.NoSuchAlgorithmException;
-import java.security.UnrecoverableKeyException;
-import java.security.cert.Certificate;
-import java.security.cert.CertificateException;
-import java.util.Date;
-import java.util.Enumeration;
-
-public final class FakeKeyStoreSpi extends KeyStoreSpi {
-
-  @Override
-  public Key engineGetKey(String alias, char[] password) throws NoSuchAlgorithmException, UnrecoverableKeyException {
-    return null;
-  }
-
-  @Override
-  public Certificate[] engineGetCertificateChain(String alias) {
-    return new Certificate[0];
-  }
-
-  @Override
-  public Certificate engineGetCertificate(String alias) {
-    return null;
-  }
-
-  @Override
-  public Date engineGetCreationDate(String alias) {
-    return null;
-  }
-
-  @Override
-  public void engineSetKeyEntry(String alias, Key key, char[] password, Certificate[] chain) throws KeyStoreException {
-
-  }
-
-  @Override
-  public void engineSetKeyEntry(String alias, byte[] key, Certificate[] chain) throws KeyStoreException {
-
-  }
-
-  @Override
-  public void engineSetCertificateEntry(String alias, Certificate cert) throws KeyStoreException {
-
-  }
-
-  @Override
-  public void engineDeleteEntry(String alias) throws KeyStoreException {
-
-  }
-
-  @Override
-  public Enumeration<String> engineAliases() {
-    return null;
-  }
-
-  @Override
-  public boolean engineContainsAlias(String alias) {
-    return false;
-  }
-
-  @Override
-  public int engineSize() {
-    return 0;
-  }
-
-  @Override
-  public boolean engineIsKeyEntry(String alias) {
-    return false;
-  }
-
-  @Override
-  public boolean engineIsCertificateEntry(String alias) {
-    return false;
-  }
-
-  @Override
-  public String engineGetCertificateAlias(Certificate cert) {
-    return null;
-  }
-
-  @Override
-  public void engineStore(OutputStream stream, char[] password) throws CertificateException, IOException, NoSuchAlgorithmException {
-
-  }
-
-  @Override
-  public void engineLoad(InputStream stream, char[] password) throws CertificateException, IOException, NoSuchAlgorithmException {
-
-  }
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeProvider.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeProvider.java
deleted file mode 100644
index 055e4a1..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeProvider.java
+++ /dev/null
@@ -1,34 +0,0 @@
-package com.oblador.keychain;
-
-import java.security.Provider;
-import java.util.HashMap;
-
-public final class FakeProvider extends Provider {
-  public static final String NAME = "AndroidKeyStore";
-  public final HashMap<String, HashMap<String, MocksForProvider>> mocks = new HashMap<>();
-  public final HashMap<String, Object> configuration = new HashMap<>();
-
-  public FakeProvider() {
-    super(NAME, 1.0, "Fake");
-
-    put("KeyStore.AndroidKeyStore", FakeKeyStoreSpi.class.getName());
-  }
-
-  @Override
-  public synchronized Service getService(String type, String algorithm) {
-    MocksForProvider mock;
-    HashMap<String, MocksForProvider> inner;
-
-    if (null == (inner = mocks.get(type))) {
-      mocks.put(type, (inner = new HashMap<>()));
-    }
-
-    if (null == (mock = inner.get(algorithm))) {
-      inner.put(algorithm, (mock = new MocksForProvider()));
-    }
-
-    mock.configure(type, this, configuration);
-
-    return mock.service;
-  }
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeSecretKeyFactorySpi.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeSecretKeyFactorySpi.java
deleted file mode 100644
index 1fc86b5..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/FakeSecretKeyFactorySpi.java
+++ /dev/null
@@ -1,17 +0,0 @@
-package com.oblador.keychain;
-
-import java.security.spec.InvalidKeySpecException;
-import java.security.spec.KeySpec;
-
-import javax.crypto.SecretKey;
-import javax.crypto.SecretKeyFactorySpi;
-
-public abstract class FakeSecretKeyFactorySpi extends SecretKeyFactorySpi {
-
-  @Override
-  protected KeySpec engineGetKeySpec(SecretKey key, Class<?> keySpec) throws InvalidKeySpecException {
-    return doEngineGetKeySpec(key, keySpec);
-  }
-
-  public abstract KeySpec doEngineGetKeySpec(SecretKey key, Class<?> keySpec);
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/KeychainModuleTests.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/KeychainModuleTests.java
deleted file mode 100644
index 38506e1..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/KeychainModuleTests.java
+++ /dev/null
@@ -1,468 +0,0 @@
-package com.oblador.keychain;
-
-import android.content.Context;
-import android.content.SharedPreferences;
-import android.content.pm.PackageManager;
-import android.hardware.fingerprint.FingerprintManager;
-import android.os.Build;
-
-import androidx.annotation.NonNull;
-import androidx.biometric.BiometricManager;
-import androidx.test.core.app.ApplicationProvider;
-
-import com.facebook.react.bridge.JavaOnlyMap;
-import com.facebook.react.bridge.Promise;
-import com.facebook.react.bridge.ReactApplicationContext;
-import com.oblador.keychain.KeychainModule.AccessControl;
-import com.oblador.keychain.KeychainModule.Errors;
-import com.oblador.keychain.KeychainModule.KnownCiphers;
-import com.oblador.keychain.KeychainModule.Maps;
-import com.oblador.keychain.cipherStorage.CipherStorage;
-import com.oblador.keychain.cipherStorage.CipherStorageBase;
-import com.oblador.keychain.cipherStorage.CipherStorageFacebookConceal;
-import com.oblador.keychain.cipherStorage.CipherStorageKeystoreAesCbc;
-import com.oblador.keychain.cipherStorage.CipherStorageKeystoreRsaEcb;
-import com.oblador.keychain.exceptions.CryptoFailedException;
-import com.oblador.keychain.exceptions.KeyStoreAccessException;
-
-import org.junit.After;
-import org.junit.Before;
-import org.junit.ClassRule;
-import org.junit.Rule;
-import org.junit.Test;
-import org.junit.rules.TestName;
-import org.junit.rules.Timeout;
-import org.junit.runner.RunWith;
-import org.mockito.ArgumentCaptor;
-import org.mockito.Mockito;
-import org.mockito.junit.MockitoJUnit;
-import org.mockito.junit.MockitoRule;
-import org.mockito.junit.VerificationCollector;
-import org.robolectric.RobolectricTestRunner;
-import org.robolectric.annotation.Config;
-
-import java.security.KeyStore;
-import java.security.Security;
-
-import javax.crypto.Cipher;
-
-import static org.hamcrest.MatcherAssert.assertThat;
-import static org.hamcrest.Matchers.instanceOf;
-import static org.hamcrest.Matchers.is;
-import static org.hamcrest.Matchers.notNullValue;
-import static org.mockito.ArgumentMatchers.any;
-import static org.mockito.ArgumentMatchers.eq;
-import static org.mockito.ArgumentMatchers.isNull;
-import static org.mockito.Mockito.mock;
-import static org.mockito.Mockito.verify;
-import static org.mockito.Mockito.when;
-import static org.robolectric.Shadows.shadowOf;
-
-@RunWith(RobolectricTestRunner.class)
-public class KeychainModuleTests {
-  public static final byte[] BYTES_USERNAME = "username".getBytes();
-  public static final byte[] BYTES_PASSWORD = "password".getBytes();
-  /**
-   * Cancel test after 5 seconds.
-   */
-  @ClassRule
-  public static Timeout timeout = Timeout.seconds(10);
-  /**
-   * Get test method name.
-   */
-  @Rule
-  public TestName methodName = new TestName();
-  /**
-   * Mock all the dependencies.
-   */
-  @Rule
-  public MockitoRule mockDependencies = MockitoJUnit.rule().silent();
-  @Rule
-  public VerificationCollector collector = MockitoJUnit.collector();
-  /**
-   * Security fake provider.
-   */
-  private FakeProvider provider = new FakeProvider();
-
-  @Before
-  public void setUp() throws Exception {
-    provider.configuration.clear();
-
-    Security.insertProviderAt(provider, 0);
-  }
-
-  @After
-  public void tearDown() throws Exception {
-    Security.removeProvider(FakeProvider.NAME);
-  }
-
-  @NonNull
-  private ReactApplicationContext getRNContext() {
-    return new ReactApplicationContext(ApplicationProvider.getApplicationContext());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.LOLLIPOP)
-  public void testFingerprintNoHardware_api21() throws Exception {
-    // GIVEN: API21 android version
-    ReactApplicationContext context = getRNContext();
-    KeychainModule module = new KeychainModule(context);
-
-    // WHEN: verify availability
-    final int result = BiometricManager.from(context).canAuthenticate();
-    final boolean isFingerprintAvailable = module.isFingerprintAuthAvailable();
-
-    // THEN: in api lower 23 - biometric is not available at all
-    assertThat(isFingerprintAvailable, is(false));
-    assertThat(result, is(BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE));
-
-    // fingerprint hardware not available, minimal API for fingerprint is api23, Android 6.0
-    // https://developer.android.com/about/versions/marshmallow/android-6.0
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testFingerprintAvailableButNotConfigured_api23() throws Exception {
-    // GIVEN:
-    //   fingerprint api available but not configured properly
-    //   API23 android version
-    ReactApplicationContext context = getRNContext();
-    KeychainModule module = new KeychainModule(context);
-
-    // set that hardware is available
-    FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-
-    // WHEN: check availability
-    final int result = BiometricManager.from(context).canAuthenticate();
-    final boolean isFingerprintWorking = module.isFingerprintAuthAvailable();
-
-    // THEN: another status from biometric api, fingerprint is still unavailable
-    assertThat(result, is(BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED));
-    assertThat(isFingerprintWorking, is(false));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testFingerprintConfigured_api23() throws Exception {
-    // GIVEN:
-    //   API23 android version
-    //   Fingerprints are configured
-    //   fingerprint feature is ignored by android os
-    ReactApplicationContext context = getRNContext();
-
-    // set that hardware is available
-    FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-    shadowOf(fm).setDefaultFingerprints(5); // 5 fingerprints are available
-
-    // WHEN: check availability
-    final int result = BiometricManager.from(context).canAuthenticate();
-    final KeychainModule module = new KeychainModule(context);
-    final boolean isFingerprintWorking = module.isFingerprintAuthAvailable();
-
-    // THEN: biometric works
-    assertThat(result, is(BiometricManager.BIOMETRIC_SUCCESS));
-    assertThat(isFingerprintWorking, is(true));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testFingerprintConfigured_api28() throws Exception {
-    // GIVEN:
-    //   API28 android version
-    //   for api24+ system feature should be enabled
-    //   fingerprints are configured
-    ReactApplicationContext context = getRNContext();
-    shadowOf(context.getPackageManager()).setSystemFeature(PackageManager.FEATURE_FINGERPRINT, true);
-
-    // set that hardware is available
-    FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-    shadowOf(fm).setDefaultFingerprints(5); // 5 fingerprints are available
-
-    // WHEN: verify availability
-    final int result = BiometricManager.from(context).canAuthenticate();
-    final KeychainModule module = new KeychainModule(context);
-    final boolean isFingerprintWorking = module.isFingerprintAuthAvailable();
-
-    // THEN: biometrics works
-    assertThat(result, is(BiometricManager.BIOMETRIC_SUCCESS));
-    assertThat(isFingerprintWorking, is(true));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.KITKAT)
-  public void testExtractFacebookConceal_NoHardware_api19() throws Exception {
-    // GIVEN:
-    //  API19, minimal Android version
-    final ReactApplicationContext context = getRNContext();
-
-    // WHEN: ask keychain for secured storage
-    final KeychainModule module = new KeychainModule(context);
-    final CipherStorage storage = module.getCipherStorageForCurrentAPILevel();
-
-    // THEN: expected Facebook cipher storage, its the only one that supports API19
-    assertThat(storage, notNullValue());
-    assertThat(storage, instanceOf(CipherStorageFacebookConceal.class));
-    assertThat(storage.isBiometrySupported(), is(false));
-    assertThat(storage.securityLevel(), is(SecurityLevel.ANY));
-    assertThat(storage.getMinSupportedApiLevel(), is(Build.VERSION_CODES.JELLY_BEAN));
-    assertThat(storage.supportsSecureHardware(), is(false));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testExtractAesCbc_NoFingerprintConfigured_api23() throws Exception {
-    // GIVEN:
-    //  API23 android version
-    final ReactApplicationContext context = getRNContext();
-
-    // WHEN: get the best secured storage
-    final KeychainModule module = new KeychainModule(context);
-    final CipherStorage storage = module.getCipherStorageForCurrentAPILevel();
-
-    // THEN:
-    //   expected AES cipher storage due no fingerprint available
-    //   AES win and returned instead of facebook cipher
-    assertThat(storage, notNullValue());
-    assertThat(storage, instanceOf(CipherStorageKeystoreAesCbc.class));
-    assertThat(storage.isBiometrySupported(), is(false));
-    assertThat(storage.securityLevel(), is(SecurityLevel.SECURE_HARDWARE));
-    assertThat(storage.getMinSupportedApiLevel(), is(Build.VERSION_CODES.M));
-    assertThat(storage.supportsSecureHardware(), is(true));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testExtractRsaEcb_EnabledFingerprint_api23() throws Exception {
-    // GIVEN:
-    //   API23 android version
-    //   fingerprints configured
-    final ReactApplicationContext context = getRNContext();
-
-    // set that hardware is available and fingerprints configured
-    final FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-    shadowOf(fm).setDefaultFingerprints(5); // 5 fingerprints are available
-
-    // WHEN: fingerprint availability influence on storage selection
-    final KeychainModule module = new KeychainModule(context);
-    final boolean isFingerprintWorking = module.isFingerprintAuthAvailable();
-    final CipherStorage storage = module.getCipherStorageForCurrentAPILevel();
-
-    // THEN: expected RsaEcb with working fingerprint
-    assertThat(isFingerprintWorking, is(true));
-    assertThat(storage, notNullValue());
-    assertThat(storage, instanceOf(CipherStorageKeystoreRsaEcb.class));
-    assertThat(storage.isBiometrySupported(), is(true));
-    assertThat(storage.securityLevel(), is(SecurityLevel.SECURE_HARDWARE));
-    assertThat(storage.getMinSupportedApiLevel(), is(Build.VERSION_CODES.M));
-    assertThat(storage.supportsSecureHardware(), is(true));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testExtractRsaEcb_EnabledFingerprint_api28() throws Exception {
-    // GIVEN:
-    //   API28 android version
-    //   fingerprint feature enabled
-    //   fingerprints configured
-    final ReactApplicationContext context = getRNContext();
-    shadowOf(context.getPackageManager()).setSystemFeature(PackageManager.FEATURE_FINGERPRINT, true);
-
-    // set that hardware is available and fingerprints configured
-    final FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-    shadowOf(fm).setDefaultFingerprints(5); // 5 fingerprints are available
-
-    // WHEN: get secured storage
-    final int result = BiometricManager.from(context).canAuthenticate();
-    final KeychainModule module = new KeychainModule(context);
-    final boolean isFingerprintWorking = module.isFingerprintAuthAvailable();
-    final CipherStorage storage = module.getCipherStorageForCurrentAPILevel();
-
-    // THEN: expected RsaEcb with working fingerprint
-    assertThat(isFingerprintWorking, is(true));
-    assertThat(result, is(BiometricManager.BIOMETRIC_SUCCESS));
-    assertThat(storage, notNullValue());
-    assertThat(storage, instanceOf(CipherStorageKeystoreRsaEcb.class));
-    assertThat(storage.isBiometrySupported(), is(true));
-    assertThat(storage.securityLevel(), is(SecurityLevel.SECURE_HARDWARE));
-    assertThat(storage.getMinSupportedApiLevel(), is(Build.VERSION_CODES.M));
-    assertThat(storage.supportsSecureHardware(), is(true));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testMigrateStorageFromOlder_api23() throws Exception {
-    // GIVEN:
-    final ReactApplicationContext context = getRNContext();
-    final CipherStorage aes = Mockito.mock(CipherStorage.class);
-    final CipherStorage rsa = Mockito.mock(CipherStorage.class);
-    when(rsa.getCipherStorageName()).thenReturn("dummy");
-
-    final CipherStorage.DecryptionResult decrypted = new CipherStorage.DecryptionResult("user", "password");
-    final CipherStorage.EncryptionResult encrypted = new CipherStorage.EncryptionResult("user".getBytes(), "password".getBytes(), rsa);
-    final KeychainModule module = new KeychainModule(context);
-    final SharedPreferences prefs = context.getSharedPreferences(PrefsStorage.KEYCHAIN_DATA, Context.MODE_PRIVATE);
-
-    when(
-      rsa.encrypt(eq("dummy"), eq("user"), eq("password"), any())
-    ).thenReturn(encrypted);
-
-    // WHEN:
-    module.migrateCipherStorage("dummy", rsa, aes, decrypted);
-    final String username = prefs.getString(PrefsStorage.getKeyForUsername("dummy"), "");
-    final String password = prefs.getString(PrefsStorage.getKeyForPassword("dummy"), "");
-    final String cipherName = prefs.getString(PrefsStorage.getKeyForCipherStorage("dummy"), "");
-
-    // THEN:
-    //   delete of key from old storage
-    //   re-store of encrypted data in shared preferences
-    verify(rsa).encrypt("dummy", "user", "password", SecurityLevel.ANY);
-    verify(aes).removeKey("dummy");
-
-    // Base64.DEFAULT force '\n' char in the end of string
-    assertThat(username, is("dXNlcg==\n"));
-    assertThat(password, is("cGFzc3dvcmQ=\n"));
-    assertThat(cipherName, is("dummy"));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testGetSecurityLevel_Unspecified_api28() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // WHEN:
-    module.getSecurityLevel(null, mockPromise);
-
-    // THEN:
-    verify(mockPromise).resolve(SecurityLevel.SECURE_HARDWARE.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testGetSecurityLevel_Unspecified_api23() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // WHEN:
-    module.getSecurityLevel(null, mockPromise);
-
-    // THEN:
-    verify(mockPromise).resolve(SecurityLevel.SECURE_HARDWARE.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.LOLLIPOP)
-  public void testGetSecurityLevel_Unspecified_api21() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // WHEN:
-    module.getSecurityLevel(null, mockPromise);
-
-    // THEN:
-    verify(mockPromise).resolve(SecurityLevel.ANY.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.KITKAT)
-  public void testGetSecurityLevel_Unspecified_api19() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // WHEN:
-    module.getSecurityLevel(null, mockPromise);
-
-    // THEN:
-    verify(mockPromise).resolve(SecurityLevel.ANY.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testGetSecurityLevel_NoBiometry_api28() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // WHEN:
-    final JavaOnlyMap options = new JavaOnlyMap();
-    options.putString(Maps.ACCESS_CONTROL, AccessControl.DEVICE_PASSCODE);
-
-    module.getSecurityLevel(options, mockPromise);
-
-    // THEN:
-    verify(mockPromise).resolve(SecurityLevel.SECURE_HARDWARE.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testGetSecurityLevel_NoBiometry_NoSecuredHardware_api28() throws Exception {
-    // GIVE:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final Promise mockPromise = mock(Promise.class);
-
-    // set key info - software method
-    provider.configuration.put("isInsideSecureHardware", false);
-
-    // WHEN:
-    final JavaOnlyMap options = new JavaOnlyMap();
-    options.putString(Maps.ACCESS_CONTROL, AccessControl.DEVICE_PASSCODE);
-
-    module.getSecurityLevel(options, mockPromise);
-
-    // THEN:
-    // expected AesCbc usage
-    assertThat(provider.mocks.get("KeyGenerator"), notNullValue());
-    assertThat(provider.mocks.get("KeyGenerator").get("AES"), notNullValue());
-    assertThat(provider.mocks.get("KeyPairGenerator"), notNullValue());
-    assertThat(provider.mocks.get("KeyPairGenerator").get("RSA"), notNullValue());
-    verify(mockPromise).resolve(SecurityLevel.SECURE_SOFTWARE.name());
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testDowngradeBiometricToAes_api28() throws Exception {
-    // GIVEN:
-    final ReactApplicationContext context = getRNContext();
-    final KeychainModule module = new KeychainModule(context);
-    final PrefsStorage prefs = new PrefsStorage(context);
-    final Cipher mockCipher = Mockito.mock(Cipher.class);
-    final KeyStore mockKeyStore = Mockito.mock(KeyStore.class);
-    final CipherStorage storage = module.getCipherStorageByName(KnownCiphers.RSA);
-    final CipherStorage.EncryptionResult result = new CipherStorage.EncryptionResult(BYTES_USERNAME, BYTES_PASSWORD, storage);
-    final Promise mockPromise = mock(Promise.class);
-    final JavaOnlyMap options = new JavaOnlyMap();
-    options.putString(Maps.SERVICE, "dummy");
-
-    // store record done with RSA/Biometric cipher
-    prefs.storeEncryptedEntry("dummy", result);
-
-    assertThat(storage, instanceOf(CipherStorage.class));
-    ((CipherStorageBase)storage).setCipher(mockCipher).setKeyStore(mockKeyStore);
-    when(mockKeyStore.getKey(eq("dummy"), isNull())).thenReturn(null); // return empty Key!
-
-    // WHEN:
-    module.getGenericPasswordForOptions(options, mockPromise);
-
-    // THEN:
-    ArgumentCaptor<Exception> exception = ArgumentCaptor.forClass(Exception.class);
-    verify(mockPromise).reject(eq(Errors.E_CRYPTO_FAILED), exception.capture());
-    assertThat(exception.getValue(), instanceOf(CryptoFailedException.class));
-    assertThat(exception.getValue().getCause(), instanceOf(KeyStoreAccessException.class));
-    assertThat(exception.getValue().getMessage(), is("Wrapped error: Empty key extracted!"));
-  }
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/MocksForProvider.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/MocksForProvider.java
deleted file mode 100644
index 8cf95e6..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/MocksForProvider.java
+++ /dev/null
@@ -1,109 +0,0 @@
-package com.oblador.keychain;
-
-import android.security.keystore.KeyInfo;
-
-import androidx.annotation.NonNull;
-import androidx.annotation.Nullable;
-
-import org.mockito.MockSettings;
-import org.mockito.Mockito;
-
-import java.security.Key;
-import java.security.KeyPair;
-import java.security.KeyPairGeneratorSpi;
-import java.security.NoSuchAlgorithmException;
-import java.security.PrivateKey;
-import java.security.Provider;
-import java.security.UnrecoverableKeyException;
-import java.security.spec.InvalidKeySpecException;
-import java.util.Map;
-
-import javax.crypto.SecretKey;
-
-import static org.mockito.ArgumentMatchers.any;
-import static org.mockito.ArgumentMatchers.isNull;
-import static org.mockito.Mockito.when;
-import static org.mockito.Mockito.withSettings;
-
-@SuppressWarnings({"WeakerAccess"})
-public final class MocksForProvider {
-  public static final String KEY_GENERATOR = "KeyGenerator";
-  public static final String KEY_PAIR_GENERATOR = "KeyPairGenerator";
-  public static final String KEY_FACTORY = "KeyFactory";
-  public static final String KEY_STORE = "KeyStore";
-  public static final String KEY_CIPHER = "Cipher";
-  public static final String SECRET_KEY_FACTORY = "SecretKeyFactory";
-
-  public final MockSettings settings = withSettings();//.verboseLogging();
-
-  public final Provider.Service service = Mockito.mock(Provider.Service.class, settings);
-  public final KeyPairGeneratorSpi kpgSpi = Mockito.mock(KeyPairGeneratorSpi.class, settings);
-  public final FakeKeyGeneratorSpi kgSpi = Mockito.mock(FakeKeyGeneratorSpi.class, settings);
-  public final FakeSecretKeyFactorySpi skfSpi = Mockito.mock(FakeSecretKeyFactorySpi.class, settings);
-  public final FakeKeyFactorySpi kfSpi = Mockito.mock(FakeKeyFactorySpi.class, settings);
-  public final FakeKeyStoreSpi ksSpi = Mockito.mock(FakeKeyStoreSpi.class, settings);
-  public final FakeCipherSpi cSpi = Mockito.mock(FakeCipherSpi.class, settings);
-  public final KeyPair keyPair = Mockito.mock(KeyPair.class, settings);
-  public final PrivateKey privateKey = Mockito.mock(PrivateKey.class, settings);
-  public final KeyInfo keyInfo = Mockito.mock(KeyInfo.class, settings);
-  public final SecretKey secretKey = Mockito.mock(SecretKey.class, settings);
-  public final Key key = Mockito.mock(Key.class, settings);
-
-  public void configure(@NonNull final String type, @NonNull final Provider provider, @Nullable final Map<String, Object> configuration) {
-    try {
-      innerConfiguration(type, provider, configuration);
-    } catch (Throwable fail) {
-      fail.printStackTrace(System.out);
-    }
-  }
-
-  private void innerConfiguration(@NonNull final String type, @NonNull final Provider provider, @Nullable final Map<String, Object> configuration)
-    throws InvalidKeySpecException, NoSuchAlgorithmException, UnrecoverableKeyException {
-    when(service.getProvider()).thenReturn(provider);
-    when(kpgSpi.generateKeyPair()).thenReturn(keyPair);
-    when(keyPair.getPrivate()).thenReturn(privateKey);
-
-    when(keyInfo.isInsideSecureHardware()).thenReturn(returnForIsInsideSecureHardware(configuration));
-
-    when(kgSpi.engineGenerateKey()).thenReturn(secretKey);
-    when(skfSpi.engineGetKeySpec(any(), any())).thenReturn(keyInfo);
-    when(kfSpi.engineGetKeySpec(any(), any())).thenReturn(keyInfo);
-    when(ksSpi.engineGetKey(any(), any())).thenReturn(key);
-
-    switch (type) {
-      case KEY_GENERATOR:
-        when(service.newInstance(any())).thenReturn(kgSpi);
-        break;
-      case KEY_PAIR_GENERATOR:
-        when(service.newInstance(any())).thenReturn(kpgSpi);
-        break;
-      case KEY_FACTORY:
-        when(service.newInstance(isNull())).thenReturn(kfSpi);
-        break;
-      case KEY_STORE:
-        when(service.newInstance(isNull())).thenReturn(ksSpi);
-        break;
-      case SECRET_KEY_FACTORY:
-        when(service.newInstance(isNull())).thenReturn(skfSpi);
-        break;
-      case KEY_CIPHER:
-        when(service.newInstance(isNull())).thenReturn(cSpi);
-        break;
-      default:
-        System.err.println("requested unsupported type: " + type);
-        break;
-    }
-  }
-
-  private boolean returnForIsInsideSecureHardware(@Nullable final Map<String, Object> configuration) {
-    return getBool(configuration, "isInsideSecureHardware", true);
-  }
-
-  private boolean getBool(@Nullable final Map<String, Object> configuration,
-                          @NonNull final String key,
-                          final boolean $default) {
-    if (null == configuration) return $default;
-
-    return Boolean.parseBoolean("" + configuration.getOrDefault(key, $default));
-  }
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreAesCbcTests.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreAesCbcTests.java
deleted file mode 100644
index f74d10a..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreAesCbcTests.java
+++ /dev/null
@@ -1,73 +0,0 @@
-package com.oblador.keychain.cipherStorage;
-
-import android.os.Build;
-
-import com.facebook.react.bridge.ReactApplicationContext;
-import com.oblador.keychain.FakeProvider;
-import com.oblador.keychain.SecurityLevel;
-
-import org.junit.After;
-import org.junit.Before;
-import org.junit.ClassRule;
-import org.junit.Rule;
-import org.junit.Test;
-import org.junit.rules.TestName;
-import org.junit.rules.Timeout;
-import org.junit.runner.RunWith;
-import org.mockito.Mockito;
-import org.mockito.junit.MockitoJUnit;
-import org.mockito.junit.MockitoRule;
-import org.mockito.junit.VerificationCollector;
-import org.robolectric.RobolectricTestRunner;
-import org.robolectric.RuntimeEnvironment;
-import org.robolectric.annotation.Config;
-
-import java.security.Key;
-import java.security.Security;
-
-import javax.crypto.SecretKey;
-
-import static org.hamcrest.MatcherAssert.assertThat;
-import static org.hamcrest.Matchers.is;
-
-@RunWith(RobolectricTestRunner.class)
-public class CipherStorageKeystoreAesCbcTests {
-  /** Cancel test after 5 seconds. */
-  @ClassRule
-  public static Timeout timeout = Timeout.seconds(10);
-  /** Get test method name. */
-  @Rule
-  public TestName methodName = new TestName();
-  /** Mock all the dependencies. */
-  @Rule
-  public MockitoRule mockDependencies = MockitoJUnit.rule().silent();
-  @Rule
-  public VerificationCollector collector = MockitoJUnit.collector();
-
-  private FakeProvider provider = new FakeProvider();
-
-  @Before
-  public void setUp() throws Exception {
-    Security.insertProviderAt(provider, 0);
-  }
-
-  @After
-  public void tearDown() throws Exception {
-    Security.removeProvider(FakeProvider.NAME);
-  }
-
-  private ReactApplicationContext getRNContext() {
-    return new ReactApplicationContext(RuntimeEnvironment.application);
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testGetSecurityLevel_api23() throws Exception {
-    final CipherStorageKeystoreRsaEcb instance = new CipherStorageKeystoreRsaEcb();
-    final Key mock = Mockito.mock(SecretKey.class);
-
-    final SecurityLevel level = instance.getSecurityLevel(mock);
-
-    assertThat(level, is(SecurityLevel.SECURE_HARDWARE));
-  }
-}
diff --git a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreRsaEcbTests.java b/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreRsaEcbTests.java
deleted file mode 100644
index 53a5691..0000000
--- a/node_modules/react-native-keychain/android/src/test/java/com/oblador/keychain/cipherStorage/CipherStorageKeystoreRsaEcbTests.java
+++ /dev/null
@@ -1,102 +0,0 @@
-package com.oblador.keychain.cipherStorage;
-
-import android.content.Context;
-import android.content.pm.PackageManager;
-import android.hardware.fingerprint.FingerprintManager;
-import android.os.Build;
-
-import androidx.biometric.BiometricManager;
-
-import com.facebook.react.bridge.ReactApplicationContext;
-import com.oblador.keychain.FakeProvider;
-import com.oblador.keychain.SecurityLevel;
-
-import org.junit.After;
-import org.junit.Before;
-import org.junit.ClassRule;
-import org.junit.Rule;
-import org.junit.Test;
-import org.junit.rules.TestName;
-import org.junit.rules.Timeout;
-import org.junit.runner.RunWith;
-import org.mockito.Mockito;
-import org.mockito.junit.MockitoJUnit;
-import org.mockito.junit.MockitoRule;
-import org.mockito.junit.VerificationCollector;
-import org.robolectric.RobolectricTestRunner;
-import org.robolectric.RuntimeEnvironment;
-import org.robolectric.annotation.Config;
-
-import java.security.Key;
-import java.security.Security;
-
-import javax.crypto.SecretKey;
-
-import static org.hamcrest.MatcherAssert.assertThat;
-import static org.hamcrest.Matchers.is;
-import static org.robolectric.Shadows.shadowOf;
-
-@RunWith(RobolectricTestRunner.class)
-public class CipherStorageKeystoreRsaEcbTests {
-  /** Cancel test after 5 seconds. */
-  @ClassRule
-  public static Timeout timeout = Timeout.seconds(10);
-  /** Get test method name. */
-  @Rule
-  public TestName methodName = new TestName();
-  /** Mock all the dependencies. */
-  @Rule
-  public MockitoRule mockDependencies = MockitoJUnit.rule().silent();
-  @Rule
-  public VerificationCollector collector = MockitoJUnit.collector();
-
-  private FakeProvider provider = new FakeProvider();
-
-  @Before
-  public void setUp() throws Exception {
-    Security.insertProviderAt(provider, 0);
-  }
-
-  @After
-  public void tearDown() throws Exception {
-    Security.removeProvider(FakeProvider.NAME);
-  }
-
-  private ReactApplicationContext getRNContext() {
-    return new ReactApplicationContext(RuntimeEnvironment.application);
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.M)
-  public void testGetSecurityLevel_api23() throws Exception {
-    final CipherStorageKeystoreAesCbc instance = new CipherStorageKeystoreAesCbc();
-    final Key mock = Mockito.mock(SecretKey.class);
-
-    final SecurityLevel level = instance.getSecurityLevel(mock);
-
-    assertThat(level, is(SecurityLevel.SECURE_HARDWARE));
-  }
-
-  @Test
-  @Config(sdk = Build.VERSION_CODES.P)
-  public void testVerifySecureHardwareAvailability_api28() throws Exception {
-    ReactApplicationContext context = getRNContext();
-
-    // for api24+ system feature should be enabled
-    shadowOf(context.getPackageManager()).setSystemFeature(PackageManager.FEATURE_FINGERPRINT, true);
-
-    // set that hardware is available and fingerprints configured
-    final FingerprintManager fm = (FingerprintManager) context.getSystemService(Context.FINGERPRINT_SERVICE);
-    shadowOf(fm).setIsHardwareDetected(true);
-    shadowOf(fm).setDefaultFingerprints(5); // 5 fingerprints are available
-
-    int result = BiometricManager.from(context).canAuthenticate();
-    assertThat(result, is(BiometricManager.BIOMETRIC_SUCCESS));
-
-    final CipherStorage storage = new CipherStorageKeystoreAesCbc();;
-
-    // expected RsaEcb with fingerprint
-    assertThat(storage.supportsSecureHardware(), is(true));
-  }
-
-}
