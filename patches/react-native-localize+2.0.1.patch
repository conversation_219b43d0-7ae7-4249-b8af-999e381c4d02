diff --git a/node_modules/react-native-localize/android/build.gradle b/node_modules/react-native-localize/android/build.gradle
index 6aa57ae..49cc8a8 100644
--- a/node_modules/react-native-localize/android/build.gradle
+++ b/node_modules/react-native-localize/android/build.gradle
@@ -2,21 +2,6 @@ def safeExtGet(prop, fallback) {
     rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
 }

-buildscript {
-    // The Android Gradle plugin is only required when opening the android folder stand-alone.
-    // This avoids unnecessary downloads and potential conflicts when the library is included as a
-    // module dependency in an application project.
-    if (project == rootProject) {
-        repositories {
-            google()
-            jcenter()
-        }
-        dependencies {
-            classpath 'com.android.tools.build:gradle:3.5.3'
-        }
-    }
-}
-
 apply plugin: 'com.android.library'

 android {
@@ -31,21 +16,7 @@ android {
     }
 }

-repositories {
-    mavenLocal()
-    maven {
-        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
-        url "$rootDir/../node_modules/react-native/android"
-    }
-    maven {
-        // Android JSC is installed from npm
-        url "$rootDir/../node_modules/jsc-android/dist"
-    }
-    google()
-    jcenter()
-}
-
 dependencies {
     //noinspection GradleDynamicVersion
-    implementation 'com.facebook.react:react-native:+' // From node_modules
+    implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}" // From node_modules
 }
diff --git a/node_modules/react-native-localize/android/src/main/java/com/zoontek/rnlocalize/RNLocalizeModule.java b/node_modules/react-native-localize/android/src/main/java/com/zoontek/rnlocalize/RNLocalizeModule.java
index d62e039..8c5a51c 100644
--- a/node_modules/react-native-localize/android/src/main/java/com/zoontek/rnlocalize/RNLocalizeModule.java
+++ b/node_modules/react-native-localize/android/src/main/java/com/zoontek/rnlocalize/RNLocalizeModule.java
@@ -89,8 +89,13 @@ public class RNLocalizeModule extends ReactContextBaseJavaModule {
     filter.addAction(Intent.ACTION_TIME_CHANGED);
     filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);

-    getReactApplicationContext()
-      .registerReceiver(mBroadcastReceiver, filter);
+    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
+      getReactApplicationContext()
+        .registerReceiver(mBroadcastReceiver, filter, Context.RECEIVER_EXPORTED);
+    } else {
+      getReactApplicationContext()
+              .registerReceiver(mBroadcastReceiver, filter);
+    }
   }

   @Override
