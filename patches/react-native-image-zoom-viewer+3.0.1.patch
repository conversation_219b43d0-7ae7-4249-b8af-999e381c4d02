diff --git a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
index 3ca5e2b..dd7fa3d 100644
--- a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
+++ b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
@@ -474,6 +474,10 @@ var ImageViewer = /** @class */ (function (_super) {
                     if (_this.props.enablePreload) {
                         _this.preloadImage(_this.state.currentShowIndex || 0);
                     }
+                    if (_this.props.useThisWH) {
+                        width = _this.width;
+                        height = _this.height;
+                    }
                     return (<react_native_image_pan_zoom_1.default key={index} ref={function (el) { return (_this.imageRefs[index] = el); }} cropWidth={_this.width} cropHeight={_this.height} maxOverflow={_this.props.maxOverflow} horizontalOuterRangeOffset={_this.handleHorizontalOuterRangeOffset} responderRelease={_this.handleResponderRelease} onMove={_this.props.onMove} onLongPress={_this.handleLongPressWithIndex.get(index)} onClick={_this.handleClick} onDoubleClick={_this.handleDoubleClick} imageWidth={width} imageHeight={height} enableSwipeDown={_this.props.enableSwipeDown} swipeDownThreshold={_this.props.swipeDownThreshold} onSwipeDown={_this.handleSwipeDown} panToMove={!_this.state.isShowMenu} pinchToZoom={_this.props.enableImageZoom && !_this.state.isShowMenu} enableDoubleClickZoom={_this.props.enableImageZoom && !_this.state.isShowMenu} doubleClickInterval={_this.props.doubleClickInterval} minScale={_this.props.minScale} maxScale={_this.props.maxScale}>
               {_this.props.renderImage(image.props)}
             </react_native_image_pan_zoom_1.default>);
diff --git a/node_modules/react-native-image-zoom-viewer/built/image-viewer.style.js b/node_modules/react-native-image-zoom-viewer/built/image-viewer.style.js
index c6efc53..737472a 100644
--- a/node_modules/react-native-image-zoom-viewer/built/image-viewer.style.js
+++ b/node_modules/react-native-image-zoom-viewer/built/image-viewer.style.js
@@ -50,7 +50,7 @@ exports.simpleStyle = {
         position: 'absolute',
         left: 0,
         right: 0,
-        top: 38,
+        top: 48,
         zIndex: 13,
         justifyContent: 'center',
         alignItems: 'center',
