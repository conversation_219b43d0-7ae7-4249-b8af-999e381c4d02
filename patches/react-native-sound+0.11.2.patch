diff --git a/node_modules/react-native-sound/RNSound/RNSound.h b/node_modules/react-native-sound/RNSound/RNSound.h
index 7f5b97b..f86168a 100644
--- a/node_modules/react-native-sound/RNSound/RNSound.h
+++ b/node_modules/react-native-sound/RNSound/RNSound.h
@@ -1,7 +1,7 @@
 #if __has_include(<React/RCTBridgeModule.h>)
 #import <React/RCTBridgeModule.h>
 #else
-#import "RCTBridgeModule.h"
+#import <React/RCTBridgeModule.h>
 #endif
 
 #import <AVFoundation/AVFoundation.h>
@@ -9,7 +9,7 @@
 #if __has_include(<React/RCTEventEmitter.h>)
 #import <React/RCTEventEmitter.h>
 #else
-#import "RCTEventEmitter.h"
+#import <React/RCTEventEmitter.h>
 #endif
 
 @interface RNSound : RCTEventEmitter <RCTBridgeModule, AVAudioPlayerDelegate>
diff --git a/node_modules/react-native-sound/RNSound/RNSound.m b/node_modules/react-native-sound/RNSound/RNSound.m
index df3784e..4aab411 100644
--- a/node_modules/react-native-sound/RNSound/RNSound.m
+++ b/node_modules/react-native-sound/RNSound/RNSound.m
@@ -1,7 +1,7 @@
 #import "RNSound.h"
 
-#if __has_include("RCTUtils.h")
-#import "RCTUtils.h"
+#if __has_include(<React/RCTUtils.h>)
+#import <React/RCTUtils.h>
 #else
 #import <React/RCTUtils.h>
 #endif
@@ -175,8 +175,7 @@ - (NSDictionary *)constantsToExport {
     if (category) {
         if (mixWithOthers) {
             [session setCategory:category
-                     withOptions:AVAudioSessionCategoryOptionMixWithOthers |
-                                 AVAudioSessionCategoryOptionAllowBluetooth
+                     withOptions:AVAudioSessionCategoryOptionMixWithOthers
                            error:nil];
         } else {
             [session setCategory:category error:nil];
diff --git a/node_modules/react-native-sound/android/src/main/java/com/zmxv/RNSound/RNSoundModule.java b/node_modules/react-native-sound/android/src/main/java/com/zmxv/RNSound/RNSoundModule.java
index e6f51ec..5ffcff2 100644
--- a/node_modules/react-native-sound/android/src/main/java/com/zmxv/RNSound/RNSoundModule.java
+++ b/node_modules/react-native-sound/android/src/main/java/com/zmxv/RNSound/RNSoundModule.java
@@ -445,6 +445,13 @@ public class RNSoundModule extends ReactContextBaseJavaModule implements AudioMa
     }
   }
 
+  @ReactMethod
+  public void setMode(final Integer mode, final Boolean speaker) {
+    AudioManager audioManager = (AudioManager)this.context.getSystemService(this.context.AUDIO_SERVICE);
+    audioManager.setMode(mode);
+    audioManager.setSpeakerphoneOn(speaker);
+  }
+
   @ReactMethod
   public void setCategory(final String category, final Boolean mixWithOthers) {
     this.category = category;
diff --git a/node_modules/react-native-sound/index.d.ts b/node_modules/react-native-sound/index.d.ts
index 27a810d..a8c93f5 100644
--- a/node_modules/react-native-sound/index.d.ts
+++ b/node_modules/react-native-sound/index.d.ts
@@ -48,7 +48,7 @@ declare class Sound {
    * @param mode AVAudioSession mode
    * @param mixWithOthers Can be set to true to force mixing with other audio sessions.
    */
-  static setMode(mode: AVAudioSessionMode): void
+  static setMode(mode: AVAudioSessionMode   | number, speakerphone?:boolean): void
 
   /**
    * @param filenameOrFile Either absolute or relative path to the sound file or the `require` call.
diff --git a/node_modules/react-native-sound/sound.js b/node_modules/react-native-sound/sound.js
index c9bcbaf..dd22c48 100644
--- a/node_modules/react-native-sound/sound.js
+++ b/node_modules/react-native-sound/sound.js
@@ -313,8 +313,10 @@ Sound.setCategory = function(value, mixWithOthers = false) {
   }
 };
 
-Sound.setMode = function(value) {
-  if (!IsAndroid && !IsWindows) {
+Sound.setMode = function(value, speakerphone) {
+  if (IsAndroid) {
+    RNSound.setMode(value, speakerphone);
+  } else if (!IsWindows) {
     RNSound.setMode(value);
   }
 };
