diff --git a/node_modules/react-native-beautiful-video-recorder/lib/.DS_Store b/node_modules/react-native-beautiful-video-recorder/lib/.DS_Store
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/index.js b/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/index.js
index d97ed84..48c6d55 100644
--- a/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/index.js
+++ b/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/index.js
@@ -3,9 +3,12 @@ import {
   TouchableOpacity,
   View,
   LayoutAnimation,
+  Platform
 } from 'react-native';
 import PropTypes from 'prop-types'
 import styles from './style';
+import * as Progress from 'react-native-progress';
+let seconds = 0;
 
 export default class RecordingButton extends Component {
 
@@ -16,27 +19,99 @@ export default class RecordingButton extends Component {
     style: PropTypes.oneOfType([PropTypes.number, PropTypes.object, PropTypes.array]),
   }
 
+  constructor(...props) {
+    super(...props);
+    this.state = {
+      progress: 0,
+      isRecording: false,
+    };
+  }
+
+  countDown() {
+    this.timer = setInterval(() => {
+      seconds += 0.1;
+      // console.log('seconds=', seconds);
+      console.log('progress---', this.state.progress);
+      const t = Platform.OS === 'android' ? 14 : 13;
+      if (seconds < t) {
+        this.setState({
+          progress: seconds / t,
+        });
+        // if (parseInt(seconds) % 1 === 0) {
+        //   console.log('seconds=', Math.round(seconds));
+        //   this.props.onRecordingProgress && this.props.onRecordingProgress(seconds);
+        // }
+      } else {
+        seconds = 0;
+        this.timer && clearInterval(this.timer);
+        this.setState({ isRecording: false, progress: 0 })
+      }
+    }, 100);
+  }
+
+  // renderRecording() {
+  //   return (
+  //     <TouchableOpacity onPress={this.props.onStopPress}
+  //       style={[styles.buttonContainer, styles.buttonStopContainer, this.props.style]}>
+  //       <View style={styles.buttonStop}></View>
+  //     </TouchableOpacity>
+  //   );
+  // }
+
   renderRecording() {
     return (
-      <TouchableOpacity onPress={this.props.onStopPress}
-        style={[styles.buttonContainer, styles.buttonStopContainer, this.props.style]}>
-        <View style={styles.buttonStop}></View>
-      </TouchableOpacity>
+      <Progress.Circle
+        style={this.state.isRecording ? styles.buttonStopContainer : styles.buttonContainer}
+        size={100} // 圆的直径
+        progress={this.state.progress} // 进度
+        fill="#C0C0C0"
+        unfilledColor="#C0C0C0" // 剩余进度的颜色
+        color="#04BE02" // 动画颜色
+        thickness={6} // 内圆厚度
+        direction="clockwise" // 方向
+        borderWidth={0} // 边框
+        children={ // 子布局
+          <TouchableOpacity
+            style={{
+              position: 'absolute',
+            }}
+            onLongPress={() => {
+              console.log("onPressIn");
+              this.setState({ isRecording: true });
+              // this.countDown();
+              this.props.onLongStartPress()
+            }}
+            onPressOut={() => {
+              console.log("onPressOut");
+              this.timer && clearInterval(this.timer);
+              this.setState({ isRecording: false, progress: 0 });
+              seconds = 0;
+              this.props.onStopPress()
+            }}
+            onPress={this.props.onStartPress}
+          // onLongPress={this.props.onLongStartPress}
+          >
+            <View style={this.state.isRecording ? styles.circleInsideMini : styles.circleInside} />
+          </TouchableOpacity>
+        }
+      >
+      </Progress.Circle>
     );
   }
 
   renderWaiting() {
     return (
-      <TouchableOpacity onPress={this.props.onStartPress} style={[styles.buttonContainer, this.props.style]}>
+      <TouchableOpacity onLongPress={this.props.onLongStartPress} onPressOut={this.props.onStopPress} onPress={this.props.onStartPress} style={[styles.buttonContainer, this.props.style]}>
         <View style={styles.circleInside}></View>
       </TouchableOpacity>
     );
   }
 
   render() {
-    if (this.props.isRecording) {
-      return this.renderRecording();
-    }
-    return this.renderWaiting();
+    // if (this.props.isRecording) {
+    //   return this.renderRecording();
+    // }
+    // return this.renderWaiting();
+    return this.renderRecording();
   }
 }
diff --git a/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/style.js b/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/style.js
index 9bf2719..7cd7456 100644
--- a/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/style.js
+++ b/node_modules/react-native-beautiful-video-recorder/lib/RecordingButton/style.js
@@ -7,20 +7,29 @@ export default StyleSheet.create({
     width: 80,
     height: 80,
     borderRadius: 40,
-    backgroundColor: '#D91E18',
+    // backgroundColor: '#ffffff88',
     alignItems: 'center',
     justifyContent: 'center',
-    borderWidth: 5,
-    borderColor: 'white',
   },
   circleInside: {
     width: 60,
     height: 60,
     borderRadius: 30,
-    backgroundColor: '#D91E18',
+    backgroundColor: '#ffffff',
+  },
+  circleInsideMini: {
+    width: 40,
+    height: 40,
+    borderRadius: 20,
+    backgroundColor: '#ffffff',
   },
   buttonStopContainer: {
-    backgroundColor: 'transparent',
+    width: 100,
+    height: 100,
+    borderRadius: 50,
+    // backgroundColor: '#ffffff88',
+    alignItems: 'center',
+    justifyContent: 'center',
   },
   buttonStop: {
     backgroundColor: '#D91E18',
diff --git a/node_modules/react-native-beautiful-video-recorder/lib/index.js b/node_modules/react-native-beautiful-video-recorder/lib/index.js
index ee0c9b3..4d21c84 100644
--- a/node_modules/react-native-beautiful-video-recorder/lib/index.js
+++ b/node_modules/react-native-beautiful-video-recorder/lib/index.js
@@ -1,11 +1,16 @@
 import React, { Component } from 'react';
 import {
-  Modal,
+  // Modal,
   View,
   TouchableWithoutFeedback,
   TouchableOpacity,
   Text,
   InteractionManager,
+  Image,
+  Dimensions,
+  StatusBar,
+  Platform,
+  ActivityIndicator
 } from 'react-native';
 import PropTypes from 'prop-types'
 import moment from 'moment';
@@ -19,6 +24,18 @@ import styles, {
   buttonSwitchCamera,
   renderSwitchCamera
 } from './style';
+import Video from 'react-native-video';
+import { getVideoDuration as getRNVideoDuration } from '@qeepsake/react-native-file-utils';
+import ImageSize from 'react-native-image-size';
+import Modal from 'react-native-modalbox';
+
+const { height: W_HEIGHT, width: W_WIDTH } = Dimensions.get('window');
+const Status = {
+  loading: 'loading', // 视频加载中
+  loaded: 'loaded', // 加载完成，可播放状态
+  playing: 'playing', // 播放中
+  error: 'error', // 加载失败
+};
 
 export default class VideoRecorder extends Component {
   static propTypes = {
@@ -55,7 +72,11 @@ export default class VideoRecorder extends Component {
       time: 0,
       recorded: false,
       recordedData: null,
-      cameraType: this.props.cameraOptions.type || RNCamera.Constants.Type.back
+      cameraType: this.props.cameraOptions.type || RNCamera.Constants.Type.back,
+      photoData: null, // 拍摄的照片
+      status: null, // 加载状态
+      showActions: true, // 显示操作按钮
+      resizeMode: 'contain',
     };
   }
 
@@ -69,17 +90,27 @@ export default class VideoRecorder extends Component {
   }
 
   onSave = () => {
+    if (this.state.photoData) {
+      this.props.onTakePhoto(this.state.photoData)
+      this.setState({ isOpen: false, photoData: null, showActions: true });
+      return
+    }
+
     if (this.callback) {
       this.callback(this.state.recordedData);
+      if (this.state.isRecording) {
+        this.stopCapture()
+      }
+      this.setState({ recorded: false, isOpen: false, showActions: true });
+      return
     }
-    
     this.close();
   }
 
   switchCamera = () => {
     let type = (this.state.cameraType === RNCamera.Constants.Type.back ? RNCamera.Constants.Type.front : RNCamera.Constants.Type.back)
-    if (!this.state.isRecording){
-      this.setState({cameraType: type })
+    if (!this.state.isRecording) {
+      this.setState({ cameraType: type })
     }
   }
 
@@ -94,63 +125,80 @@ export default class VideoRecorder extends Component {
       recorded: false,
       recordedData: null,
       converting: false,
+      photoData: null,
+      showActions: true,
     });
   }
 
   close = () => {
+    if (this.state.photoData) {
+      this.setState({ photoData: null, showActions: true });
+      return;
+    }
+
+    if (this.state.recorded) {
+      this.setState({ recorded: false, showActions: true });
+      return;
+    }
+
+    if (this.state.isRecording) {
+      this.stopCapture()
+    }
     this.setState({ isOpen: false });
   }
 
+  async  getVideoDuration(uri) {
+    const size = await getRNVideoDuration(uri);
+    // console.log('getVideoDuration', size);
+    return Number(size) ;
+  }
+
+
   startCapture = () => {
-    const shouldStartCapture = () => {
-      this.camera.recordAsync(this.props.recordOptions)
-      .then((data) => {
+    this.camera?.recordAsync(this.props.recordOptions)
+      .then( async(data) => {
         console.log('video capture', data);
+        const videoDuration = await this.getVideoDuration(data.uri)
         this.setState({
           recorded: true,
           recordedData: data,
+          showActions: true,
+          time: videoDuration,
         });
+        // if (this.props.onPlayVideo) {
+        //   this.props.onPlayVideo(data)
+        // }
       }).catch(err => console.error(err));
-      setTimeout(() => {
-        this.startTimer();
-        this.setState({
-          isRecording: true,
-          recorded: false,
-          recordedData: null,
-          time: 0,
-        });
+    setTimeout(() => {
+      this.startTimer();
+      this.recordingButton?.countDown();
+      this.setState({
+        isRecording: true,
+        showActions: true,
+        recorded: false,
+        recordedData: null,
+        time: 0,
       });
-    };
-    if ((this.state.maxLength > 0) || (this.state.maxLength < 0)) {
-      if (this.props.runAfterInteractions) {
-        InteractionManager.runAfterInteractions(shouldStartCapture);
-      } else {
-        shouldStartCapture();
-      }
-    }
+    }, Platform.OS === 'android'?500:1000);
+
   }
 
   stopCapture = () => {
-    const shouldStopCapture = () => {
-      this.stopTimer();
-      this.camera.stopRecording();
-      this.setState({
-        isRecording: false,
-      });
-    };
-    if (this.props.runAfterInteractions) {
-      InteractionManager.runAfterInteractions(shouldStopCapture);
-    } else {
-      shouldStopCapture();
-    }
+    this.stopTimer();
+    this.camera?.stopRecording();
+    this.setState({
+      showActions: true,
+      isRecording: false,
+    });
   }
 
   startTimer = () => {
     this.timer = setInterval(() => {
       const time = this.state.time + 1;
-      this.setState({ time });
       if (this.state.maxLength > 0 && time >= this.state.maxLength) {
         this.stopCapture();
+      }else {
+      this.setState({ time });
       }
     }, 1000);
   }
@@ -163,15 +211,88 @@ export default class VideoRecorder extends Component {
     return moment().startOf('day').seconds(time).format('mm:ss');
   }
 
+  onTakePhoto = async () => {
+    const options = { quality: 1, base64: true };
+    if (Platform.OS === 'android') {
+      // options.exif = true;
+      options.fixOrientation = true;
+    }
+    const data = await this.camera?.takePictureAsync(options);
+    console.log('data.uri',data.uri);
+    const size=  await ImageSize.getSize(data.uri);
+    this.setState({ resizeMode: size.width > size.height ? 'contain' : 'cover', photoData: data });
+  }
+
+  getStatusBarHeight = () => {
+    const isIPhoneX = W_HEIGHT / W_WIDTH > 1.8;
+    return Platform.select({
+      ios: isIPhoneX ? 44 : 20,
+      android: StatusBar.currentHeight,
+      default: 0,
+    });
+  }
+
+  initVideoRef = ref => (this.videoPlayer = ref);
+
+  /// 媒体开始加载时调用的回调函数。
+  onLoadStart = res => {
+    console.log('MessageVideo onLoadStart', res);
+    this.setState({ status: Status.loading });
+  };
+
+  /// 当媒体加载并准备播放时调用的回调函数
+  onLoad = res => {
+    console.log('MessageVideo onLoad', res);
+    this.setState({ status: Status.playing });
+  };
+
+  /// 当播放器到达媒体末尾时调用的回调函数。
+  onEnd = () => {
+    console.log('MessageVideo onEnd');
+    this.setState({
+      status: Status.loaded,
+    });
+  };
+
+  /// 无法加载视频时的回调
+  onError = e => {
+    console.warn('MessageVideo onError', e);
+    this.setState({ status: Status.error });
+  };
+
+  /// 视频加载出错后，点击重新播放
+  onReLoad = () => {
+    console.log('MessageVideo onReLoad');
+    this.onRePlay();
+  };
+
+  /// 视频播放结束，重新播放
+  onRePlay = () => {
+    console.log('MessageVideo onRePlay');
+    this.videoPlayer?.seek(0);
+    this.setState({ isPlaying: true, status: Status.playing });
+  };
+
+  /// 远程视频缓冲时回调
+  onBuffer = () => {
+    console.log('MessageVideo onBuffer');
+  };
+
+  /// 视频进度更新
+  onProgressChanged = data => { };
+
   renderTimer() {
     const { isRecording, time, recorded } = this.state;
     return (
       <View>
         {
           (recorded || isRecording) &&
-          <Text style={this.props.durationTextStyle}>
-            <Text style={styles.dotText}>●</Text> {this.convertTimeString(time)}
-          </Text>
+          <View style={this.props.durationTextStyle}>
+            <Text style={styles.dotText}>●</Text>
+            <Text style={styles.durationText}>
+              {this.convertTimeString(time)}
+            </Text>
+          </View>
         }
       </View>
     );
@@ -182,14 +303,19 @@ export default class VideoRecorder extends Component {
     return (
       <View style={styles.controlLayer}>
         {this.renderTimer()}
-        <View style={[styles.controls]}>
-          <RecordingButton style={styles.recodingButton} isRecording={isRecording} onStartPress={this.startCapture}
+        <View style={styles.cameraTextBox}>
+          <Text style={styles.cameraText}>{this.props.cameraTopText}</Text>
+        </View>
+        <View style={[styles.controls, this.props?.controlsStyle]}>
+          <RecordingButton ref={ref => this.recordingButton = ref} style={styles.recodingButton}
+            isRecording={isRecording} onLongStartPress={this.startCapture} onStartPress={this.onTakePhoto}
+            // onRecordingProgress={this.startTimer}
             onStopPress={this.stopCapture} />
           {
             recorded &&
-              <TouchableOpacity onPress={this.onSave} style={styles.btnUse}>
-                {this.props.renderDone()}
-              </TouchableOpacity>
+            <TouchableOpacity onPress={this.onSave} style={[styles.btnUse, this.props?.buttonUseStyle]}>
+              {this.props.renderDone()}
+            </TouchableOpacity>
           }
         </View>
       </View>
@@ -204,22 +330,114 @@ export default class VideoRecorder extends Component {
         {...this.props.cameraOptions}
         type={this.state.cameraType}
         captureAudio
+        onRecordingStart={() => {
+          console.log('onRecordingStart')
+
+        }}
       >
-        {this.renderContent()}
+        {this.state.showActions ? this.renderContent() : null}
       </RNCamera>
     );
   }
 
+  renderVideoPlayer = () => {
+    const { recordedData } = this.state;
+    return (
+      <View style={styles.container}>
+        <Video
+          ref={this.initVideoRef}
+          source={{ uri: recordedData?.uri }} // Can be a URL or a local file.
+          controls={false}
+          fullscreen={false}
+          autoPlay={true}
+          pictureInPicture={false} // [iOS] 是否以画中画模式播放
+          repeat={true} // 是否重复播放
+          resizeMode="contain" // 视频的自适应伸缩铺放行为
+          // poster={poster} // 封面图
+          posterResizeMode="cover"
+          paused={false} // 控制暂停、播放
+          muted={false} // 控制静音
+          rate={1.0}
+          volume={1.0} // 0静音 1是正常的
+          fullscreenAutorotate={false}
+          playInBackground={false} // 当应用程序输入背景音频时，音频继续播放。
+          playWhenInactive={false} // [iOS]当显示控制或通知中心时，视频继续播放。
+          ignoreSilentSwitch="ignore" // [iOS] ignore | 服从 - 当'忽略'时，音频仍然会播放与iOS硬静音开关设置为静音。当“服从”时，音频将切换开关。当未指定时，将照常继承音频设置。
+          progressUpdateInterval={250.0} // [iOS] Interval to fire onProgress（默认为〜250ms ）
+          onLoadStart={this.onLoadStart} // 视频开始加载时的回调
+          onLoad={this.onLoad} // 当媒体加载并准备播放时调用的回调函数
+          onEnd={this.onEnd} // 播放完成时的回调
+          onError={this.onError} // 当视频无法加载时
+          onProgress={this.onProgressChanged} // 回调每250毫秒〜与currentTime的
+          onBuffer={this.onBuffer} // 当远程视频正在缓冲时回调
+          style={styles.container}
+        />
+        {this.renderAction()}
+      </View>
+    )
+  }
+
+  renderAction = () => {
+    const { status } = this.state;
+    if (status === Status.loading) {
+      return (
+        <View style={styles.player}>
+          <ActivityIndicator animating color="#999999" size="small" />
+        </View>
+      );
+    }
+    return null;
+  };
+
+  renderDisplay = () => {
+    const { photoData, recorded, resizeMode } = this.state;
+    console.log('resizeMode', resizeMode);
+    if (photoData) {
+      return (
+        <View style={[styles.container, { position: 'absolute', top: 0, zIndex: 8 }]}>
+          <Image source={{ uri: photoData.uri }} style={styles.photoImage} resizeMode={resizeMode} />
+          <TouchableOpacity onPress={this.close} style={[styles.photoBtnClose, { top: this.getStatusBarHeight() + 4 }]}>
+            {this.props.renderClose()}
+          </TouchableOpacity>
+          <TouchableOpacity onPress={this.onSave} style={[styles.photoBtnUse, this.props?.photoBtnUseStyle]}>
+            {this.props.renderDone()}
+          </TouchableOpacity>
+        </View>)
+    }
+    //  else if (recorded) {
+    //   return (
+    //     <View style={[styles.container, { position: 'absolute', top: 0, zIndex: 8 }]}>
+    //       {this.renderVideoPlayer()}
+    //       <TouchableOpacity onPress={this.close} style={[styles.photoBtnClose, { top: this.getStatusBarHeight() + 4 }]}>
+    //         {this.props.renderClose()}
+    //       </TouchableOpacity>
+    //       <TouchableOpacity onPress={this.onSave} style={styles.photoBtnUse}>
+    //         {this.props.renderDone()}
+    //       </TouchableOpacity>
+    //     </View>)
+    // }
+    return null;
+  }
+
   render() {
-    const { loading, isOpen } = this.state;
+    const { loading, isOpen, showActions } = this.state;
     if (loading) return <View />;
     return (
-      <Modal visible={isOpen} transparent animationType="fade"
-        onRequestClose={this.close}>
+      // <Modal visible={isOpen} transparent animationType="fade"
+      //   onRequestClose={this.close}>
+      <Modal
+      isOpen={isOpen}
+      coverScreen
+      swipeToClose={false}
+      backdrop={false}
+      backdropPressToClose={false}
+      onClosed={this.close}
+      >
         <View style={styles.modal}>
           <TouchableWithoutFeedback onPress={this.close}>
             <View style={styles.backdrop} />
           </TouchableWithoutFeedback>
+          {this.renderDisplay()}
           <View style={styles.container}>
             <View style={styles.content}>
               {this.renderCamera()}
@@ -227,9 +445,10 @@ export default class VideoRecorder extends Component {
             <TouchableOpacity onPress={this.close} style={this.props.buttonCloseStyle}>
               {this.props.renderClose()}
             </TouchableOpacity>
-            <TouchableOpacity onPress={this.switchCamera} style={this.props.buttonSwitchCameraStyle}>
+            {showActions ? <TouchableOpacity onPress={this.switchCamera} style={this.props.buttonSwitchCameraStyle}>
               {this.props.renderSwitchCamera()}
-            </TouchableOpacity>
+            </TouchableOpacity> : null}
+            {showActions ? this.props?.children : null}
           </View>
         </View>
       </Modal>
diff --git a/node_modules/react-native-beautiful-video-recorder/lib/style.js b/node_modules/react-native-beautiful-video-recorder/lib/style.js
index 8c6535c..e4d2891 100644
--- a/node_modules/react-native-beautiful-video-recorder/lib/style.js
+++ b/node_modules/react-native-beautiful-video-recorder/lib/style.js
@@ -4,6 +4,7 @@ import {
   Dimensions,
   Platform,
   View,
+  Text
 } from 'react-native';
 import Icon from 'react-native-vector-icons/MaterialIcons';
 
@@ -19,12 +20,12 @@ export const buttonClose = {
   justifyContent: 'center',
 };
 export const buttonSwitchCamera = {
-  position:'absolute',
+  position: 'absolute',
   width: 40,
   height: 40,
   right: 20,
-  bottom:35,
-  alignSelf:'flex-end',
+  bottom: 35,
+  alignSelf: 'flex-end',
 }
 export const durationText = {
   marginTop: Platform.OS === 'ios' ? 20 : 20,
@@ -39,13 +40,14 @@ export default StyleSheet.create({
     alignItems: 'center',
     justifyContent: 'center',
     width,
-    height,
+    // height,
+    flex: 1,
   },
   backdrop: {
     position: 'absolute',
     top: 0,
     left: 0,
-    backgroundColor: 'rgba(0,0,0,0.9)',
+    backgroundColor: '#ffffff',
     width,
     height,
   },
@@ -54,6 +56,7 @@ export default StyleSheet.create({
     justifyContent: 'center',
     width,
     height,
+    position: 'relative'
   },
   content: {
     alignItems: 'center',
@@ -83,11 +86,14 @@ export default StyleSheet.create({
   recodingButton: {
     marginBottom: Platform.OS === 'ios' ? 0 : 20,
   },
-  durationText,
+  durationText:{
+    color: '#fff',
+    fontSize: 20,
+  },
   dotText: {
     color: '#D91E18',
     fontSize: 10,
-    lineHeight: 20,
+    marginRight: 5
   },
   btnUse: {
     position: 'absolute',
@@ -104,6 +110,51 @@ export default StyleSheet.create({
     marginTop: 5,
     textAlign: 'center',
   },
+  photoImage: {
+    width:'100%',
+    height:'100%',
+    position: 'absolute',
+    top: 0,
+    backgroundColor:'#000'
+  },
+  photoBtnUse: {
+    position: 'absolute',
+    width: 80,
+    height: 80,
+    right: 20,
+    bottom: 40,
+    alignItems: 'center',
+    justifyContent: 'center',
+  },
+  photoBtnClose: {
+    position: 'absolute',
+    width: 40,
+    height: 40,
+    left: 18,
+    alignSelf: 'flex-start',
+    zIndex: 9
+  },
+  cameraTextBox: {
+    position: 'absolute',
+    bottom: 140,
+    width: '100%'
+  },
+  cameraText: {
+    color: '#ffffff',
+    fontSize: 14,
+    textAlign: 'center',
+  },
+  player: {
+    position: 'absolute',
+    right: 0,
+    left: 0,
+    top: 0,
+    bottom: 0,
+    zIndex: 99,
+    flexDirection: 'row',
+    justifyContent: 'center',
+    alignItems: 'center',
+  },
 });
 
 export const renderClose = () => <Icon name="close" size={32} color="white" />;
