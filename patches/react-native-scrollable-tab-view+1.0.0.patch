diff --git a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
index 4cb92de..6569828 100644
--- a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
+++ b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
@@ -76,7 +76,7 @@ const DefaultTabBar = createReactClass({
         {this.props.tabs.map((name, page) => {
           const isTabActive = this.props.activeTab === page;
           const renderTab = this.props.renderTab || this.renderTab;
-          return renderTab(name, page, isTabActive, this.props.goToPage);
+          return renderTab(name.label || name, page, isTabActive, this.props.goToPage);
         })}
         <Animated.View
           style={[
diff --git a/node_modules/react-native-scrollable-tab-view/index.js b/node_modules/react-native-scrollable-tab-view/index.js
index 82e53c5..36213dd 100644
--- a/node_modules/react-native-scrollable-tab-view/index.js
+++ b/node_modules/react-native-scrollable-tab-view/index.js
@@ -137,14 +137,14 @@ const ScrollableTabView = createReactClass({
     if (Platform.OS === 'ios') {
       const offset = pageNumber * this.state.containerWidth;
       if (this.scrollView) {
-        this.scrollView.getNode().scrollTo({x: offset, y: 0, animated: !this.props.scrollWithoutAnimation, });
+        this.scrollView.scrollTo({x: offset, y: 0, animated: !this.props.scrollWithoutAnimation, });
       }
     } else {
       if (this.scrollView) {
         if (this.props.scrollWithoutAnimation) {
-          this.scrollView.getNode().setPageWithoutAnimation(pageNumber);
+          this.scrollView.setPageWithoutAnimation(pageNumber);
         } else {
-          this.scrollView.getNode().setPage(pageNumber);
+          this.scrollView.setPage(pageNumber);
         }
       }
     }
