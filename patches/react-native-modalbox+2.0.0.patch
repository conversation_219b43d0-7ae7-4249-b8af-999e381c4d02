diff --git a/node_modules/react-native-modalbox/index.js b/node_modules/react-native-modalbox/index.js
index 3bbf065..0777509 100644
--- a/node_modules/react-native-modalbox/index.js
+++ b/node_modules/react-native-modalbox/index.js
@@ -375,8 +375,9 @@ export default class ModalBox extends React.PureComponent {
         this.props.onClosingState(newClosingState);
       closingState = newClosingState;
       state.customY = state.dy + this.state.positionDest;
-
-      animEvt(evt, state);
+      if (typeof animEvt === 'function') {
+        animEvt(evt, state);
+      }
     };
 
     const onPanRelease = (evt, state) => {
