diff --git a/node_modules/@baronha/react-native-multiple-image-picker/android/build.gradle b/node_modules/@baronha/react-native-multiple-image-picker/android/build.gradle
index ceb41b0..234ca59 100644
--- a/node_modules/@baronha/react-native-multiple-image-picker/android/build.gradle
+++ b/node_modules/@baronha/react-native-multiple-image-picker/android/build.gradle
@@ -1,19 +1,3 @@
-buildscript {
-  // Buildscript is evaluated before everything else so we can't use getExtOrDefault
-  def kotlin_version = "1.6.20"
-
-  repositories {
-    google()
-    mavenCentral()
-  }
-
-  dependencies {
-    classpath 'com.android.tools.build:gradle:7.1.1'
-    // noinspection DifferentKotlinGradleVersion
-    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
-  }
-}
-
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
 
@@ -42,88 +26,12 @@ android {
   lintOptions {
     disable 'GradleCompatible'
   }
-  compileOptions {
-    sourceCompatibility JavaVersion.VERSION_1_8
-    targetCompatibility JavaVersion.VERSION_1_8
-  }
 }
 
-repositories {
-  mavenCentral()
-  google()
-
-  def found = false
-  def defaultDir = null
-  def androidSourcesName = 'React Native sources'
-
-  if (rootProject.ext.has('reactNativeAndroidRoot')) {
-    defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
-  } else {
-    defaultDir = new File(
-            projectDir,
-            '/../../../node_modules/react-native/android'
-    )
-  }
-
-  if (defaultDir.exists()) {
-    maven {
-      url defaultDir.toString()
-      name androidSourcesName
-    }
-
-    logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
-    found = true
-  } else {
-    def parentDir = rootProject.projectDir
-
-    1.upto(5, {
-      if (found) return true
-      parentDir = parentDir.parentFile
-
-      def androidSourcesDir = new File(
-              parentDir,
-              'node_modules/react-native'
-      )
-
-      def androidPrebuiltBinaryDir = new File(
-              parentDir,
-              'node_modules/react-native/android'
-      )
-
-      if (androidPrebuiltBinaryDir.exists()) {
-        maven {
-          url androidPrebuiltBinaryDir.toString()
-          name androidSourcesName
-        }
-
-        logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
-        found = true
-      } else if (androidSourcesDir.exists()) {
-        maven {
-          url androidSourcesDir.toString()
-          name androidSourcesName
-        }
-
-        logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
-        found = true
-      }
-    })
-  }
-
-  if (!found) {
-    throw new GradleException(
-            "${project.name}: unable to locate React Native android sources. " +
-                    "Ensure you have you installed React Native as a dependency in your project and try again."
-    )
-  }
-}
-
-def kotlin_version = "1.6.0"
-
 dependencies {
   // noinspection GradleDynamicVersion
   api 'com.facebook.react:react-native:+'
-  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
+  implementation "org.jetbrains.kotlin:kotlin-stdlib:${getExtOrDefault('kotlinVersion')}"
   implementation 'com.github.bumptech.glide:glide:4.16.0'
   annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
   // PictureSelector basic (Necessary)
diff --git a/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt b/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
index a4bd8c8..de05353 100644
--- a/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
+++ b/node_modules/@baronha/react-native-multiple-image-picker/android/src/main/java/com/reactnativemultipleimagepicker/MultipleImagePickerModule.kt
@@ -134,7 +134,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         val mainStyle: SelectMainStyle = style.selectMainStyle
 
         options.setShowCropFrame(true)
-        options.setShowCropGrid(true)
+        options.setShowCropGrid(false)
         options.setCircleDimmedLayer(libOption.getBoolean("isCropCircle"))
         options.setCropOutputPathDir(getSandboxPath(appContext))
         options.isCropDragSmoothToCenter(false)
@@ -144,6 +144,18 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         options.setToolbarWidgetColor(Color.BLACK)
         options.setStatusBarColor(mainStyle.statusBarColor)
         options.isDarkStatusBarBlack(mainStyle.isDarkStatusBarBlack)
+        if (libOption.hasKey("aspectRatioX") && libOption.hasKey("aspectRatioY")) {
+            options.withAspectRatio(
+                libOption.getDouble("aspectRatioX").toFloat(),
+                libOption.getDouble("aspectRatioY").toFloat()
+            )
+        }
+        if (libOption.hasKey("width") && libOption.hasKey("height")) {
+            options.withMaxResultSize(libOption.getInt("width"), libOption.getInt("height"))
+        }
+        if (libOption.hasKey("cropTitle")) {
+            options.setToolbarTitle(libOption.getString("cropTitle"))
+        }
 
         cropOption = options
     }
@@ -155,8 +167,8 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
 
         // ANIMATION SLIDE FROM BOTTOM
         val animationStyle = PictureWindowAnimationStyle()
-        animationStyle.setActivityEnterAnimation(R.anim.ps_anim_up_in)
-        animationStyle.setActivityExitAnimation(R.anim.ps_anim_down_out)
+        animationStyle.setActivityEnterAnimation(com.luck.picture.lib.R.anim.ps_anim_up_in)
+        animationStyle.setActivityExitAnimation(com.luck.picture.lib.R.anim.ps_anim_down_out)
 
         // TITLE BAR
         val titleBar = TitleBarStyle()
@@ -166,10 +178,10 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         titleBar.isHideCancelButton = true
         titleBar.isAlbumTitleRelativeLeft = true
 
-        titleBar.titleAlbumBackgroundResource = R.drawable.ps_album_bg
-        titleBar.titleDrawableRightResource = R.drawable.ps_ic_grey_arrow
-        titleBar.previewTitleLeftBackResource = R.drawable.ps_ic_black_back
-        titleBar.titleLeftBackResource = R.drawable.ps_ic_black_back
+        titleBar.titleAlbumBackgroundResource = com.luck.picture.lib.R.drawable.ps_album_bg
+        titleBar.titleDrawableRightResource = com.luck.picture.lib.R.drawable.ps_ic_grey_arrow
+        titleBar.previewTitleLeftBackResource = com.luck.picture.lib.R.drawable.ps_ic_black_back
+        titleBar.titleLeftBackResource = com.luck.picture.lib.R.drawable.ps_ic_black_back
         titleBar.isHideCancelButton = true
 
         // BOTTOM BAR
@@ -179,12 +191,12 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         bottomBar.bottomPreviewSelectTextColor =
                 ContextCompat.getColor(appContext, R.color.app_color_pri)
         bottomBar.bottomNarBarBackgroundColor =
-                ContextCompat.getColor(appContext, R.color.ps_color_white)
+                ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_white)
         bottomBar.bottomSelectNumResources = R.drawable.num_oval_orange
         bottomBar.bottomEditorTextColor =
-                ContextCompat.getColor(appContext, R.color.ps_color_53575e)
+                ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_53575e)
         bottomBar.bottomOriginalTextColor =
-                ContextCompat.getColor(appContext, R.color.ps_color_53575e)
+                ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_53575e)
         bottomBar.bottomPreviewNormalTextColor = R.color.app_color_53575e
         bottomBar.bottomPreviewNormalTextColor = Color.BLACK
         bottomBar.isCompleteCountTips = false
@@ -198,7 +210,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
         mainStyle.isSelectNumberStyle = true
         mainStyle.selectBackground = R.drawable.picture_selector
         mainStyle.mainListBackgroundColor =
-                ContextCompat.getColor(appContext, R.color.ps_color_white)
+                ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_white)
         mainStyle.previewSelectBackground =
                 R.drawable.picture_selector
 
@@ -209,7 +221,7 @@ class MultipleImagePickerModule(reactContext: ReactApplicationContext) :
 
 
         mainStyle.selectNormalTextColor =
-                ContextCompat.getColor(appContext, R.color.ps_color_9b)
+                ContextCompat.getColor(appContext, com.luck.picture.lib.R.color.ps_color_9b)
         mainStyle.selectTextColor = primaryColor
         mainStyle.selectText = doneTitle
 
