diff --git a/node_modules/@react-native-community/viewpager/android/build.gradle b/node_modules/@react-native-community/viewpager/android/build.gradle
index 03ebdad..ddd6ae2 100644
--- a/node_modules/@react-native-community/viewpager/android/build.gradle
+++ b/node_modules/@react-native-community/viewpager/android/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-    repositories {
-        google()
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:3.2.1'
-    }
-}
-
 def getExtOrDefault(name) {
     return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeViewPager_' + name]
 }
@@ -29,14 +18,8 @@ android {
     }
 }
 
-repositories {
-    google()
-    jcenter()
-    mavenCentral()
-}
-
 dependencies {
     //noinspection GradleDynamicVersion
-    api 'com.facebook.react:react-native:+'
+    implementation 'com.facebook.react:react-native:+'
     implementation 'androidx.viewpager2:viewpager2:1.0.0'
 }
\ No newline at end of file
