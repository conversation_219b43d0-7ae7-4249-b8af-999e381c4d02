diff --git a/node_modules/@react-navigation/native/lib/commonjs/createAppContainer.js b/node_modules/@react-navigation/native/lib/commonjs/createAppContainer.js
index 6a2810c..7da6c46 100644
--- a/node_modules/@react-navigation/native/lib/commonjs/createAppContainer.js
+++ b/node_modules/@react-navigation/native/lib/commonjs/createAppContainer.js
@@ -283,7 +283,7 @@ function createNavigationContainer(Component) {
 
       _statefulContainerCount++;
 
-      _reactNative.Linking.addEventListener('url', this._handleOpenURL); // Pull out anything that can impact state
+      this.urlLinkingListener = _reactNative.Linking.addEventListener('url', this._handleOpenURL); // Pull out anything that can impact state
 
 
       let parsedUrl = null;
@@ -381,7 +381,7 @@ function createNavigationContainer(Component) {
     componentWillUnmount() {
       this._isMounted = false;
 
-      _reactNative.Linking.removeEventListener('url', this._handleOpenURL);
+      this.urlLinkingListener && this.urlLinkingListener.remove();
 
       this.subs && this.subs.remove();
 
diff --git a/node_modules/@react-navigation/native/lib/commonjs/withOrientation.js b/node_modules/@react-navigation/native/lib/commonjs/withOrientation.js
index 26895d0..80d052f 100644
--- a/node_modules/@react-navigation/native/lib/commonjs/withOrientation.js
+++ b/node_modules/@react-navigation/native/lib/commonjs/withOrientation.js
@@ -47,11 +47,11 @@ function _default(WrappedComponent) {
     }
 
     componentDidMount() {
-      _reactNative.Dimensions.addEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener = _reactNative.Dimensions.addEventListener('change', this.handleOrientationChange);
     }
 
     componentWillUnmount() {
-      _reactNative.Dimensions.removeEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener && this.dimensionsListener.remove();
     }
 
     render() {
diff --git a/node_modules/@react-navigation/native/lib/module/createAppContainer.js b/node_modules/@react-navigation/native/lib/module/createAppContainer.js
index a43cebd..3f317b5 100644
--- a/node_modules/@react-navigation/native/lib/module/createAppContainer.js
+++ b/node_modules/@react-navigation/native/lib/module/createAppContainer.js
@@ -266,7 +266,7 @@ export default function createNavigationContainer(Component) {
       }
 
       _statefulContainerCount++;
-      Linking.addEventListener('url', this._handleOpenURL); // Pull out anything that can impact state
+      this.urlLinkingListener = Linking.addEventListener('url', this._handleOpenURL); // Pull out anything that can impact state
 
       let parsedUrl = null;
       let userProvidedStartupState = null;
@@ -362,7 +362,7 @@ export default function createNavigationContainer(Component) {
 
     componentWillUnmount() {
       this._isMounted = false;
-      Linking.removeEventListener('url', this._handleOpenURL);
+      this.urlLinkingListener && this.urlLinkingListener.remove();
       this.subs && this.subs.remove();
 
       if (this._isStateful()) {
diff --git a/node_modules/@react-navigation/native/lib/module/withOrientation.js b/node_modules/@react-navigation/native/lib/module/withOrientation.js
index 8f8cf20..7f0399d 100644
--- a/node_modules/@react-navigation/native/lib/module/withOrientation.js
+++ b/node_modules/@react-navigation/native/lib/module/withOrientation.js
@@ -31,11 +31,11 @@ export default function (WrappedComponent) {
     }
 
     componentDidMount() {
-      Dimensions.addEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener = Dimensions.addEventListener('change', this.handleOrientationChange);
     }
 
     componentWillUnmount() {
-      Dimensions.removeEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener && this.dimensionsListener.remove();
     }
 
     render() {
diff --git a/node_modules/@react-navigation/native/src/createAppContainer.js b/node_modules/@react-navigation/native/src/createAppContainer.js
index b265c8e..0f08d8e 100644
--- a/node_modules/@react-navigation/native/src/createAppContainer.js
+++ b/node_modules/@react-navigation/native/src/createAppContainer.js
@@ -214,7 +214,7 @@ export default function createNavigationContainer(Component) {
         }
       }
       _statefulContainerCount++;
-      Linking.addEventListener('url', this._handleOpenURL);
+      this.urlLinkingListener = Linking.addEventListener('url', this._handleOpenURL);
 
       // Pull out anything that can impact state
       let parsedUrl = null;
@@ -331,7 +331,7 @@ export default function createNavigationContainer(Component) {
 
     componentWillUnmount() {
       this._isMounted = false;
-      Linking.removeEventListener('url', this._handleOpenURL);
+      this.urlLinkingListener && this.urlLinkingListener.remove();
       this.subs && this.subs.remove();
 
       if (this._isStateful()) {
diff --git a/node_modules/@react-navigation/native/src/withOrientation.js b/node_modules/@react-navigation/native/src/withOrientation.js
index a539a16..c0ba793 100644
--- a/node_modules/@react-navigation/native/src/withOrientation.js
+++ b/node_modules/@react-navigation/native/src/withOrientation.js
@@ -14,11 +14,11 @@ export default function (WrappedComponent) {
     }
 
     componentDidMount() {
-      Dimensions.addEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener = Dimensions.addEventListener('change', this.handleOrientationChange);
     }
 
     componentWillUnmount() {
-      Dimensions.removeEventListener('change', this.handleOrientationChange);
+      this.dimensionsListener && this.dimensionsListener.remove();
     }
 
     handleOrientationChange = ({ window }) => {
