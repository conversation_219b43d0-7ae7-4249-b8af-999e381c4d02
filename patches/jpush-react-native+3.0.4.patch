diff --git a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/JPushModule.java b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/JPushModule.java
index 32b07a1..d9441d9 100644
--- a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/JPushModule.java
+++ b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/JPushModule.java
@@ -40,6 +40,7 @@ public class JPushModule extends ReactContextBaseJavaModule {
     public static ReactApplicationContext reactContext;
 
     public static boolean isAppForeground = false;
+    public static int activityNum = 0;
 
     public JPushModule(ReactApplicationContext reactApplicationContext) {
         super(reactContext);
@@ -591,29 +592,30 @@ public class JPushModule extends ReactContextBaseJavaModule {
         application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
             @Override
             public void onActivityCreated(Activity activity, Bundle bundle) {
-                JLogger.d("onActivityCreated");
+                JPushModule.activityNum++;
+                JLogger.d("onActivityCreated activity:" + activity + " activityNum:" + JPushModule.activityNum);
             }
 
             @Override
             public void onActivityStarted(Activity activity) {
-                JLogger.d("onActivityStarted");
+                JLogger.d("onActivityStarted activity:" + activity);
             }
 
             @Override
             public void onActivityResumed(Activity activity) {
-                JLogger.d("onActivityResumed");
+                JLogger.d("onActivityResumed activity:" + activity);
                 isAppForeground = true;
             }
 
             @Override
             public void onActivityPaused(Activity activity) {
-                JLogger.d("onActivityPaused");
+                JLogger.d("onActivityPaused activity:" + activity);
                 isAppForeground = false;
             }
 
             @Override
             public void onActivityStopped(Activity activity) {
-                JLogger.d("onActivityStopped");
+                JLogger.d("onActivityStopped activity:" + activity);
             }
 
             @Override
@@ -623,7 +625,8 @@ public class JPushModule extends ReactContextBaseJavaModule {
 
             @Override
             public void onActivityDestroyed(Activity activity) {
-                JLogger.d("onActivityDestroyed");
+                JPushModule.activityNum--;
+                JLogger.d("onActivityDestroyed activity:" + activity + " activityNum:" + JPushModule.activityNum);
             }
         });
     }
diff --git a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/helper/JPushHelper.java b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/helper/JPushHelper.java
index 4b03a8b..e56cdf9 100644
--- a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/helper/JPushHelper.java
+++ b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/helper/JPushHelper.java
@@ -38,6 +38,7 @@ public class JPushHelper {
         writableMap.putString(JConstants.MESSAGE_ID, message.msgId);
         writableMap.putString(JConstants.TITLE, message.notificationTitle);
         writableMap.putString(JConstants.CONTENT, message.notificationContent);
+        writableMap.putString(JConstants.NOTIFICATION_ID, message.notificationId + "");
         convertExtras(message.notificationExtras, writableMap);
         return writableMap;
     }
@@ -50,6 +51,7 @@ public class JPushHelper {
         writableMap.putString(JConstants.INAPPCLICKACTION, message.inAppClickAction);
         writableMap.putString(JConstants.INAPPEXTRAS, message.inAppExtras);
         writableMap.putString(JConstants.INAPPSHOWTARGET, message.inAppShowTarget);
+        writableMap.putString(JConstants.NOTIFICATION_ID, message.notificationId + "");
         return writableMap;
     }
 
diff --git a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushBroadcastReceiver.java b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushBroadcastReceiver.java
index 29ce6be..f2a9deb 100644
--- a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushBroadcastReceiver.java
+++ b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushBroadcastReceiver.java
@@ -7,6 +7,7 @@ import android.os.Bundle;
 
 import cn.jiguang.plugins.push.common.JLogger;
 import cn.jiguang.plugins.push.helper.JPushHelper;
+import cn.jiguang.plugins.push.JPushModule;
 import cn.jpush.android.api.JPushInterface;
 
 public class JPushBroadcastReceiver extends BroadcastReceiver {
@@ -15,8 +16,9 @@ public class JPushBroadcastReceiver extends BroadcastReceiver {
 
   @Override
   public void onReceive(Context context, Intent data) {
+    JLogger.d("JPushBroadcastReceiver " + data.getAction());
+    JLogger.d("JPushBroadcastReceiver: activityNum:" + JPushModule.activityNum);
     if (JPushInterface.ACTION_NOTIFICATION_OPENED.equals(data.getAction())) {
-      JLogger.d("JPushBroadcastReceiver ACTION_NOTIFICATION_OPENED");
       try {
         NOTIFICATION_BUNDLE = data.getExtras();
         JPushHelper.launchApp(context);
diff --git a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushModuleReceiver.java b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushModuleReceiver.java
index 45c3de8..1b84ada 100644
--- a/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushModuleReceiver.java
+++ b/node_modules/jpush-react-native/android/src/main/java/cn/jiguang/plugins/push/receiver/JPushModuleReceiver.java
@@ -1,8 +1,11 @@
 package cn.jiguang.plugins.push.receiver;
 
 import android.content.Context;
+import android.os.Handler;
 
+import android.os.Looper;
 import com.facebook.react.bridge.Arguments;
+import com.facebook.react.bridge.ReadableMap;
 import com.facebook.react.bridge.WritableMap;
 
 import cn.jiguang.plugins.push.JPushModule;
@@ -27,7 +30,19 @@ public class JPushModuleReceiver extends JPushMessageReceiver {
   @Override
   public void onNotifyMessageArrived(Context context, NotificationMessage notificationMessage) {
     JLogger.d("onNotifyMessageArrived:" + notificationMessage.toString());
+    JLogger.d("onNotifyMessageArrived: activityNum:" + JPushModule.activityNum + " isAppForeground:" + JPushModule.isAppForeground);
     WritableMap writableMap = JPushHelper.convertNotificationToMap(JConstants.NOTIFICATION_ARRIVED, notificationMessage);
+    // 如果收到音视频，app不在前台，直接打开APP
+    if (!JPushModule.isAppForeground) {
+      ReadableMap map = writableMap.getMap(JConstants.EXTRAS);
+      if (map != null && "voip".equals(map.getString("type"))) {
+        try {
+          JPushHelper.launchApp(context);
+        } catch (Exception e) {
+          e.printStackTrace();
+        }
+      }
+    }
     if(notificationMessage.notificationType!=1){
       JPushHelper.sendEvent(JConstants.NOTIFICATION_EVENT, writableMap);
     }else {
@@ -43,10 +58,21 @@ public class JPushModuleReceiver extends JPushMessageReceiver {
   @Override
   public void onNotifyMessageOpened(Context context, NotificationMessage notificationMessage) {
     JLogger.d("onNotifyMessageOpened:" + notificationMessage.toString());
+    JLogger.d("onNotifyMessageOpened:" + (JPushModule.reactContext != null) + " isAppForeground:" + JPushModule.isAppForeground);
     if (JPushModule.reactContext != null) {
       if (!JPushModule.isAppForeground) JPushHelper.launchApp(context);
-      WritableMap writableMap = JPushHelper.convertNotificationToMap(JConstants.NOTIFICATION_OPENED, notificationMessage);
-      JPushHelper.sendEvent(JConstants.NOTIFICATION_EVENT, writableMap);
+      JLogger.d("onNotifyMessageOpened: activityNum:" + JPushModule.activityNum);
+      // 每次点按都会创建一个cn.jpush.android.service.JNotifyActivity，如果activityNum为1，则认为APP未打开创建主页面的情况
+      if (JPushModule.activityNum > 1) {
+        WritableMap writableMap = JPushHelper.convertNotificationToMap(JConstants.NOTIFICATION_OPENED, notificationMessage);
+        JPushHelper.sendEvent(JConstants.NOTIFICATION_EVENT, writableMap);
+      } else {
+        new Handler(Looper.getMainLooper()).postDelayed(() -> {
+          JLogger.d("onNotifyMessageOpened: delay send event");
+          WritableMap writableMap = JPushHelper.convertNotificationToMap(JConstants.NOTIFICATION_OPENED, notificationMessage);
+          JPushHelper.sendEvent(JConstants.NOTIFICATION_EVENT, writableMap);
+        }, 500);
+      }
     } else {
       super.onNotifyMessageOpened(context, notificationMessage);
     }
