diff --git a/node_modules/aliyun-oss-react-native/android/build.gradle b/node_modules/aliyun-oss-react-native/android/build.gradle
index 261655d..75ecb5f 100644
--- a/node_modules/aliyun-oss-react-native/android/build.gradle
+++ b/node_modules/aliyun-oss-react-native/android/build.gradle
@@ -1,14 +1,3 @@
-
-buildscript {
-    repositories {
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:1.3.1'
-    }
-}
-
 apply plugin: 'com.android.library'
 
 android {
@@ -26,12 +15,8 @@ android {
     }
 }
 
-repositories {
-    mavenCentral()
-}
-
 dependencies {
-    compile 'com.facebook.react:react-native:+'
-    compile 'com.aliyun.dpa:oss-android-sdk:+'
+    implementation 'com.facebook.react:react-native:+'
+    implementation 'com.aliyun.dpa:oss-android-sdk:+'
 }
   
\ No newline at end of file
diff --git a/node_modules/aliyun-oss-react-native/android/src/main/java/com/reactlibrary/AliyunUploadManager.java b/node_modules/aliyun-oss-react-native/android/src/main/java/com/reactlibrary/AliyunUploadManager.java
index 2d08bfa..ca653e0 100644
--- a/node_modules/aliyun-oss-react-native/android/src/main/java/com/reactlibrary/AliyunUploadManager.java
+++ b/node_modules/aliyun-oss-react-native/android/src/main/java/com/reactlibrary/AliyunUploadManager.java
@@ -72,11 +72,11 @@ public class AliyunUploadManager {
      */
     public void asyncUpload(final ReactContext context, String bucketName, String ossFile, String sourceFile, ReadableMap options, final Promise promise) {
         // Content to file:// start
-        Uri selectedVideoUri = Uri.parse(sourceFile);
+//        Uri selectedVideoUri = Uri.parse(sourceFile);
 
         // 1. content uri -> file path
         // 2. inputstream -> temp file path
-        Cursor cursor = null;
+        /*Cursor cursor = null;
         try {
             String[] proj = {MediaStore.Images.Media.DATA};
             cursor = context.getCurrentActivity().getContentResolver().query(selectedVideoUri, proj, null, null, null);
@@ -90,9 +90,9 @@ public class AliyunUploadManager {
             if (cursor != null) {
                 cursor.close();
             }
-        }
+        }*/
         // init upload request
-        PutObjectRequest put = new PutObjectRequest(bucketName, ossFile, sourceFile);
+        PutObjectRequest put = new PutObjectRequest(bucketName, ossFile, sourceFile.replace("file://", ""));
         ObjectMetadata metadata = new ObjectMetadata();
         metadata.setContentType("application/octet-stream");
         put.setMetadata(metadata);
