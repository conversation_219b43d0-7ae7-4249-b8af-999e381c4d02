diff --git a/node_modules/react-native-swiper/src/index.js b/node_modules/react-native-swiper/src/index.js
index b462fc6..0a7bfac 100644
--- a/node_modules/react-native-swiper/src/index.js
+++ b/node_modules/react-native-swiper/src/index.js
@@ -295,6 +295,11 @@ export default class extends Component {
       },
       isScrolling: false
     }
+    if (this.scrollView && this.internals.offset[initState.dir] != initState.offset[initState.dir]) {
+      this.internals.offset.x = initState.offset.x || 0;
+      this.internals.offset.y = initState.offset.y || 0;
+      this.scrollView.scrollTo({ ...this.internals.offset, animated: false });
+    }
     return initState
   }
 
