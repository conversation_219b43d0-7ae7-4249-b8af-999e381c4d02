// console.log('process.argv:', process.argv);
let env, apk, versionName, versionCode, clean, bundle, mac, noKill, kill, killAll, run, buildProd;
let proxy, pod, patch, start;
let port = '8081';
let abi = 'Arm';
let bundleChannel = 'Google';
let offline = '';
let buildPlatform = '';
process.argv.forEach(it => {
  if (it.startsWith('env-')) {
    env = it.substring(4);
  } else if (it.startsWith('apk-')) {
    apk = it.substring(4);
  } else if (it.startsWith('versionName-') || it.startsWith('vn')) {
    versionName = it.split('-')[1];
  } else if (it.startsWith('versionCode-') || it.startsWith('vc-')) {
    versionCode = it.split('-')[1];
  } else if (it === 'bundle') {
    bundle = true;
  } else if (it === 'offline') {
    offline = '--offline';
  } else if (it === 'clean') {
    clean = true;
  } else if (it === 'proxy') {
    proxy = true;
  } else if (it === 'pod') {
    pod = true;
    proxy = true;
  } else if (it === 'mac') {
    mac = true;
  } else if (it === 'ios') {
    buildPlatform = 'ios';
  } else if (it === 'android') {
    buildPlatform = 'android';
  } else if (it.startsWith('abi-')) {
    abi = it.substring(4);
  } else if (it === 'common') {
    bundleChannel = 'Common';
  } else if (it === 'php') {
    bundleChannel = 'Php';
  } else if (it.startsWith('channel-')) {
    bundleChannel = it.substring(8);
  } else if (it === 'noKill') {
    noKill = true;
  } else if (it === 'kill') {
    kill = true;
  } else if (it === 'killAll') {
    kill = true;
    killAll = true;
  } else if (it === 'buildProd') {
    buildProd = true;
  } else if (it === 'start') {
    start = true;
  } else if (it.startsWith('run-')) {
    run = it.substring(4);
  } else if (it.startsWith('port-')) {
    port = it.substring(5);
  } else if (it.startsWith('patch-')) {
    patch = it.substring(6);
  }
});

const shell = require('shelljs');

if (!shell.which('git')) {
  shell.echo('Sorry, this script requires git');
  shell.exit(1);
}

if (process.platform === 'darwin') {
  mac = true;
}

if (env) {
  if (!/dev|qa|prod/.test(env)) {
    shell.echo(`Sorry, env error: ${env}`);
    shell.exit(1);
  }
  console.log(`=======> update env: ${env}`);
  shell.sed('-i', "env = '\\w+'", `env = '${env}'`, './src/configs/index.js');
}

if (proxy) {
  console.log('=======> set proxy');
  shell.exec(
    'export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890'
  );
}

// yarn报错时，如：Failed to apply patch for package react-native-gifted-chat at path
// 执行命令解决，如：yarn build patch-react-native-gifted-chat
if (patch) {
  let content = shell.cat('./package.json').toString();
  let str = new shell.ShellString(content.replace(new RegExp(`\\s+"${patch}": "[^"]+"(,?)`), ''));
  str.to('./package.json');
  let patchPath;
  for (let it of shell.ls(`./patches/*.patch`)) {
    if (it.includes('/' + patch)) {
      patchPath = it.substring(2);
      shell.rm('-f', it);
      break;
    }
  }

  shell.exec('yarn');

  if (patchPath) {
    shell.exec('git checkout HEAD -- ' + patchPath);
  }
  str = new shell.ShellString(content);
  str.to('./package.json');
  shell.exec('git checkout HEAD -- yarn.lock');

  shell.exec('yarn');
}

/**
 * Android需要最新版本来修复部分机型停留在启动页问题
 * iOS还未适配新版，暂时使用旧版本
 */
/*function updateFacebookVersion(targetVersion, isAndroid) {
  const content = shell.cat('./package.json').toString();
  const arr = content.match(/"react-native-fbsdk-next": "([.\d]+)"/);
  if (!arr) {
    console.warn('未找到Facebook SDK ？？？');
    return;
  }
  const currentVersion = arr[1];
  console.log('Facebook 当前版本', currentVersion);
  if (currentVersion === targetVersion) return;
  shell.sed(
    '-i',
    '"react-native-fbsdk-next": "([.\\d]+)"',
    `"react-native-fbsdk-next": "${targetVersion}"`,
    './package.json'
  );
  console.log('Facebook 版本修改为', targetVersion);
  if (isAndroid) {
    clean = true;
  } else {
    pod = true;
  }
}
if (buildPlatform === 'android' || apk || run) {
  updateFacebookVersion('12.1.4', true);
} else if (buildPlatform === 'ios') {
  updateFacebookVersion('6.0.0');
}*/

if (pod && mac) {
  console.log('=======> yarn');
  shell.exec('yarn');
  console.log('=======> pod install');
  shell.exec('cd ios && pod install');
}

if (versionName) {
  console.log(`=======> update android versionName: ${versionName}`);
  shell.sed(
    '-i',
    'versionName "(\\d+\\.?)+"',
    `versionName "${versionName}"`,
    './android/app/build.gradle'
  );
  console.log(`=======> update ios versionName: ${versionName}`);
  shell.sed(
    '-i',
    'MARKETING_VERSION = (\\d+\\.?)+;',
    `MARKETING_VERSION = ${versionName};`,
    './ios/Camhr.xcodeproj/project.pbxproj'
  );

  if (!versionCode && /^\d+(\.\d+)*$/.test(versionName)) {
    const codeArr = versionName.split('.');
    let code = 0;
    for (let i = 0; i < codeArr.length; i++) {
      code += parseInt(codeArr[i], 10) * Math.pow(10, (codeArr.length - i - 1) * 3);
    }
    if (code > 2100000000) {
      shell.echo('versionCode最大值为2100000000，当前：' + code);
      shell.exit(1);
    }
    versionCode = code;
    console.log('versionName => versionCode: ', versionCode);
  }
}

if (versionCode) {
  console.log(`=======> update versionCode: ${versionCode}`);
  shell.sed('-i', /versionCode \d+$/m, `versionCode ${versionCode}`, './android/app/build.gradle');
}
const gradlew = mac ? 'cd android && sh gradlew' : 'cd android & gradlew.bat';
if (clean) {
  console.log('=======> yarn');
  shell.exec('yarn');
  console.log('=======> clean android');
  shell.exec(`${gradlew} clean ${offline}`);
}

const appName = 'Camhr';
const basePath = `./android/apks/${appName}`;

function firstLowerCase(str) {
  if (!str) return '';
  return `${str.substring(0, 1).toLowerCase()}${str.substring(1)}`;
}

function handleRequestInstall(isRemove) {
  const s1 = '<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />';
  const s2 =
    '<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" tools:node="remove" />';
  if (isRemove) {
    shell.sed('-i', s1, s2, './android/app/src/main/AndroidManifest.xml');
  } else {
    shell.sed('-i', s2, s1, './android/app/src/main/AndroidManifest.xml');
  }
}

function moveApk(folder) {
  const spkFiles = shell.ls('./android/app/build/outputs/apk/**/*.apk').toString().split(',');
  const destFolder = `${basePath}/${folder}/`;
  if (!shell.test('-d', destFolder)) {
    shell.mkdir('-p', destFolder);
  }
  const s = `-${folder}-`;
  spkFiles.forEach(file => {
    if (file.includes(s) && file.includes(`/${appName}-`)) {
      console.log(`=======> apk移到${folder}文件夹 ${file}`);
      shell.mv('-f', file, destFolder);
    }
  });
}

if (apk) {
  const task = `${apk}${bundleChannel}${abi}Release`;
  if (bundle) {
    console.log(`=======> build bundle: ${apk}`);
    // handleRequestInstall(true);

    shell.exec(`${gradlew} bundle${task} ${offline}`);

    // 复制aab到目标目录
    const folderName = firstLowerCase(task);
    const aabName = `app-${firstLowerCase(apk)}-${firstLowerCase(bundleChannel)}-${firstLowerCase(
      abi
    )}-release.aab`;
    const sourcePath = `./android/app/build/outputs/bundle/${folderName}/${aabName}`;
    console.log(`=======> sourcePath: ${sourcePath}`);

    const appBuildContent = shell.cat('./android/app/build.gradle');
    const vn = appBuildContent.replace(/[\s\S]+versionName "((\d+\.?)+)"[\s\S]+/, '$1');
    let vc = appBuildContent.replace(/[\s\S]+versionCode (\d+)\s+versionName[\s\S]+/, '$1');
    const vcs = {
      Dev: 0,
      Hk: 1000000,
      Qa: 2000000,
      Prod: 8000000,
    };
    vc = vcs[apk] + Number(vc);

    const destAabName = `${appName}-${firstLowerCase(apk)}-${firstLowerCase(
      bundleChannel
    )}-${firstLowerCase(abi)}-${vn}-${vc}.aab`;
    let destPath = `./android/apks/${appName}/aab/`;
    // let destPath = `${basePath}/${apk.toLowerCase()}/`;
    if (!shell.test('-d', destPath)) {
      console.log(`=======> 目录不存在，创建: ${destPath}`);
      shell.mkdir('-p', destPath);
    }
    destPath += destAabName;
    console.log(`=======> destPath: ${destPath}`);

    shell.mv('-f', sourcePath, destPath);
    console.log(`=======> move aab success`);
    // handleRequestInstall(false);
  } else {
    console.log(`=======> build apk: assemble${task}`);
    shell.exec(`${gradlew} assemble${task} ${offline}`);
    moveApk(apk.toLowerCase());
  }
} else if (start) {
  console.log(`=======> yarn start ${port}`);
  shell.exec(`react-native start --port ${port}`);
} else if (run) {
  console.log(`=======> yarn ${run}`);
  if (run === 'android' || run === 'android-dev') {
    shell.exec(
      `react-native run-android --mode dev${bundleChannel}AllAbiDebug --appId com.camhr.app --port ${port}`
    );
  } else if (run === 'android-prod') {
    shell.exec(
      `react-native run-android --mode prod${bundleChannel}AllAbiDebug --appId com.camhr.app --port ${port}`
    );
  }

  moveApk('debug');
} else if (buildProd) {
  console.log('=======> 编译生产 apk');
  shell.exec(`yarn apk-prod`);
  console.log('=======> 编译生产 google aab');
  shell.exec(`yarn apk-prod-bundle killAll`);
}

if (kill && !noKill) {
  if (killAll) {
    console.log('=======> 杀死所有gradle守护进程');
    if (mac) {
      shell.exec('pkill -f GradleDaemon');
    } else {
      shell.exec(
        `WMIC PROCESS where "Name like 'java%' AND CommandLine like '%GradleDaemon%'" Call Terminate`
      );
    }
  } else {
    console.log('=======> 杀死当前gradle守护进程');
    shell.exec(`${gradlew} --stop`);
  }
}
