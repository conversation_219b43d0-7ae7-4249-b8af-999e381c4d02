var path = require('path');

module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
    [
      'module-resolver',
      {
        root: ['.'],
        resolvePath(sourcePath, currentFile) {
          if (
            sourcePath === 'react-native' &&
            !(
              (
                currentFile.includes('node_modules/react-native/') || // macos/linux paths
                currentFile.includes('node_modules\\react-native\\')
              ) // windows path
            ) &&
            !(
              currentFile.includes('resolver/react-native/') ||
              currentFile.includes('resolver\\react-native\\')
            )
          ) {
            return path.resolve(__dirname, 'resolver/react-native');
          }
          return undefined;
        },
      },
    ],
    '@babel/plugin-transform-flow-strip-types',
    '@babel/plugin-proposal-unicode-property-regex',
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    ['@babel/plugin-proposal-class-properties', { loose: true }],
    ['@babel/plugin-transform-class-properties', { loose: true }],
  ],
  env: {
    production: {
      plugins: ['transform-remove-console'],
    },
  },
};
