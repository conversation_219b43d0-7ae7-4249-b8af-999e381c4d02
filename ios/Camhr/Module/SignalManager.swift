//
//  SignalManager.swift
//  Wallet
//
//  Created by luke on 2021/11/22.
//


import Foundation
import CryptoSwift

@objc(SignalMagager)
open class SignalMagager: NSObject {
  
  func getAESKey(userId:NSInteger, localId:NSInteger)->String{
    let key = userId<localId ? (String(userId) + String(localId)):(String(localId) + String(userId))
    return key.md5();
  }
  
  func getGrroupAESKey(userId:NSInteger, groupId:String)->String{
    let key = groupId + String(userId);
    return key.md5();
  }
  
  

  @objc(aesEncryptMessage:userId:localId:groupId:resolver:rejecter:)
  func aesEncryptMessage(_ msg:String, userId:NSInteger, localId:NSInteger, groupId:String, resolver resolve: RCTPromiseResolveBlock, rejecter reject:RCTPromiseRejectBlock) -> Void {
    do {
      var key = "";
      if(groupId.isEmpty){
        key = getAESKey(userId: userId, localId: localId);
      }else{
        key = getGrroupAESKey(userId: localId, groupId: groupId);
      }
      let iv = AES.randomIV(AES.blockSize)
      
      let msgData = Array(msg.utf8);
      print("aesEncryptMessage", "111",key,iv);

      let aes = try AES(key:Array(key.utf8), blockMode: CBC(iv: iv), padding: .pkcs7)
      let encryptedBytes = try aes.encrypt(msgData)
      
      let dic = NSMutableDictionary.init(capacity: 0);
      dic.setValue(Data(encryptedBytes).base64EncodedString(), forKey: "content")
      dic.setValue(Data(iv).base64EncodedString(), forKey: "aesExt")
      resolve(dic)
    } catch let error {
        print("aesEncryptMessage", "加密失败",error);
        resolve(nil)
    }
  }
  
  @objc(aesDecryptMessage:aesExt:userId:localId:groupId:resolver:rejecter:)
  func aesDecryptMessage(_ msg:String, aesExt:String, userId:NSInteger, localId:NSInteger, groupId:String, resolver resolve: RCTPromiseResolveBlock, rejecter reject:RCTPromiseRejectBlock) -> Void {

    do {
      var key = "";
      if(groupId.isEmpty){
        key = getAESKey(userId: userId, localId: localId);
      }else{
        key = getGrroupAESKey(userId: userId, groupId: groupId);
      }
      
      let iv:[UInt8] = [UInt8](Data(base64Encoded: aesExt) ?? Data())
      let msgData:[UInt8] = [UInt8](Data(base64Encoded: msg) ?? Data())
      print("aesDecryptMessage", "111",key,iv);

      let aes = try AES(key: Array(key.utf8), blockMode: CBC(iv: iv), padding: .pkcs7)
      let decryptBytes = try aes.decrypt(msgData)
      let message = String(bytes: decryptBytes, encoding: .utf8)
      resolve(message)
    } catch let error {
      print("aesDecryptMessage", "解密失败",error);
      resolve(nil)
    }
  }

}

