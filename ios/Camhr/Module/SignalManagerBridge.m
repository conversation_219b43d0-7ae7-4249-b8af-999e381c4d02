//
//  SignalManagerBridge.m
//  Wallet
//
//  Created by luke on 2021/11/22.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(SignalMagager, NSObject)
RCT_EXTERN_METHOD(test:(NSString *)str);


RCT_EXTERN_METHOD(aesEncryptMessage:(NSString *)msg userId:(NSInteger)userId localId:(NSInteger)localId groupId:(NSString *)groupId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject);

RCT_EXTERN_METHOD(aesDecryptMessage:(NSString *)msg aesExt:(NSString *)aesExt userId:(NSInteger)userId localId:(NSInteger)localId groupId:(NSString *)groupId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject);



//RCT_EXPORT_METHOD(loadLocalKeys:(RCTResponseSenderBlock)callback)

@end


