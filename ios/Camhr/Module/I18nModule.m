//
//  I18nModule.m
//  Camhr
//
//  Created by SXW on 2019/4/19.
//  Copyright © 2019年 Facebook. All rights reserved.
//

#import "I18nModule.h"
#import <React/RCTConvert.h>
#import <React/RCTEventDispatcher.h>

@implementation I18nModule

RCT_EXPORT_MODULE();

- (dispatch_queue_t)methodQueue
{
  return dispatch_get_main_queue();
}


RCT_EXPORT_METHOD(getDefaultLanguage:(NSString *)language completion:(RCTResponseSenderBlock)completion)
{
  [[NSNotificationCenter defaultCenter] postNotificationName:@"languageInit" object:language];
}

RCT_EXPORT_METHOD(modifyDefaultLanguage:(NSString *)language completion:(RCTResponseSenderBlock)completion)
{
  [[NSNotificationCenter defaultCenter] postNotificationName:@"languageModify" object:language];
}


@end
