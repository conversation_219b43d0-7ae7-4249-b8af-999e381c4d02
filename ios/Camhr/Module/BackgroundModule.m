//
//  BackgroundModule.m
//  LongPay
//
//  Created by sxw on 2023/7/12.
//

#import "BackgroundModule.h"
#import <React/RCTConvert.h>
#import <React/RCTEventDispatcher.h>

@implementation BackgroundModule

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(getLocalConfig:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    resolve([self configForKey:@"appStatus"]);
}

RCT_EXPORT_METHOD(removeLocalConfig)
{
    if([self configForKey:@"appStatus"]) {
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"appStatus"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}

RCT_EXPORT_METHOD(calculateSum:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  NSInteger sum = 3 + 5;
  resolve(@(sum));
}

-(NSString *)configForKey:(NSString *)key
{
    NSUserDefaults* defaults = [NSUserDefaults standardUserDefaults];
    return [defaults objectForKey:key];
}


@end
