//
//  AppModule.m
//  LongPay
//
//  Created by sun on 2023/12/1.
//

#import "AppModule.h"

@interface AppModule ()


@end

@implementation AppModule


RCT_EXPORT_MODULE(AppModule)

RCT_EXPORT_METHOD(getRingerMode:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject){
//    dispatch_async(dispatch_get_main_queue(), ^{
//        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
//    });
    if (true) {
        resolve(@(YES));
      } else {
        reject(@"warning", @"msg cannot be empty!", nil);
      }

}

RCT_EXPORT_METHOD(keepScreenOn:(BOOL)isAlwaysOn)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIApplication sharedApplication].idleTimerDisabled = isAlwaysOn;
    });
}


-(NSArray *)supportedEvents {
    return @[];
}

@end


