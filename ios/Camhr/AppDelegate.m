/**
 * Copyright (c) 2015-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import "AppDelegate.h"

#import <RCTJPushModule.h>
#ifdef NSFoundationVersionNumber_iOS_9_x_Max
#import <UserNotifications/UserNotifications.h>
#endif

#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <RNSplashScreen.h>
#import <UserNotifications/UserNotifications.h>
#import "UIView+Toast.h"
//#import "RNUMConfigure.h"
//#import <UMShare/UMShare.h>
#import <GoogleMaps/GoogleMaps.h>
#import <RNGoogleSignin/RNGoogleSignin.h>
#import <Firebase.h>
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <React/RCTLinkingManager.h>

#import "PKPushRegistry+PKPushFix_m.h"
#import <AudioToolbox/AudioToolbox.h>


@interface AppDelegate ()<JPUSHRegisterDelegate,PKPushRegistryDelegate,RCTBridgeDelegate>

@property (nonatomic, copy) NSString *notifyTips;

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  //注册通知(接收,监听,一个通知)
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(initLanguage:) name:@"languageInit" object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(modifyLanguage:) name:@"languageModify" object:nil];
  //  self.notifyTips = @"Please turn on push or you will not receive push notifications";
  //  [self registerPushService];
  
  [FBSDKApplicationDelegate.sharedInstance initializeSDK];
  
  [self configUSharePlatforms];
  //  [UMConfigure setLogEnabled:YES];
  [FIRApp configure];
  [GMSServices provideAPIKey:@"AIzaSyAq4SHLJfdVUiPApU9atm4Bh5FZt9ucEYc"];
  [GIDSignIn sharedInstance].clientID = @"307806623105-e5a48f02fk48sb5ef2mhk13gmpdrhmk5.apps.googleusercontent.com";
  [GIDSignIn sharedInstance].serverClientID = @"com.googleusercontent.apps.307806623105-e5a48f02fk48sb5ef2mhk13gmpdrhmk5";
  //  [UMConfigure initWithAppkey:@"599d6d81c62dca07c5001db6" channel:@"ios"];
  //  [RNUMConfigure initWithAppkey:@"5be9196cb465f5754200045c" channel:@"App Store"];
  
  #define JPUSHKEY @"d8dd3a02edf39f4ed42fc715"
  // #define JPUSHKEY @"189057fe4f71ef86b21ab544"
  
  
  [JPUSHService setupWithOption:launchOptions appKey:JPUSHKEY
                        channel:nil apsForProduction:NO];
  JPUSHRegisterEntity * entity = [[JPUSHRegisterEntity alloc] init];
  if (@available(iOS 12.0, *)) {
    entity.types = JPAuthorizationOptionAlert|JPAuthorizationOptionBadge|JPAuthorizationOptionSound|JPAuthorizationOptionProvidesAppNotificationSettings;
  }
  [JPUSHService registerForRemoteNotificationConfig:entity delegate:self];
  [launchOptions objectForKey: UIApplicationLaunchOptionsRemoteNotificationKey];
  // 自定义消息
  NSNotificationCenter *defaultCenter = [NSNotificationCenter defaultCenter];
  [defaultCenter addObserver:self selector:@selector(networkDidReceiveMessage:) name:kJPFNetworkDidReceiveMessageNotification object:nil];
  [defaultCenter addObserver:self selector:@selector(applicationDidReceiveMemoryWarning) name:UIApplicationDidReceiveMemoryWarningNotification object:nil];
  
  [self voipRegistration];
  
  // NSURL *jsCodeLocation;
  
  // jsCodeLocation = [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index" fallbackResource:nil];
  // RCTRootView *rootView = [[RCTRootView alloc] initWithBundleURL:jsCodeLocation
  //                                                     moduleName:@"Camhr"
  //                                              initialProperties:nil
  //                                                  launchOptions:launchOptions];
  
  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"Camhr"
                                            initialProperties:nil];
  
  [[NSUserDefaults standardUserDefaults] setObject:nil forKey:@"voipDic"];
  
  rootView.backgroundColor = [[UIColor alloc] initWithRed:1.0f green:1.0f blue:1.0f alpha:1];
  
  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  [self.window makeKeyAndVisible];
  [RNSplashScreen show];
  return YES;
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

- (void)configUSharePlatforms
{
  //  /* 设置微信的appKey和appSecret */
  //  [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_WechatSession appKey:@"wx04b7054e189453d4" appSecret:@"ca171c901a0c126f3c4f29e239214869" redirectURL:@"http://mobile.umeng.com/social"];
  //
  //  [[UMSocialManager defaultManager] setPlaform: UMSocialPlatformType_AlipaySession appKey:@"2015111700822536" appSecret:nil redirectURL:@"http://mobile.umeng.com/social"];
  //
  //  /* 设置领英的appKey和appSecret */
  //  [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_Linkedin appKey:@"8150xzpfc1jk9i"  appSecret:@"ot0HhNqShbAEJccm" redirectURL:@"https://api.linkedin.com/v1/people"];
  //
  //  /* 设置Facebook的appKey和UrlString */
  //  [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_Facebook appKey:@"531317358425002"  appSecret:nil redirectURL:@"http://www.umeng.com/social"];
  
}

- (void)initLanguage:(NSNotification *)noti
{
  if ([noti.object isEqualToString: @"zh"]) {
    self.notifyTips = @"请开启推送功能否则无法收到推送通知";
  } else if([noti.object isEqualToString: @"km"]) {
    self.notifyTips = @"សូមបើកការរុញឬអ្នកនឹងមិនទទួលការជូនដំណឹងរុញទេ";
  }else {
    self.notifyTips = @"Please turn on push or you will not receive push notifications";
  }
  [self registerPushService];
  //  NSLog(@"%@ === %@ === %@", noti.object, noti.userInfo, noti.name);
}

- (void)modifyLanguage:(NSNotification *)noti
{
  if ([noti.object isEqualToString: @"zh"]) {
    self.notifyTips = @"请开启推送功能否则无法收到推送通知";
  } else if([noti.object isEqualToString: @"km"]) {
    self.notifyTips = @"សូមបើកការរុញឬអ្នកនឹងមិនទទួលការជូនដំណឹងរុញទេ";
  }else {
    self.notifyTips = @"Please turn on push or you will not receive push notifications";
  }
}

//iOS 10 前台收到消息
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center  willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(NSInteger))completionHandler
{
  NSDictionary * userInfo = notification.request.content.userInfo;
  if([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
    // Apns
    NSLog(@"iOS 10 APNS 前台收到消息");
    [JPUSHService handleRemoteNotification:userInfo];
    [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_ARRIVED_EVENT object:userInfo];
  }
  else {
    // 本地通知 todo
    NSLog(@"iOS 10 本地通知 前台收到消息");
    [[NSNotificationCenter defaultCenter] postNotificationName:J_LOCAL_NOTIFICATION_ARRIVED_EVENT object:userInfo];
  }
  //需要执行这个方法，选择是否提醒用户，有 Badge、Sound、Alert 三种类型可以选择设置
  completionHandler(UNNotificationPresentationOptionAlert);
}

//iOS 10 消息事件回调
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler: (void (^)(void))completionHandler
{
  NSDictionary * userInfo = response.notification.request.content.userInfo;
  if([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
    // Apns
    NSLog(@"iOS 10 APNS 消息事件回调");
    [JPUSHService handleRemoteNotification:userInfo];
    // 保障应用被杀死状态下，用户点击推送消息，打开app后可以收到点击通知事件
    [[RCTJPushEventQueue sharedInstance]._notificationQueue insertObject:userInfo atIndex:0];
    [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_OPENED_EVENT object:userInfo];
  }
  else {
    // 本地通知
    NSDictionary *dict = [userInfo objectForKey:@"_j_voip"];
    // 保障应用被杀死状态下，用户点击推送消息，打开app后可以收到点击通知事件
    if (dict) {
      [[RCTJPushEventQueue sharedInstance]._notificationQueue insertObject:dict atIndex:0];
      [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_OPENED_EVENT object:dict];
    }else{
      NSLog(@"iOS 10 本地通知 消息事件回调");
      // 保障应用被杀死状态下，用户点击推送消息，打开app后可以收到点击通知事件
      [[RCTJPushEventQueue sharedInstance]._localNotificationQueue insertObject:userInfo atIndex:0];
      [[NSNotificationCenter defaultCenter] postNotificationName:J_LOCAL_NOTIFICATION_OPENED_EVENT object:userInfo];
    }
  }
  // 系统要求执行这个方法
  completionHandler();
}

- (void)registerPushService
{
  if (@available(iOS 11.0, *))
  {
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    NSInteger firstOpen = [[NSUserDefaults standardUserDefaults] integerForKey:@"firstOpen"];
    [center requestAuthorizationWithOptions:(UNAuthorizationOptionBadge | UNAuthorizationOptionSound | UNAuthorizationOptionAlert) completionHandler:^(BOOL granted, NSError * _Nullable error) {
      if (!granted)
      {
        if (firstOpen && firstOpen == 8) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [[UIApplication sharedApplication].keyWindow makeToast:self.notifyTips duration:2 position:CSToastPositionCenter];
          });
        }
        [[NSUserDefaults standardUserDefaults] setInteger:8 forKey:@"firstOpen"];
        [[NSUserDefaults standardUserDefaults] synchronize];
      }
    }];
  }
}


#pragma mark - ApplicationDelegate

//- (void)applicationWillResignActive:(UIApplication *)application {
//}

- (void)applicationDidEnterBackground:(UIApplication *)application {
  [[NSNotificationCenter defaultCenter] removeObserver:self name:@"languageInit" object:nil];
  [[NSNotificationCenter defaultCenter] removeObserver:self name:@"languageModify" object:nil];
  //FIX ME:
  //  NSInteger cnt = [[UIApplication sharedApplication] unreadCount];
  //  [[UIApplication sharedApplication] setApplicationIconBadgeNumber:cnt];
}

//// Required to register for notifications
//- (void)application:(UIApplication *)application didRegisterUserNotificationSettings:(UIUserNotificationSettings *)notificationSettings
//{
//}

// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  NSLog(@"didRegisterForRemoteNotificationsWithDeviceToken:%@", deviceToken);
  [JPUSHService registerDeviceToken:deviceToken];
}

// Required for the notification event. You must call the completion handler after handling the remote notification.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [JPUSHService handleRemoteNotification:userInfo];
  [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_ARRIVED_EVENT object:userInfo];
  completionHandler(UIBackgroundFetchResultNewData);
}

//// Required for the registrationError event.
//- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
//{
//}

//// Required for the localNotification event.
//- (void)application:(UIApplication *)application didReceiveLocalNotification:(UILocalNotification *)notification
//{
//  [[NSNotificationCenter defaultCenter] postNotificationName:J_LOCAL_NOTIFICATION_ARRIVED_EVENT object: notification.userInfo];
//}

//自定义消息
- (void)networkDidReceiveMessage:(NSNotification *)notification {
  NSLog(@"自定义消息:%@", notification.userInfo);
  NSDictionary * userInfo = [notification userInfo];
  [[NSNotificationCenter defaultCenter] postNotificationName:J_CUSTOM_NOTIFICATION_EVENT object:userInfo];
}

- (void)applicationDidReceiveMemoryWarning
{
  NSLog(@"app status : applicationDidReceiveMemoryWarning");  
}

- (BOOL)application:(UIApplication *)application handleOpenURL:(NSURL *)url {
  return  [WXApi handleOpenURL:url delegate:self];
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options{
  [RCTLinkingManager application:app openURL:url options:options];
  
  if([WXApi handleOpenURL:url delegate:self]) {
    return [WXApi handleOpenURL:url delegate:self];
  }
  
  if ([[FBSDKApplicationDelegate sharedInstance] application:app openURL:url options:options]) {
    return YES;
  }
  
  return [RNGoogleSignin application:app
                             openURL:url
                             options:options];
}

- (BOOL)application:(UIApplication *)application
continueUserActivity:(NSUserActivity *)userActivity
 restorationHandler:(void(^)(NSArray<id<UIUserActivityRestoring>> * __nullable
                             restorableObjects))restorationHandler {
  // 触发回调方法
  [RCTLinkingManager application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
  return [WXApi handleOpenUniversalLink:userActivity
                               delegate:self];
}


- (void)voipRegistration{
  dispatch_queue_t mainQueue = dispatch_get_main_queue();
  PKPushRegistry *voipRegistry = [[PKPushRegistry alloc] initWithQueue:mainQueue];
  voipRegistry.delegate = self;
  // Set the push type to VoIP
  voipRegistry.desiredPushTypes = [NSSet setWithObject:PKPushTypeVoIP];
}

#pragma mark PKPushRegistryDelegate

/// 系统返回VoipToken,上报给极光服务器
- (void)pushRegistry:(PKPushRegistry *)registry didUpdatePushCredentials:(PKPushCredentials *)pushCredentials forType:(PKPushType)type{
  [JPUSHService registerVoipToken:pushCredentials.token];
}

- (void)pushRegistry:(PKPushRegistry *)registry didReceiveIncomingPushWithPayload:(PKPushPayload *)payload forType:(PKPushType)type withCompletionHandler:(void (^)(void))completion
{
  [self voipPush:payload.dictionaryPayload];
}

// - (void)pushRegistry:(PKPushRegistry *)registry didReceiveIncomingPushWithPayload:(PKPushPayload *)payload forType:(NSString *)type{
//   [self voipPush:payload.dictionaryPayload];
// }

//- (void)pushRegistry:(PKPushRegistry *)registry didInvalidatePushTokenForType:(NSString *)type
//{
//  NSLog(@"registry %@ invalidate %@",registry,type);
//}

- (void)voipPush:(NSDictionary *)dic{
  
  NSDictionary *dict = [dic objectForKey:@"_j_voip"];
  NSString *callType = [dict objectForKey:@"callType"];
  NSInteger state = [[dict objectForKey:@"state"] integerValue];
  NSString *channel = [dict objectForKey:@"channel"];
  NSString *text = [dict objectForKey:@"content"];
  
  if (state == 1) {
    
    self.voipDic =dict;
    
    UNUserNotificationCenter* center = [UNUserNotificationCenter currentNotificationCenter];
    UNMutableNotificationContent* content = [[UNMutableNotificationContent alloc] init];
    content.body =[NSString localizedUserNotificationStringForKey:[NSString stringWithFormat:@"%@",text] arguments:nil];;
    content.userInfo = dic;
    UNNotificationSound *customSound = [UNNotificationSound soundNamed:@"video_chat_tip_sender.aac"];
    content.sound = customSound;
    UNTimeIntervalNotificationTrigger* trigger = [UNTimeIntervalNotificationTrigger
                                                  triggerWithTimeInterval:0.01 repeats:NO];
    UNNotificationRequest * request = [UNNotificationRequest requestWithIdentifier:@"Voip_Push"
                                                                           content:content trigger:trigger];
    [center addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
      NSLog(@"结束");
    }];
    //[[NSUserDefaults standardUserDefaults] setObject:dict forKey:@"voipDic"];
    
    
  }else if (state == 2){
    
    UNUserNotificationCenter* center = [UNUserNotificationCenter currentNotificationCenter];
    UNMutableNotificationContent* content = [[UNMutableNotificationContent alloc] init];
    content.body =[NSString localizedUserNotificationStringForKey:[NSString stringWithFormat:@"%@",text] arguments:nil];;
    UNTimeIntervalNotificationTrigger* trigger = [UNTimeIntervalNotificationTrigger
                                                  triggerWithTimeInterval:0.01 repeats:NO];
    
    UNNotificationRequest * request = [UNNotificationRequest requestWithIdentifier:@"Voip_Push"
                                                                           content:content trigger:trigger];
    [center addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
      NSLog(@"结束");
    }];
    [[NSUserDefaults standardUserDefaults] setObject:nil forKey:@"voipDic"];
    
  }
  
  [self playkSystemSound];
  
  [JPUSHService handleVoipNotification:dic];
  
}

//振动
- (void)playkSystemSound{
  AudioServicesPlaySystemSound(kSystemSoundID_Vibrate);
}



@end
