/**
 * Copyright (c) 2015-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
#import <RCTAppDelegate.h>

#import <UIKit/UIKit.h>
#import "WXApi.h"
#import <PushKit/PushKit.h>

@interface AppDelegate : UIResponder <UIApplicationDelegate,WXApiDelegate>

@property (nonatomic, strong) UIWindow *window;

@property (nonatomic, strong) RCTBridge *bridge;
@property(nonatomic, strong)NSDictionary *voipDic;


@end
