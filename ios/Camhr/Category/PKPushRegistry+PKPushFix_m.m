//
//  PKPushRegistry+PKPushFix_m.m
//  LongPay
//
//  Created by sun on 2023/12/6.
//

#import "PKPushRegistry+PKPushFix_m.h"
#import <objc/runtime.h>
#import <PushKit/PushKit.h>

@interface PKPushRegistry (PKPushFix_m)
@end

@implementation PKPushRegistry (PKPushFix_m)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];
    
        SEL originalSelector = @selector(_terminateAppIfThereAreUnhandledVoIPPushes);
        SEL swizzledSelector = @selector(doNotCrash);

        Method originalMethod = class_getInstanceMethod(class, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(class, swizzledSelector);

        // When swizzling a class method, use the following:
        // Class class = object_getClass((id)self);
        // ...
        // Method originalMethod = class_getClassMethod(class, originalSelector);
        // Method swizzledMethod = class_getClassMethod(class, swizzledSelector);

        BOOL didAddMethod =
            class_addMethod(class,
                originalSelector,
                method_getImplementation(swizzledMethod),
                method_getTypeEncoding(swizzledMethod));

        if (didAddMethod) {
            class_replaceMethod(class,
                swizzledSelector,
                method_getImplementation(originalMethod),
                method_getTypeEncoding(originalMethod));
        } else {
            method_exchangeImplementations(originalMethod, swizzledMethod);
        }
        [super load];
    });
}

#pragma mark - Method Swizzling

- (void)doNotCrash {
    NSLog(@"Unhandled VoIP Push");
}

@end
