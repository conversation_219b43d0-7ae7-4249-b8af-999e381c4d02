<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Camhr</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb531317358425002</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx04b7054e189453d4</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipayShare</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ap2015111700822536</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>li5366035</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb531317358425002</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.googleusercontent.apps.307806623105-e5a48f02fk48sb5ef2mhk13gmpdrhmk5</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.307806623105-e5a48f02fk48sb5ef2mhk13gmpdrhmk5</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Camhr</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>camhr</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>531317358425002</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>CamHR</string>
	<key>LIAppId</key>
	<string>5366035</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixinULAPI</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>alipay</string>
		<string>alipayshare</string>
		<string>linkedin</string>
		<string>linkedin-sdk2</string>
		<string>linkedin-sdk</string>
		<string>whatsapp</string>
		<string>fbapi</string>
		<string>fb-messenger-api</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>com.google.gppconsent.2.4.1</string>
		<string>com.google.gppconsent.2.4.0</string>
		<string>com.google.gppconsent.2.3.0</string>
		<string>com.google.gppconsent.2.2.0</string>
		<string>hasgplus4</string>
		<string>googlechrome-x-callback</string>
		<string>googlechrome</string>
		<string>longpay</string>
		<string>longchat</string>
		<string>abamobilebank</string>
		<string>instagram</string>
  	<string>twitter</string>
  	<string>tiktoksharesdk</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>$(PRODUCT_NAME) requires access to the Apple Music library</string>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) need to access your camera and photo album to to upload your avatar and post a dynamic image.</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) need to access your address book, for address book contacts, add mobile contacts and other functions</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) need to get your position to recommend local positions. </string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) need to get your position to recommend local positions. </string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) need your consent to access the microphone for voice chat or voice text input.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(PRODUCT_NAME) need to access your photo album to save the images in the dynamic.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) need to access your camera and photo album to to upload your avatar and post a dynamic image.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Roboto_italic.ttf</string>
		<string>Roboto.ttf</string>
		<string>Roboto_bold.ttf</string>
		<string>Roboto_bold_italic.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Entypo.ttf</string>
		<string>AntDesign.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>voip</string>
	</array>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
