// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		050293802191A44300C5C8F4 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0502937F2191A44300C5C8F4 /* Accelerate.framework */; };
		050293822191A44B00C5C8F4 /* CoreData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293812191A44B00C5C8F4 /* CoreData.framework */; };
		050293842191A46C00C5C8F4 /* CoreImage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293832191A46B00C5C8F4 /* CoreImage.framework */; };
		050293862191A47800C5C8F4 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293852191A47800C5C8F4 /* CoreLocation.framework */; };
		050293892191A48300C5C8F4 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293872191A48300C5C8F4 /* CoreTelephony.framework */; };
		0502938A2191A48300C5C8F4 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293882191A48300C5C8F4 /* CoreText.framework */; };
		0502938C2191A49400C5C8F4 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0502938B2191A49400C5C8F4 /* GLKit.framework */; };
		0502938E2191A49B00C5C8F4 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0502938D2191A49B00C5C8F4 /* ImageIO.framework */; };
		050293902191A4CC00C5C8F4 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0502938F2191A4CB00C5C8F4 /* OpenGLES.framework */; };
		050293922191A4D400C5C8F4 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293912191A4D400C5C8F4 /* QuartzCore.framework */; };
		050293942191A4E400C5C8F4 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293932191A4E400C5C8F4 /* SystemConfiguration.framework */; };
		050293962191A4EE00C5C8F4 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 050293952191A4EE00C5C8F4 /* UIKit.framework */; };
		0559A68A21E339B600AABD8E /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0559A68921E339B600AABD8E /* GoogleService-Info.plist */; };
		05A8C52221F9540700279EB4 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 05A8C4EA21F9540700279EB4 /* UserNotifications.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		2D96C590CE565396CB5D61BA /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 70B8CBF1C88B4FB861CE2906 /* PrivacyInfo.xcprivacy */; };
		3596021F82BA441B84F6B125 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = A04125021FCF457CA6102403 /* libz.tbd */; };
		4545B7FA23FBE5FC007810AC /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4545B7F923FBE5FC007810AC /* AssetsLibrary.framework */; };
		47CDCFC3C17E4216BF0B4C51 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6F530DEAD13C4B87BA49F861 /* libc++.tbd */; };
		4A54842721463E0F0095F88E /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 4A54842021463E0E0095F88E /* LaunchScreen.xib */; };
		4A54842821463E0F0095F88E /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A54842321463E0E0095F88E /* main.m */; };
		4A54842921463E0F0095F88E /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4A54842421463E0E0095F88E /* Images.xcassets */; };
		4A54842A21463E0F0095F88E /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A54842521463E0E0095F88E /* AppDelegate.m */; };
		4AB4335A218054CA00947A5F /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 4AB43359218054CA00947A5F /* libsqlite3.tbd */; };
		4AB4335C218054DC00947A5F /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4AB4335B218054DC00947A5F /* CoreGraphics.framework */; };
		648EF974161E40547D584372 /* libPods-Camhr.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F25545CCB770B01C12A5E5C /* libPods-Camhr.a */; };
		A3F6040D279931FB00D78263 /* LinkPresentation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A3F6040C279931FB00D78263 /* LinkPresentation.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		A3F6040E279932A200D78263 /* LinkPresentation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A3F6040C279931FB00D78263 /* LinkPresentation.framework */; };
		A3F6040F279932AC00D78263 /* LinkPresentation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A3F6040C279931FB00D78263 /* LinkPresentation.framework */; };
		A45B09B02536A01D00D96849 /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = A45B09A42536A01A00D96849 /* LICENSE */; };
		A45B09B12536A01D00D96849 /* CoustomView.m in Sources */ = {isa = PBXBuildFile; fileRef = A45B09A52536A01B00D96849 /* CoustomView.m */; };
		A45B09B22536A01D00D96849 /* SignHandleKeyChain.m in Sources */ = {isa = PBXBuildFile; fileRef = A45B09A62536A01B00D96849 /* SignHandleKeyChain.m */; };
		A45B09B32536A01D00D96849 /* SignWithAppleHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A45B09A82536A01C00D96849 /* SignWithAppleHelper.m */; };
		A45B09B42536A01D00D96849 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = A45B09A92536A01C00D96849 /* README.md */; };
		A45B09B52536A01D00D96849 /* SignWithApple.m in Sources */ = {isa = PBXBuildFile; fileRef = A45B09AC2536A01C00D96849 /* SignWithApple.m */; };
		A82C02282B4BDA4E0082508E /* video_chat_tip_sender.aac in Resources */ = {isa = PBXBuildFile; fileRef = A82C02262B4BDA4E0082508E /* video_chat_tip_sender.aac */; };
		A82C02292B4BDA4E0082508E /* message.wav in Resources */ = {isa = PBXBuildFile; fileRef = A82C02272B4BDA4E0082508E /* message.wav */; };
		A82C02372B4BDACF0082508E /* I18nModule.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C022B2B4BDACF0082508E /* I18nModule.m */; };
		A82C02382B4BDACF0082508E /* AppUtilModule.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C022D2B4BDACF0082508E /* AppUtilModule.m */; };
		A82C02392B4BDACF0082508E /* PermissionsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C02302B4BDACF0082508E /* PermissionsManager.m */; };
		A82C023C2B4BDACF0082508E /* AppModule.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C02352B4BDACF0082508E /* AppModule.m */; };
		A82C023D2B4BDACF0082508E /* BackgroundModule.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C02362B4BDACF0082508E /* BackgroundModule.m */; };
		A82C02422B4BDB920082508E /* test.swift in Sources */ = {isa = PBXBuildFile; fileRef = A82C02412B4BDB920082508E /* test.swift */; };
		A82C02452B4BDBDE0082508E /* SignalManagerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C02432B4BDBDD0082508E /* SignalManagerBridge.m */; };
		A82C02462B4BDBDE0082508E /* SignalManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A82C02442B4BDBDE0082508E /* SignalManager.swift */; };
		A82C024C2B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C02492B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.m */; };
		A82C024D2B4BE94F0082508E /* UIView+Toast.m in Sources */ = {isa = PBXBuildFile; fileRef = A82C024B2B4BE94F0082508E /* UIView+Toast.m */; };
		A86411D42AD6481C00302879 /* Roboto_bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A86411D02AD6481C00302879 /* Roboto_bold.ttf */; };
		A86411D52AD6481C00302879 /* Roboto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A86411D12AD6481C00302879 /* Roboto.ttf */; };
		A86411D62AD6481C00302879 /* Roboto_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A86411D22AD6481C00302879 /* Roboto_italic.ttf */; };
		A86411D72AD6481C00302879 /* Roboto_bold_italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A86411D32AD6481C00302879 /* Roboto_bold_italic.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		4A3AA8F9214B74240080323C /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0502937F2191A44300C5C8F4 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		050293812191A44B00C5C8F4 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		050293832191A46B00C5C8F4 /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		050293852191A47800C5C8F4 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		050293872191A48300C5C8F4 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		050293882191A48300C5C8F4 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		0502938B2191A49400C5C8F4 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		0502938D2191A49B00C5C8F4 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		0502938F2191A4CB00C5C8F4 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		050293912191A4D400C5C8F4 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		050293932191A4E400C5C8F4 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		050293952191A4EE00C5C8F4 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		0559A68921E339B600AABD8E /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		05A8C4EA21F9540700279EB4 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		13B07F961A680F5B00A75B9A /* Camhr.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Camhr.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2F25545CCB770B01C12A5E5C /* libPods-Camhr.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Camhr.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		37A48E9835C375AC63A8F25D /* Pods-Camhr.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Camhr.debug.xcconfig"; path = "Target Support Files/Pods-Camhr/Pods-Camhr.debug.xcconfig"; sourceTree = "<group>"; };
		4545B7F923FBE5FC007810AC /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		4A54841F21463E0E0095F88E /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		4A54842121463E0E0095F88E /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		4A54842321463E0E0095F88E /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		4A54842421463E0E0095F88E /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		4A54842521463E0E0095F88E /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		4A6C5F8F2160DE5600E5B5F2 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4AB43359218054CA00947A5F /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		4AB4335B218054DC00947A5F /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		4ABD491A219EC29100F00C16 /* Camhr.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Camhr.entitlements; sourceTree = "<group>"; };
		6F530DEAD13C4B87BA49F861 /* libc++.tbd */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		70B8CBF1C88B4FB861CE2906 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = ../PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		A04125021FCF457CA6102403 /* libz.tbd */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		A3F6040C279931FB00D78263 /* LinkPresentation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LinkPresentation.framework; path = System/Library/Frameworks/LinkPresentation.framework; sourceTree = SDKROOT; };
		A45B09A42536A01A00D96849 /* LICENSE */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		A45B09A52536A01B00D96849 /* CoustomView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CoustomView.m; sourceTree = "<group>"; };
		A45B09A62536A01B00D96849 /* SignHandleKeyChain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SignHandleKeyChain.m; sourceTree = "<group>"; };
		A45B09A82536A01C00D96849 /* SignWithAppleHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SignWithAppleHelper.m; sourceTree = "<group>"; };
		A45B09A92536A01C00D96849 /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		A45B09AA2536A01C00D96849 /* SignHandleKeyChain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SignHandleKeyChain.h; sourceTree = "<group>"; };
		A45B09AB2536A01C00D96849 /* SignWithApple.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SignWithApple.h; sourceTree = "<group>"; };
		A45B09AC2536A01C00D96849 /* SignWithApple.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SignWithApple.m; sourceTree = "<group>"; };
		A45B09AD2536A01D00D96849 /* SignWithAppleHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SignWithAppleHelper.h; sourceTree = "<group>"; };
		A45B09AE2536A01D00D96849 /* CoustomView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CoustomView.h; sourceTree = "<group>"; };
		A82C02262B4BDA4E0082508E /* video_chat_tip_sender.aac */ = {isa = PBXFileReference; lastKnownFileType = file; path = video_chat_tip_sender.aac; sourceTree = "<group>"; };
		A82C02272B4BDA4E0082508E /* message.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = message.wav; sourceTree = "<group>"; };
		A82C022B2B4BDACF0082508E /* I18nModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = I18nModule.m; sourceTree = "<group>"; };
		A82C022C2B4BDACF0082508E /* PermissionsManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PermissionsManager.h; sourceTree = "<group>"; };
		A82C022D2B4BDACF0082508E /* AppUtilModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppUtilModule.m; sourceTree = "<group>"; };
		A82C022E2B4BDACF0082508E /* BackgroundModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BackgroundModule.h; sourceTree = "<group>"; };
		A82C022F2B4BDACF0082508E /* AppModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppModule.h; sourceTree = "<group>"; };
		A82C02302B4BDACF0082508E /* PermissionsManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PermissionsManager.m; sourceTree = "<group>"; };
		A82C02312B4BDACF0082508E /* I18nModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = I18nModule.h; sourceTree = "<group>"; };
		A82C02322B4BDACF0082508E /* AppUtilModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppUtilModule.h; sourceTree = "<group>"; };
		A82C02352B4BDACF0082508E /* AppModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppModule.m; sourceTree = "<group>"; };
		A82C02362B4BDACF0082508E /* BackgroundModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BackgroundModule.m; sourceTree = "<group>"; };
		A82C02402B4BDB920082508E /* Camhr-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Camhr-Bridging-Header.h"; sourceTree = "<group>"; };
		A82C02412B4BDB920082508E /* test.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = test.swift; sourceTree = "<group>"; };
		A82C02432B4BDBDD0082508E /* SignalManagerBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SignalManagerBridge.m; sourceTree = "<group>"; };
		A82C02442B4BDBDE0082508E /* SignalManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SignalManager.swift; sourceTree = "<group>"; };
		A82C02482B4BE94F0082508E /* UIView+Toast.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Toast.h"; sourceTree = "<group>"; };
		A82C02492B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "PKPushRegistry+PKPushFix_m.m"; sourceTree = "<group>"; };
		A82C024A2B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PKPushRegistry+PKPushFix_m.h"; sourceTree = "<group>"; };
		A82C024B2B4BE94F0082508E /* UIView+Toast.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Toast.m"; sourceTree = "<group>"; };
		A86411D02AD6481C00302879 /* Roboto_bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Roboto_bold.ttf; sourceTree = "<group>"; };
		A86411D12AD6481C00302879 /* Roboto.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Roboto.ttf; sourceTree = "<group>"; };
		A86411D22AD6481C00302879 /* Roboto_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Roboto_italic.ttf; sourceTree = "<group>"; };
		A86411D32AD6481C00302879 /* Roboto_bold_italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Roboto_bold_italic.ttf; sourceTree = "<group>"; };
		C4A37A605A25B4F422A35935 /* Pods-Camhr.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Camhr.release.xcconfig"; path = "Target Support Files/Pods-Camhr/Pods-Camhr.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4545B7FA23FBE5FC007810AC /* AssetsLibrary.framework in Frameworks */,
				05A8C52221F9540700279EB4 /* UserNotifications.framework in Frameworks */,
				050293962191A4EE00C5C8F4 /* UIKit.framework in Frameworks */,
				050293942191A4E400C5C8F4 /* SystemConfiguration.framework in Frameworks */,
				050293922191A4D400C5C8F4 /* QuartzCore.framework in Frameworks */,
				050293902191A4CC00C5C8F4 /* OpenGLES.framework in Frameworks */,
				3596021F82BA441B84F6B125 /* libz.tbd in Frameworks */,
				47CDCFC3C17E4216BF0B4C51 /* libc++.tbd in Frameworks */,
				A3F6040F279932AC00D78263 /* LinkPresentation.framework in Frameworks */,
				A3F6040E279932A200D78263 /* LinkPresentation.framework in Frameworks */,
				0502938E2191A49B00C5C8F4 /* ImageIO.framework in Frameworks */,
				0502938C2191A49400C5C8F4 /* GLKit.framework in Frameworks */,
				050293892191A48300C5C8F4 /* CoreTelephony.framework in Frameworks */,
				0502938A2191A48300C5C8F4 /* CoreText.framework in Frameworks */,
				050293862191A47800C5C8F4 /* CoreLocation.framework in Frameworks */,
				050293842191A46C00C5C8F4 /* CoreImage.framework in Frameworks */,
				4AB4335C218054DC00947A5F /* CoreGraphics.framework in Frameworks */,
				050293822191A44B00C5C8F4 /* CoreData.framework in Frameworks */,
				A3F6040D279931FB00D78263 /* LinkPresentation.framework in Frameworks */,
				050293802191A44300C5C8F4 /* Accelerate.framework in Frameworks */,
				4AB4335A218054CA00947A5F /* libsqlite3.tbd in Frameworks */,
				648EF974161E40547D584372 /* libPods-Camhr.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A3F6040C279931FB00D78263 /* LinkPresentation.framework */,
				4545B7F923FBE5FC007810AC /* AssetsLibrary.framework */,
				05A8C4EA21F9540700279EB4 /* UserNotifications.framework */,
				050293952191A4EE00C5C8F4 /* UIKit.framework */,
				050293932191A4E400C5C8F4 /* SystemConfiguration.framework */,
				050293912191A4D400C5C8F4 /* QuartzCore.framework */,
				0502938F2191A4CB00C5C8F4 /* OpenGLES.framework */,
				0502938D2191A49B00C5C8F4 /* ImageIO.framework */,
				0502938B2191A49400C5C8F4 /* GLKit.framework */,
				050293872191A48300C5C8F4 /* CoreTelephony.framework */,
				050293882191A48300C5C8F4 /* CoreText.framework */,
				050293852191A47800C5C8F4 /* CoreLocation.framework */,
				050293832191A46B00C5C8F4 /* CoreImage.framework */,
				050293812191A44B00C5C8F4 /* CoreData.framework */,
				0502937F2191A44300C5C8F4 /* Accelerate.framework */,
				4AB4335B218054DC00947A5F /* CoreGraphics.framework */,
				4AB43359218054CA00947A5F /* libsqlite3.tbd */,
				6F530DEAD13C4B87BA49F861 /* libc++.tbd */,
				A04125021FCF457CA6102403 /* libz.tbd */,
				2F25545CCB770B01C12A5E5C /* libPods-Camhr.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3A69410DF3A148B4B490D32C /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			name = Resources;
			sourceTree = "<group>";
		};
		4A54841E21463E0E0095F88E /* Camhr */ = {
			isa = PBXGroup;
			children = (
				4A54841F21463E0E0095F88E /* AppDelegate.h */,
				4A54842521463E0E0095F88E /* AppDelegate.m */,
				A82C02272B4BDA4E0082508E /* message.wav */,
				A82C02262B4BDA4E0082508E /* video_chat_tip_sender.aac */,
				A82C02472B4BE94F0082508E /* Category */,
				A82C022A2B4BDACF0082508E /* Module */,
				A86411CF2AD6481C00302879 /* ttf */,
				A45B09A32536A00100D96849 /* appleLogin */,
				4ABD491A219EC29100F00C16 /* Camhr.entitlements */,
				4A6C5F8F2160DE5600E5B5F2 /* Info.plist */,
				0559A68921E339B600AABD8E /* GoogleService-Info.plist */,
				4A54842021463E0E0095F88E /* LaunchScreen.xib */,
				4A54842321463E0E0095F88E /* main.m */,
				4A54842421463E0E0095F88E /* Images.xcassets */,
				70B8CBF1C88B4FB861CE2906 /* PrivacyInfo.xcprivacy */,
			);
			path = Camhr;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				4A54841E21463E0E0095F88E /* Camhr */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				3A69410DF3A148B4B490D32C /* Resources */,
				AF8CBA4F4BA7F597641620F6 /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Camhr.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A45B09A32536A00100D96849 /* appleLogin */ = {
			isa = PBXGroup;
			children = (
				A45B09AE2536A01D00D96849 /* CoustomView.h */,
				A45B09A52536A01B00D96849 /* CoustomView.m */,
				A45B09A42536A01A00D96849 /* LICENSE */,
				A45B09A92536A01C00D96849 /* README.md */,
				A45B09AA2536A01C00D96849 /* SignHandleKeyChain.h */,
				A45B09A62536A01B00D96849 /* SignHandleKeyChain.m */,
				A45B09AB2536A01C00D96849 /* SignWithApple.h */,
				A45B09AC2536A01C00D96849 /* SignWithApple.m */,
				A45B09AD2536A01D00D96849 /* SignWithAppleHelper.h */,
				A45B09A82536A01C00D96849 /* SignWithAppleHelper.m */,
			);
			path = appleLogin;
			sourceTree = "<group>";
		};
		A82C022A2B4BDACF0082508E /* Module */ = {
			isa = PBXGroup;
			children = (
				A82C022F2B4BDACF0082508E /* AppModule.h */,
				A82C02352B4BDACF0082508E /* AppModule.m */,
				A82C02322B4BDACF0082508E /* AppUtilModule.h */,
				A82C022D2B4BDACF0082508E /* AppUtilModule.m */,
				A82C022E2B4BDACF0082508E /* BackgroundModule.h */,
				A82C02362B4BDACF0082508E /* BackgroundModule.m */,
				A82C022C2B4BDACF0082508E /* PermissionsManager.h */,
				A82C02302B4BDACF0082508E /* PermissionsManager.m */,
				A82C02312B4BDACF0082508E /* I18nModule.h */,
				A82C022B2B4BDACF0082508E /* I18nModule.m */,
				A82C02412B4BDB920082508E /* test.swift */,
				A82C02442B4BDBDE0082508E /* SignalManager.swift */,
				A82C02432B4BDBDD0082508E /* SignalManagerBridge.m */,
				A82C02402B4BDB920082508E /* Camhr-Bridging-Header.h */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		A82C02472B4BE94F0082508E /* Category */ = {
			isa = PBXGroup;
			children = (
				A82C02482B4BE94F0082508E /* UIView+Toast.h */,
				A82C024B2B4BE94F0082508E /* UIView+Toast.m */,
				A82C024A2B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.h */,
				A82C02492B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		A86411CF2AD6481C00302879 /* ttf */ = {
			isa = PBXGroup;
			children = (
				A86411D02AD6481C00302879 /* Roboto_bold.ttf */,
				A86411D12AD6481C00302879 /* Roboto.ttf */,
				A86411D22AD6481C00302879 /* Roboto_italic.ttf */,
				A86411D32AD6481C00302879 /* Roboto_bold_italic.ttf */,
			);
			path = ttf;
			sourceTree = "<group>";
		};
		AF8CBA4F4BA7F597641620F6 /* Pods */ = {
			isa = PBXGroup;
			children = (
				37A48E9835C375AC63A8F25D /* Pods-Camhr.debug.xcconfig */,
				C4A37A605A25B4F422A35935 /* Pods-Camhr.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* Camhr */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Camhr" */;
			buildPhases = (
				8C9ADB77F90C5D8A4E83D803 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				4A3AA8F9214B74240080323C /* Embed Frameworks */,
				BFF770ED17EDA34682B0BB6D /* [CP] Copy Pods Resources */,
				5114949AF3F5818300225588 /* [CP] Embed Pods Frameworks */,
				A8FEC0F328C8920B00BF7BA4 /* Start Packager */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Camhr;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* Camhr.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 610;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = YNQ74S6Y45;
						LastSwiftMigration = 1320;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 1;
							};
							com.apple.iCloud = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Camhr" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				de,
				"zh-Hans",
				ja,
				es,
				pl,
				fr,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Camhr */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4A54842921463E0F0095F88E /* Images.xcassets in Resources */,
				A45B09B42536A01D00D96849 /* README.md in Resources */,
				A86411D52AD6481C00302879 /* Roboto.ttf in Resources */,
				A45B09B02536A01D00D96849 /* LICENSE in Resources */,
				A82C02282B4BDA4E0082508E /* video_chat_tip_sender.aac in Resources */,
				4A54842721463E0F0095F88E /* LaunchScreen.xib in Resources */,
				A86411D42AD6481C00302879 /* Roboto_bold.ttf in Resources */,
				A82C02292B4BDA4E0082508E /* message.wav in Resources */,
				0559A68A21E339B600AABD8E /* GoogleService-Info.plist in Resources */,
				A86411D62AD6481C00302879 /* Roboto_italic.ttf in Resources */,
				A86411D72AD6481C00302879 /* Roboto_bold_italic.ttf in Resources */,
				2D96C590CE565396CB5D61BA /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		5114949AF3F5818300225588 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8C9ADB77F90C5D8A4E83D803 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Camhr-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A8FEC0F328C8920B00BF7BA4 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\n\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    # 启动 Metro bundler\n    open -a Terminal \"$SRCROOT/../node_modules/react-native/scripts/packager.sh\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
		};
		BFF770ED17EDA34682B0BB6D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Camhr/Pods-Camhr-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A45B09B52536A01D00D96849 /* SignWithApple.m in Sources */,
				A82C024C2B4BE94F0082508E /* PKPushRegistry+PKPushFix_m.m in Sources */,
				4A54842821463E0F0095F88E /* main.m in Sources */,
				A82C02422B4BDB920082508E /* test.swift in Sources */,
				A45B09B12536A01D00D96849 /* CoustomView.m in Sources */,
				4A54842A21463E0F0095F88E /* AppDelegate.m in Sources */,
				A82C023C2B4BDACF0082508E /* AppModule.m in Sources */,
				A82C02382B4BDACF0082508E /* AppUtilModule.m in Sources */,
				A82C024D2B4BE94F0082508E /* UIView+Toast.m in Sources */,
				A82C02462B4BDBDE0082508E /* SignalManager.swift in Sources */,
				A82C02372B4BDACF0082508E /* I18nModule.m in Sources */,
				A45B09B22536A01D00D96849 /* SignHandleKeyChain.m in Sources */,
				A82C023D2B4BDACF0082508E /* BackgroundModule.m in Sources */,
				A82C02392B4BDACF0082508E /* PermissionsManager.m in Sources */,
				A82C02452B4BDBDE0082508E /* SignalManagerBridge.m in Sources */,
				A45B09B32536A01D00D96849 /* SignWithAppleHelper.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		4A54842021463E0E0095F88E /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				4A54842121463E0E0095F88E /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37A48E9835C375AC63A8F25D /* Pods-Camhr.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Camhr/Camhr.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = YNQ74S6Y45;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Camhr/GoogleMaps",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-image-pickerios",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-vector-iconsRNVectorIconsManager",
					"$(SRCROOT)..",
					ode_modules,
					"ealmsrc/**",
					"$(SRCROOT)/node_modules/react-native/Libraries/PushNotificationIOS/**",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-fs/**",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-videoios",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-video/ios",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-soundRNSound",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-audioios",
					"$(SRCROOT)/../node_modules/react-native-i18n/ios",
					"$(SRCROOT)/../node_modules/react-native-device-info/ios/RNDeviceInfo",
					"$(SRCROOT)/../node_modules/react-native-image-crop-picker/ios/**",
					"$(SRCROOT)/../node_modules/react-native-picker/ios/RCTBEEPickerManager",
					"$(SRCROOT)/../node_modules/react-native-maps/lib/ios/AirMaps",
					"$(SRCROOT)/../node_modules/react-native-linkedin-login/ios/RCTLinkedinLogin/**",
					"$(SRCROOT)/../node_modules/jpush-react-native/ios/RCTJPushModule",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Camhr/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(SDKROOT)/usr/lib/swift$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS\"",
				);
				MARKETING_VERSION = 1.5.30;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.camhr.app;
				PRODUCT_NAME = Camhr;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Camhr/Module/Camhr-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C4A37A605A25B4F422A35935 /* Pods-Camhr.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Camhr/Camhr.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YNQ74S6Y45;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Camhr/GoogleMaps",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-image-pickerios",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-vector-iconsRNVectorIconsManager",
					"$(SRCROOT)..",
					ode_modules,
					"ealmsrc/**",
					"$(SRCROOT)/node_modules/react-native/Libraries/PushNotificationIOS/**",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-fs/**",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-videoios",
					"$(SRCROOT)/../node_modules/react-native-fs/**",
					"$(SRCROOT)/../node_modules/react-native-video/ios",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-soundRNSound",
					"$(SRCROOT)..",
					ode_modules,
					"eact-native-audioios",
					"$(SRCROOT)/../node_modules/react-native-i18n/ios",
					"$(SRCROOT)/../node_modules/react-native-device-info/ios/RNDeviceInfo",
					"$(SRCROOT)/../node_modules/react-native-image-crop-picker/ios/**",
					"$(SRCROOT)/../node_modules/react-native-picker/ios/RCTBEEPickerManager",
					"$(SRCROOT)/../node_modules/react-native-maps/lib/ios/AirMaps",
					"$(SRCROOT)/../node_modules/react-native-linkedin-login/ios/RCTLinkedinLogin/**",
					"$(SRCROOT)/../node_modules/jpush-react-native/ios/RCTJPushModule",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Camhr/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(SDKROOT)/usr/lib/swift$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS\"",
				);
				MARKETING_VERSION = 1.5.30;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.camhr.app;
				PRODUCT_NAME = Camhr;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Camhr/Module/Camhr-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
					"-ObjC",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
					"-ObjC",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Camhr" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Camhr" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
