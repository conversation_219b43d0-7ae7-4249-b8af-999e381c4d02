# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

def normal_pods

  config = use_native_modules!

  # use_react_native!(:path => config["reactNativePath"])
  use_react_native!(
    :path => config[:reactNativePath],
    # :hermes_enabled => false,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
 )

 # pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
#  pod 'Picker', :path => '../node_modules/react-native-picker'
 pod 'GoogleMaps'  # Remove this line if you don't want to support GoogleMaps on iOS
 pod 'Firebase/Core'
 pod 'FirebaseCore', :modular_headers => true
 pod 'FirebaseCoreInternal', :modular_headers => true
 pod 'GoogleUtilities', :modular_headers => true
 pod 'react-native-google-maps', :path => '../node_modules/react-native-maps'

#  pod 'React-Core',:path => '../node_modules/react-native/', :modular_headers => true

 permissions_path = '../node_modules/react-native-permissions/ios'
 pod 'Permission-AppTrackingTransparency', :path => "#{permissions_path}/AppTrackingTransparency"
 pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
 pod 'Permission-Contacts', :path => "#{permissions_path}/Contacts"
 pod 'Permission-FaceID', :path => "#{permissions_path}/FaceID"
 pod 'Permission-LocationAccuracy', :path => "#{permissions_path}/LocationAccuracy"
 pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways"
 pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"
 pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
 pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"
 pod 'Permission-PhotoLibraryAddOnly', :path => "#{permissions_path}/PhotoLibraryAddOnly"
 pod 'Permission-MediaLibrary', :path => "#{permissions_path}/MediaLibrary"
 pod 'Permission-Microphone', :path => "#{permissions_path}/Microphone"

 # Enables Flipper.
 #
 # Note that if you have use_frameworks! enabled, Flipper will not work and
 # you should disable these next few lines.
 
 pod 'CryptoSwift', '1.4.1'

end

target 'Camhr' do
  normal_pods

end

post_install do |installer|
  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)
  end
    
  framework_paths = [
    "Pods/AgoraRtcEngine_iOS/AgoraAIDenoiseExtension.xcframework/ios-arm64_armv7/AgoraAIDenoiseExtension.framework/AgoraAIDenoiseExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraCIExtension.xcframework/ios-arm64_armv7/AgoraCIExtension.framework/AgoraCIExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraCore.xcframework/ios-arm64_armv7/AgoraCore.framework/AgoraCore",
    "Pods/AgoraRtcEngine_iOS/AgoraDav1dExtension.xcframework/ios-arm64_armv7/AgoraDav1dExtension.framework/AgoraDav1dExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraFDExtension.xcframework/ios-arm64_armv7/AgoraFDExtension.framework/AgoraFDExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraFullAudioFormatExtension.xcframework/ios-arm64_armv7/AgoraFullAudioFormatExtension.framework/AgoraFullAudioFormatExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraReplayKitExtension.xcframework/ios-arm64_armv7/AgoraReplayKitExtension.framework/AgoraReplayKitExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraRtcKit.xcframework/ios-arm64_armv7/AgoraRtcKit.framework/AgoraRtcKit",
    "Pods/AgoraRtcEngine_iOS/AgoraSoundTouch.xcframework/ios-arm64_armv7/AgoraSoundTouch.framework/AgoraSoundTouch",
    "Pods/AgoraRtcEngine_iOS/AgoraSpatialAudioExtension.xcframework/ios-arm64_armv7/AgoraSpatialAudioExtension.framework/AgoraSpatialAudioExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraSuperResolutionExtension.xcframework/ios-arm64_armv7/AgoraSuperResolutionExtension.framework/AgoraSuperResolutionExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraVideoProcessExtension.xcframework/ios-arm64_armv7/AgoraVideoProcessExtension.framework/AgoraVideoProcessExtension",
    "Pods/AgoraRtcEngine_iOS/AgoraVideoSegmentationExtension.xcframework/ios-arm64_armv7/AgoraVideoSegmentationExtension.framework/AgoraVideoSegmentationExtension",
    "Pods/AgoraRtcEngine_iOS/Agorafdkaac.xcframework/ios-arm64_armv7/Agorafdkaac.framework/Agorafdkaac",
    "Pods/AgoraRtcEngine_iOS/Agoraffmpeg.xcframework/ios-arm64_armv7/Agoraffmpeg.framework/Agoraffmpeg",
  ]

  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end

  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['APPLICATION_EXTENSION_API_ONLY'] = target.name != "Sentry" ? 'NO' :'YES'
    end
    if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
      target.build_configurations.each do |config|
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
      end
    end
  end

  configNew = use_native_modules!

  react_native_post_install(
        installer,
        configNew[:reactNativePath],
        :mac_catalyst_enabled => false,
        # :ccache_enabled => true
      )
end