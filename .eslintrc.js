module.exports = {
  root: true,
  extends: ['@react-native'],
  rules: {
    // React Native has JSX in JS files
    'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx'] }],
    'react/destructuring-assignment': 'off',
    // 取消换行符检验
    'linebreak-style': 'off',
    'no-param-reassign': 'off',
    'global-require': 0,
    'react/prop-types': 'off',
    'react/prefer-stateless-function': 'off',
    'class-methods-use-this': 'off',
    'no-restricted-syntax': 'off',
    'no-await-in-loop': 'off',
    'no-console': 'off',
    forOf: 'off',
    curly: 'off',
    'no-continue': 'off',
    'no-template-curly-in-string': 'off',
    'no-underscore-dangle': 'off',
    'import/prefer-default-export': 'off',
    'import/order': 'off',
    'no-undef': 0,
    'prefer-promise-reject-errors': 'off',
    'no-return-assign': 'off',
    'no-nested-ternary': 'off',
    'no-bitwise': 'off',
    'no-plusplus': 'off',
    'arrow-body-style': 'off',
    'arrow-parens': 'always',
    'prefer-spread': 'off',
    'prefer-const': 'off',
    'prefer-destructuring': 'off',
    'prefer-object-spread': 'off',
    'prefer-template': 'off',
    'spaced-comment': 'off',
    'lines-between-class-members': 'off',
    'react/no-this-in-sfc': 'off',
    quotes: 'off',
    'max-classes-per-file': 'off',
    'react-native/no-inline-styles': 'off',
    'react/jsx-one-expression-per-line': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/no-access-state-in-setstate': 'off',
    'react/no-unstable-nested-components': 'off',
    'react/sort-comp': 'off',
    'react/jsx-wrap-multilines': [
      'error',
      {
        declaration: 'parens-new-line',
        assignment: 'parens-new-line',
        return: 'parens-new-line',
        arrow: 'parens-new-line',
        condition: 'parens-new-line',
        logical: 'parens-new-line',
        prop: 'ignore',
      },
    ],
    'jsx-a11y/no-static-element-interactions': [0],
    'jsx-a11y/no-noninteractive-element-interactions': [0],
    'jsx-a11y/click-events-have-key-events': [0],
    'jsx-a11y/anchor-is-valid': [0],
    'comma-dangle': [
      'warn',
      {
        arrays: 'always-multiline',
        objects: 'always-multiline',
        imports: 'always-multiline',
        exports: 'always-multiline',
        functions: 'never',
      },
    ],
  },
  parserOptions: {
    ecmaFeatures: {
      legacyDecorators: true,
    },
  },
};
