rootProject.name = '<PERSON>hr'
include ':react-native-wechat-lib'
project(':react-native-wechat-lib').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-wechat-lib/android')
include ':react-native-webview'
project(':react-native-webview').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-webview/android')
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesSettingsGradle(settings)
include ':jcore-react-native'
project(':jcore-react-native').projectDir = new File(rootProject.projectDir, '../node_modules/jcore-react-native/android')
include ':jpush-react-native'
project(':jpush-react-native').projectDir = new File(rootProject.projectDir, '../node_modules/jpush-react-native/android')

include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')
