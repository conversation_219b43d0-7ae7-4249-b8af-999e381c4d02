// Top-level build file where you can add configuration options common to all sub-projects/modules.

gradle.startParameter.excludedTaskNames.addAll(
        gradle.startParameter.taskNames.findAll { it.contains("testClasses") }
)

buildscript {
    ext.kotlin_version = '1.9.22'
    repositories {
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.8'
    }
}

ext {
    supportLibVersion = '28.0.0'
    androidXVersion = '1.0.0'
    androidXCore = '1.0.0'
    buildToolsVersion = '34.0.0'
    compileSdkVersion = 34
    minSdkVersion = 23
    targetSdkVersion = 34
    ndkVersion = "26.1.10909125"
    reactNative = '0.74.3'
    reactNativeVersion = '0.74.3'
    gradleBuildTools = 'com.android.tools.build:gradle:8.2.1'
    playServicesVersion = '17.0.0'
    playServicesLocationVersion = '17.0.0'
    googlePlayServicesVersion = '17.0.0'
    googlePlayServicesVisionVersion = '20.1.0'
//    googlePlayServicesAuthVersion = '16.0.1'
//    androidMapsUtilsVersion = '0.5'
//    firebaseVersion = "18.0.0"
//    "firebase-ml-vision" = "19.0.3"
//    "firebase-ml-vision-face-model" = "17.0.2"
    kotlinVersion = "1.9.22"
    pictureVersion = "v2.6.1"
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url "$rootDir/../node_modules/react-native/android"
        }
        maven {
            // Android JSC is installed from npm
            url "$rootDir/../node_modules/jsc-android/dist"
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven {
            url 'https://maven.aliyun.com/repository/public'
            // We don't want to fetch react-native from Maven Central as there are older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        maven { url "https://jitpack.io" }
        google()
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
    }

    /*configurations.all {
        resolutionStrategy {
            // Remove this override in 0.65+, as a proper fix is included in react-native itself.
            force "com.facebook.react:react-native:${rootProject.ext.reactNative}"
        }
    }*/
}

subprojects {
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                compileSdk rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion

                defaultConfig {
                    minSdkVersion rootProject.ext.minSdkVersion
                    targetSdkVersion rootProject.ext.targetSdkVersion
                }
            }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
