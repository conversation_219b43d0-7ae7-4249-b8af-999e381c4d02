package com.camhr.module;

import android.app.Activity;
import android.util.Log;
import android.view.WindowManager.LayoutParams;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

public class SoftInputModule extends ReactContextBaseJavaModule {

  public SoftInputModule(ReactApplicationContext reactContext) {
    super(reactContext);
  }

  @Override
  public String getName() {
    return "SoftInputModeAndroid";
  }

  @Nullable
  @Override
  public Map<String, Object> getConstants() {
    Map<String, Object> map = new HashMap<>();
    map.put("adjustPan", LayoutParams.SOFT_INPUT_ADJUST_PAN);
    map.put("adjustResize", LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
    map.put("adjustNothing", LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    map.put("adjustUnspecified", LayoutParams.SOFT_INPUT_ADJUST_UNSPECIFIED);
    map.put("stateAlwaysHidden", LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
    map.put("stateAlwaysVisible", LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    map.put("stateHidden", LayoutParams.SOFT_INPUT_STATE_HIDDEN);
    map.put("stateVisible", LayoutParams.SOFT_INPUT_STATE_VISIBLE);
    map.put("stateUnchanged", LayoutParams.SOFT_INPUT_STATE_UNCHANGED);
    map.put("stateUnspecified", LayoutParams.SOFT_INPUT_STATE_UNSPECIFIED);
    return map;
  }

  @ReactMethod
  public void setMode(final int mode) {
    final Activity activity = this.getCurrentActivity();
    if (activity == null) {
      return;
    }
    Log.i("setMode", mode + "");
    activity.runOnUiThread(new Runnable() {
      @Override
      public void run() {
        activity.getWindow().setSoftInputMode(mode);
      }
    });
  }
}
