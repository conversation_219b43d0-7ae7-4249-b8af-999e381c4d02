package com.camhr.module;

import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
//import com.google.firebase.messaging.FirebaseMessaging;
//import com.google.firebase.messaging.RemoteMessage;
import com.camhr.app.MainActivity;
import com.camhr.app.service.AppForegroundService;
//import com.camhr.service.MyFirebaseMessagingService;
import com.camhr.util.Log;

//import org.greenrobot.eventbus.EventBus;
//import org.greenrobot.eventbus.Subscribe;

import java.util.Map;

public class AppModule extends ReactContextBaseJavaModule {

    public AppModule(ReactApplicationContext reactContext) {
        super(reactContext);
//        EventBus.getDefault().register(this);
        reactContext.addActivityEventListener(new BaseActivityEventListener() {
            @Override
            public void onNewIntent(Intent intent) {
                Log.i("AppModule", "onNewIntent:"
                    + " action: " + intent.getAction()
                );
                sendEvent("onNewIntent", intentToMap(intent));
            }
        });
    }

    private WritableMap intentToMap(Intent intent) {
        WritableMap map = Arguments.createMap();
        if (intent == null) return map;
        Log.i("AppModule", "intentToMap:"
            + " action: " + intent.getAction()
        );
        map.putString("action", intent.getAction());
        Uri data = intent.getData();
        if (data != null) {
            map.putString("data", data.toString());
        }
        map.putString("type", intent.getType());
        bundleToMap("extras", intent.getExtras(), map);
        return map;
    }

    private void bundleToMap(String key, Bundle bundle, WritableMap map) {
        Object obj;
        if (bundle != null) {
            WritableMap extras = Arguments.createMap();
            for (String s : bundle.keySet()) {
                try {
                    obj = bundle.get(s);
                    if (obj instanceof Bundle) {
                        bundleToMap(s, (Bundle) obj, extras);
                    } else {
                        extras.putString(s, String.valueOf(obj));
                    }
                } catch (Exception e) {
                    Log.w("AppModule", "bundleToMap: key: " + s, e);
                }
            }
            map.putMap(key, extras);
        }
    }

    /*@Subscribe
    public void onFcmEvent(MyFirebaseMessagingService.FcmTokenEvent event) {
        WritableMap map = Arguments.createMap();
        map.putString("token", event.token);
        sendEvent("fcmToken", map);
    }*/

    /*@Subscribe
    public void onFcmMessageEvent(RemoteMessage message) {
        WritableMap map = Arguments.createMap();
        map.putString("messageId", message.getMessageId());

        RemoteMessage.Notification notification = message.getNotification();
        if (notification != null) {
            map.putString("title", notification.getTitle());
            map.putString("content", notification.getBody());
        }

        WritableMap extras = Arguments.createMap();
        for (Map.Entry<String, String> entry : message.getData().entrySet()) {
            extras.putString(entry.getKey(), entry.getValue());
        }
        map.putMap("extras", extras);

        sendEvent("fcmMessage", map);
    }*/

    private void sendEvent(String eventName, WritableMap map) {
        map.putString("_eventName", eventName);
        getReactApplicationContext()
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit("event", map);
    }

    /*@ReactMethod
    public void getFcmToken(Promise promise) {
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(task -> {
            String token = task.isSuccessful() ? task.getResult() : MyFirebaseMessagingService.getFcmToken();
            Log.i("AppModule", "getFcmToken:"
                + " isSuccessful: " + task.isSuccessful()
                + " token: " + token
            );
            promise.resolve(token);
        });
    }*/

    @ReactMethod
    public void getIntent(Promise promise) {
        Activity activity = getCurrentActivity();
        promise.resolve(intentToMap(activity == null ? null : activity.getIntent()));
    }

    /**
     * 移除所有已显示的通知
     */
    @ReactMethod
    public void cancelAllNotification() {
        Activity activity = getCurrentActivity();
        if (activity != null) {
            NotificationManager notificationManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.cancelAll();
        }
    }

    /**
     * 移除通知
     */
    /*@ReactMethod
    public void cancelNotification(Integer notificationId) {
        Activity activity = getCurrentActivity();
        if (activity != null) {
            NotificationManager notificationManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.cancel(notificationId);
        }
    }*/

    @ReactMethod
    public void addListener(String eventName) {
        // Set up any upstream listeners or background tasks as necessary
    }

    @ReactMethod
    public void removeListeners(Integer count) {
        // Remove upstream listeners, stop unnecessary background tasks
    }

    @NonNull
    @Override
    public String getName() {
        return "AppModule";
    }

    @ReactMethod
    public void keepScreenOn(boolean isTrue) {
        try {
            Activity activity = this.getCurrentActivity();
            if (activity == null) return;
            activity.runOnUiThread(() -> {
                if (isTrue) {
                    activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                } else {
                    activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取手机当前的铃声模式
     */
    @ReactMethod
    public void getRingerMode(Promise promise) {
        try {
            Activity activity = this.getCurrentActivity();
            if (activity == null) return;
            AudioManager audioManager = (AudioManager) activity.getSystemService(Context.AUDIO_SERVICE);
            promise.resolve(audioManager.getRingerMode());
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(e);
        }
    }

    @ReactMethod
    public void startForegroundService(String notificationTitle, String notificationContent) {
        try {
            Activity activity = this.getCurrentActivity();
            if (activity == null) return;
            activity.runOnUiThread(() -> AppForegroundService.start(activity, notificationTitle, notificationContent));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void stopForegroundService() {
        try {
            Activity activity = this.getCurrentActivity();
            if (activity == null) return;
            activity.runOnUiThread(() -> AppForegroundService.stop(activity));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void isSaveInstanceState(Promise promise) {
        try {
            Activity activity = this.getCurrentActivity();
            if (activity instanceof MainActivity) {
                MainActivity mainActivity = (MainActivity) activity;
                promise.resolve(mainActivity.isSaveInstanceState);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        promise.resolve(false);
    }

    @ReactMethod
    public void elapsedRealtime(Promise promise) {
        promise.resolve(SystemClock.elapsedRealtime() + "");
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public String elapsedRealtimeSync() {
        return SystemClock.elapsedRealtime() + "";
    }
}
