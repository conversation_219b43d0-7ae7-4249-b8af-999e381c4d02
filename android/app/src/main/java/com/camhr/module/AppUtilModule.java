package com.camhr.module;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.RomUtils;
import com.blankj.utilcode.util.Utils;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.camhr.app.MainActivity;
import com.camhr.util.NotificationUtil;
import com.camhr.util.Log;

public class AppUtilModule extends ReactContextBaseJavaModule {

    public AppUtilModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return "AppUtilModule";
    }

    @ReactMethod
    public void existGooglePlay(Promise promise) {
        existApp("com.android.vending", promise);
    }

    @ReactMethod
    public void existApp(String applicationId, Promise promise) {
        promise.resolve(isAppInstalled(applicationId));
    }

    public static boolean isAppInstalled(final String pkgName) {
        try {
            PackageManager pm = Utils.getApp().getPackageManager();
            return pm.getApplicationInfo(pkgName, 0) != null;
        } catch (Exception e) {
            return false;
        }
    }

    @ReactMethod
    public void addListener(String eventName) {
        // Set up any upstream listeners or background tasks as necessary
    }

    @ReactMethod
    public void removeListeners(Integer count) {
        // Remove upstream listeners, stop unnecessary background tasks
    }

    @ReactMethod
    public void isHuawei(Promise promise) {
        promise.resolve(RomUtils.isHuawei());
    }

    @ReactMethod
    public void checkNotificationEnabled(ReadableMap map) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) return;
        NotificationUtil.checkNotificationEnabled(currentActivity,
            map.getString("message"), map.getString("btnConfirm"),
            map.getString("btnCancel"), map.getString("btnNeu"));
    }

    @ReactMethod
    public void isNotificationEnabled(Promise promise) {
        promise.resolve(NotificationUtil.isNotificationEnabled());
    }

    @ReactMethod
    public void launchAppNotificationsSettings() {
        NotificationUtil.launchAppNotificationsSettings();
    }

    @ReactMethod
    public void launchAppDetailsSettings() {
        AppUtils.launchAppDetailsSettings();
    }

    @ReactMethod
    public void printLog(Boolean isPrint) {
        Log.isPrint = isPrint != null && isPrint;
    }

    @ReactMethod
    public void goHome() {
        try {
            ActivityUtils.startHomeActivity();
        } catch (Exception e) {
            Log.w("AppUtilModule", "goHome", e);
        }
    }

    /**
     * 启动启动器页面
     */
    @ReactMethod
    public void startLauncherActivity() {
        try {
            Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                Log.w("AppUtilModule", "startLauncherActivity fail currentActivity is null");
                ActivityUtils.startLauncherActivity();
                return;
            }
            Intent intent = new Intent(currentActivity, MainActivity.class);
            currentActivity.startActivity(intent);
        } catch (Exception e) {
            Log.w("AppUtilModule", "startLauncherActivity", e);
        }
    }

    /**
     * 关闭当前原生页面
     */
    @ReactMethod
    public void finishCurrentActivity() {
        try {
            Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                Log.d("AppUtilModule", "finishMainActivity currentActivity is null");
                return;
            }
            currentActivity.finish();
        } catch (Exception e) {
            Log.w("AppUtilModule", "finishMainActivity", e);
        }
    }
}
