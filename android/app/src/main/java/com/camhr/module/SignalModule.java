package com.camhr.module;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.camhr.util.LLSignalMagager;

public class SignalModule extends ReactContextBaseJavaModule {
    LLSignalMagager signalMagager;
    public SignalModule(ReactApplicationContext reactContext) {
        super(reactContext);
        signalMagager = LLSignalMagager.getInstance(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return "SignalMagager";
    }

    @ReactMethod
    public void aesEncryptMessage(String msg, int userId, int localId,String groupId, Promise promise){
        try {
            promise.resolve(signalMagager.aesEncryptMessage(msg,userId,localId,groupId));
        } catch (Exception e) {
            e.printStackTrace();
            promise.resolve(null);
        }
    }

    @ReactMethod
    public void aesDecryptMessage(String msg, String aesExt, int userId, int localId,String groupId, Promise promise){
        try {
            promise.resolve(signalMagager.aesDecryptMessage(msg,aesExt,userId,localId,groupId));
        } catch (Exception e) {
            e.printStackTrace();
            promise.resolve(null);
        }
    }
}
