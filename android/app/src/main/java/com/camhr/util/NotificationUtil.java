package com.camhr.util;

import android.app.AlertDialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.provider.Settings;

import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.IntentUtils;
import com.blankj.utilcode.util.NotificationUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.Utils;
import com.camhr.app.MainActivity;
import com.camhr.app.R;

/**
 * Created by <PERSON>s on 2021/4/8.
 */
public final class NotificationUtil {
    private static final String KEY_NOTIFICATION_NO_AGAIN = "KEY_NOTIFICATION_NO_AGAIN";

    public static int getPendingIntentFlag() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return PendingIntent.FLAG_MUTABLE;
        } else {
            return 0;
        }
    }

    /**
     * 启动前台服务通知
     */
    public static void startForegroundWithNotification(
            Service service,
            String notificationChannelId,
            String notificationChannelName,
            int notificationId,
            String title,
            String content
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(
                    notificationChannelId,
                    notificationChannelName,
                    NotificationManager.IMPORTANCE_LOW
            );
            notificationChannel.setShowBadge(false);
            ((NotificationManager) service.getSystemService(Context.NOTIFICATION_SERVICE))
                    .createNotificationChannel(notificationChannel);
        }
        Intent intent = new Intent(service, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        Notification notification =
                new NotificationCompat.Builder(service.getApplicationContext(), notificationChannelId)
                        .setAutoCancel(false)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setSmallIcon(R.drawable.jpush_notification_icon)
                        .setContentIntent(
                                PendingIntent.getActivity(
                                        service,
                                        0,
                                        intent,
                                        getPendingIntentFlag()
                                )
                        )
                        .setOngoing(true)
                        .build();
        service.startForeground(notificationId, notification);
    }

    public static void checkNotificationEnabled(Context context, String message, String btnConfirm, String btnCancel, String btnNeu) {
        if (isNotificationEnabled() || SPUtils.getInstance()
                .getBoolean(KEY_NOTIFICATION_NO_AGAIN, false)
        ) {
            return;
        }
        new AlertDialog.Builder(context)
                .setMessage(message)
                .setPositiveButton(btnConfirm, (dialog, which) -> {
                    dialog.dismiss();
                    launchAppNotificationsSettings();
                })
                .setNeutralButton(btnNeu, (dialog, which) -> {
                    dialog.dismiss();
                    SPUtils.getInstance().put(KEY_NOTIFICATION_NO_AGAIN, true);
                })
                .setNegativeButton(btnCancel, (dialog, which) -> dialog.dismiss())
                .setCancelable(false)
                .show();
    }

    /**
     * 通知是否启用
     */
    public static boolean isNotificationEnabled() {
        return NotificationUtils.areNotificationsEnabled();
    }

    /**
     * 通知是否启用
     */
    public static void launchAppNotificationsSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Intent intent = new Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, Utils.getApp().getPackageName());
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (IntentUtils.isIntentAvailable(intent)) {
                try {
                    Utils.getApp().startActivity(intent);
                    return;
                } catch (Throwable tr) {
                    Log.w("NotificationUtil", "go app_notification_settings error", tr);
                }
            }
        }
        PermissionUtils.launchAppDetailsSettings();
    }

}
