package com.camhr.util;

/**
 * Created by <PERSON><PERSON> on 2023/1/6.
 */
public final class Log {
    public static boolean isPrint = false;

    public static void d(String tag, String msg) {
        if (isPrint) android.util.Log.d(tag, msg);
    }

    public static void i(String tag, String msg) {
        if (isPrint) android.util.Log.d(tag, msg);
    }

    public static void w(String tag, String msg) {
        if (isPrint) android.util.Log.w(tag, msg);
    }

    public static void w(String tag, String msg, Throwable tr) {
        if (isPrint) android.util.Log.w(tag, msg, tr);
    }

    public static void e(String tag, String msg) {
        if (isPrint) android.util.Log.e(tag, msg);
    }
}
