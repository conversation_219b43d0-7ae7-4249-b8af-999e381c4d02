package com.camhr.util;

import static android.content.Context.MODE_PRIVATE;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.RequiresApi;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;

import java.io.IOException;
import java.lang.reflect.Array;
import java.security.InvalidAlgorithmParameterException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class LLSignalMagager {
    private static final String IDKEYPAIR = "idKeyPairKey";
    private static final String PREKEY = "preKeyKey";
    private static final String SIGNEDPREKEY = "signedPreKeyKey";
    private static final String SIGNEDPREKEYSIG = "signedPreKeySignatureKey";

    private SharedPreferences sp;
    private static LLSignalMagager signalMagager = null;

    private LLSignalMagager(Context context) {
        sp = context.getSharedPreferences("SP", MODE_PRIVATE);
    }

    public static LLSignalMagager getInstance(Context context) {
        if (signalMagager == null) {
            signalMagager = new LLSignalMagager(context);  //在第一次调用getInstance()时才实例化，实现懒加载,所以叫懒汉式
        }
        return signalMagager;
    }

    public String getAESKey(int userId, int localId) throws NoSuchAlgorithmException {
        String key = userId < localId ? "" + userId + localId : "" + localId + userId;
        return md5(key);
    }

    public String getGroupAESKey(int userId, String groupId) throws NoSuchAlgorithmException {
        String key = groupId+ userId;
        return md5(key);
    }

    public WritableMap aesEncryptMessage(String msg, int userId, int localId, String groupId) throws  IOException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidAlgorithmParameterException, java.security.InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        byte[] key;
        if (TextUtils.isEmpty(groupId)){
            key = getAESKey(userId,localId).getBytes();
        }else {
            key = getGroupAESKey(localId,groupId).getBytes();
        }
        SecureRandom randomSecureRandom = new SecureRandom();

        SecretKey secretKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");

        byte[] iv = new byte[cipher.getBlockSize()];
        randomSecureRandom.nextBytes(iv);
        IvParameterSpec ivParams = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParams);
        byte[] ciphertext = cipher.doFinal(msg.getBytes());

        WritableMap map = Arguments.createMap();
        map.putString("content",Base64.encodeToString(ciphertext, Base64.NO_WRAP));
        map.putString("aesExt",Base64.encodeToString(iv, Base64.NO_WRAP));

        return map ;
    }

    public String aesDecryptMessage(String msg, String aesExt, int userId, int localId, String groupId) throws  IOException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidAlgorithmParameterException, java.security.InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        byte[] key;
        if (TextUtils.isEmpty(groupId)){
            key = getAESKey(userId,localId).getBytes();
        }else {
            key = getGroupAESKey(userId,groupId).getBytes();
        }

        byte[] iv =  Base64.decode(aesExt, Base64.NO_WRAP);
        byte[] messageData = Base64.decode(msg, Base64.NO_WRAP);

        SecretKey secretKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
        IvParameterSpec ivParams = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParams);
        byte[] decrypted = cipher.doFinal(messageData);
        return new String(decrypted);
    }

    public static String md5(String string) throws NoSuchAlgorithmException {
        if (TextUtils.isEmpty(string)) {
            return "";
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(string.getBytes());
            String result = "";
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == 1) {
                    temp = "0" + temp;
                }
                result += temp;
            }
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }
}
