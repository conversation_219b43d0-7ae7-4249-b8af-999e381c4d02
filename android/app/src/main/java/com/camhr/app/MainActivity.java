package com.camhr.app;

import android.os.Bundle;

import androidx.annotation.NonNull;

import com.camhr.util.Log;
import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate;
import com.facebook.react.ReactRootView;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactActivityDelegate;
import com.swmansion.gesturehandler.react.RNGestureHandlerEnabledRootView;

import org.devio.rn.splashscreen.SplashScreen;

public class MainActivity extends ReactActivity {
    private static final String TAG = "MainActivity";
    private static final String KEY_IS_SAVE_INSTANCE_STATE = "KEY_IS_SAVE_INSTANCE_STATE";
    public boolean isSaveInstanceState = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        SplashScreen.show(this, true);
        if (savedInstanceState != null) {
            isSaveInstanceState = savedInstanceState.getBoolean(KEY_IS_SAVE_INSTANCE_STATE, false);
        }
        Log.d(TAG, "onCreate: isSaveInstanceState=" + isSaveInstanceState);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        Log.d(TAG, "onSaveInstanceState: ");
        outState.putBoolean(KEY_IS_SAVE_INSTANCE_STATE, true);
        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        isSaveInstanceState = savedInstanceState.getBoolean(KEY_IS_SAVE_INSTANCE_STATE, false);
        Log.d(TAG, "onRestoreInstanceState: isSaveInstanceState=" + isSaveInstanceState);
    }

    /**
     * Returns the name of the main component registered from JavaScript. This is
     * used to schedule rendering of the component.
     */
    @NonNull
    @Override
    protected String getMainComponentName() {
        return "Camhr";
    }

    @Override
    protected ReactActivityDelegate createReactActivityDelegate() {
        return new DefaultReactActivityDelegate(this, getMainComponentName(), DefaultNewArchitectureEntryPoint.getFabricEnabled()) {
            @Override
            protected ReactRootView createRootView() {
                return new RNGestureHandlerEnabledRootView(MainActivity.this);
            }
        };
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        Log.d(TAG, "onRestart: ");
    }

    @Override
    protected void onStart() {
        super.onStart();
        Log.d(TAG, "onStart: ");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: isSaveInstanceState=" + isSaveInstanceState);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: isSaveInstanceState=" + isSaveInstanceState);
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d(TAG, "onStop: ");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy: ");
    }
}
