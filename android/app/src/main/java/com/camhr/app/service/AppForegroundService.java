package com.camhr.app.service;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.camhr.app.R;
import com.camhr.util.Log;
import com.camhr.util.NotificationUtil;

/**
 * 前台保活服务
 * Created by <PERSON><PERSON> on 2022/7/29.
 */
public class AppForegroundService extends Service {

    private static final String TAG = "AppForegroundService";
    private static final int NOTIFICATION_ID = 101;
    private static final String NOTIFICATION_CHANNEL_ID = "channel_foreground";
    private static final String NOTIFICATION_CHANNEL_NAME = "Foreground";
    private static final String EXTRA_NOTIFICATION_TITLE = "notificationTitle";
    private static final String EXTRA_NOTIFICATION_CONTENT = "notificationContent";
    private static String notificationTitle = null;
    private static String notificationContent = null;
    private boolean serviceIsLive = false;

    public static void start(Context context, String notificationTitle, String notificationContent) {
        try {
            Intent intent = new Intent(context, AppForegroundService.class);
            AppForegroundService.notificationTitle = notificationTitle;
            AppForegroundService.notificationContent = notificationContent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        } catch (Exception e) {
            Log.w(TAG, "start error", e);
        }
    }

    public static void stop(Context context) {
        try {
            context.stopService(new Intent(context, AppForegroundService.class));
        } catch (Exception e) {
            Log.w(TAG, "stop error", e);
        }
    }

    public IBinder onBind(Intent intent) {
        return null;
    }

    public void onCreate() {
        Log.d(TAG, "onCreate: ");
        serviceIsLive = true;
        AppUtils.registerAppStatusChangedListener(new Utils.OnAppStatusChangedListener() {
            @Override
            public void onForeground(Activity activity) {
                Log.d(TAG, "OnAppStatusChangedListener onForeground: ");
                stopDelayTask();
            }

            @Override
            public void onBackground(Activity activity) {
                Log.d(TAG, "OnAppStatusChangedListener onBackground: ");
                startDelayTask();
            }
        });
    }

    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand: ");
        String title = AppForegroundService.notificationTitle;
        String content = AppForegroundService.notificationContent;
        if (title == null) {
            title = getString(R.string.foreground_service_notification_title, AppUtils.getAppName());
            content = getString(R.string.foreground_service_notification_content);
        }
        NotificationUtil.startForegroundWithNotification(
                this,
                NOTIFICATION_CHANNEL_ID,
                NOTIFICATION_CHANNEL_NAME,
                NOTIFICATION_ID,
                title,
                content
        );
        return super.onStartCommand(intent, flags, startId);
    }

    private void startDelayTask() {
        stopDelayTask();
        delayTask.run();
    }

    private void stopDelayTask() {
        ThreadUtils.getMainHandler().removeCallbacks(delayTask);
    }

    private final Runnable delayTask = new Runnable() {
        @Override
        public void run() {
            Log.d(TAG, "=======delayTask======= serviceIsLive: " + serviceIsLive);
            if (serviceIsLive) {
                ThreadUtils.runOnUiThreadDelayed(delayTask, 3000);
            }
        }
    };

    public void onDestroy() {
        try {
            Log.d(TAG, "onDestroy: ");
            serviceIsLive = false;
            stopDelayTask();
        } catch (Exception e) {
            Log.w(TAG, "onDestroy: ", e);
        }
    }
}
