<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.camhr.app">

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data
                android:host="*"
                android:scheme="longpay" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data
                android:host="*"
                android:scheme="longchat" />
        </intent>

        <package android:name="com.tencent.mm" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data
                android:host="ababank.com"
                android:scheme="abamobilebank" />
        </intent>
        <package android:name="com.facebook.katana" />
        <package android:name="com.instagram.android" />
        <package android:name="com.twitter.android" />
        <package android:name="com.zhiliaoapp.musically" />
    </queries>

    <!-- <uses-permission android:name="com.camhr.app.permission.JPUSH_MESSAGE" /> -->
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Android 13 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />-->
<!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />-->
    <uses-permission
        android:name="android.permission.ACCESS_BACKGROUND_LOCATION"
        tools:node="remove" />
    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:node="remove" />

    <permission
        android:name="com.camhr.app.push.permission.MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.camhr.app.push.permission.MESSAGE" />
    <permission
        android:name="com.camhr.app.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />
    <uses-permission android:name="com.camhr.app.permission.C2D_MESSAGE" />

    <permission
        android:name="com.camhr.app.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />


    <uses-sdk tools:overrideLibrary="co.apptailor.googlesignin" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:maxAspectRatio="2.4"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <data
                    android:host="${HOST_NAME}"
                    android:scheme="camhr" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            tools:replace="android:theme" />

        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="${JPUSH_APPKEY}" />
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="${JPUSH_CHANNEL}" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAq4SHLJfdVUiPApU9atm4Bh5FZt9ucEYc" />

        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".apshare.ShareEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider531317358425002"
            android:exported="true" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <receiver android:name="cn.jpush.android.asus.AsusPushMessageReceiver" />

        <service
            android:name="cn.jpush.android.service.PluginFCMMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/jpush_notification_icon" />
        <meta-data
            android:name="google_analytics_adid_collection_enabled"
            android:value="false" />

        <service
            android:name="io.agora.rtc.ss.impl.LocalScreenSharingService"
            android:exported="true"
            android:foregroundServiceType="mediaProjection"
            tools:node="replace">

            <intent-filter>

                <action android:name="android.intent.action.screenshare" />
            </intent-filter>
        </service>

        <!--<service
            android:name=".service.AppForegroundService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />-->

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
    </application>

</manifest>
