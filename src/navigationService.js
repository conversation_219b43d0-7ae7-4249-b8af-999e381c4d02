/**
 * 导航服务
 * author: moke
 */
import { NavigationActions, StackActions } from 'react-navigation';
import constant from './store/constant';
import promiseUtil from './util/promiseUtil';

function routeInterceptor(routeName, params) {
  return { routeName, params };
}

let _navigator;

function setTopLevelNavigator(navigatorRef) {
  _navigator = navigatorRef;
}

function navigate(routeName, params) {
  _navigator.dispatch(NavigationActions.navigate(routeInterceptor(routeName, params)));
}

function push(routeName, params) {
  _navigator.dispatch(StackActions.push(routeInterceptor(routeName, params)));
}

/**
 * The pop action takes you back to a previous screen in the stack. The n param allows you to specify how many screens to pop back by.
 * @param options {{n: number}}
 */
function pop(options) {
  _navigator.dispatch(StackActions.pop(options));
}

/**
 * The popToTop action takes you back to the first screen in the stack, dismissing all the others. It's functionally identical to StackActions.pop({n: currentIndex}).
 */
function popToTop() {
  _navigator.dispatch(StackActions.popToTop());
}

/**
 * Go back to previous screen and close current screen.
 * @param options {{key: string}}
 */
function goBack(options) {
  _navigator.dispatch(NavigationActions.back(options));
}

function reset(routeName, params) {
  const resetAction = StackActions.reset({
    index: 0,
    actions: [NavigationActions.navigate(routeInterceptor(routeName, params))],
  });
  _navigator.dispatch(resetAction);
}

// 支持传入多个路由
function resetMultiple(routes) {
  if (routes && routes.length) {
    const actions = routes.map((item) => {
      return NavigationActions.navigate({
        routeName: item.routeName,
        params: item.params,
      });
    });

    const resetAction = StackActions.reset({
      index: routes.length - 1,
      actions,
    });
    _navigator.dispatch(resetAction);
  }
}

function replace(routeName, params) {
  const replaceAction = StackActions.replace(routeInterceptor(routeName, params));
  _navigator.dispatch(replaceAction);
}

function getActiveRouteName(navigationState) {
  // if (!navigationState) {
  //   return null;
  // }
  // const route = navigationState.routes[navigationState.index];
  // if (route.routes) {
  //   return getActiveRouteName(route);
  // }
  return (
    navigationState &&
    navigationState.routes &&
    navigationState.routes[navigationState.index].routeName
  );
}

let currentScreen = '';

let currentNavigationState = null;

function getCurrentScreen() {
  return currentScreen;
}

function getCurrentNavigationState() {
  // {key: 'StackRouterRoot', isTransitioning: false, index: 1, routes[{ params: null, routeName: 'home', key: 'id-1638773142071' }]}
  return currentNavigationState;
}

function getRouteKey(routeName) {
  if (!currentNavigationState || !currentNavigationState.routes) return null;
  const route = currentNavigationState.routes.find((item) => item.routeName === routeName);
  return route && route.key;
}

function getRoute(routeName) {
  if (!currentNavigationState || !currentNavigationState.routes) return null;
  return currentNavigationState.routes.find((item) => item.routeName === routeName);
}

function hasRouteName(routeName) {
  return !!getRoute(routeName);
}

function onNavigationStateChange(prevState, currentState) {
  currentNavigationState = currentState;
  const prevScreen = getActiveRouteName(prevState);
  currentScreen = getActiveRouteName(currentState);
  console.log(
    'onNavigationStateChange',
    currentScreen,
    prevScreen,
    currentState.routes && currentState.routes[currentState.index]
  );
  // console.log('onNavigationStateChange', currentState.routes && currentState.routes[currentState.index], currentState);
  // 广播事件路由改变事件出去
  const result = {
    prevScreen,
    currentScreen,
  };
  global.emitter.emit(constant.event.navigationStateChange, result);
  return result;
}

async function navigateMessage(params) {
  if (currentScreen && currentScreen === 'chatMessage') {
    return;
  }
  if (!hasRouteName('main')) return;
  if (hasRouteName('chatMessage')) {
    /*resetMultiple([
      { routeName: 'main' },
      {
        routeName: 'message',
        params,
      },
    ]);*/
    navigate('main');
    await promiseUtil.sleep(500);
    navigate('chatMessage', params);
  } else {
    navigate('chatMessage', params);
  }
}

export default {
  navigate,
  push,
  pop,
  reset,
  setTopLevelNavigator,
  goBack,
  replace,
  resetMultiple,
  getCurrentScreen,
  onNavigationStateChange,
  navigateMessage,
  getCurrentNavigationState,
  getRouteKey,
  hasRouteName,
};
