import * as RNKeychain from 'react-native-keychain';
import { Platform } from 'react-native';

class Keychain {
  async set(key, value) {
    await RNKeychain.setGenericPassword(key, value, {
      accessible: RNKeychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
      service: key,
    });
  }

  async get(key) {
    const result = await RNKeychain.getGenericPassword({
      service: key,
    });
    return result && result.password;
  }

  async convertToNew() {
    if (Platform.OS.toLowerCase() !== 'ios') return;
    const result = await RNKeychain.getGenericPassword();
    if (result && result.password && result.username === 'qxh') {
      await RNKeychain.setGenericPassword('qxh', '');
      await this.set('paymentInfos', result.password);
    }
  }
}

export default new Keychain();
