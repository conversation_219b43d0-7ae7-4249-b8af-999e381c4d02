
/**
 * 标准化电话号码，因为已经有了区号，所以要把前面的0去掉
 * eg：08964663362 -> 8964663362
 * @param {*} phone
 */
export function normalizePhone(phone, region) {
  if (!phone) {
    return phone;
  }
  let result = phone.trim();
  switch (region) {
    case '86':
    case '855':
      // 柬埔寨的电话号码
      if (result.indexOf('0') === 0) {
        result = result.substr(1);
      }
      break;
    default:
      break;
  }
  return result;
} 