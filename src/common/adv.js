/**
 * 文件管理
 * 本地缓存
 * 使用内部缓存
 */

import RNFS from 'react-native-fs';
import { deviceHeight } from './index';
import Storage from './storage';

// 缓存目录
const cachesDirectoryPath = RNFS.CachesDirectoryPath;
// 缓存图片生成路径
const output = `${cachesDirectoryPath}/adv.jpg`;

function download(url) {
  // console.log('output', output);
  return RNFS.downloadFile({
    fromUrl: url,
    toFile: output,
    background: true,
  });
}

// function exist() {
//   try {
//     return RNFS.exists(output);
//   } catch (e) {
//     return Promise.resolve(false);
//   }
// }

function readFile() {
  return RNFS.readFile(output, 'base64');
}

/**
 * 缓存广告图片
 * 根据设备尺寸来缓存对应的图片
 * @param {*} adv
 */
export async function cacheAdvImage(adv) {
  if (!adv) {
    // 没有广告了，清除本地的
    await Storage.setHasAdv(false);
    await Storage.setUrlHash('');
    return;
  }
  // global.storage.remove({ key: URL_HASH });
  // 根据设备尺寸来缓存
  const defaultSizeImage = adv.find((a) => a.size === '1080×1920');

  let url = defaultSizeImage ? defaultSizeImage.imageUrl : '';
  if (deviceHeight > 736) {
    const bigSizeImage = adv.find((a) => a.size === '1125×2436');
    url = bigSizeImage ? bigSizeImage.imageUrl : '';
  }

  // 如果图片url为空，返回，不需要缓存
  if (!url) {
    return;
  }

  // 判断是否已经缓存过，如果已缓存，不需要再下载，浪费流量
  const urlHash = await Storage.getUrlHash();
  if (urlHash === url) {
    return;
  }

  // 开始缓存
  download(url);

  Storage.setHasAdv(true);
  Storage.setUrlHash(url);
}

/**
 * 获取缓存图片，返回base64的图片信息
 */
export async function getCacheAdvImage() {
  const exist1 = await RNFS.exists(output);
  if (!exist1) {
    return '';
  }
  return readFile();
}
