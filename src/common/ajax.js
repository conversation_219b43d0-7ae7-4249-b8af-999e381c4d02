import axios from 'axios';
import qs from 'qs';
import Configs from '../configs';
import I18n from '../i18n';
// import sentryUtil from '../util/sentryUtil';

/**
 * ajax 请求封装
 */
export default class Ajax {
  constructor(baseURL) {
    this.request = axios.create();
    this.request.defaults.baseURL = baseURL;
    this.request.defaults.headers.common.Accept = 'application/json';
    this.request.defaults.headers.common['Content-Type'] = 'application/json';
    // this.request.interceptors.request.use(config => config, error => Promise.reject(error));
    this.request.interceptors.response.use(
      (response) => {
        if (Configs.printHttpResponse) {
          // const { status, statusText, data, headers, config, request } = response;
          const { status, data, headers, config } = response;
          // console.debug('response keys', Object.keys(response));
          console.debug('response =====>', status, config.method, config.url);
          console.debug('response request headers', config.headers);
          console.debug('response request data', config.data);
          console.debug('response headers', headers);
          console.debug('response data', data);
          console.debug('response <=====');
        }
        return response;
      },
      (error) => {
        if (!error?.response) {
          console.warn('response error', error?.message, error?.request?._response, error);
          /*sentryUtil.captureMessage({
            status: -77,
            message: error?.message,
            desc: error?.config?.url,
          });*/
        } else {
          const { status, data, headers, config } = error.response;
          if (Configs.printHttpResponseError) {
            console.warn('response error =====>', status, config.method, config.url);
            console.warn(
              'response error request headers',
              config.headers && JSON.stringify(config.headers)
            );
            if (typeof config.data === 'string') {
              console.warn('response error request data', config.data);
            } else {
              console.warn(
                'response error request data',
                config.data && JSON.stringify(config.data)
              );
            }
            console.warn('response error headers', headers && JSON.stringify(headers));
            console.warn('response error data', data && JSON.stringify(data));
            console.warn('response error <=====');
          } else {
            const content = {
              status,
              data,
              headers,
              config,
            };
            console.warn('response error', JSON.stringify(content));
          }
          if (data && !data.message && data.fieldErrors) {
            const message = Object.keys(data.fieldErrors)
              .map((key) => `${key}${data.fieldErrors[key]}`)
              .join(';');
            error.response.data = {
              message,
              ...data,
            };
          }
        }

        if (error?.message?.includes('timeout')) {
          // 判断请求异常信息中是否含有超时timeout字符串 提示请求超时
          toast.show(I18n.t('msg_network_timeout'));
        }

        return Promise.reject(error);
      }
    );
  }

  setHeader(headers) {
    this.headers = headers || {};
    return this;
  }

  get({ url, headers }) {
    return this.request.get(url, { headers });
  }

  post({ url, data, headers }) {
    return this.request.post(url, data, { headers });
  }

  delete({ url, data, headers }) {
    return this.request.delete(url, { headers, data });
  }

  put({ url, data, headers }) {
    return this.request.put(url, data, { headers });
  }

  patch({ url, data, headers }) {
    return this.request.patch(url, data, { headers });
  }

  formPost({ url, data, headers }) {
    return this.request.post(url, qs.stringify(data, { arrayFormat: 'repeat' }), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', ...headers },
    });
  }

  formGet({ url, headers }) {
    return this.request.get(url, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', ...headers },
    });
  }

  upload({ url, formData, headers }) {
    return this.request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Accept: 'application/json',
        ...headers,
      },
    });
  }

  uploadProgress({ url, formData, onUploadProgress, headers }) {
    return this.request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Accept: 'application/json',
        ...headers,
      },
      onUploadProgress,
    });
  }
}
