import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';

import TabbarPersonal from './components/tabbar/tabbarPersonal';
import TabbarEnterprise from './components/tabbar/tabbarEnterprise';
import chatSessionDao from './database/dao/chatSessionDao';
import constant from './store/constant';
import { autorun } from 'mobx';
import uiUtil from './util/uiUtil';
import NavigationService from './navigationService';
import loginAction from './store/actions/login';
import I18n from './i18n';
import AppModule from './modules/AppModule';
import AppUtil from './util/appUtil';

let showCheckNotificationEnabled = true;

@inject('globalStore', 'stores')
@observer
export default class Tabbar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      msgBadge: 0,
    };
  }

  componentDidMount() {
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.willFocus);
    global.emitter.on(constant.event.chatSessionChange, this.checkChatBadge);
    global.emitter.on(constant.event.imSessionTimeout, this.handleImSessionTimeout);
    this.autoLoginImDisposer = autorun(() => {
      const { hasConnectImSocket } = this.props.stores.userStore;
      console.debug(`autoLoginIM hasConnectImSocket:${hasConnectImSocket}`);
      this.checkChatBadge();
    });
    this.checkChatBadge();
    if (showCheckNotificationEnabled) {
      showCheckNotificationEnabled = false;
      AppUtil.checkNotificationEnabled();
    }
    AppModule.startForegroundService();
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
    global.emitter.off(constant.event.chatSessionChange, this.checkChatBadge);
    global.emitter.off(constant.event.imSessionTimeout, this.handleImSessionTimeout);
    this.autoLoginImDisposer?.();
  }

  handleImSessionTimeout = (data) => {
    const { isLogin, isCompany, themeStyle } = this.props.stores.userStore;
    if (data?.isSocket && isLogin) {
      if (isCompany) {
        loginAction.logoutForEp();
      } else {
        loginAction.logout();
      }
      uiUtil.showAlert({
        showCancel: false,
        title: I18n.t('page_setting_remind_text'),
        message: I18n.t('msg_device_logout'),
        themeStyle,
      });
    }
  };

  willFocus = () => {
    this.checkChatBadge();
  };

  checkChatBadge = async () => {
    try {
      if (!this.props.stores.userStore.hasConnectImSocket) {
        this.setState({ msgBadge: 0 });
        return;
      }
      const unReadNum = await chatSessionDao.totalUnReadNum();
      this.setState({ msgBadge: unReadNum });
    } catch (e) {
      console.warn('checkChatBadge error', e);
    }
  };

  render() {
    const {
      globalStore: { isEnterprise },
    } = this.props;
    const { msgBadge } = this.state;
    return isEnterprise ? (
      <TabbarEnterprise navigation={this.props.navigation} msgBadge={msgBadge} />
    ) : (
      <TabbarPersonal navigation={this.props.navigation} msgBadge={msgBadge} />
    );
  }
}
