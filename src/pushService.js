import JPush from 'jpush-react-native';
import _ from 'lodash';
// import { NativeModules } from 'react-native';
import NavigationService from './navigationService';
import userStore from './store/stores/user';
import pageAction from './store/actions/page';
import constant from './store/constant';
import settingsAction from './store/actions/settings';
import chatSessionDao from './database/dao/chatSessionDao';
import imAction from './store/actions/imAction';
import pushUtil from './util/pushUtil';
import callAction from './store/actions/callAction';
import meetingAction from './store/actions/meetingAction';
import pushNoticeManager from './util/pushNoticeManager';
import regExp from './util/regExp';
import Session from './api/session';

class PushService {
  init() {
    JPush.setLoggerEnable(__DEV__);
    JPush.init();
    this.clearBadge();

    // 测试FCM
    /*setTimeout(() => {
      if (NativeModules.JCoreModule) {
        console.log('PushService testCountryCode us 1');
        NativeModules.JCoreModule.testCountryCode({ code: 'us' });
        console.log('PushService testCountryCode us 2');
      }
    }, 2000);*/
    pushUtil.getRegistrationID();
  }

  registerListeners() {
    JPush.addNotificationListener(this.onNotificationListener);
  }

  removeListeners() {
    JPush.removeListener(this.onNotificationListener);
    JPush.clearLocalNotifications();
  }

  clearBadge() {
    JPush.setBadge({ badge: 0, appBadge: 0 });
    this.clearAllNotifications();
  }

  clearAllNotifications() {
    JPush.clearAllNotifications();
    JPush.clearLocalNotifications();
    pushNoticeManager.clearAllNotifications();
  }

  onNotificationListener = (map) => {
    try {
      console.debug('push notificationListener', map);
      // pushNoticeManager.receiveNotificationListener(map);

      let { extras, notificationEventType } = map;
      if (_.isString(extras)) {
        extras = JSON.parse(extras);
      }
      if (notificationEventType === 'notificationOpened') {
        if (IS_IOS) {
          JPush.setBadge({ badge: -1, appBadge: 0 });
        }
        this.onNotificationOpened(extras);
      } else if (notificationEventType === 'notificationArrived') {
        this.onNotificationArrived(extras);
      }
    } catch (e) {
      console.warn('onNotificationListener error', e);
    }
  };

  onNotificationArrived = (extras) => {
    // if (!extras?.type) return;
  };

  onNotificationOpened = (extras) => {
    if (!extras?.type) return;
    switch (extras.type) {
      case 'chat':
        this.gotoChat(extras);
        break;
      case 'voip':
        this._onVoipMessage(extras);
        break;
      case 'notification':
        this.gotoMessageDetail(extras);
        break;
      case 'article':
        NavigationService.navigate('articleDetail', {
          articleId: extras.articleId,
        });
        break;
      case 'new.job.publish':
        NavigationService.navigate('jobDetail', {
          isPush: true,
          clickData: extras?.clickData,
          detail: { id: extras.jobId },
        });
        break;
      case 'like':
      case 'newComment':
        this.onDynamicClick(extras);
        break;
    }
  };

  onDynamicClick = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        NavigationService.navigate('dynamicDetail', {
          info: { id: item.twitterId },
          dynamicId: item.twitterId,
          onRefreshDynamic: (dynamicId, commets, likes, liked) => {},
          onDynamicDelete: (dynamicId) => {},
        });
      } else {
      }
    });
  };

  gotoChat = async (extras) => {
    if (!userStore.isLogin || !userStore.hasConnectImSocket || !extras.sessionId) return;
    if (userStore.isCompany) {
      pageAction.selectedTab(constant.tabs.chat);
    } else {
      settingsAction.selectedTab(constant.tabs.chat);
    }
    const params = { sessionId: extras.sessionId, isGroup: extras.sessionType === 'group' ? 1 : 0 };
    params.session = await chatSessionDao.getChatSession(params);
    if (!params.session) return;
    NavigationService.navigateMessage(params);
    imAction.getAllOfflineMessageList();
  };

  gotoMessageDetail = (extras) => {
    if (!userStore.isLogin) return;
    NavigationService.navigate('pushMessageDetail', { messageId: extras.messageId });
  };

  _onVoipMessage = async (message) => {
    message.state = parseInt(message.state, 10);
    message.sessionId = parseInt(message.sessionId, 10);
    if (message.meetingInfo && typeof message.meetingInfo === 'string') {
      message.meetingInfo = JSON.parse(message.meetingInfo);
    }
    if (message?.callType === 'meetingCall') {
      await meetingAction.onReceiveVoipInvite(message);
    } else {
      await callAction.onReceiveVoipInvite(message);
    }
  };
  /**
   * 显示推送通知
   * messageID必须为数字，不要大于9位数，Android使用int会超出范围
   * @param params
   */
  async addLocalNotification(params) {
    try {
      const unReadNum = await chatSessionDao.totalUnReadNum();
      JPush.setBadge({ badge: unReadNum, appBadge: unReadNum });

      console.debug('PushService addLocalNotification', params);
      if (!params.title || !regExp.numberExtra.test(params.messageID)) return;
      if (params.messageID.length > 9) {
        params.messageID = params.messageID.slice(params.messageID.length - 9);
      }
      JPush.addLocalNotification(pushNoticeManager.addLocalNotificationInterceptor(params));
    } catch (e) {
      console.warn('PushService addLocalNotification', params, e);
    }
  }
}

export default new PushService();
