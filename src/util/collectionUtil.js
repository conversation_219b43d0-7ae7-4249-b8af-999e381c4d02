/**
 * 集合工具类
 */
const CollectionUtil = {};

/**
 * 将item添加到value为数组的map中
 * @param map {Map}
 * @param key
 * @param item
 */
CollectionUtil.mapArrayAdd = function(map, key, item) {
  let arr = map.get(key);
  if (arr) {
    arr.push(item);
  } else {
    map.set(key, [item]);
  }
};

/**
 * 将item添加到value为Set的map中
 * @param map {Map}
 * @param key
 * @param item
 */
CollectionUtil.mapSetAdd = function(map, key, item) {
  let set = map.get(key);
  if (set) {
    set.add(item);
  } else {
    map.set(key, new Set([item]));
  }
};

export default CollectionUtil;
