import _ from 'lodash';
import { NativeModules } from 'react-native';
import moment from 'moment';
import { Linking } from '../components';
import NavigationService from '../navigationService';
import configs from '../configs';
import I18n from '../i18n';
import Ajax from '../api/ajax';

/**
 * 支付管理类
 */
class payManager {
  // LongPay支付
  onLongPay = async (data) => {
    try {
      let canOpenURL = await Linking.canOpenURL(configs.longPayAppUrl);
      if (!canOpenURL) {
        Linking.openURL(configs.longPayDownloadUrl);
        return;
      }
      await Linking.openURL(
        `${configs.longPayAppUrl}?dlt=2&data=${encodeURIComponent(JSON.stringify(data.result))}`
      );
    } catch (error) {
      console.log('onLongPay-error', error);
    }
  };

  // ABA支付
  onABAPay = async ({ prePayData, route, routeParam, callback }) => {
    const data = {
      merchant_id: prePayData?.merchantId,
      hash: prePayData?.hash,
      tran_id: prePayData?.paymentId,
      amount: prePayData?.amount,
      req_time: prePayData?.regTime,
      continue_success_url: configs.appUrl,
      payment_option: 'abapay_khqr_deeplink', // abapay_khqr_deeplink
      phone: prePayData.phone,
      email: prePayData.email,
    };
    console.log('onABAPay-data', data);
    const paywayData = await Ajax.post(configs.paywayPurchaseUrl, data);
    console.log('paywayData', paywayData);
    const payResult = paywayData.data;
    let canOpenURL = false;
    if (payResult?.abapay_deeplink) {
      try {
        callback?.();
        await Linking.openURL(payResult.abapay_deeplink);
        canOpenURL = true;
        NavigationService.replace(route, routeParam);
      } catch (e) {
        canOpenURL = false;
        callback?.();
        console.warn(`openURL error url: ${payResult.abapay_deeplink}`, e);
      }
    }
    if (!canOpenURL) {
      const storeUrl = IS_ANDROID ? payResult.play_store : payResult.app_store;
      if (storeUrl) {
        await Linking.openURL(storeUrl);
      } else {
        toast.show(I18n.t('msg_text_unsupport'));
      }
      callback?.();
      return;
    }
  };
}

export default new payManager();
