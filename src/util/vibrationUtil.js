import AppModule, { RingerMode } from '../modules/AppModule';
import { Vibration } from 'react-native';

const vibrationUtil = {};

vibrationUtil.vibrate = async function (pattern, repeat) {
  const ringerMode = await AppModule.getRingerMode();
  if (ringerMode === RingerMode.VIBRATE || ringerMode === RingerMode.NORMAL) {
    Vibration.vibrate(pattern, repeat);
  } else {
    console.debug(`vibrationUtil vibrate 非震动/响铃模式 ringerMode:${ringerMode}`);
  }
};

vibrationUtil.cancel = function () {
  Vibration.cancel();
};

/**
 * 聊天音视频震动
 */
vibrationUtil.chatVibrate = function () {
  vibrationUtil.vibrate([300, 500, 200, 500], true);
};

/**
 * 聊天新消息震动
 */
vibrationUtil.chatNewMsgVibrate = function () {
  vibrationUtil.vibrate([0, 200], false);
};

export default vibrationUtil;
