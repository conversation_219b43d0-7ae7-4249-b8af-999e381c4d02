/**
 * 任务工具
 * <AUTHOR>
 */
class TaskUtil {
  tasks = new Map();

  /**
   * 同一任务单个执行，等上一个完成后，如果有等待的，再执行一次
   * 注意：并非队列执行，如果有3个等待，只会执行最后那个
   * @param key
   * @param promiseFun
   * @return {Promise<void>}
   */
  singleExec = async (key, promiseFun) => {
    let task = this.tasks.get(key);
    console.log(`singleExec ${key} 开始`, task);
    if (!task) {
      task = { isExec: false };
      this.tasks.set(key, task);
    }
    if (task.isExec) {
      task.promiseFun = promiseFun;
      console.log(`singleExec ${key} 等待执行`);
      return;
    }
    task.isExec = true;
    task.promiseFun = null;
    try {
      await promiseFun();
    } catch (e) {
      console.warn(`singleExec ${key} error`, e);
    }
    if (task.promiseFun) {
      task.isExec = false;
      console.log(`singleExec ${key} 完成 执行下一个`);
      this.singleExec(key, task.promiseFun);
    } else {
      console.log(`singleExec ${key} 完成 delete`);
      this.tasks.delete(key);
    }
  };

  autoRetryTasks = {};

  /**
   * 自动重试，方法抛异常才会重试，重试延迟从2s开始每次增加2s，知道10s
   * isSingle：是否单个执行，如果当前任务正在执行，则新进来的忽略，但会修改delay，保证当前任务失败后能再次立即执行
   */
  autoRetry = async (key, promiseFun, isSingle = true) => {
    try {
      this.removeAutoRetry(key, false);
      const task = this.autoRetryTasks[key] || {};
      this.autoRetryTasks[key] = task;
      console.debug(`autoRetry ${key} 开始`, isSingle, task);
      if (isSingle && task.isExec) {
        console.warn(`autoRetry ${key} 正在执行`, task);
        task.delay = Number.MIN_SAFE_INTEGER;
        return;
      }
      task.isExec = true;
      await promiseFun();
      task.isExec = false;
      this.removeAutoRetry(key, true);
      console.debug(`autoRetry ${key} 完成`, this.autoRetryTasks);
    } catch (e) {
      const task = this.autoRetryTasks[key];
      console.warn(`autoRetry ${key} 错误`, task, e);
      // 任务失败之前外面调用了删除任务，所以不用继续继续执行了
      if (!task) {
        console.warn(`autoRetry ${key} 任务已移除`, this.autoRetryTasks, e);
        return;
      }
      this.removeAutoRetry(key, false);
      task.isExec = false;
      task.delay = Math.min(Math.max((task.delay || 0) + 2000, 0), 10000);
      task.timeoutTask = setTimeout(this.autoRetry, task.delay, key, promiseFun, isSingle);
      console.log(`autoRetry ${key} 定时重试...`, task);
    }
  };

  removeAutoRetry = (key, isRemove = true) => {
    const task = this.autoRetryTasks[key];
    console.debug(`autoRetry ${key} 移除任务`, isRemove, task);
    if (task) {
      if (task.timeoutTask) {
        clearTimeout(task.timeoutTask);
        task.timeoutTask = null;
      }
      if (isRemove) {
        delete this.autoRetryTasks[key];
      }
    }
  };
}

export default new TaskUtil();
