import moment from 'moment';
import _ from 'lodash';
import currencyFormatter from 'currency-formatter';
import I18n from '../i18n';
import BigDecimal from './BigDecimal';
import qs from 'qs';

const util = {};
let isCalled = false;
let timer = null;
/* eslint-disable */
util.encode = function (_map, _content) {
  _content = `${_content}`;
  if (!_map || !_content) {
    return _content || '';
  }
  return _content.replace(_map.r, ($1) => {
    const _result = _map[!_map.i ? $1.toLowerCase() : $1];
    return _result != null ? _result : $1;
  });
};

util.simpleClone = (obj) => {
  var cache = [];
  var strObj = JSON.stringify(obj, function (key, value) {
    if (typeof value === 'object' && value !== null) {
      if (cache.indexOf(value) !== -1) {
        // Circular reference found, discard key
        return;
      }
      // Store value in our collection
      cache.push(value);
    }
    return value;
  });
  return JSON.parse(strObj);
};

util.object2query = function (obj) {
  const keys = Object.keys(obj);
  const queryArray = keys.map((item) => `${item}=${encodeURIComponent(obj[item])}`);
  return queryArray.join('&');
};

// 消息类型列表
util.mapMsgType = function (msg) {
  const map = {
    text: I18n.t('util_index_msg_text'),
    image: I18n.t('util_index_msg_img'),
    file: '文件消息',
    audio: '语音消息',
    video: '视频消息',
    geo: '地理位置消息',
    tip: '提醒消息',
    custom: I18n.t('util_index_msg_custom'),
    notification: '系统通知',
    robot: '机器人消息',
  };
  const type = msg.type;
  return map[type] || I18n.t('util_index_msg_unknow');
};

util.stringifyDate = function (datetime, simple = false) {
  // let weekMap = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  const weekMap = [
    I18n.t('util_index_msg_sunday'),
    I18n.t('util_index_msg_monday'),
    I18n.t('util_index_msg_tuesday'),
    I18n.t('util_index_msg_wednesday'),
    I18n.t('util_index_msg_thursday'),
    I18n.t('util_index_msg_friday'),
    I18n.t('util_index_msg_saturday'),
  ];
  datetime = new Date(datetime);
  const year = datetime.getFullYear();
  const simpleYear = datetime.getYear() - 100;
  let month = datetime.getMonth() + 1;
  month = month > 9 ? month : `0${month}`;
  let day = datetime.getDate();
  day = day > 9 ? day : `0${day}`;
  let hour = datetime.getHours();
  hour = hour > 9 ? hour : `0${hour}`;
  let min = datetime.getMinutes();
  min = min > 9 ? min : `0${min}`;
  let week = datetime.getDay();
  week = weekMap[week];
  const thatDay = new Date(year, month - 1, day, 0, 0, 0).getTime();

  if (simple) {
    return {
      withYear: `${day}/${month}/${simpleYear}`,
      withMonth: `${month}-${day}`,
      withDay: `${week}`,
      withLastDay: I18n.t('util_index_msg_yesterday'),
      withHour: `${hour}:${min}`,
      thatDay,
    };
  }
  return {
    withYear: `${year}-${month}-${day} ${hour}:${min}`,
    withMonth: `${month}-${day} ${hour}:${min}`,
    withDay: `${week} ${hour}:${min}`,
    withLastDay: `${I18n.t('util_index_msg_yesterday')} ${hour}:${min}`,
    withHour: `${hour}:${min}`,
    thatDay,
  };
};

/* 格式化日期 */
util.formatDate = function (datetime, simple = false) {
  const tempDate = new Date().getTime();
  // 今天 00:00
  const todayDate = this.stringifyDate(tempDate, true).thatDay;
  const result = this.stringifyDate(datetime, simple);
  const thatDay = result.thatDay;
  const deltaTime = (datetime - todayDate) / 1000;

  if (deltaTime > 0) {
    return result.withHour;
  } else if (deltaTime > -3600 * 24) {
    return result.withLastDay;
  } else if (deltaTime > -3600 * 24 * 7) {
    return result.withDay;
  } else if (deltaTime > -3600 * 24 * 30) {
    return result.withMonth;
  }
  return result.withYear;
};

util.parseSession = function (sessionId) {
  if (/^p2p-/.test(sessionId)) {
    return {
      scene: 'p2p',
      to: sessionId.replace(/^p2p-/, ''),
    };
  } else if (/^team-/.test(sessionId)) {
    return {
      scene: 'team',
      to: sessionId.replace(/^team-/, ''),
    };
  }
};

util.parseCustomMsg = function (msg) {
  if (msg.type === 'custom') {
    try {
      const cnt = JSON.parse(msg.content);
      switch (cnt.type) {
        case 98:
          return `[${I18n.t('util_index_msg_job')}]`;
        case 99:
          return `[${I18n.t('util_index_msg_resume_send')}]`;
      }
    } catch (e) {}
    return `[${I18n.t('util_index_msg_custom')}]`;
  }
  return '';
};

/* 获得有效的备注名 */
util.getFriendAlias = function (userInfo) {
  const alias = userInfo.alias ? userInfo.alias.trim() : '';
  return alias || userInfo.nick || userInfo.account;
};

/**
 * 缩略图
 * genAvatar
 * @param {String} url
 * @returns {String}
 */
util.genAvatar = function genAvatar(url = '') {
  if (url.indexOf('nim.nosdn.127.net') !== -1) {
    return `${url}?imageView&thumbnail=80x80&quality=85`;
  }
  return url;
};

util.debounce = function (idle, action) {
  var last;
  return (function () {
    clearTimeout(last);
    last = setTimeout(function () {
      action();
    }, idle);
  })();
};

util.countWordLength = function (word) {
  let count = 0;
  for (let i = 0; i < word.length; i++) {
    if (word.charCodeAt(i) < 128) {
      count += 1;
    } else {
      count += 2;
    }
  }
  return count;
};

util.shortenWord = function (word, maxLen = 20) {
  let count = 0;
  for (let i = 0; i < word.length; i++) {
    if (word.charCodeAt(i) < 128) {
      count += 1;
    } else {
      count += 2;
    }
    if (count > maxLen) {
      return word.substr(0, i - 1) + '...';
    }
  }
  return word;
};

/**
 * 时间转时间戳
 * @param {*} date
 */
util.dateToTimestamp = function (date) {
  return moment(date).format('X');
};

/**
 * 时间戳转时间
 * @param {*} timestamp
 */
util.timestampToDate = function (timestamp) {
  return moment(timestamp * 1000);
};

/**
 * 时间转短时间格式
 * @param {*} timestamp
 */
util.dateToShortString = function (date) {
  return moment(date).format('YYYY-MM-DD');
};

/**
 * 时间戳转短时间格式
 * @param {*} timestamp
 */
util.timestampToShortString = function (timestamp) {
  return moment(timestamp * 1000).format('YYYY-MM-DD');
};

/**
 * 时间转长时间格式
 * @param {*} timestamp
 */
util.dateToLongString = function (date) {
  return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 时间年月格式
 * @param {*} timestamp
 */
util.dateToYMString = function (date) {
  if (date && date.indexOf(I18n.t('page_resume_work_exprience_up_to_now')) > -1) {
    return I18n.t('page_resume_work_exprience_up_to_now');
  }
  return moment(date).format('YYYY-MM');
};

/**
 * 时间年月格式2
 * @param {*} timestamp
 */
util.dateToYM1String2 = function (date) {
  if (!date || date.indexOf(I18n.t('page_resume_work_exprience_up_to_now')) > -1) {
    return I18n.t('page_resume_work_exprience_up_to_now');
  }
  return moment(date).format('YYYY-MM');
};

/**
 * 时间年月格式
 * @param {*} timestamp
 */
util.dateToYM1String = function (date) {
  if (!date) {
    return I18n.t('page_resume_work_exprience_up_to_now');
  }
  return moment(date).format('YYYY/MM');
};

/**
 * 时间戳转长时间格式
 * @param {*} timestamp
 */
util.timestampToLongString = function (timestamp) {
  return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 时间戳转长时间格式
 * @param {*} timestamp
 */
util.timestampToLongString1 = function (timestamp) {
  return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm');
};

/**
 * 时间转当地时间格式
 * @param {*} timestamp
 */
util.dateToLocalString = function (date) {
  return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 时间转当地时间格式
 * @param {*} timestamp
 */
util.dateToLocalString1 = function (date) {
  return moment(date).format('YYYY-MM-DD HH:mm');
};

/**
 * 时间戳转当地时间格式
 * @param {*} timestamp
 */
util.timestampToLocalString = function (timestamp) {
  return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 时间戳转当地时间格式  (只有小时和分钟)
 * @param {*} timestamp
 */
util.timestampToHM = function (timestamp) {
  return moment(timestamp * 1000).format('HH:mm');
};

util.getAge = (birthday) => {
  let pickDate = moment();
  let diffDay = pickDate.diff(moment(birthday), 'days');
  let year = 0;
  if (diffDay && diffDay > 0) {
    year = parseInt(diffDay / 365);
  } else {
    year = 0;
  }
  return year;
};

util.getYesrBetween = (time1, time2) => {
  let pickDate = moment(time2);
  let diffDay = pickDate.diff(moment(time1), 'days');
  let year = 0;
  if (diffDay && diffDay > 0) {
    year = Math.round(diffDay / 365);
  } else {
    year = 0;
  }
  return year;
};

util.getDiffBetween = (time) => {
  let text = moment(moment(parseFloat(time) * 1000).format('YYYY-MM-DD HH:mm:ss')).from(
    moment().format('YYYY-MM-DD HH:mm:ss')
  );
  // let text = moment(time, 'YYYY-MM-DD').from(moment(), 'YYYY-MM-DD');
  if (text.indexOf('second') > -1) {
    text = I18n.t('util_date_formattime_just_moment');
  } else if (text.indexOf('minute') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_minute_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_minute_ago')}`;
  } else if (text.indexOf('hour') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_hour_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_hour_ago')}`;
  } else if (text.indexOf('day') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_day_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_day_ago')}`;
  } else if (text.indexOf('year') > -1 || text.indexOf('month') > -1) {
    text = moment(parseFloat(time) * 1000).format('YYYY-MM-DD');
  }
  return text;
};

util.getDiffBetweenHan = (time) => {
  let text = moment(moment(parseFloat(time) * 1000).format('YYYY-MM-DD HH:mm:ss')).from(
    moment().format('YYYY-MM-DD HH:mm:ss')
  );
  // let text = moment(time, 'YYYY-MM-DD').from(moment(), 'YYYY-MM-DD');
  if (text.indexOf('second') > -1) {
    text = I18n.t('util_date_formattime_just_moment');
  } else if (text.indexOf('minute') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_minute_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_minute_ago')}`;
  } else if (text.indexOf('hour') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_hour_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_hour_ago')}`;
  } else if (text.indexOf('day') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_day_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_day_ago')}`;
  } else if (text.indexOf('year') > -1 || text.indexOf('month') > -1) {
    text = moment(parseFloat(time) * 1000).format('YYYY年M月D日 HH:mm');
  }
  return text;
};

util.getDiffBetweenHanSimple = (time) => {
  let text = moment(moment(parseFloat(time) * 1000).format('YYYY-MM-DD HH:mm:ss')).from(
    moment().format('YYYY-MM-DD HH:mm:ss')
  );
  // let text = moment(time, 'YYYY-MM-DD').from(moment(), 'YYYY-MM-DD');
  if (text.indexOf('second') > -1) {
    text = I18n.t('util_date_formattime_just_moment');
  } else if (text.indexOf('minute') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_minute_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_minute_ago')}`;
  } else if (text.indexOf('hour') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_hour_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_hour_ago')}`;
  } else if (text.indexOf('day') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_day_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_day_ago')}`;
  } else if (text.indexOf('year') > -1 || text.indexOf('month') > -1) {
    text = moment(parseFloat(time) * 1000).format('M月D日 HH:mm');
  }
  return text;
};

util.getDiffBetweenHM = (time) => {
  let text = moment(moment(parseFloat(time) * 1000).format('YYYY-MM-DD HH:mm:ss')).from(
    moment().format('YYYY-MM-DD HH:mm:ss')
  );
  // let text = moment(time, 'YYYY-MM-DD').from(moment(), 'YYYY-MM-DD');
  if (text.indexOf('second') > -1) {
    text = I18n.t('util_date_formattime_just_moment');
  } else if (text.indexOf('minute') > -1) {
    text = isNaN(parseInt(text))
      ? `1 ${I18n.t('util_date_formattime_minute_ago')}`
      : `${parseInt(text)} ${I18n.t('util_date_formattime_minute_ago')}`;
  } else if (text.indexOf('hour') > -1) {
    text = moment(parseFloat(time) * 1000).format('HH:mm');
  } else if (text.indexOf('day') > -1) {
    text = moment(parseFloat(time) * 1000).format('M月D日 HH:mm');
  } else if (text.indexOf('year') > -1 || text.indexOf('month') > -1) {
    text = moment(parseFloat(time) * 1000).format('M月D日 HH:mm');
  }
  return text;
};

util.tryJSONParse = function (jsonStr) {
  try {
    return JSON.parse(jsonStr);
  } catch (e) {
    return null;
  }
};

util.HandlerOnceTap = (functionTobeCalled, interval = 600) => {
  if (!isCalled) {
    isCalled = true;
    clearTimeout(timer);
    timer = setTimeout(() => {
      isCalled = false;
    }, interval);
    return functionTobeCalled();
  }
};

util.onCheckVersion = (onlineVersionArray, currentVersionArray) => {
  if (parseInt(onlineVersionArray[0], 10) < parseInt(currentVersionArray[0], 10)) {
    return false;
  } else if (parseInt(onlineVersionArray[0], 10) === parseInt(currentVersionArray[0], 10)) {
    if (parseInt(onlineVersionArray[1], 10) < parseInt(currentVersionArray[1], 10)) {
      return false;
    } else if (parseInt(onlineVersionArray[1], 10) === parseInt(currentVersionArray[1], 10)) {
      if (parseInt(onlineVersionArray[2], 10) < parseInt(currentVersionArray[2], 10)) {
        return false;
      } else if (parseInt(onlineVersionArray[2], 10) === parseInt(currentVersionArray[2], 10)) {
        return false;
      }
    }
  }
  return true;
};

util.calLength = (cityName) => {
  let len = 58;
  if (cityName.length <= 3) {
    len = 34;
  } else if (cityName.length <= 5 && cityName.length > 3) {
    len = 40;
  } else {
    len = 44;
  }
  if (I18n.locale.indexOf('zh') > -1) {
    if (cityName.length <= 2) {
      len = 36;
    } else if (cityName.length === 3) {
      len = 48;
    } else {
      len = 58;
    }
  }
  return len;
};

/**
 * 小数点取近似值
 * @param {*} number    传入的值 可以是 字符串类型
 * @param {*} precision 取几位小数
 */
util.approximateValue = (number, precision) => {
  return Math.round(+number + 'e' + precision) / Math.pow(10, precision);
};

/**
 * 根据语言取城市名称
 */
util.getCityNameWithCityData = (cityData) => {
  let languageText = 'nameEn';
  if (I18n.locale == 'zh') {
    languageText = 'nameZh';
  }
  if (I18n.locale == 'en') {
    languageText = 'nameEn';
  }
  if (I18n.locale == 'km') {
    languageText = 'nameKh';
  }
  return cityData[languageText];
};

/**
 *
 *计算获取年份 月份 和对应天数
 */
util.dates = () => {
  const date = new Date();
  const y = date.getFullYear();
  const data = [];
  let year = null;
  let month = null;
  const maxY = y + 20; // 最大年份
  const minY = y; // 最小年份
  for (let i = minY; i <= maxY; i += 1) {
    year = {};
    year[i] = [];
    for (let j = 1; j <= 12; j += 1) {
      month = {};
      month[(Array(2).join('0') + j).slice(-2)] = [];
      const monthDay = util.currentMonth(j, i);
      for (let k = 1; k <= monthDay; k += 1) {
        month[(Array(2).join('0') + j).slice(-2)].push((Array(2).join('0') + k).slice(-2));
      }
      year[i].push(month);
    }
    data.push(year);
  }
  console.log('data', data);
  return data;
};

// 计算当月天数
util.currentMonth = (m, y) => {
  let monthDay = 0;
  if (m === 1 || m === 3 || m === 5 || m === 7 || m === 8 || m === 10 || m === 12) {
    monthDay = 31;
  } else if (m === 4 || m === 6 || m === 9 || m === 11) {
    monthDay = 30;
  } else if (m === 2) {
    if ((y % 4 === 0 && y % 100 !== 0) || y % 400 === 0) {
      monthDay = 29;
    } else {
      monthDay = 28;
    }
  }
  return monthDay;
};

/**
 * 获得当前年
 */
util.getNowYear = () => {
  const now = new Date(); // 当前日期
  const nowYear = now.getFullYear(); // 当前年
  return nowYear;
};

/**
 * 获得当前月
 */
util.getNowMonth = () => {
  const now = new Date(); // 当前日期
  const nowMonth = now.getMonth() + 1; // 当前月
  return nowMonth;
};

/**
 * 获得当前日
 */
util.getNowDay = () => {
  const now = new Date(); // 当前日期
  const nowDay = now.getDate(); // 当前日
  return nowDay;
};

util.getNowHour = () => {
  const now = new Date(); // 当前日期
  const nowHour = now.getHours(); // 当前日
  return nowHour;
};

util.getNowMinute = () => {
  const now = new Date(); // 当前日期
  const nowMinute = now.getMinutes(); // 当前日
  return nowMinute;
};

/**
 * 获取国际化名称
 */
util.getI18nName = (nameData) => {
  let languageText = 'nameEn';
  if (I18n.locale == 'zh') {
    languageText = 'nameZh';
  }
  if (I18n.locale == 'en') {
    languageText = 'nameEn';
  }
  if (I18n.locale == 'km') {
    languageText = 'nameKh';
  }
  return nameData[languageText];
};

util.formatAmount = (amount, options = {}) => {
  if (!amount && amount !== 0) {
    return '';
  }
  if (_.isString(amount)) {
    amount = parseFloat(amount);
  }
  const floorAmount = new BigDecimal(amount).toFixed(2, BigDecimal.ROUND_DOWN);
  const value = currencyFormatter.format(floorAmount, {
    code: 'USD',
    symbol: '',
    thousand: ',',
    precision: floorAmount % 1 === 0 ? 0 : 2,
    ...options,
  });

  const arr = value.split('.');
  const int = arr[0];
  const decimalStr = arr[1];
  if (decimalStr === '00') {
    return int;
  }
  if (decimalStr && decimalStr[1] === '0') {
    return `${int}.${decimalStr[0]}`;
  }
  return value;
};

util.fullDates = () => {
  const date = new Date();
  const y = date.getFullYear();
  let years = [],
    months = [],
    days = [],
    hours = [],
    minutes = [];
  for (let i = 0; i < 21; i++) {
    years.push(i + y);
  }
  for (let i = 1; i < 13; i++) {
    months.push((Array(2).join('0') + i).slice(-2));
  }
  for (let i = 0; i < 24; i++) {
    if (i < 10) {
      hours.push((Array(2).join('0') + i).slice(-2));
    } else {
      hours.push(i);
    }
  }
  for (let i = 1; i < 32; i++) {
    if (i < 10) {
      days.push((Array(2).join('0') + i).slice(-2));
    } else {
      days.push(i);
    }
  }
  for (let i = 0; i < 60; i++) {
    if (i < 10) {
      minutes.push((Array(2).join('0') + i).slice(-2));
    } else {
      minutes.push(i);
    }
  }
  let pickerData = [years, months, days, hours, minutes];
  return pickerData;
};

util.pushData = (key, value, arr) => {
  const obj = {};
  const keyStr = key < 10 ? `0${key}` : key.toString();
  obj[keyStr] = value;
  arr.push(obj);
};

util.isLeapYear = (year) => {
  // 如果year年2月没有29则自动进一变为3月1日
  const date = new Date(year, 1, 29);
  return date.getDate() === 29;
};

util.datePickerData = (minDate = new Date()) => {
  const result = [];
  const minY = minDate.getFullYear();
  let isFirst = true;
  for (let y = minY; y < minY + 77; y++) {
    const months = [];
    for (let m = isFirst ? minDate.getMonth() + 1 : 1; m < 13; m++) {
      const dates = [];
      let maxDate = 30;
      if ([1, 3, 5, 7, 8, 10, 12].indexOf(m) > -1) {
        maxDate = 31;
      } else if (m === 2) {
        maxDate = util.isLeapYear(y) ? 29 : 28;
      }
      let minD = 1;
      if (isFirst) {
        isFirst = false;
        minD = minDate.getDate();
      }
      for (let d = minD; d <= maxDate; d++) {
        dates.push(d < 10 ? `0${d}` : d.toString());
      }
      util.pushData(m, dates, months);
    }
    util.pushData(y, months, result);
  }
  // console.log('datePickerData result', result.length, result);
  return result;
};

util.datePickerDataPast = () => {
  const result = [];
  const minY = 2000;
  for (let y = minY; y < minY + 100; y++) {
    const months = [];
    for (let m = 1; m < 13; m++) {
      const dates = [];
      let maxDate = 30;
      if ([1, 3, 5, 7, 8, 10, 12].indexOf(m) > -1) {
        maxDate = 31;
      } else if (m === 2) {
        maxDate = util.isLeapYear(y) ? 29 : 28;
      }
      let minD = 1;
      for (let d = minD; d <= maxDate; d++) {
        dates.push(d < 10 ? `0${d}` : d.toString());
      }
      util.pushData(m, dates, months);
    }
    util.pushData(y, months, result);
  }
  return result;
};

util.timePickerData = (minDate = new Date()) => {
  const result = [];
  let isFirst = true;
  for (let h = isFirst ? minDate.getHours() : 0; h < 24; h++) {
    const minutes = [];
    let minM = 0;
    if (isFirst) {
      isFirst = false;
      minM = minDate.getMinutes();
    }
    for (let i = minM; i < 60; i++) {
      minutes.push(i < 10 ? `0${i}` : i.toString());
    }
    util.pushData(h, minutes, result);
  }
  // console.log('timePickerData result', result.length, result);
  return result;
};

util.convertSelectedDate = (selectedValue, date = new Date()) => {
  if (selectedValue) {
    if (selectedValue.length >= 3) {
      date.setFullYear(+selectedValue[0]);
      date.setMonth(+selectedValue[1] - 1);
      date.setDate(+selectedValue[2]);
    }
    if (selectedValue.length >= 5) {
      date.setHours(+selectedValue[3]);
      date.setMinutes(+selectedValue[4]);
    }
  }
  return date;
};

util.getUrlParams = function (url) {
  const result = {};
  if (url) {
    const splits = decodeURI(url).split('?');
    if (splits.length > 1 && splits[1]) {
      return qs.parse(splits[1]);
      // const params = splits[1].split('&');
      // params.forEach(item => {
      //   const arr = (item && item.split('=')) || [];
      //   if (arr.length === 2 && arr[0]) {
      //     result[arr[0]] = arr[1];
      //   }
      // });
    }
  }
  return result;
};

util.getCodePhone = function (codePhone) {
  if (!codePhone) {
    return { code: '+855', phone: '' };
  }
  if (codePhone.indexOf('-') === -1) {
    return { code: '+855', phone: codePhone };
  }
  const [code, phone] = codePhone.split('-');
  return { code, phone };
};

/**
 * 时间转当地时间格式
 * @param {number} timestamp 毫秒
 */
util.dateToLocalString6 = function (timestamp) {
  const now = new Date();
  if (moment(timestamp).isSame(now, 'day')) {
    return moment(timestamp).format('HH:mm');
  }
  if (moment(timestamp).isSame(now, 'year')) {
    return moment(timestamp).format(I18n.t('dateformat_month'));
  }
  return moment(timestamp).format(I18n.t('dateformat_year'));
};

/**
 * 获取用户显示的名字，拼装firstName和lastName
 */
util.getUserDisplayName = function (user) {
  if (!user || !(user.lastName || user.firstName)) return I18n.t('page_mine_name_text_null');
  const lastName = (user.lastName || '').trim();
  const firstName = (user.firstName || '').trim();
  if (firstName) {
    if (lastName) {
      return I18n.locale === 'en' ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
    }
    return firstName;
  }
  if (lastName) return lastName;
  return I18n.t('page_mine_name_text_null');
};

/**
 * 处理显示的名字
 */
util.handleDisplayName = function (name) {
  return (name || '').trim() || I18n.t('page_mine_name_text_null');
};

util.formatChatTime = (msgTime) => {
  const current = moment();
  const target = moment(msgTime);
  if (current.isSame(target, 'day')) {
    return target.format('HH:mm');
  }
  if (current.isSame(target, 'year')) {
    return target.format(I18n.t('page_trade_list_month_day'));
  }
  return target.format(I18n.t('page_trade_list_year_month_day'));
};

/**
 * 获取时间，多久以前
 */
util.getAgoTime = function (time) {
  time = (Date.now() - time) / 1000;
  // 小于一分钟
  if (time < 60) {
    return I18n.t('time_just');
  }
  // 小于一小时
  if (time < 3600) {
    time = Math.floor(time / 60);
    return I18n.t('time_ago_minutes', { num: time });
  }
  // 小于一天
  if (time < 86400) {
    time = Math.floor(time / 3600);
    return I18n.t('time_ago_hours', { num: time });
  }
  // 小于一周
  if (time < 604800) {
    time = Math.floor(time / 86400);
    return I18n.t('time_ago_days', { num: time });
  }
  // 小于一个月
  if (time < 2592000) {
    time = Math.floor(time / 604800);
    return I18n.t('time_ago_weeks', { num: time });
  }
  // 小于一年
  if (time < 31536000) {
    time = Math.floor(time / 2592000);
    return I18n.t('time_ago_months', { num: time });
  }
  time = Math.floor(time / 31536000);
  return I18n.t('time_ago_months', { num: time });
};

/**
 * 是否包含url
 */
util.includeUrl = function (text) {
  return typeof text === 'string' && /[\s\S]*http(s?):\/\/([\w-]+\.)+([\w-]+)[\s\S]*/i.test(text);
};

util.formatOnlyPhone = function (codePhone) {
  if (!codePhone) {
    return '';
  }
  if (codePhone.indexOf('-') === -1) {
    return codePhone;
  }
  const [code, phone] = codePhone.split('-');
  return `${phone}`;
};

export default util;
