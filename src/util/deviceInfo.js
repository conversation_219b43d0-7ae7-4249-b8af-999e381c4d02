import DeviceInfo from 'react-native-device-info';
import * as RNLocalize from 'react-native-localize';

/**
 * 设备信息
 * <AUTHOR>
 */
export default {
  getUniqueID() {
    // 4f7d90c99709510d
    return DeviceInfo.getUniqueIdSync();
  },
  getVersion() {
    // 1.48.2
    return DeviceInfo.getVersion();
  },
  getSystemVersion() {
    return DeviceInfo.getSystemVersion();
  },
  /**
   * 获取系统api级别，如Android 13的是33
   */
  async getApiLevel() {
    return DeviceInfo.getApiLevel();
  },
  getApplicationName() {
    return DeviceInfo.getApplicationName();
  },
  getModel() {
    return DeviceInfo.getModel();
  },
  async getDeviceName() {
    const deviceName = await DeviceInfo.getDeviceName();
    if (deviceName && deviceName !== 'unknown') return deviceName;
    return DeviceInfo.getModel();
  },
  getBrand() {
    return DeviceInfo.getBrand();
  },
  async getMacAddress() {
    // Android高版本，拿不到Mac地址或返回一样的
    if (IS_ANDROID) return DeviceInfo.getUniqueId();
    const macAddress = await DeviceInfo.getMacAddress();
    return macAddress || DeviceInfo.getUniqueId();
  },
  getTimezone() {
    // Asia/Shanghai
    return RNLocalize.getTimeZone();
  },
  getDeviceLocale() {
    // zh-CN
    return RNLocalize.getLocales()[0].languageTag;
  },
};
