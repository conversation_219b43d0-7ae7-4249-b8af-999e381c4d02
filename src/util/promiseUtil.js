/**
 * 执行一批异步任务，不管单个任务成功或失败，直到所有任务返回
 * @param tasks {Promise[]}
 */
function allSettled(tasks) {
  let count = 0;
  const arr = new Array(tasks.length);
  const fulfillFun = function(value, index, status, resolveFun) {
    arr[index] = {
      status,
    };
    if (status === 'fulfilled') {
      arr[index].value = value;
    } else {
      arr[index].reason = value;
    }
    count++;
    if (count >= tasks.length) {
      resolveFun(arr);
    }
  };
  return new Promise((resolve, reject) => {
    tasks.forEach((item, index) => {
      Promise.resolve(item).then(
        value => fulfillFun(value, index, 'fulfilled', resolve, reject),
        e => fulfillFun(e, index, 'rejected', resolve, reject)
      );
    });
  });
}

/**
 * 下拉刷新最小时间
 */
function refreshMin(minTime = 300) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true);
    }, minTime);
  });
}

/**
 * 下拉刷新请求
 */
function refresh(tasks) {
  tasks.push(refreshMin());
  return raceByAllSettledWithTimeout(tasks, 3000, false);
}

function timeout(time = 0, isReject = true) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (isReject) {
        reject(`timeout ${time}`);
      } else {
        resolve(`timeout ${time}`);
      }
    }, time);
  });
}

function sleep(time = 0) {
  return timeout(time, false);
}

/**
 * 请求单个接口，不管成功还是失败，请求时间最少300毫秒，防止iOS的state失效
 * @param task
 * @param minTime
 * @return {Promise<*>}
 */
async function requestMin(task, minTime) {
  const [res] = await allSettled([task, refreshMin(minTime)]);
  if (res.status === 'fulfilled') {
    return res.value;
  } else {
    return Promise.reject(res.reason);
  }
}

/**
 * 请求多个接口，不管成功还是失败，请求时间最少300毫秒，防止iOS的state失效
 * @param tasks {Promise[]}
 * @param minTime
 * @return {Promise<*>}
 */
async function batchRequestMin(tasks, minTime) {
  tasks.push(refreshMin(minTime));
  const batchRes = await allSettled(tasks);
  batchRes.splice(tasks.length - 1, 1);
  const result = [];
  for (let res of batchRes) {
    if (res.status === 'rejected') {
      return Promise.reject(res.reason);
    }
    result.push(res.value);
  }
  return result;
}

/**
 * 执行一批异步任务，不管单个任务成功或失败，直到所有任务返回或者超时
 * @param tasks Promise任务数组
 * @param timeoutTime 超时时间
 * @param isReject 超时时是否抛错误
 */
function raceByAllSettledWithTimeout(tasks, timeoutTime, isReject = false) {
  if (timeoutTime > 0) {
    return Promise.race([allSettled(tasks), timeout(timeoutTime, isReject)]);
  }
  return allSettled(tasks);
}

/**
 * 执行一批异步任务，只要其中一个失败或成功或超时，马上返回结果
 * @param tasks Promise任务数组
 * @param timeoutTime 超时时间
 * @param isReject 超时时是否抛错误
 */
function raceTimeout(tasks, timeoutTime, isReject = true) {
  if (timeoutTime > 0) {
    tasks.push(timeout(timeoutTime, isReject));
  }
  return Promise.race(tasks);
}

/**
 * ES2020开始支持Promise.allSettled
 */
function init() {
  global.Promise.allSettled = global.Promise.allSettled || allSettled;
}

export default {
  init,
  allSettled,
  raceByAllSettledWithTimeout,
  raceTimeout,
  timeout,
  refresh,
  sleep,
  requestMin,
  batchRequestMin,
};
