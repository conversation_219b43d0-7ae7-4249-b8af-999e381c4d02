import JPush from 'jpush-react-native';

/**
 * 推送工具类
 * <AUTHOR>
 */
class PushUtil {
  /**
   * 获取推送ID，卸载重装后会发生变化
   */
  getRegistrationID = async () => {
    let id = await global.storage.load({ key: 'pushRegistrationID' }).catch(() => null);
    console.debug('PushUtil getRegistrationID', id);
    if (id) {
      this.getRegistrationIDByJPush();
    } else {
      id = await this.getRegistrationIDByJPush();
    }
    return id;
  };

  /**
   * 获取推送ID，卸载重装后会发生变化
   */
  getRegistrationIDByJPush = async () => {
    return new Promise((resolve) => {
      try {
        JPush.getRegistrationID(async ({ registerID }) => {
          try {
            console.log('PushUtil getRegistrationIDByJPush', registerID);
            if (registerID) {
              resolve(registerID);
              global.storage.save({ key: 'pushRegistrationID', data: registerID });
            } else {
              resolve('');
            }
          } catch (e) {
            resolve('');
            console.warn('PushUtil getRegistrationIDByJPush', e);
          }
        });
      } catch (e) {
        resolve('');
        console.warn('PushUtil getRegistrationIDByJPush 2', e);
      }
    });
  };
}

export default new PushUtil();
