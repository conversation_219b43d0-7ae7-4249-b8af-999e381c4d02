import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import DocumentPicker from 'react-native-document-picker';
import BigDecimal from './BigDecimal';
import resIcon from '../res';
import PermissionUtil from './permissionUtil';
import I18n from '../i18n';
import { createThumbnail } from 'react-native-create-thumbnail';
import ImageSize from 'react-native-image-size';
import { getVideoDuration as getRNVideoDuration } from '@qeepsake/react-native-file-utils';
import deviceInfo from './deviceInfo';
// import Session from '../api/session';

async function getImageFilePath(suffix = '.jpg') {
  const dirs = await getDir('/image');
  return `${dirs}/${getRandomFileName(suffix)}`;
}

async function getDir(path, basePath) {
  if (!basePath) {
    basePath = IS_ANDROID ? RNFS.ExternalCachesDirectoryPath : RNFS.LibraryDirectoryPath;
  }
  const dirs = `${basePath}${path || ''}`;
  const exist = await RNFS.exists(dirs);
  if (!exist) {
    await RNFS.mkdir(dirs);
  }
  return dirs;
}

function getFileName(filePath, suffix, defaultFileName) {
  if (typeof filePath === 'string') {
    const index = filePath.lastIndexOf('/');
    if (index > -1) {
      return filePath.substring(index + 1);
    }
  }
  return defaultFileName === undefined ? getRandomFileName(suffix) : defaultFileName;
}

function getRandomFileName(suffix) {
  const timestamp = global.now();
  const random = Math.round(Math.random() * 1000000);
  return `${timestamp}_${random}${suffix || ''}`;
}

function getSuffix(name, defaultValue) {
  if (typeof name === 'string') {
    const arr = name.match(/^.*(\.\w+)(\?.*)?$/);
    if (arr) {
      return arr[1];
    }
  }
  return defaultValue;
}

function checkEncodeURI(uri, isCheck = IS_ANDROID) {
  // Android如果是绝对路径，进行URL编码，修复文件名含特殊字符
  if (isCheck && /^((file:\/\/\/)|\/).+/.test(uri)) {
    uri = uri
      .split('/')
      .map((it, index) => (index && it ? encodeURIComponent(it) : it))
      .join('/');
  }
  return uri;
}

function isURLEncoded(url) {
  try {
    // 解码 URL，并比较解码前后的结果
    const decodedUrl = decodeURIComponent(url);
    // 如果解码后的 URL 与原始 URL 不同，说明 URL 中包含编码字符
    return decodedUrl !== url && decodedUrl;
  } catch (e) {
    // 解码过程中出现错误，可能是因为 URL 中有无效的编码字符
    return false;
  }
}

async function copyFile(filePath, destPath) {
  console.log('copyFile 原始值', filePath, destPath, isURLEncoded(filePath));
  if (IS_IOS) {
    filePath = isURLEncoded(filePath) || filePath;
  } else {
    filePath = checkEncodeURI(filePath);
  }
  const dest = checkEncodeURI(destPath);
  console.log('copyFile 处理后', filePath, dest);
  await RNFS.copyFile(filePath, dest);
  const exists = await RNFS.exists(destPath);
  console.log('copyFile 结果', exists);
  if (!exists) {
    return Promise.reject({ message: 'copy file fail' });
  }
  return destPath;
}

function cameraRollSave(path, optioins) {
  return CameraRoll.saveToCameraRoll(checkEncodeURI(path));
}

/**
 * 保存图片到相册
 */
async function saveImage(data) {
  const path = await getImageFilePath();
  console.log('saveImage', path, data);
  data = data.replace(/data:image\/([a-z]+);base64,/, '');
  await RNFS.writeFile(path, data, 'base64');
  return path;
}

/**
 * 保存图片到相册
 */
async function saveToPhoto(data) {
  const path = await saveImage(data);
  return cameraRollSave(path, { type: 'photo' });
}

/**
 * 保存图片到相册，支持本地图片和网络图片
 */
async function saveToPhotoByUrl(url, callback = (m) => toast.show(m)) {
  try {
    if (!url?.trim?.()) {
      callback(I18n.t('msg_save_fail'));
      return;
    }
    await PermissionUtil.requestExternalStoragePermission();
    if (IS_ANDROID && url.startsWith('http')) {
      const path = await getImageFilePath();
      const options = {
        fromUrl: url,
        toFile: path,
        // background: true,
      };
      await downloadFile(options).promise;
      url = path;
    }
    const uri = await cameraRollSave(url, { type: 'photo' });
    callback(I18n.t('msg_save_success'), uri);
    return uri;
  } catch (e) {
    callback(I18n.t('msg_save_fail'));
  }
}

/**
 * 保存视频到相册
 */
async function saveVideoToPhotoByUrl(url, callback = (m) => toast.show(m)) {
  try {
    if (!url?.trim?.()) {
      callback(I18n.t('msg_save_fail'));
      return;
    }
    await PermissionUtil.requestExternalStoragePermission();
    if (IS_ANDROID && url.startsWith('http')) {
      const dirs = await getDir('/video');
      const path = `${dirs}/${getRandomFileName('.mp4')}`;
      const options = {
        fromUrl: url,
        toFile: path,
        // background: true,
      };
      await downloadFile(options).promise;
      url = path;
    }
    await cameraRollSave(url, { type: 'video' });
    callback(I18n.t('msg_save_success'));
  } catch (e) {
    console.warn('saveVideoToPhotoByUrl', e);
    callback(I18n.t('msg_save_fail'));
  }
}

/**
 * 保存图片到相册
 */
async function saveToPhotoByPath(filePath) {
  // const destPath = await getImageFilePath(getSuffix(filePath));
  // await copyFile(filePath, destPath);
  return cameraRollSave(filePath, { type: 'photo' });
}

/**
 * 保存文件到指定文件路径
 */
async function saveToFileByUrl(url, suffix) {
  const path = await getImageFilePath(suffix);
  const options = {
    fromUrl: url,
    toFile: path,
    // background: true,
  };
  await downloadFile(options).promise;
  return path;
}

/**
 * 下载文件，默认附带im token
 */
function downloadFile(options) {
  // Session.appendImTokenToHeader(options);
  return RNFS.downloadFile(options);
}

/**
 * 获取文档路径
 */
async function getDocumentPath(filePath, suffix) {
  const dir = await getDir(
    '/document',
    IS_IOS ? RNFS.DocumentDirectoryPath : RNFS.ExternalCachesDirectoryPath
  );
  return `${dir}/${getFileName(filePath, suffix)}`;
}

async function checkAndMkdir(dirs) {
  const exist = await RNFS.exists(dirs);
  if (!exist) {
    try {
      await RNFS.mkdir(dirs);
    } catch (e) {
      return Promise.reject({ message: e?.message, status: 'mkdir' });
    }
  }
}

function randomFileName(suffix = '') {
  const timestamp = global.now();
  const random = Math.round(Math.random() * 1000000);
  return `${timestamp}_${random}${suffix}`;
}

function getMsgFileDir({ ownerId, sessionId, type }) {
  type = type ? `/${type}` : '';
  return `${
    IS_ANDROID ? RNFS.ExternalDirectoryPath : RNFS.LibraryDirectoryPath
  }/chat${type}/${ownerId}_${sessionId}`;
}

async function getMsgFilePath(data, options) {
  let dirs = data.dir;
  // 优先下载到外部下载目录，如果报错则回退到App目录
  const isDownload = !dirs && IS_ANDROID && options?.isDownload;
  if (isDownload) {
    await PermissionUtil.requestExternalStoragePermission();
    dirs = RNFS.DownloadDirectoryPath + '/' + deviceInfo.getApplicationName();
  }
  if (!dirs) {
    dirs = getMsgFileDir(data);
  }
  try {
    await checkAndMkdir(dirs);
  } catch (e) {
    if (isDownload) {
      return getMsgFilePath(data, { ...options, isDownload: false });
    }
    return Promise.reject(e);
  }
  let fileName;
  try {
    fileName =
      data.fileName || (data.type === 'file' && data.extra && JSON.parse(data.extra)?.fileName);
  } catch (e) {
    console.warn('getMsgFilePath', e, data.extra);
  }
  if (!fileName) {
    const suffix = data.suffix || getSuffix(data.url || data.content, '');
    fileName = randomFileName(suffix);
  }
  fileName = sanitizeFilename(fileName);
  return checkAndGetFilePath(`${dirs}/${fileName}`);
}

/**
 * 替换文件名称包含/\:*?<>| 的特殊字符为空格
 */
function sanitizeFilename(filename) {
  if (!filename) return '';
  return filename.replace(/[/\\:*?"<>|]/g, '');
}

async function checkAndGetFilePath(filePath) {
  const exist = await RNFS.exists(filePath);
  console.log('checkAndGetFilePath exist', exist, filePath);
  if (!exist) return filePath;
  const match = filePath.match(/^(.+\/[^/]+?)(\((\d+)\))?(\.\w+)?$/);
  console.log('checkAndGetFilePath match', match?.length);
  if (match?.length === 5) {
    filePath = match[1] + `(${Number(match[3] || '0') + 1})` + (match[4] || '');
  } else {
    filePath += '(1)';
  }
  return checkAndGetFilePath(filePath);
}

function isImage(type) {
  return typeof type === 'string' && (type.startsWith('image') || type === 'public.image');
}

function isVideo(type) {
  return typeof type === 'string' && (type.startsWith('video') || type === 'public.movie');
}

/**
 * 创建视频封面
 */
async function createVideoThumbnail(uri) {
  const url = IS_IOS ? decodeURI(uri.replace('file://', '')) : checkEncodeURI(uri);
  console.log('createVideoThumbnail', url, uri);
  const timestamp = global.now();
  const random = Math.round(Math.random() * 1000000);
  const fileName = `M_${timestamp}_${random}`;
  const thumbnail = await createThumbnail({
    url,
    timeStamp: 0,
    format: 'png', //jpeg、png
    cacheName: fileName,
  });
  console.debug('thumbnail', thumbnail);
  const imageInfo = await getImageInfo(thumbnail.path);
  console.debug('imageInfo', imageInfo);
  thumbnail.width = imageInfo.width;
  thumbnail.height = imageInfo.height;
  thumbnail.size = imageInfo.size;
  return thumbnail;
}

/**
 * 获取图片宽高和大小等信息
 */
async function getImageInfo(uri) {
  const res = await Promise.all([getImageSize(uri), RNFS.stat(uri)]);
  return Object.assign(res[0], res[1]);
}

/**
 * 获取图片宽高
 */
async function getImageSize(uri, notEncode) {
  return ImageSize.getSize(notEncode ? uri : checkEncodeURI(uri));
}

/**
 * 获取文件大小
 */
async function getFileInfo(uri, options) {
  const res = await RNFS.stat(uri);
  if (options?.getVideoDuration) {
    res.duration = await getVideoDuration(uri);
  }
  if (!res.name) {
    res.name = getFileName(uri);
  }
  return res;
}

async function getVideoDuration(uri) {
  const size = await getRNVideoDuration(checkEncodeURI(uri));
  return Number(size) * 1000;
}

/**
 * 获取显示的文件大小
 */
function getDisplayFileSize(fileSize) {
  if (!fileSize) return '0B';
  const bigDecimal = new BigDecimal(fileSize.toString());
  if (bigDecimal.lt(1024)) {
    return `${fileSize}B`;
  }
  if (bigDecimal.divide(1024).lt(1024)) {
    return `${bigDecimal.toFixed(1).replace('.0', '')}KB`;
  }
  if (bigDecimal.divide(1024).lt(1024)) {
    return `${bigDecimal.toFixed(1).replace('.0', '')}M`;
  }
  return `${bigDecimal
    .divide(1024)
    .toFixed(2)
    .replace(/(\.0)?0$/, '')}G`;
}

/**
 * 获取文件类型
 */
function getFileType(type, isAndroid = IS_ANDROID, isStop) {
  if (type) {
    const types = isAndroid
      ? DocumentPicker.perPlatformTypes.android
      : DocumentPicker.perPlatformTypes.ios;
    for (let key of Object.keys(types)) {
      if (
        type === types[key] ||
        (types[key].endsWith('/*') &&
          type.startsWith(types[key].substring(0, types[key].length - 1)))
      ) {
        return key;
      }
    }
    if (!isStop) {
      return getFileType(type, !isAndroid, true);
    }
  }
  return null;
}

const fileTypeIcons = {
  doc: resIcon.fileTypeWord,
  docx: resIcon.fileTypeWord,
  xls: resIcon.fileTypeExcel,
  xlsx: resIcon.fileTypeExcel,
  ppt: resIcon.fileTypePPT,
  pptx: resIcon.fileTypePPT,
  zip: resIcon.fileTypeZip,
  txt: resIcon.fileTypeTxt,
  plainText: resIcon.fileTypeTxt,
  text: resIcon.fileTypeTxt,
  pdf: resIcon.fileTypePDF,
  // mov: resIcon.fileTypeVideo,
  // mp4: resIcon.fileTypeVideo,
  // avi: resIcon.fileTypeVideo,
  // png: resIcon.fileTypeImage,
  // jpg: resIcon.fileTypeImage,
};

/**
 * 获取文件类型图片
 */
function getFileTypeImage(fileType) {
  if (!fileType) {
    return resIcon.fileTypeUnknown;
  }
  return fileTypeIcons[fileType?.toLowerCase()] || resIcon.fileTypeUnknown;
}

async function moveFile(filePath, destPath) {
  await copyFile(filePath, destPath);
  return destPath;
}

/**
 * 复制文件到缓存目录下
 */
async function copyToCache(filePath, destPath, fileName) {
  if (!destPath) {
    const dir = await getDir('/file');
    destPath = dir + '/' + getRandomFileName(getSuffix(fileName));
  }
  await copyFile(filePath, destPath);
  return destPath;
}

/**
 * 删除文件/文件夹
 */
async function deleteFile(filePath) {
  const exist = await RNFS.exists(filePath);
  if (exist) {
    console.debug('删除文件/文件夹', filePath);
    await RNFS.unlink(filePath);
  }
}

/**
 * 清理缓存
 */
async function clearCache() {
  const paths = [
    RNFS.CachesDirectoryPath,
    IS_ANDROID && RNFS.ExternalCachesDirectoryPath,
    IS_IOS && RNFS.TemporaryDirectoryPath,
  ];
  console.log('clearCache', paths);
  for (let path of paths) {
    if (path) {
      try {
        await deleteFile(path);
      } catch (e) {
        logger.warn('clearCache', e, path);
      }
    }
  }
}

function getFileIconByExtension(fileUrl) {
  if (fileUrl) {
    const parts = fileUrl.split('/');
    const fileName = parts.pop();
    const extension = fileName.split('.').pop().toLowerCase();
    return {
      filePathName: fileName,
      typeIcon: fileTypeIcons[extension] || resIcon.fileTypeUnknown,
    };
  }
  return {
    filePathName: '',
    typeIcon: resIcon.fileTypeUnknown,
  };
}

/**
 * agora log路径
 */
async function getAgoraLogFilePath() {
  // const loginInfo = await Session.getLoginInfo();
  // const filePath = `${
  //   IS_ANDROID ? RNFS.ExternalDirectoryPath : RNFS.LibraryDirectoryPath
  // }/agoraLog/${loginInfo?.userId}/agorasdk.log`;
  return `${
    IS_ANDROID ? RNFS.ExternalDirectoryPath : RNFS.LibraryDirectoryPath
  }/agoraLog/0/agorasdk.log`;
}

/**
 * 文件工具
 * <AUTHOR>
 */
export default {
  saveImage,
  saveToPhoto,
  saveToPhotoByUrl,
  saveVideoToPhotoByUrl,
  saveToPhotoByPath,
  saveToFileByUrl,
  downloadFile,
  getDocumentPath,
  getFileName,
  isImage,
  isVideo,
  createVideoThumbnail,
  getImageInfo,
  getImageSize,
  getFileInfo,
  getVideoDuration,
  getDisplayFileSize,
  moveFile,
  copyToCache,
  deleteFile,
  getFileType,
  getFileTypeImage,
  getMsgFileDir,
  getMsgFilePath,
  randomFileName,
  clearCache,
  getSuffix,
  getFileIconByExtension,
  checkEncodeURI,
  getAgoraLogFilePath,
};
