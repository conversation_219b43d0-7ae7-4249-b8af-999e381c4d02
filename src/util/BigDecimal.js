import Big from 'big.js';

Big.NE = -20;

/**
 * 高精度计算
 * <AUTHOR>
 */
export default class BigDecimal {
  static ROUND_DOWN = 0; // 舍入模式 - 舍去法

  static ROUND_HALF_UP = 1; // 舍入模式 - 四舍五入法

  static ROUND_HALF_EVEN = 2;

  static ROUND_UP = 3; // 舍入模式 - 进一法

  constructor(n) {
    try {
      this.num = this._BigDecimal(n);
      this.isNum = true;
    } catch (e) {
      console.log(`${n} is not number`);
      this.isNum = false;
    }
  }

  _BigDecimal(n) {
    return new Big(n);
  }

  /**
   * 加法
   * @return {BigDecimal}
   */
  add(b) {
    this.num = this.num.plus(b);
    return this;
  }

  /**
   * 减法
   * @return {BigDecimal}
   */
  subtract(b) {
    this.num = this.num.sub(b);
    return this;
  }

  /**
   * 乘法
   * @return {BigDecimal}
   */
  multiply(b) {
    this.num = this.num.mul(b);
    return this;
  }

  /**
   * 除法
   * @return {BigDecimal}
   */
  divide(b) {
    this.num = this.num.div(b);
    return this;
  }

  /**
   * 比较函数，可用于排序，a大于b时返回1，a小于b时返回-1，a等于b时返回0
   * @return {number} -1 | 0 | 1
   */
  compare(b) {
    return this.num.cmp(b);
  }

  /**
   * 是否等于
   * @return {boolean}
   */
  eq(b) {
    if (!this.isNumber()) {
      return false;
    }
    return this.num.eq(b);
  }

  /**
   * 是否大于
   * @return {boolean}
   */
  gt(b) {
    return this.num.gt(b);
  }

  /**
   * 是否大于等于
   * @return {boolean}
   */
  gte(b) {
    return this.num.gte(b);
  }

  /**
   * 是否小于
   * @return {boolean}
   */
  lt(b) {
    return this.num.lt(b);
  }

  /**
   * 是否小于等于
   * @return {boolean}
   */
  lte(b) {
    return this.num.lte(b);
  }

  /**
   * 取大数字
   * @return {BigDecimal}
   */
  max(b) {
    if (this.lt(b)) {
      this.num = this._BigDecimal(b);
    }
    return this;
  }

  /**
   * 取小数字
   * @return {BigDecimal}
   */
  min(b) {
    if (this.gt(b)) {
      this.num = this._BigDecimal(b);
    }
    return this;
  }

  /**
   * 设置小数位数，默认保留两位小数，使用四舍五入
   * @return {BigDecimal}
   */
  setPrecision(precision = 2, roundingMode = BigDecimal.ROUND_HALF_UP) {
    this.num = this.num.round(precision, roundingMode);
    return this;
  }

  /**
   * 固定小数位数，不足时补0，多余时采用舍入模式处理
   * @return {string}
   */
  toFixed(precision = 2, roundingMode = BigDecimal.ROUND_HALF_UP) {
    this.setPrecision(precision, roundingMode);
    return this.num.toFixed(precision);
  }

  toString() {
    return this.num.toString();
  }

  /**
   * 是否为数字
   * @return {boolean}
   */
  isNumber() {
    return this.isNum;
  }

  /**
   * 是否不是数字
   * @return {boolean}
   */
  isNotNumber() {
    return !this.isNum;
  }

  /**
   * 绝对值
   * @return {BigDecimal}
   */
  abs() {
    this.num = this.num.abs();
    return this;
  }
}
