// import * as Sentry from '@sentry/react-native';
import configs from '../configs';
import _ from 'lodash';

export const SentrySeverity = {
  Fatal: 'fatal',
  Error: 'error',
  Warning: 'warning',
  Log: 'log',
  Info: 'info',
  Debug: 'debug',
};

const ignoreSentry = ['Request failed with status code', 'timeout of', 'Network Error'];

function isIgnore(arr, message) {
  return (
    typeof message === 'string' &&
    arr.find((item) => typeof item === 'string' && message.startsWith(item))
  );
}

/**
 * Sentry工具类
 * <AUTHOR>
 */
class SentryUtil {
  init = () => {
    // 正式环境才需要启用 sentry 错误跟踪
    /*if (!configs.enableSentry) return;
    this.isInit = true;
    Sentry.init({
      dsn: '',
      environment: configs.env,
      debug: __DEV__,
    });*/
  };

  setUser = (user) => {
    /*if (!this.isInit || !user) return;
    console.log('SentryUtil setUser', user);
    Sentry.setUser({ id: user.userId, username: user.name });
    this.setMobileTag(user.mobile);*/
  };

  setMobileTag = (mobile) => {
    this.setTag('mobile', mobile);
  };

  setTag = (key, value) => {
    // if (!this.isInit) return;
    // Sentry.setTag(key, value);
  };

  setTags = (obj) => {
    if (!this.isInit) return;
    Object.keys(obj).forEach((key, index) => this.setTag(key, obj[key]));
  };

  parseSafe = (json, errTag, isThrow) => json && this.parse(json, errTag, isThrow);

  parse = (json, errTag, isThrow) => {
    try {
      return JSON.parse(json);
    } catch (e) {
      this.captureMessage(`${errTag || ''} d: ${json} e: ${e?.message}`);
      if (isThrow) {
        throw e;
      }
      return undefined;
    }
  };

  captureMessage = (message, severity = SentrySeverity.Warning) => {
    /*if (!this.isInit) return;
    if (typeof message !== 'string') {
      try {
        if (_.isArray(message) && isIgnore(ignoreSentry, message[0]?.message)) {
          return;
        }
        message = JSON.stringify(message);
      } catch (e) {
        return;
      }
    }
    Sentry.captureMessage(message, severity);*/
  };

  captureException = (exception) => {
    // if (!this.isInit) return;
    // Sentry.captureException(exception);
  };
}

export default new SentryUtil();
