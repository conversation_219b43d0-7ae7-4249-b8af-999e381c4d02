import { Keyboard } from 'react-native';
import AppModule from '../modules/AppModule';

/**
 * Keyboard工具
 * <AUTHOR>
 */
class KeyboardUtil {
  keyBoardIsShow = false;

  init() {
    this.stop();
    this.keyboardDidShowListener = this.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = this.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  stop() {
    this.keyboardDidShowListener?.remove();
    this.keyboardDidHideListener?.remove();
  }

  _keyboardDidShow = async event => {
    console.debug('KeyboardUtil keyboardDidShow', event);
    this.keyBoardIsShow = true;
    await AppModule.stopDetectRingerMode();
  };

  _keyboardDidHide = async event => {
    console.debug('KeyboardUtil keyboardDidHide', event);
    this.keyBoardIsShow = false;
    await AppModule.startDetectRingerMode();
  };

  addListener = (eventType, callback) => {
    return Keyboard.addListener(eventType, callback);
  };

  dismiss = () => {
    Keyboard.dismiss();
  };
}

export default new KeyboardUtil();
