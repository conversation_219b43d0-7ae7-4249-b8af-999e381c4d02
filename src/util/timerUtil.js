import BackgroundTimer from 'react-native-background-timer';

/**
 * 解决定时器在APP回到后台时不执行的问题
 * <AUTHOR>
 */
class TimerUtil {
  _isStart = false;

  start() {
    console.log('TimerUtil start', this._isStart, IS_IOS);
    if (this._isStart) return;
    this._isStart = true;
    if (IS_IOS) {
      BackgroundTimer.start();
    }
    global.setInterval = (handler, timeout, ...args) =>
      BackgroundTimer.setInterval(handler, timeout || 0, ...args);
    const oldClearInterval = clearInterval;
    global.clearInterval = (handle) => {
      if (handle) {
        BackgroundTimer.clearInterval(handle);
        oldClearInterval(handle);
      }
    };
    global.setTimeout = (handler, timeout, ...args) =>
      BackgroundTimer.setTimeout(handler, timeout || 0, ...args);
    const oldClearTimeout = clearTimeout;
    global.clearTimeout = (handle) => {
      if (handle) {
        BackgroundTimer.clearTimeout(handle);
        oldClearTimeout(handle);
      }
    };
  }

  stop() {
    console.log('TimerUtil stop', this._isStart);
    if (!this._isStart) return;
    this._isStart = false;
    if (IS_IOS) {
      BackgroundTimer.stop();
    }
  }
}

export default new TimerUtil();
