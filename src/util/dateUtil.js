import { NativeModules } from 'react-native';

/**
 * 时间工具
 * <AUTHOR>
 */
class DateUtil {
  init() {
    console.debug('DateUtil init');
    global.now = () => {
      // if (this.serverTimeInfo) {
      //   const now =
      //     global.elapsedRealtime() -
      //     this.serverTimeInfo.elapsedRealtime +
      //     this.serverTimeInfo.milliTime;
      //   console.debug('global.now 服务器时间', now);
      //   return now;
      // }
      const now = Date.now();
      // console.warn('global.now 本地时间', now);
      return now;
    };
    global.elapsedRealtime = function() {
      // 目前使用js的，如果获取不到，再考虑原生桥接获取
      // js的好像有问题，可能不会计算手机睡眠的时间
      // let now = global.performance?.now?.() || global.nativePerformanceNow?.();
      // if (now) return Math.floor(now);
      // const now = NativeModules.AppModule.elapsedRealtimeSync();
      // if (now) return Number(now);
      return global.now();
    };
  }

  /**
   * 检查是否需要刷新服务器时间
   * @param isRefresh
   */
  checkRefreshServerTime(isRefresh) {
    const b = isRefresh || !this.serverTimeInfo || this.serverTimeInfo.duration > 200;
    // global.elapsedRealtime() - this.serverTimeInfo.elapsedRealtime > 3600000;
    return b ? null : this.serverTimeInfo;
  }

  /**
   * 处理获取的服务器时间，毫秒
   * @param res {object} {milliTime:1706174572462,durationInfo:{startElapsedRealtime:722413707,duration:63}}
   */
  handleServerTime(res) {
    console.debug('dateUtil setServerTime', res, this.serverTimeInfo);
    res.duration = res.durationInfo.duration;
    res.milliTime = res.milliTime || res.time * 1000;
    res.elapsedRealtime = Math.floor(res.duration / 2) + res.durationInfo.startElapsedRealtime;
    delete res.durationInfo;
    if (!this.serverTimeInfo || this.serverTimeInfo.duration > res.duration) {
      this.serverTimeInfo = res;
    }
    return res;
  }

  /**
   * 是否小于当前时间对应间隔，毫秒，主要防止用户将系统时间改大出现的问题
   */
  isLTDuration(duration, time = 0) {
    const d = global.now() - time;
    return d > 0 && d < duration;
  }

  /**
   * 是否大于当前时间对应间隔，毫秒，主要防止用户将系统时间改大出现的问题
   */
  isGTDuration(duration, time = 0) {
    const d = global.now() - time;
    return d < 0 || d > duration;
  }

  /**
   * 获取剩余时间，毫秒，主要防止用户将系统时间改大出现的问题
   */
  getRemainTime(duration, time = 0) {
    const d = global.now() - time;
    return d > 0 ? duration - d : 0;
  }
}

export default new DateUtil();
