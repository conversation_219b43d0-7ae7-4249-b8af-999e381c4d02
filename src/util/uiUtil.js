import constant from '../store/constant';

/**
 * UI工具
 * <AUTHOR>
 */
class UIUtil {
  showGlobalLoading = () => {
    global.emitter.emit(constant.event.globalLoading, { showLoading: true });
  };

  hideGlobalLoading = () => {
    global.emitter.emit(constant.event.globalLoading, { showLoading: false });
  };

  showRequestResult = (error) => {
    global.emitter.emit(constant.event.globalLoading, { showLoading: false, error });
  };

  showResultMessage = (message) => {
    this.showRequestResult({ message });
  };

  /**
   * 显示对话框
   * @param alertProps {{title, message, onConfirm, onCancel, textConfirm, textCancel, showCancel, showConfirm}?}
   */
  showAlert = (alertProps) => {
    if (alertProps && alertProps.onConfirm === undefined) {
      alertProps.onConfirm = this.dismissAlert;
    }
    global.emitter.emit(constant.event.globalAlert, { alertProps });
  };

  dismissAlert = () => {
    this.showAlert(null);
  };
}

export default new UIUtil();
