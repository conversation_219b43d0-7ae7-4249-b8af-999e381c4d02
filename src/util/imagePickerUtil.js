import { openPicker } from '@baronha/react-native-multiple-image-picker';
// import ImgToBase64 from 'react-native-image-base64';
import { Image, Video } from 'react-native-compressor';
import I18n from '../i18n';
// import LocalBarcodeRecognizer from 'react-native-local-barcode-recognizer';
import fileUtil from './fileUtil';
import { getWindowInfo } from '../common';
import ImagePicker from 'react-native-image-crop-picker';
import constant from '../store/constant'
// import uiUtil from './uiUtil';
import RNFS from 'react-native-fs';

/**
 * 图片选择器
 * <AUTHOR>
 */
const ImagePickerUtil = {};

async function handleItem(it, options) {
  // iOS视频返回的秒，Android返回的毫秒，统一为毫秒
  if (it.duration && IS_IOS) {
    it.duration = Math.floor(it.duration * 1000);
  }
  if (it.crop) {
    it.origin = { ...it };
    delete it.origin.crop;
    Object.assign(it, it.crop);
    if (!it.crop.realPath) {
      it.realPath = it.crop.path;
    }
  }
  const uri = it.realPath || it.path;
  it.uri = uri.startsWith('file://') ? uri : `file://${uri}`;
  if (IS_IOS) {
    it.uri = decodeURIComponent(it.uri);
  }

  // 图片裁剪未返回大小
  if (it.crop) {
    const fileInfo = await fileUtil.getFileInfo(it.uri);
    it.size = fileInfo.size;
  }
  // if (options.enableBase64) {
  //   await getBase64String(it);
  // }
  await compression(it, options);
  it.fileSize = it.size;
}

export async function compression(it, options = {}) {
  console.log('compression', it, options);
  const exist = await RNFS.exists(it.uri);
  if (!exist) {
    return;
  }

  if (fileUtil.isImage(it.type)) {
    let { compressionImageMethod = 'auto' } = options;
    let compressOptions;
    if (compressionImageMethod === 'auto') {
      // 大于1M压缩
      if (it.size > 1048576) {
        compressOptions = {
          compressionMethod: 'manual',
          maxWidth: it.width,
          maxHeight: it.height,
          quality: 0.8,
        };
        if (IS_IOS && options.isCrop && options.width && options.height) {
          compressOptions.maxWidth = Math.min(options.width, compressOptions.maxWidth);
          compressOptions.maxHeight = Math.min(options.height, compressOptions.maxHeight);
        }
        const { pxWidth, pxHeight } = getWindowInfo();
        console.log('compress deviceWH', pxWidth, pxHeight);
        // 宽高都大于屏幕宽高时裁剪尺寸
        if (compressOptions.maxWidth > pxWidth && compressOptions.maxHeight > pxHeight) {
          const wRate = compressOptions.maxWidth / pxWidth;
          const hRate = compressOptions.maxHeight / pxHeight;
          if (wRate > hRate) {
            compressOptions.maxWidth = Math.round(compressOptions.maxWidth / hRate);
            compressOptions.maxHeight = pxHeight;
          } else {
            compressOptions.maxWidth = pxWidth;
            compressOptions.maxHeight = Math.round(compressOptions.maxHeight / wRate);
          }
        }
      }
    } else if (compressionImageMethod === 'manual') {
      compressOptions = {
        compressionMethod: 'manual',
        maxWidth: options.compressImageMaxWidth || it.width,
        maxHeight: options.compressImageMaxHeight || it.height,
        quality: options.compressImageQuality || 0.8,
      };
    }
    if (compressOptions) {
      try {
        console.log('compression image options', it.uri, compressOptions);
        const result = await Image.compress(fileUtil.checkEncodeURI(it.uri), compressOptions);
        console.log('compression image result', result);
        const imageInfo = await fileUtil.getImageInfo(result);
        console.log('compression image imageInfo', imageInfo);
        it.width = imageInfo.width;
        it.height = imageInfo.height;
        it.size = imageInfo.size;
        it.uri = result;
      } catch (e) {
        console.warn('compression image error', e);
      }
    }
    await moveToChatDir(it, constant.messageType.image, options);
  } else if (fileUtil.isVideo(it.type)) {
    if (options.compressVideo) {
      try {
        let path = fileUtil.checkEncodeURI(it.uri);
        if (IS_ANDROID) {
          path = path.replace('file://', '');
        }
        console.log('compression video path', path);
        let result = await Video.compress(path, {}, progress => {
          console.log('compression video Progress: ', progress);
        });
        console.log('compression video result', result);
        const fileInfo = await fileUtil.getFileInfo(result);
        console.log('compression video fileInfo', fileInfo);
        it.size = fileInfo.size;
        it.uri = result;
      } catch (e) {
        console.warn('compression video error', e);
      }
    }
    await moveToChatDir(it, constant.messageType.video, options);
  }
}

async function moveToChatDir(it, type, options) {
  if (!options?.session) return;
  // 从临时缓存移到永久目录
  const destPath = await fileUtil.getMsgFilePath({
    url: it.uri,
    ownerId: options.session.ownerId,
    sessionId: options.session.sessionId,
    type,
  });
  await fileUtil.moveFile(it.uri, destPath);
  it.uri = `file://${destPath}`;
}


/*async function getBase64String(image) {
  if (!fileUtil.isImage(image.type)) return;
  image.data = await ImgToBase64.getBase64String(image.uri);
  return image.data;
}*/

/**
 * 扫一扫从相册选图 并 识别二维码
 */
/*ImagePickerUtil.openPickerByScan = async function () {
  const image = await ImagePickerUtil.openPicker({
    usedCameraButton: false,
    mediaType: 'image',
    isPreview: false,
    isCrop: false,
    singleSelectedMode: true,
    enableBase64: true,
    compressionImageMethod: '',
  });
  if (/^image\/(webp|gif)/.test(image.mime)) return '';
  image.data = image.data.replace(/^data:image\/[a-z]+;base64,/, '');
  return LocalBarcodeRecognizer.decode(image.data, {
    codeTypes: [
      'aztec',
      'ean13',
      'ean8',
      'qr',
      'pdf417',
      'upc_e',
      'datamatrix',
      'code39',
      'code93',
      'interleaved2of5',
      'codabar',
      'code128',
      'maxicode',
      'rss14',
      'rssexpanded',
      'upc_a',
      'upc_ean',
    ],
  });
};*/

/**
 * 选择图片或视频
 *
 * @param options
 * @return {Promise<*>}
 */
ImagePickerUtil.openPicker = async function (options = {}) {
  try {
    // https://github.com/baronha/react-native-multiple-image-picker
    options = {
      // usedCameraButton: true, // Show camera button in first row
      // mediaType: 'all', // Select the media format you want. Values include all, image, video. Default is all
      // isPreview: true, // Allows to preview the image / video will select (iOS - Forcetouch)
      maxVideoDuration: 300, // Show only video with time allowed (in seconds)
      // maxVideo: 20, // Number of videos allowed to select
      maxSelectedAssets: 6, // Maximum number of one selection 多选时，最大选择数量
      // singleSelectedMode: false, // Only one image / video can be selected 是否单选
      // isExportThumbnail: false, // Export thumbnail image for Video type
      // isCrop: false, // Enable crop image for singleSelectedMode: true
      // isCropCircle: false, // Crop Image Circle for Avatar.
      doneTitle: I18n.t('op_complete_title'),
      cancelTitle: I18n.t('op_cancel_title'),
      cropTitle: '', // 裁剪标题
      // 压缩使用了另一个库
      compressionImageMethod: 'auto', // auto为自动压缩，使用质量、尺寸压缩，manual为手动压缩，指定以下三个值，其他值不压缩
      // compressImageQuality: 0.8, // 压缩图片，0-1，0为不压缩
      // compressImageMaxWidth: options.width || 1280,
      // compressImageMaxHeight: options.height || 1920,
      compressVideo: false, // 压缩视频

      ...options,
    };
    const images = await openPicker(options);
    console.log('openPicker res', images);
    /*[{
        size: 250196,
        duration: 0,
        chooseModel: 0,
        localIdentifier: **********,
        mime: 'image/jpeg',
        path: 'content://media/external/images/media/**********',
        height: 2772,
        parentFolderName: 'Screenshots',
        width: 1240,
        fileName: 'Screenshot_2023-08-09-18-20-15-55_cf3cf72bd8e53b0db7ddb0a6f2208af9.jpg',
        realPath: '/storage/emulated/0/Pictures/Screenshots/Screenshot_2023-08-09-18-20-15-55_cf3cf72bd8e53b0db7ddb0a6f2208af9.jpg',
        bucketId: **********,
        position: 1,
        type: 'image'
    },
    {
        size: 16922153,
        duration: 7552,
        chooseModel: 0,
        localIdentifier: **********,
        mime: 'video/mp4',
        path: 'content://media/external/video/media/**********',
        height: 1920,
        parentFolderName: 'Camera',
        width: 1080,
        fileName: 'VID20230806151348.mp4',
        realPath: '/storage/emulated/0/DCIM/Camera/VID20230806151348.mp4',
        bucketId: -1739773001,
        position: 6,
        type: 'video'
    }
]*/
    /*function handleItem(it) {
      if (it.crop) {
        it.origin = { ...it };
        delete it.origin.crop;
        Object.assign(it, it.crop);
        if (!it.crop.realPath) {
          it.realPath = it.crop.path;
        }
      }
      it.uri = it.realPath.startsWith('file://') ? it.realPath : `file://${it.realPath}`;
      it.fileSize = it.size;
    }*/
    if (Array.isArray(images)) {
      for (let it of images) {
        await handleItem(it, options);
      }
    } else {
      await handleItem(images, options);
    }
    console.log('openPicker result', images);
    return images;
  } catch (e) {
    logger.warn('openPicker error', e);
    return Promise.reject({ isCanceled: true });
  }
};

ImagePickerUtil.openVideoPicker = async function () {
  return ImagePickerUtil.openPicker({
    mediaType: 'video',
    singleSelectedMode: true,
  });
};

ImagePickerUtil.openPickerByCrop = async function (options = {}) {
  // 根据宽高比，固定裁剪比例
  if (options.width && options.height && !options.aspectRatioX) {
    options.aspectRatioX = 1;
    options.aspectRatioY = options.height / options.width;
  }
  return ImagePickerUtil.openPicker({
    mediaType: 'image',
    singleSelectedMode: true,
    isCrop: true,
    ...options,
  });
};

ImagePickerUtil.openPickerByAvatar = async function (options = {}) {
  return ImagePickerUtil.openPickerByCrop({
    width: 400,
    height: 400,
    isCropCircle: true,
    ...options,
  });
};

ImagePickerUtil.openCamera = async function (options = {}) {
  return ImagePicker.openCamera(options);
};

export default ImagePickerUtil;
