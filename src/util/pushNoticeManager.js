import NavigationService from '../navigationService';
import chatSessionDao from '../database/dao/chatSessionDao';
import userStore from '../store/stores/user';
import imAction from '../store/actions/imAction';
import callAction from '../store/actions/callAction';
import meetingAction from '../store/actions/meetingAction';
import I18n from '../i18n';
import JPush from 'jpush-react-native';

/**
 * 推送通知业务处理
 */
class PushNoticeManager {
  _hasHome = false; // 首页是否已挂载

  _pushMessage = null; // 点击推送通知的消息

  setHasHome = (isDidMount) => {
    this._hasHome = isDidMount;
    if (isDidMount) {
      this._parsePushMessage(this._pushMessage);
    }
    this._pushMessage = null;
  };

  onNotificationOpened = (message) => {
    if (this._hasHome) {
      logger.info('PushService 处理推送消息1 {@message} ', message);
      this._parsePushMessage(message);
    } else {
      logger.info('PushService 稍后处理推送消息1 {@message} ', message);
      this._pushMessage = message;
    }
  };

  _parsePushMessage = async (message) => {
    try {
      if (!message?.extras) return;
      const { extras } = message;
      if (extras.type) {
        switch (extras.type) {
          case 'chat':
            await this._onChatMessage(extras);
            break;
          case 'voip':
            await this._onVoipMessage(extras);
            break;
          case 'joinGroup':
            NavigationService.navigate('requestJoinGroupList');
            break;
        }
      } else {
        await this.gotoMessage(extras);
      }
    } catch (e) {
      logger.warn('pushNoticeManager _parsePushMessage', e, message);
    }
  };

  _onVoipMessage = async (message) => {
    message.state = parseInt(message.state, 10);
    message.sessionId = parseInt(message.sessionId, 10);
    if (message.meetingInfo && typeof message.meetingInfo === 'string') {
      message.meetingInfo = JSON.parse(message.meetingInfo);
    }
    if (message?.callType === 'meetingCall') {
      await meetingAction.onReceiveVoipInvite(message);
    } else {
      await callAction.onReceiveVoipInvite(message);
    }
  };

  _onChatMessage = async ({ sessionId, sessionType }) => {
    // 注意：极光给过来的数据都变成字符串了
    const isGroup = sessionType === 'group' ? 1 : 0;
    sessionId = Number(sessionId);
    let session = await chatSessionDao.getChatSession({ sessionId, isGroup });
    if (!session) {
      session = {
        ownerId: userStore.imId,
        sessionId,
        isGroup,
      };
    }
    NavigationService.navigateMessage({
      session,
      sessionId: session.sessionId,
    });
    imAction.getAllOfflineMessageList();
  };

  gotoMessage = async (extras) => {
    if (!extras?.sessionId) return;
    const sessionId = Number(extras.sessionId);
    const { imId } = userStore;
    const result = await chatSessionDao.querySessionList({ ownerId: imId });
    const session = result.find((x) => x.sessionId === sessionId);
    if (!session) return;
    NavigationService.navigateMessage({
      session,
      sessionId: session.sessionId,
    });
    imAction.getAllOfflineMessageList();
  };

  notifyMap = new Map();

  getLocalNotifyId(params, messageID) {
    if (!messageID) {
      messageID = Number(params.extras.sessionId) % 2147483647;
    }
    for (let value of this.notifyMap.values()) {
      if (value.messageID === messageID) {
        return this.getLocalNotifyId(messageID + 1);
      }
    }
    return messageID;
  }

  /**
   * 处理同一个聊天仅显示一条通知
   */
  addLocalNotificationInterceptor(params) {
    const { key, isGroup } = this.getNotifyKey(params);
    if (!key) return params;
    let data = this.notifyMap.get(key);
    if (!data) {
      data = {
        sessionId: Number(params.extras.sessionId),
        isGroup,
        num: 0,
      };
      this.notifyMap.set(key, data);
    }
    data.num++;
    // 本地通知ID，Android不能大于int最大值2147483647
    data.messageID = data.messageID || this.getLocalNotifyId(params);
    params.messageID = data.messageID.toString();
    if (data.num > 1) {
      params.content = I18n.t('page_chat_notifyNewMessageNum', { num: data.num });
    } else {
      params.content = I18n.t('page_chat_tips_a_new_message');
    }
    console.debug('pushNoticeManager addLocalNotificationInterceptor', params);
    return params;
  }

  clearAllNotifications() {
    this.notifyMap.clear();
  }

  getNotifyKey(params) {
    if (params?.extras?.type !== 'chat' || !params.extras.sessionId) return {};
    const isGroup = params.extras.sessionType === 'group';
    const key = (isGroup ? 'g' : 'p') + params.extras.sessionId;
    return { key, isGroup };
  }

  receiveNotificationListener(params) {
    const { key } = this.getNotifyKey(params);
    if (!key) return;
    if (params.notificationEventType === 'notificationArrived') {
      // 收到服务端新通知，直接删除，显示本地通知
      if (!params.notificationId) return;
      JPush.clearNotificationById({
        notificationId: IS_ANDROID ? Number(params.notificationId) : params.notificationId,
      });
      params = {
        messageID: params.messageID,
        title: params.title,
        content: params.content,
        extras: params.extras,
      };
      JPush.addLocalNotification(this.addLocalNotificationInterceptor(params));
    } else {
      // 点击通知 或 关闭通知
      this.notifyMap.delete(key);
    }
  }
}

export default new PushNoticeManager();
