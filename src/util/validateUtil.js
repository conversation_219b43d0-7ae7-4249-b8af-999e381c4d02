import I18n from '../i18n';
import util from './index';

const validateUtil = {};

function showMessage(msg, showErrorMsg = true, isKey = true) {
  if (showErrorMsg) {
    toast.show(isKey ? I18n.t(msg) : msg);
  }
}

/**
 * 验证手机号
 */
validateUtil.validatePhone = function (phone, region, showErrorMsg, includeZero) {
  if (!phone || !phone.trim()) {
    showMessage('page_login_text_input_phone', showErrorMsg);
    return '';
  }
  phone = util.formatOnlyPhone(phone.trim());
  if (region && region.pattern && !region.pattern.test(phone)) {
    // 柬埔寨增加提示首位不能为0
    showMessage(
      region.code === '+855' && !includeZero
        ? 'page_login_regExp_phone_kh'
        : 'page_login_op_phone_required',
      showErrorMsg
    );
    return '';
  }
  return phone;
};

export default validateUtil;
