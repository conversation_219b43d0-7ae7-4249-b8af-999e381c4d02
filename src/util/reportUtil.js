import qs from 'qs';
import I18n, { getLanguage } from '../i18n';
import NavigationService from '../navigationService';
import configs from '../configs';
import Session from '../api/session';

async function navigateWebview(data) {
  const accessToken = await Session.getImAccessToken();
  const params = {
    lang: getLanguage(),
    env: configs.env,
    baseURL: configs.im.serverImURL,
    ...data,
    token: accessToken?.token,
  };
  const url = `${configs.im.complainUrl}?${qs.stringify(params, { arrayFormat: 'repeat' })}`;
  NavigationService.navigate('webviewDetail', {
    url,
    title: I18n.t('page_chat_title_complaint'),
    showLoading: true,
    showShare: false,
  });
}

// 举报用户
export async function onReportUser(imId) {
  const data = {
    type: 'personal',
    imId,
  };
  navigateWebview(data);
}

// 举报群
export async function onReportGroup(groupId) {
  const data = {
    type: 'group',
    groupId,
  };
  navigateWebview(data);
}

/**
 * 投诉举报工具类
 * <AUTHOR>
 */
export default {
  onReportUser,
  onReportGroup,
};
