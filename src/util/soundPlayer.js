import Sound from 'react-native-sound';
import AppModule, { RingerMode } from '../modules/AppModule';

let isPlaying = false;

const AudioMode = {
  NORMAL: 0, // 普通模式
  IN_COMMUNICATION: 3, // 通信/通话模式，听筒
  IN_COMMUNICATION_SPEAKERPHONE: 4, // 通信/通话模式，免提
};

class BaseSound {
  constructor(file, isLoop, audioMode) {
    this.file = file;
    this.isLoop = isLoop;
    this.audioMode = audioMode || AudioMode.NORMAL;
  }

  isPlaying() {
    return this.sound?.isPlaying();
  }

  async play(isMessagePlay) {
    const ringerMode = await AppModule.getRingerMode();
    if (ringerMode !== RingerMode.NORMAL) {
      console.debug(`BaseSound 非响铃模式 ringerMode:${ringerMode}`);
      return;
    }
    // 已经在播放，跳过
    if ((this.sound || isPlaying) && !isMessagePlay) {
      console.debug(`BaseSound 播放中 isPlaying:${isPlaying} sound:${!!this.sound}`);
      return;
    }
    switch (this.audioMode) {
      case AudioMode.IN_COMMUNICATION:
        Sound.setCategory(IS_ANDROID ? 'Voice' : 'PlayAndRecord', IS_IOS);
        break;
      case AudioMode.IN_COMMUNICATION_SPEAKERPHONE:
        Sound.setCategory(IS_ANDROID ? 'Voice' : 'Playback', IS_IOS);
        break;
      default:
        Sound.setCategory('Playback', IS_IOS);
        break;
    }
    this.setMode(this.audioMode);
    Sound.setActive(true);
    if (typeof this.file === 'string') {
      this.sound = new Sound(this.file, Sound.MAIN_BUNDLE, this._initCallback);
    } else {
      this.sound = new Sound(this.file, this._initCallback);
    }
  }

  stop() {
    try {
      if (!this.sound) return;
      console.debug('BaseSound stop stop');
      this.sound.stop();
      console.debug('BaseSound stop release');
      this.sound.release();
    } catch (e) {
      console.warn('BaseSound stop', e);
    }
    // 重置为默认模式
    // if (IS_ANDROID) {
    //   Sound.setMode(0, true);
    // }
    this.sound = null;
    isPlaying = false;
  }

  setMode(audioMode) {
    console.debug(`BaseSound setMode audioMode:${audioMode}`);
    this.audioMode = audioMode;
    if (IS_ANDROID) {
      switch (audioMode) {
        case AudioMode.IN_COMMUNICATION:
          Sound.setMode(3, false);
          break;
        case AudioMode.IN_COMMUNICATION_SPEAKERPHONE:
          Sound.setMode(3, true);
          break;
        default:
          Sound.setMode(0, true);
          break;
      }
    } else {
      switch (audioMode) {
        case AudioMode.IN_COMMUNICATION:
          Sound.setCategory('PlayAndRecord', IS_IOS);
          break;
        case AudioMode.IN_COMMUNICATION_SPEAKERPHONE:
          Sound.setCategory('Playback', IS_IOS);
          break;
        default:
          Sound.setCategory('Playback', IS_IOS);
          break;
      }
    }
  }

  /**
   * 是否免提/外放
   * @param value
   */
  setSpeakerphoneOn(value) {
    console.debug(`BaseSound setSpeakerphoneOn value:${value} sound:${!!this.sound}`);
    this.setMode(value ? AudioMode.IN_COMMUNICATION_SPEAKERPHONE : AudioMode.IN_COMMUNICATION);
  }

  _initCallback = (error) => {
    if (error) {
      console.warn('BaseSound 初始化错误', error);
      this.stop();
      return;
    }
    try {
      if (this.isLoop) {
        this.sound.setNumberOfLoops(-1);
      }
      console.debug('BaseSound play');
      this.sound.play(this._onPlayEnd);
      isPlaying = true;
    } catch (e) {
      console.warn('BaseSound play error', e);
      this.stop();
    }
  };

  _onPlayEnd = (success) => {
    console.debug(`BaseSound _onPlayEnd success:${success}`);
    if (!this.isLoop) {
      this.stop();
    }
  };
}

export const messageSound = new BaseSound('message.wav');

export const callSound = new BaseSound(
  'video_chat_tip_sender.aac',
  true,
  AudioMode.IN_COMMUNICATION_SPEAKERPHONE
);

// Reduce the volume by half
// whoosh.setVolume(0.5);

// Position the sound to the full right in a stereo field
// whoosh.setPan(1);

// Loop indefinitely until stop() is called

// Get properties of the player instance
// console.log('volume: ' + whoosh.getVolume());
// console.log('pan: ' + whoosh.getPan());
// console.log('loops: ' + whoosh.getNumberOfLoops());

// Seek to a specific point in seconds
// whoosh.setCurrentTime(2.5);

// Get the current playback point in seconds
// whoosh.getCurrentTime((seconds) => console.log('at ' + seconds));

// Pause the sound
// whoosh.pause();

// Stop the sound and rewind to the beginning
// whoosh.stop(() => {
// Note: If you want to play a sound after stopping and rewinding it,
// it is important to call play() in a callback.
//   whoosh.play();
// });

// Release the audio player resource
// whoosh.release();
