import I18n from '../i18n';

function setInputText(input, text) {
  if (input && input.setNativeProps) {
    input.setNativeProps({ text });
  }
}

/**
 * 处理金额输入
 */
function handleAmount(amount, options = {}) {
  const { input, maxLength = 13, precision = 2, thousand = ',', maxAmount = 10000000 } = options;
  if (!amount) {
    setInputText(input, '');
    return '';
  }
  amount = amount.replace(/[^\d\\.]/g, '');
  if (!amount) {
    return handleAmount('', options);
  }
  // 处理第一位0的情况
  if (amount.length > 1 && amount.startsWith('0') && !amount.startsWith('0.')) {
    return handleAmount(amount.substring(1, amount.length), options);
  }
  const idx = amount.indexOf('.');
  if (idx > -1) {
    // 处理第一位小数点的情况
    if (idx === 0) {
      return handleAmount(amount.substring(1, amount.length), options);
    }
    // 处理多个小数点的情况
    if (idx !== amount.lastIndexOf('.')) {
      const split = amount.split('.');
      return handleAmount(
        `${split.slice(0, split.length - 1).join('')}.${split[split.length - 1]}`,
        options
      );
    }
    // 处理小数点后两位以上的情况
    if (idx + precision < amount.length - 1) {
      return handleAmount(amount.substring(0, idx + precision + 1), options);
    }
    if (idx > 3) {
      const split = amount.split('.');
      amount = `${split[0].replace(/\d{1,3}(?=(\d{3})+$)/g, `$&${thousand}`)}.${split[1]}`;
    }
  } else if (amount.length > 3) {
    amount = amount.replace(/\d{1,3}(?=(\d{3})+$)/g, `$&${thousand}`);
  }
  if (amount.length > maxLength) {
    amount = amount.substring(
      0,
      amount.lastIndexOf('.') === maxLength - 1 ? maxLength - 1 : maxLength
    );
  }
  if (parseFloat(amount.replace(/,/g, '')) > maxAmount) {
    amount = maxAmount.toString().replace(/\d{1,3}(?=(\d{3})+$)/g, `$&${thousand}`);
    toast.show(I18n.t('msg_validate_amount_lower_max', { amount }));
  }
  setInputText(input, amount);
  return options.isFormat ? amount : amount.replace(/,/g, '');
}

/**
 * 处理手机号输入
 */
function handlePhone(phone, input, region) {
  if (!input) {
    return phone;
  }
  if (!phone) {
    input.setNativeProps({ text: '' });
    return '';
  }
  phone = phone.replace(/[^\d]/g, '');
  if (region && region.handleInput) {
    const newText = region.handleInput(phone);
    if (newText !== phone) return handlePhone(newText, input, region);
  }
  input.setNativeProps({ text: phone });
  return phone;
}

// 处理输入年龄，最大输入2位数，最小值输入是1, 只能输入数字
function handleAge(age, { input }) {
  if (!input) {
    return age;
  }
  if (!age) {
    input.setNativeProps({ text: '' });
    return '';
  }
  age = age.replace(/[^\d]/g, '');
  if (age.length > 2) {
    age = age.substring(0, 2);
  }
  if (age.length === 1 && age === '0') {
    age = '';
  }
  input.setNativeProps({ text: age });
  return age;
}

/**
 * 处理手机验证码输入
 */
function handlePhoneCode(phoneCode, options) {
  const { input, maxLength = 5 } = options;
  if (!phoneCode) {
    setInputText(input, '');
    return '';
  }
  phoneCode = phoneCode.replace(/[^\d]/g, '');
  if (phoneCode.length > maxLength) {
    phoneCode = phoneCode.substring(0, maxLength);
  }
  setInputText(input, phoneCode);
  return phoneCode;
}

/**
 * 处理银行卡号输入
 */
function handleBankCard(cardNum, input) {
  if (!input) {
    return cardNum;
  }
  if (!cardNum) {
    input.setNativeProps({ text: '' });
    return '';
  }
  cardNum = cardNum.replace(/[^\d]/g, '').replace(/(\d{4})(?=\d)/g, '$1 ');
  input.setNativeProps({ text: cardNum });
  return cardNum;
}

/**
 * 处理护照输入
 */
function handlePassportNumber(passportNumber, input) {
  if (!input) {
    return passportNumber;
  }
  if (!passportNumber) {
    input.setNativeProps({ text: '' });
    return '';
  }
  passportNumber = passportNumber.replace(/[^a-zA-Z0-9]/g, '');
  input.setNativeProps({ text: passportNumber });
  return passportNumber;
}

/**
 * 处理输入框艾特删除时的功能
 * @param atList 必须包含atName字段，且不带空格
 * @param text
 * @return {{text, atList}}
 */
function handleAtName(atList, text) {
  if (!atList.length) return { atList, text };
  if (!text) return { atList: [], text };
  let atName, idx, idx2;
  let i = 0;
  const nameIdxMap = new Map();
  while (i < atList.length) {
    atName = atList[i].atName;
    idx = text.indexOf(atName, nameIdxMap.get(atName) || 0);
    if (idx < 0) {
      atList.splice(i, 1);
      continue;
    }
    idx2 = text.indexOf(atName + ' ', idx);
    if (idx2 < 0 || idx2 !== idx) {
      atList.splice(i, 1);
      if (idx === 0) {
        text = text.substring(atName.length);
      } else if (idx + atName.length === text.length) {
        text = text.substring(0, idx);
      } else {
        text = text.substring(0, idx) + text.substring(idx + atName.length);
      }
      continue;
    }
    nameIdxMap.set(atName, idx + atName.length);
    i++;
  }
  console.log('handleAtName end', atList.length, atList.map((item) => item.atName).join(' '), text);
  return { atList, text };
}

/**
 * 输入框工具类
 * <AUTHOR>
 */
export default {
  handleAmount,
  handlePhone,
  handlePhoneCode,
  handleBankCard,
  handlePassportNumber,
  handleAtName,
  handleAge,
};
