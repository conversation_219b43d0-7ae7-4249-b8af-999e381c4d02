/**
 * 刷新管理
 * <AUTHOR>
 */
export default class RefreshManager {
  refreshTotalCount = 0; // 总请求刷新任务数量

  refreshTotal = 0;

  startRefreshTime = 0;

  refreshing = false;

  startRefresh = (tasks, callback) => {
    this.callback = callback;
    this.refreshing = true;
    this.refreshTotal = 0;
    this.startRefreshTime = new Date().getTime();
    this.refreshTotalCount = tasks.length;
    tasks.forEach(task => {
      if (typeof task === 'function') {
        task().finally(this.taskFinally);
      } else {
        this.taskFinally();
      }
    });
  };

  taskFinally = () => {
    this.refreshTotal++;
    this.stopRefresh();
  };

  stopRefresh = () => {
    // console.log('stopRefresh', this.refreshTotal, this.refreshTotalCount);
    if (this.refreshTotal < this.refreshTotalCount) {
      return;
    }
    const timeout = 300 - (new Date().getTime() - this.startRefreshTime);
    if (timeout > 0) {
      // 数据加载时间少于250ms，会导致刷新指示器一直转
      // console.log('数据加载时间少于300ms', timestamp);
      setTimeout(this.stopRefresh, timeout);
    } else {
      this.refreshing = false;
      if (typeof this.callback === 'function') {
        this.callback();
      }
    }
  };
}
