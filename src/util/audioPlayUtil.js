import AudioRecorderPlayer from 'react-native-audio-recorder-player';

function append0(number) {
  return number < 10 ? `0${number}` : number.toString();
}

/**
 * 播放语音工具类
 * <AUTHOR>
 */
class AudioPlayUtil {
  formatMMSSByMillisecond = (millisecond) => {
    return this.formatMMSS(millisecond / 1000);
  };

  formatMMSS = (second) => {
    if (typeof second !== 'number' || second <= 0) {
      return '0:00';
    }
    second = Math.floor(second);
    const m = Math.floor(second / 60);
    const s = second % 60;
    return `${m}:${append0(s)}`;
  };

  startPlayer = async (path, playBackListener) => {
    try {
      if (!this.audioRecorderPlayer) {
        this.audioRecorderPlayer = new AudioRecorderPlayer();
      } else {
        await this.stopPlayer(true);
      }
      this.playBackListener = playBackListener;
      this.audioRecorderPlayer.addPlayBackListener(this._onPlayBackListener);
      await this.audioRecorderPlayer.startPlayer(path);
      console.log('AudioPlayUtil startPlayer', path);
    } catch (e) {
      logger.warn('AudioPlayUtil startPlayer', e, path);
      this.playBackListener = null;
      return Promise.reject(e);
    }
  };

  stopPlayer = async (isAbort) => {
    const { playBackListener } = this;
    this.playBackListener = null;
    try {
      if (this.audioRecorderPlayer) {
        this.audioRecorderPlayer.removePlayBackListener();
        await this.audioRecorderPlayer.stopPlayer();
      }
    } catch (e) {
      logger.warn('AudioPlayUtil stopPlayer', e);
      this._onExtraListener(playBackListener, { isAbort, isComplete: true, e });
      return Promise.reject(e);
    }
    this._onExtraListener(playBackListener, { isAbort, isComplete: true });
  };

  _onPlayBackListener = (playBack) => {
    const isComplete = playBack.currentPosition >= playBack.duration;
    console.log('AudioPlayUtil onPlayBackListener', playBack, isComplete);
    const { playBackListener } = this;
    if (isComplete) {
      if (this.audioRecorderPlayer) {
        this.audioRecorderPlayer.removePlayBackListener();
      }
      this.playBackListener = null;
    }
    this._onExtraListener(playBackListener, { ...playBack, isComplete });
  };

  _onExtraListener = (listener, data) => {
    if (typeof listener === 'function') {
      listener(data);
    }
  };
}

export default new AudioPlayUtil();
