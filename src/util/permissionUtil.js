import { Alert } from 'react-native';
import { check, PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import I18n from '../i18n';
import AppUtil from './appUtil';
import deviceInfo from './deviceInfo';

export const PermissionResults = RESULTS;

function noAction() {}

function launchAppDetailsSettings() {
  AppUtil.launchAppDetailsSettings();
}

function showAlert({ notAlert, isUnavailable, title, desc }) {
  if (notAlert) return;
  if (isUnavailable) {
    Alert.alert(null, I18n.t('permission_device_unavailable'));
    return;
  }
  Alert.alert(
    I18n.t(title),
    I18n.t(desc),
    IS_IOS
      ? [
          {
            text: I18n.t('op_cancel_title'),
            onPress: noAction,
            style: 'cancel',
          },
          {
            text: I18n.t('op_confirm_title'),
            onPress: launchAppDetailsSettings,
          },
        ]
      : [
          {
            text: I18n.t('op_cancel_title'),
            onPress: noAction,
            style: 'cancel',
          },
          {
            text: I18n.t('op_confirm_title'),
            onPress: launchAppDetailsSettings,
          },
        ]
  );
}

/**
 * 请求权限
 * <AUTHOR>
 */
export default class PermissionUtil {
  static async requestExternalStoragePermission(options) {
    const { isRead, ...rest } = options || {};
    if (IS_ANDROID && !isRead) {
      const apiLevel = await deviceInfo.getApiLevel();
      console.log('requestExternalStoragePermission apiLevel', apiLevel);
      if (apiLevel >= 33) return true;
    }
    return this.requestPermission({
      permission: IS_IOS
        ? PERMISSIONS.IOS.PHOTO_LIBRARY
        : isRead
        ? PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE
        : PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
      title: IS_IOS ? 'permission_photo_title' : 'permission_storage_title',
      desc: IS_IOS ? 'permission_photo_ios_desc' : 'permission_storage_android_desc',
      ...rest,
    });
  }

  static async requestReadContactsPermission() {
    return this.requestPermission({
      permission: IS_IOS ? PERMISSIONS.IOS.CONTACTS : PERMISSIONS.ANDROID.READ_CONTACTS,
      title: 'permission_contacts_title',
      desc: IS_IOS ? 'permission_contacts_ios_desc' : 'permission_contacts_android_desc',
    });
  }

  static async requestCameraPermission() {
    return this.requestPermission({
      permission: IS_IOS ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA,
      title: 'permission_camera_title',
      desc: IS_IOS ? 'permission_camera_ios_desc' : 'permission_camera_android_desc',
    });
  }

  static async requestPhotoPermission() {
    return this.requestExternalStoragePermission({ isRead: true });
  }

  static async requestCoarseLocationPermission(options = {}) {
    return this.requestPermission({
      permission: IS_IOS
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
        : PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
      title: 'permission_location_title',
      desc: IS_IOS ? 'permission_location_ios_desc' : 'permission_location_android_desc',
      ...options,
    });
  }

  static async requestPermission({ permission, title, desc, notIOS, notAndroid, notAlert }) {
    try {
      // const existPermission = Object.values(
      //   IS_IOS ? PERMISSIONS.IOS : PERMISSIONS.ANDROID
      // ).includes(permission);
      // console.log('requestPermission existPermission', existPermission, permission);
      if (notIOS || notAndroid) return true;

      let result = await check(permission);
      console.log('requestPermission check', result, permission);
      let time;
      switch (result) {
        case RESULTS.GRANTED:
          return true;
        case RESULTS.DENIED:
          time = new Date().getTime();
          result = await request(permission);
          console.log('requestPermission request', result, permission);
          if (result === RESULTS.GRANTED) return true;
          // 有时返回了DENIED，但是未出现系统授权弹窗
          if (result === RESULTS.DENIED && new Date().getTime() - time < 300) {
            showAlert({ notAlert, title, desc });
          }
          break;
        case RESULTS.BLOCKED:
          showAlert({ notAlert, title, desc });
          break;
        case RESULTS.LIMITED:
          // The permission is limited: some actions are possible
          // 权限有限：一些操作是可能的
          return true;
        case RESULTS.UNAVAILABLE:
          // 设备不支持
          showAlert({ notAlert, isUnavailable: true });
          break;
      }
      return Promise.reject({ permissionDenied: result });
    } catch (e) {
      console.warn('requestPermission error', e);
      return Promise.reject({ permissionDenied: 'error', message: e?.message });
    }
  }

  static async requestMultiple(permissions) {
    let granted;
    for (let permission of permissions) {
      granted = await this.requestPermission(permission);
      if (!granted) {
        return false;
      }
    }
    return true;
  }
}
