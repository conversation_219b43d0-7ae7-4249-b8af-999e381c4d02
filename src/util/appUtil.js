import { BackHandler, NativeModules } from 'react-native';
import I18n from '../i18n';
import Configs from '../configs';

/**
 * APP工具
 * <AUTHOR>
 */
export default class AppUtil {
  static existGooglePlay() {
    if (IS_ANDROID) {
      const { AppUtilModule } = NativeModules;
      if (AppUtilModule) {
        return AppUtilModule.existGooglePlay();
      }
    }
    return Promise.resolve(false);
  }

  static checkNotificationEnabled() {
    if (IS_ANDROID) {
      const { AppUtilModule } = NativeModules;
      if (AppUtilModule) {
        return AppUtilModule.checkNotificationEnabled({
          message: I18n.t('notification_permissions'),
          btnConfirm: I18n.t('op_confirm_title'),
          btnCancel: I18n.t('op_cancel_title'),
          btnNeu: I18n.t('page_security_alert_no_again'),
        });
      }
    }
  }

  static isHuawei() {
    if (IS_ANDROID) {
      const { AppUtilModule } = NativeModules;
      if (AppUtilModule) {
        return AppUtilModule.isHuawei();
        // return Promise.resolve(true);
      }
    }
    return Promise.resolve(false);
  }

  /**
   * 返回到手机桌面
   */
  static goHome() {
    if (IS_ANDROID) {
      NativeModules.AppUtilModule?.goHome();
    }
  }

  /**
   * 返回到手机桌面
   */
  static startLauncherActivity() {
    if (IS_ANDROID) {
      console.debug('appUtil startLauncherActivity');
      NativeModules.AppUtilModule?.startLauncherActivity();
    }
  }

  /**
   * 关闭当前原生页面，相当于退出APP，但是进程还在
   */
  static finishCurrentActivity() {
    if (IS_ANDROID) {
      console.debug('appUtil finishCurrentActivity');
      NativeModules.AppUtilModule?.finishCurrentActivity();
    }
  }

  static launchAppDetailsSettings() {
    const { AppUtilModule } = NativeModules;
    if (AppUtilModule) {
      console.log('launchAppDetailsSettings');
      AppUtilModule.launchAppDetailsSettings();
    }
  }

  static setPrintLog() {
    if (IS_ANDROID) {
      NativeModules.AppUtilModule?.printLog(!!Configs.printLog);
    }
  }

  static _hardwareBackPressSet = new Set();

  /**
   * 为对话框、加载框等弹窗添加Android返回按钮事件，在弹窗显示时监听，隐藏时不监听
   * @param isAdd
   * @param isUnmount
   * @param fun
   */
  static addAndroidBackListener(isAdd, fun, isUnmount) {
    if (IS_IOS || !fun) return;
    isAdd = isUnmount ? false : !!isAdd;
    if (this._hardwareBackPressSet.has(fun) === isAdd) return;
    if (isAdd) {
      this._hardwareBackPressSet.add(fun);
      BackHandler.addEventListener('hardwareBackPress', fun);
    } else {
      this._hardwareBackPressSet.delete(fun);
      BackHandler.removeEventListener('hardwareBackPress', fun);
    }
  }
}
