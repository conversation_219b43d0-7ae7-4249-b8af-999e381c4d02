import fileUtil from '../util/fileUtil';
import RNFS from 'react-native-fs';
import messageDao from './dao/messageDao';
import constant from '../store/constant';
import promiseUtil from '../util/promiseUtil';

const maxDownloadCount = 3;
/**
 * 消息下载类
 * <AUTHOR>
 */
class MessageFileDownloadUtil {
  downloadQueue = []; // 下载队列

  downloadCount = 0; // 正在下载数量

  /**
   * 检查本地文件是否存在
   * @param message
   * @return {Promise<boolean>}
   */
  checkAndAddDownloadQueue = async (message, notCheckFile) => {
    if (message.imagePath) {
      if (notCheckFile) return true;
      try {
        const exists = await RNFS.exists(message.imagePath);
        if (exists) {
          return false;
        }
      } catch (e) {
        logger.warn('MessageFileDownloadUtil checkAndAddDownloadQueue 文件不存在', e, message);
      }
    }
    this.addDownloadQueue(message);
    return true;
  };

  /**
   * 添加到下载队列
   * @param message {ChatMessage} 数据
   */
  addDownloadQueue = (message) => {
    if (!message || typeof message.content !== 'string' || !message.content.startsWith('http')) {
      logger.warn('MessageFileDownloadUtil addDownload 无效的可下载', message?.msgId);
      return;
    }
    if (this.downloadQueue.includes(message.msgId)) {
      console.log('MessageFileDownloadUtil addDownload 已在下载队列', message.msgId);
      return;
    }
    this.downloadQueue.push(message);
    this._startDownload();
  };

  _startDownload = async () => {
    if (!this.downloadQueue.length || this.downloadCount >= maxDownloadCount) {
      return;
    }
    this.downloadCount++;
    const message = this.downloadQueue.splice(0, 1)[0];
    this._startDownload();
    try {
      const fromUrl = message.content;
      const toFile = await fileUtil.getMsgFilePath(message);
      console.log('MessageFileDownloadUtil _startDownload', fromUrl, toFile);
      const res = await RNFS.downloadFile({
        fromUrl,
        toFile,
        discretionary: true,
      }).promise;
      console.log('MessageFileDownloadUtil _startDownload', res, fromUrl);
      if (res.statusCode !== 200) {
        throw new Error(`Download fail, code: ${res.statusCode}`);
      }
      message.imagePath = `file://${toFile}`;
      this._downloadComplete(message);
    } catch (e) {
      logger.warn('MessageFileDownloadUtil _startDownload', e);
      this.downloadQueue.push(message);
      await promiseUtil.sleep(1000); // 失败后，延迟再试
    }
    this.downloadCount--;
    this._startDownload();
  };

  _downloadComplete = async (message) => {
    try {
      await messageDao.updateMessageBySeq(message);
      const onMerge = (currentMessage) => {
        currentMessage.imagePath = message.imagePath;
      };
      global.emitter.emit(constant.event.messageChange, { message, onMerge });
    } catch (e) {
      logger.warn('MessageFileDownloadUtil _downloadComplete', e);
    }
  };
}

export default new MessageFileDownloadUtil();
