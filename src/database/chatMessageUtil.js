import constant from '../store/constant';
import chatStore from '../store/stores/chatStore';
import userStore from '../store/stores/user';
import messageDao from './dao/messageDao';
import resIcon from '../res';
import deviceInfo from '../util/deviceInfo';
import sentryUtil from '../util/sentryUtil';
import Friend from './friend';
import WordsSearch from '../util/WordsSearch';
import I18n from '../i18n';
import util from '../util';

const SystemMsgType = new Set([
  constant.messageType.tip,
  constant.messageType.job,
  constant.messageType.resume,
]);

/**
 * 聊天消息工具类
 * <AUTHOR>
 */
class ChatMessageUtil {
  /**
   * 转换消息列表，适用于消息列表组件
   * @param messageList {ChatMessage[]}
   */
  convertMessageList = (messageList) => {
    messageList.forEach((item) => {
      this.convertMessage(item);
    });
    return messageList;
  };

  /**
   * 转换消息，适用于消息列表组件
   * @param item {ChatMessage}
   */
  convertMessage = (item) => {
    const { user } = chatStore;
    item._id = item.seq || item.msgId;
    item.text = item.content;
    item.createdAt = new Date(item.msgTime);
    if (item.type === constant.messageType.call) {
      item.text = '-';
    }
    if (item.isSelfSend) {
      if (item.type === constant.messageType.reqResume) {
        item.system = true;
        item.text = I18n.t('page_chat_req_resume_send');
      } else if (item.type === constant.messageType.sendResume) {
        item.system = true;
        item.text = I18n.t('page_chat_resume_send');
      }
    }
    if (SystemMsgType.has(item.type)) {
      item.system = true;
    } else if (item.isSelfSend) {
      item.received = !!item.isRead;
      item.sent = item.received || item.sendState === constant.messageSendState.successful;
      item.user = user;
    } else {
      item.received = false;
      item.sent = false;
      item.user = {
        _id: item.senderId,
      };
    }
    return item;
  };

  handleSession = (item) => {
    if (!item) return item;
    if (item.userType === constant.userType.robot) {
      item.title = item.title || '';
      item.avatar = item.avatar || resIcon.messageRobotAvatar;
    }
    if (item.isGroup) return item;
    if (item.ownerId && item.ownerId === item.sessionId) {
      item.userType = userStore.userType;
    }
    if (item.userType === constant.userType.manage) {
      item.isManage = true;
      item.isSelfManage = item.sessionId === item.ownerId;
      item.setTopTime = 1;
      item.title = deviceInfo.getApplicationName();
      item.name = item.title;
      item.avatar = resIcon.loginLogo;
    } else if (item.sessionId === constant.fixedSessionId.gpt) {
      item.title = I18n.t('page_chat_gpt_session_title');
      item.avatar = resIcon.messageRobotAvatar;
      item.isGPT = true;
    }
    return item;
  };

  getMessageUserInfo = (message, memberMap) => {
    if (message.isSelfSend) {
      const { user } = chatStore;
      if (user.userType === constant.userType.manage) {
        const item = this.handleSession({ userType: user.userType, sessionId: message.sessionId });
        return {
          ...user,
          name: item.title,
          avatar: item.avatar,
        };
      }
      return user;
    }
    const member = memberMap.get(message.senderId) || {};
    const userType = Reflect.has(member, 'userType') ? member.userType : member.user_type;
    let name = util.handleDisplayName(
      member.friendMemo || member.title || member.memo || member.nickname
    );
    let avatar = member.avatar || '';
    const item = this.handleSession({
      userType,
      sessionId: message.sessionId,
      title: name,
      avatar,
    });
    return {
      _id: message.senderId,
      name: item.title,
      avatar: item.avatar,
      user_type: userType,
      userType,
      nameColor: member.nameColor,
    };
  };

  appendEarlierMessages = (messages, earlierMessages, isOld) => {
    console.log(
      'chatMessageList appendEarlierMessages',
      isOld,
      messages.length,
      earlierMessages.length
    );
    if (!messages.length) return earlierMessages;
    if (!earlierMessages.length) return messages;
    if (isOld) {
      if (
        !this.isSameMsg(earlierMessages[0], messages[messages.length - 1]) &&
        earlierMessages[0].msgTime < messages[messages.length - 1].msgTime
      ) {
        return messages.concat(earlierMessages);
      }
      for (let i = 0; i < earlierMessages.length; i++) {
        this.insertMessage(messages, earlierMessages[i], true);
      }
      return messages;
    }
    if (
      !this.isSameMsg(earlierMessages[earlierMessages.length - 1], messages[0]) &&
      earlierMessages[earlierMessages.length - 1].msgTime > messages[0].msgTime
    ) {
      return earlierMessages.concat(messages);
    }
    for (let i = earlierMessages.length - 1; i > -1; i--) {
      this.insertMessage(messages, earlierMessages[i], false);
    }
    return messages;
  };

  /**
   * 判断是否同一条消息
   */
  isSameMsg = (a, b) => {
    return a.seq === b.seq || (a.originSeq && a.originSeq === b.originSeq);
  };

  insertMessage = (messages, item, isOld) => {
    if (isOld) {
      for (let i = messages.length - 1; i > -1; i--) {
        if (this.isSameMsg(item, messages[i])) {
          if (messages[i].msgId && !item.msgId) return;
          messages[i] = item;
          return;
        }
        if (item.msgTime < messages[i].msgTime) {
          messages.splice(i + 1, 0, item);
          return;
        }
      }
      messages.splice(0, 0, item);
      return;
    }
    for (let i = 0; i < messages.length; i++) {
      if (this.isSameMsg(item, messages[i])) {
        if (messages[i].msgId && !item.msgId) return;
        messages[i] = item;
        return;
      }
      if (item.msgTime > messages[i].msgTime) {
        messages.splice(i, 0, item);
        return;
      }
    }
    messages.push(item);
  };

  appendNewMessage = (messages, newMessage) => {
    console.log('chatMessageList appendNewMessage', messages.length, newMessage);
    newMessage.updateAt = new Date().getTime();
    this.removeMessage(messages, newMessage);
    let isAdd = false;
    for (let i = 0; i < messages.length; i++) {
      if (newMessage.msgTime > messages[i].msgTime) {
        messages.splice(i, 0, newMessage);
        isAdd = true;
        break;
      }
    }
    if (!isAdd) {
      messages.push(newMessage);
    }
    // 修改相关联的消息
    const contentObj =
      /^{.*"msgIds".*}$/.test(newMessage.content) &&
      sentryUtil.parse(newMessage.content, 'cmu anm');
    console.log('appendNewMessage', contentObj, newMessage);
    if (contentObj?.msgIds?.length) {
      const set = new Set(contentObj.msgIds);
      for (let i = 0; i < messages.length; i++) {
        if (set.delete(messages[i].msgId)) {
          messages[i].content = newMessage.content;
          messages[i].text = newMessage.content;
          messages[i].updateAt = newMessage.updateAt;
          console.log('appendNewMessage =====', messages[i]);
          if (!set.size) {
            break;
          }
        }
      }
    }
    return messages;
  };

  /**
   * 检查并附加被引用的消息
   * @param messages {ChatMessage[]}
   */
  checkQuoteMessage = async (messages) => {
    try {
      const msgMap = new Map();
      const quoteMessages = [];
      messages.forEach((item) => {
        if (item.msgId) {
          msgMap.set(item.msgId, item);
        }
        if (item.quoteId && !item.quoteMessage) {
          item.quoteMessage = msgMap.get(item.quoteId);
          if (!item.quoteMessage) {
            quoteMessages.push(item);
          }
        }
      });
      if (quoteMessages.length) {
        const msgIds = quoteMessages.map((item) => item.quoteId);
        const msgList = await messageDao.queryMessageListByMsgIds({
          ownerId: quoteMessages[0].ownerId,
          sessionId: quoteMessages[0].sessionId,
          msgIds,
        });
        const map = new Map();
        msgList.forEach((item) => map.set(item.msgId, item));
        quoteMessages.forEach((item) => {
          item.quoteMessage = map.get(item.quoteId);
        });
      }
    } catch (e) {
      logger.warn('chatMessageList checkQuoteMessage', e, messages);
    }
  };

  replaceMessage = (messages, message) => {
    for (let i = 0; i < messages.length; i++) {
      if (this.isSameMsg(message, messages[i])) {
        messages.splice(i, 1, message);
        return true;
      }
    }
    return false;
  };

  removeMessage = (messages, message) => {
    for (let i = 0; i < messages.length; i++) {
      if (this.isSameMsg(message, messages[i])) {
        messages.splice(i, 1);
        return true;
      }
    }
    return false;
  };

  /**
   * 服务器返回的msg，赋值到本地message
   * @param msg 服务器的 {
            "content": "刚刚",
            "fileName": "",
            "fileSize": 0,
            "msgId": 0,
            "timeLength": 0,
            "type": "text"
          }
   * @param message {ChatMessage} 本地的
   */
  handleMessage = (msg, message) => {
    message.type = msg.type;
    message.content = msg.content;
    message.timeLength = msg.timeLength;
    message.referTo = msg.referTo;
    message.extra = msg.extra;
    message.quoteId = msg.quoteId;
    message.jobId = msg.jobId;
    message.resumeId = msg.resumeId;
    message.jobTitle = msg.jobTitle;
  };

  handleAvatar = (item) => {
    if (!item.avatar) {
      item.avatar = '';
    }
    return item.avatar;
  };

  isAtMe = (message) => {
    if (!message.isGroup || typeof message.referTo !== 'string' || !message.referTo) return false;
    const [idStr] = message.referTo.split('++');
    const ids = idStr?.split('-') || [];
    return ids.indexOf(message.ownerId.toString()) > -1;
  };

  getExtra = (data) => {
    if (!data.msg.extraObj) {
      data.msg.extraObj = (data.msg.extra && sentryUtil.parse(data.msg.extra, 'cmu ge')) || {};
    }
    return data.msg.extraObj;
  };

  isSkipMsg = (data, isGroup, isReceive) => {
    // 群红包，发红包和领红包的都不是我，丢掉该消息
    if (!data.msg.type) return true;
    const { imId } = userStore;
    if (isGroup) {
      if (data.msg.type === constant.messageType.tip && data.msg.extra) {
        const extra = this.getExtra(data);
        if (extra.tipType === 'redPacket' && extra.fromImId !== imId && extra.openImId !== imId) {
          return true;
        }
      }
      return false;
    }
    // 自己收到自己发送的消息
    if (imId === data.sender_id) {
      // 管理员发送公告
      if (data.msg.isBroadCast) return true;
      // 发送消息给自己，同设备的过滤实时发过来的
      const extra = this.getExtra(data);
      if (extra.deviceId === deviceInfo.getUniqueID()) return true;
    }
    return false;
  };

  handleImContact = (item) => {
    if (!item) return item;
    // 处理公司和联系人问题
    const extra = item.extra && sentryUtil.parse(item.extra);
    if (extra?.contact?.name) {
      extra.company = extra.company || item.nickname;
      item.company = extra.company;
      item.nickname = extra.contact.name;
      item.contact = extra.contact;
      item.extra = JSON.stringify(extra);
    }
    return item;
  };

  toFriend = (item, friend) => {
    if (!friend) {
      friend = new Friend();
      friend.imId = item.im_id;
      friend.ownerId = userStore.imId;
      friend.action = 'insert';
    } else {
      friend.action = 'update';
    }
    this.handleImContact(item);
    friend.extra = item.extra;
    friend.nickname = item.nickname || item.account_alias;
    friend.memo = item.friend_nickname;
    friend.avatar = this.handleAvatar(item);
    if (item.phone) {
      friend.phone = item.phone;
    }
    friend.account = item.account;
    if (typeof item.tags !== 'string' || !/^\[[\s\S]*\]$/.test(item.tags)) {
      item.tags = '[]';
    }
    friend.tags = item.tags;
    friend.countryCode = item.country_code?.replace(/\+?(\d+)/, '+$1');
    friend.beDeleted = item.relation_type === 1 ? 0 : 1;
    friend.relationType = item.relation_type;
    if (item.relation_type === 1) {
      friend.isHide = 0;
    } else if (friend.action === 'insert' || item.relation_type === 0) {
      friend.isHide = 1;
    }
    if (Reflect.has(item, 'blacklist_type')) {
      friend.isBlacked = item.blacklist_type;
    }
    if (Reflect.has(item, 'is_disturb')) {
      friend.isDisturb = item.is_disturb ? 1 : 0;
    }
    if (typeof item.user_type === 'number') {
      friend.userType = item.user_type;
    }
    if (friend.imId === friend.ownerId) {
      friend.isHide = 0;
      friend.beDeleted = 0;
      friend.relationType = 1;
      friend.isDisturb = 0;
      friend.isBlacked = 0;
    }
    return friend;
  };

  checkSensitiveWords = async (text) => {
    const findFirst = WordsSearch.findFirst(text?.toLowerCase());
    if (findFirst) {
      const msg = I18n.t('msg_contains_sensitive_words', { word: findFirst.Keyword });
      toast.show(msg);
      return msg;
    }
    return false;
  };

  /** 缓存socket实时传输并处理成功的消息ID，在处理离线消息列表时判断跳过 */
  cacheMsgIdSet = new Set();

  cacheSocketMsg = (data) => {
    if (data.msg_id) {
      data.sessionId =
        data.sessionId ||
        data.group_id ||
        (userStore.imId === data.sender_id ? data.receiver_id : data.sender_id);
      this.cacheMsgIdSet.add(`${data.msg_id}_${data.sessionId}`);
      console.log(
        'chatMessageUtil cacheSocketMsg',
        data.msg_id,
        data.sessionId,
        this.cacheMsgIdSet.size
      );
    } else if (data.notice_id) {
      this.cacheMsgIdSet.add(data.notice_id + '_n');
      console.log('chatMessageUtil cacheSocketMsg', data.notice_id, this.cacheMsgIdSet.size);
    }
    // 如果缓存数量超过1200，则删除保留新的1000条，利用ID是升序排序
    if (this.cacheMsgIdSet.size > 1200) {
      const arr = [...this.cacheMsgIdSet].sort().slice(this.cacheMsgIdSet.size - 1000);
      this.cacheMsgIdSet = new Set(arr);
    }
  };

  existMsgId = (item) => {
    if (item.msg_id) {
      item.sessionId =
        item.group_id || (userStore.imId === item.sender_id ? item.receiver_id : item.sender_id);
      const b = this.cacheMsgIdSet.delete(`${item.msg_id}_${item.sessionId}`);
      console.log('chatMessageUtil existMsgId', b, item.msg_id, this.cacheMsgIdSet.size);
      return b;
    }
    return this.existNoticeId(item);
  };

  existNoticeId = (item) => {
    if (item.notice_id) {
      const b = this.cacheMsgIdSet.delete(item.notice_id + '_n');
      console.log('chatMessageUtil existNoticeId', b, item.notice_id, this.cacheMsgIdSet.size);
      return b;
    }
    return false;
  };

  refMessageTypeSet = new Set([
    constant.messageType.transfer,
    constant.messageType.groupTransfer,
    constant.messageType.transferReturn,
    constant.messageType.transferAccept,
    constant.messageType.receiveCreate,
    constant.messageType.receivePaid,
    constant.messageType.redPacket,
    constant.messageType.redPacketAccept,
  ]);

  checkRefMessage = (message) => {
    return this.refMessageTypeSet.has(message.type) && messageDao.checkRefMessage(message);
  };
}

export default new ChatMessageUtil();
