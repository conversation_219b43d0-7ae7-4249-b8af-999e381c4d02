import userStore from '../store/stores/user';
import imAction from '../store/actions/imAction';
import callAction from '../store/actions/callAction';
import meetingAction from '../store/actions/meetingAction';
import _ from 'lodash';
import { AppState, NativeModules } from 'react-native';
import constant from '../store/constant';
import ChatMessage from './chatMessage';
import receiptMessageUtil from './receiptMessageUtil';
import chatSessionDao from './dao/chatSessionDao';
import chatMessageUtil from './chatMessageUtil';
import friendDao from './dao/friendDao';
import { messageSound } from '../util/soundPlayer';
import JPush from 'jpush-react-native';
import I18n from '../i18n';
import sentryUtil from '../util/sentryUtil';
import isValidBase64 from '../util/messageUtil';
import messageReadUtil from './messageReadUtil';
import sendMessageUtil from './sendMessageUtil';
import chatAction from '../store/actions/chatAction';
import vibrationUtil from '../util/vibrationUtil';
import pushService from '../pushService';

/**
 * 接收处理聊天消息类
 * <AUTHOR>
 */
class ReceiveMessageUtil {
  /**
   * 收到服务器消息
   * @param data
   */
  receiveMessage = (data) => {
    try {
      data.action = data.reply || data.event;
      if (!data.action) {
        logger.warn('ReceiveMessageUtil 未知的消息类型', data);
        return;
      }
      switch (data.action) {
        case constant.messageAction.heartbeatAck:
          break;
        case constant.messageAction.msgGroup:
        case constant.messageAction.msgP2P:
          if (data.data.msg.type === constant.messageType.notice) {
            this.onMessageNotice(data).catch((e) => console.warn('onMessage onMessageNotice', e));
          } else {
            this.onMessage(data).then(() => chatMessageUtil.cacheSocketMsg(data.data));
          }
          break;
        case constant.messageAction.msgGroupAck:
        case constant.messageAction.msgP2PAck:
          this.onMessageAck(data);
          break;
        case constant.messageAction.notice:
          this.onNotice(data).catch((e) => console.warn('receiveMessage onNotice', e));
          break;
      }
    } catch (e) {
      logger.warn('ReceiveMessageUtil receiveMessage', e);
    }
  };

  pushLocationMessage = async (message) => {
    // if (AppState.currentState !== 'background') return;
    // console.log('pushLocationMessage', message);
    const msgId = `${message.msgId}`;
    const params = {
      messageID: msgId.slice(Math.max(msgId.length - 9, 0)),
      // title: message.extra?.groupName || message.extra?.fromName,
      content: I18n.t('page_chat_tips_a_new_message'),
      extras: { type: 'chat', state: 0, sessionId: message?.senderId },
    };
    let { extra } = message;
    if (_.isString(extra)) {
      extra = sentryUtil.parse(extra, 'rm plm');
    }
    const friend = await friendDao.getFriend({ imId: message.senderId });
    const name = friend?.memo || friend?.nickname || extra?.fromName || '';
    if (!message.isGroup) {
      params.title = name;
    } else {
      params.title = extra?.groupName || '';
      switch (message.type) {
        case 'referTo':
          if (
            message.referTo &&
            message.referTo
              .split('++')[0]
              .split('-')
              .map((x) => Number(x))
              .indexOf(Number(userStore.imId)) > -1
          ) {
            params.content = `${friend?.memo || extra?.fromName}${I18n.t(
              'page_chat_tips_new_message_group'
            )}`;
          }
          break;

        default:
          break;
      }
    }
    if (params.title) {
      pushService.addLocalNotification(params);
    }
  };

  /**
   * 收到服务器通知消息
   */
  onNotice = async ({ data }) => {
    switch (data.notice.notify_type) {
      // case constant.messageNoticeType.groupInvite:
      case constant.messageNoticeType.groupRemove:
        await imAction.onGroupInvite(data);
        break;
      case constant.messageNoticeType.groupDisbanded:
        await imAction.onGroupDisbanded(data);
        break;
      case constant.messageNoticeType.noSpeaking:
        await imAction.onNoSpeaking(data);
        break;
      case constant.messageNoticeType.groupSetting:
      case constant.messageNoticeType.modifyGroup:
        await imAction.onGroupModify(data);
        break;
      case constant.messageNoticeType.modifyMember:
        await imAction.onGroupMemberInfoUpdate(data);
        break;
      case constant.messageNoticeType.groupRole:
        await imAction.onGroupRole(data);
        break;
      case constant.messageNoticeType.deleteContacts:
        await imAction.onDeleteContactNotice(data);
        break;
      case constant.messageNoticeType.addBlacklist:
      case constant.messageNoticeType.rmBlacklist:
        await imAction.updateFriendStatus(data);
        break;
      case constant.messageNoticeType.pcLogin:
        await imAction.onPCLogin(data);
        break;
    }
    chatMessageUtil.cacheSocketMsg(data);
  };

  onMessageNotice = async ({ data }) => {
    switch (data.msg.data.notice_type) {
      case constant.messageNoticeType.messageRead:
        messageReadUtil.updateSessionUnreadNum(data);
      case constant.messageNoticeType.messageArrived:
        return messageReadUtil.onMessageRead(data, true);
      case constant.messageNoticeType.onLinePing:
        break;
      case constant.messageNoticeType.addContact:
      case constant.messageNoticeType.agreeContact:
      case constant.messageNoticeType.AddedContact:
        await imAction.onSaveAddContactNotice(data);
        break;
      case constant.messageNoticeType.groupInvite:
        await imAction.onGroupInvite(data);
        break;
    }
    chatMessageUtil.cacheSocketMsg(data);
  };

  /**
   * 收到消息
   * @param data
   */
  onMessage = async (data) => {
    // { event: 'msg_p2p',
    //       data:
    //        { sender_id: 10103,
    //          receiver_id: 10102,
    //          msg:
    //           { content: '考虑',
    //             fileName: '',
    //             fileSize: 0,
    //             msgId: 0,
    //             timeLength: 0,
    //             type: 'text' },
    //          msg_id: 1634278005953210,
    //          msg_time: 1634278005953,
    //          send_self: 0 } }
    // 群消息
    // {"event":"msg_group","data":{"group_id":1635304492110001,"sender_id":10103,"msg":{"content":"hh","fileName":"","fileSize":0,"msgId":0,"referTo":"","timeLength":0,"type":"text"},"msg_id":1635326455588010,"msg_time":1635326455588,"send_self":0}}
    try {
      const { imId } = userStore;
      const isGroup = data.event === constant.messageAction.msgGroup;
      const isSelfSend = data.data.sender_id === imId;
      const sessionId = isGroup
        ? data.data.group_id
        : isSelfSend
        ? data.data.receiver_id
        : data.data.sender_id;
      if (chatMessageUtil.isSkipMsg(data.data, isGroup, true)) return;
      const { disableSend } = await imAction.isDisableSend({
        sessionId,
        imId: data.data.sender_id,
        isGroup,
        type: data.data.msg.type,
      });
      if (disableSend) {
        // 如果不是好友、不在群里、非个人转账消息，不接收保存
        console.warn('onMessage 拦截非法消息', data);
        return;
      }

      if (
        data.data.msg.type === constant.messageType.audioCall ||
        data.data.msg.type === constant.messageType.videoCall
      ) {
        await callAction.onReceiveSingleCall(data.data);
        return;
      }

      if (data.data.msg.type === constant.messageType.meetingCall) {
        await meetingAction.onReceiveMeetingCall(data.data);
        return;
      }

      if (data.data.msg.type === constant.messageType.withdraw) {
        await imAction.onWithdrawMsg(data.data);
        return;
      }

      await this.decryptMessage(data.data, isGroup);
      const message = new ChatMessage();
      message.isGroup = isGroup;
      message.isSelfSend = data.data.sender_id === imId;
      message.senderId = data.data.sender_id;
      message.msgId = data.data.msg_id;
      message.msgTime = data.data.msg_time;
      message.pay_type = data.data.msg?.pay_type; // topay 群组讯息回传标示
      message.receiver = data.data.msg?.receiver; // topay 群组讯息接收人
      message.sendState = constant.messageSendState.successful;
      message.ownerId = imId;
      message.sessionId = sessionId;
      chatMessageUtil.handleMessage(data.data.msg, message);

      // 如果为topay讯息则直接跳转至支付页面
      /*if (
        message?.pay_type &&
        message?.pay_type === 'to_pay' &&
        message?.receiver === String(imId)
      ) {
        const supported = await Linking.canOpenURL(data.data.msg.content);
        if (supported) {
          await Linking.openURL(data.data.msg.content);
        } else {
          Alert.alert(`${data.data.msg.content}`);
        }
        // NavigationService.navigate('webview', {
        //   url: data.data.msg.content,
        //   title: 'ToPay ' + I18n.t('page_transfer_btn_pay'),
        //   showLoading: true,
        // });
        return;
      }*/

      await chatSessionDao.updateMessageWithSession(message, true);
      this.newMsgNotify(message);

      global.emitter.emit(constant.event.receiveMessage, { message });
      //await imAction.markReadMessage(message.msgId, message.isGroup);
      sendMessageUtil
        .sendMsgReadByCheck({
          messages: [message],
          session: { sessionId, isGroup },
          notice_type: constant.messageNoticeType.messageArrived,
        })
        .catch((e) => console.warn('ReceiveMessageUtil onMessage smr', e, message));
    } catch (e) {
      console.warn('ReceiveMessageUtil onMessage', e);
      return Promise.reject(e);
    }
  };

  newMsgNotify = async (message) => {
    // 自己发送的 或 未启用新消息消息通知 或 在当前聊天，则不提醒
    if (
      message.isSelfSend ||
      !userStore.receiveNotify ||
      chatAction.isCurrentSessionAndActive({
        sessionId: message.sessionId,
        isGroup: message.isGroup,
      })
    ) {
      return;
    }
    const isDisturb = await chatSessionDao.isDisturb(message);
    // 开启免打扰，关闭新消息通知 不推送通知
    if (isDisturb) return;
    messageSound.play();
    vibrationUtil.chatNewMsgVibrate();
    this.pushLocationMessage(message);
  };

  decryptMessage = async (msgData, isGroup) => {
    if (!msgData.msg.aesExt) {
      return;
    }
    if (!isValidBase64(msgData.msg.aesExt)) {
      console.warn('decryptMessage 无效的加密数据', msgData);
      return;
    }
    const msg = await NativeModules.SignalMagager.aesDecryptMessage(
      msgData.msg.content,
      msgData.msg.aesExt,
      msgData.sender_id,
      msgData.receiver_id || userStore.imId,
      msgData.group_id?.toString()
    );
    if (msg) {
      msgData.msg.content = msg;
      delete msgData.msg.aesExt;
      console.debug('decryptMessage 解密成功', msg, msgData);
    } else {
      console.warn('decryptMessage 解密失败', msgData);
      sentryUtil.captureMessage(`rmu adm a:${msgData.msg.aesExt} c:${msgData.msg.content}`);
    }
  };

  /**
   * 收到单聊消息回执
   * @param data 示例：{"reply":"msg_p2p_ack","ack":1634033214498,"status":"OK","data":{"msg_time":1634033216022,"msg_id":1634033216022310}}
   */
  onMessageAck = async (data) => {
    try {
      const { receiptType, message } = await receiptMessageUtil.getMessageByReceipt(data.ack);
      switch (receiptType || '') {
        case constant.messageNoticeType.onLinePing:
          return;
        case constant.messageType.withdraw:
          break;
        case constant.messageAction.msgGroup:
        case constant.messageAction.msgP2P:
          await this.onMsgReceipt(data, message);
          break;
      }
    } catch (e) {
      logger.warn('ReceiveMessageUtil onMessageAck', e, data);
    }
  };

  /**
   * 收到单聊消息回执
   */
  onMsgReceipt = async (data, message) => {
    if (!message) {
      logger.warn('ReceiveMessageUtil onMsgReceipt 回执没找到消息', data);
      return;
    }

    if (data.status === 'OK') {
      message.msgTime = data.data.msg_time;
      message.msgId = data.data.msg_id;
      message.sendState = constant.messageSendState.successful;
    } else {
      message.sendState = constant.messageSendState.failed;
    }

    // 消息已读回执
    if (message.notice_type) {
      await messageReadUtil.onMsgReadReceipt(data, message);
    } else {
      await chatSessionDao.updateMessageWithSession(message, false);
      global.emitter.emit(constant.event.receiveMessage, { message });
      chatMessageUtil.cacheSocketMsg({ msg_id: message.msgId, sessionId: message.sessionId });
    }
  };
}

export default new ReceiveMessageUtil();
