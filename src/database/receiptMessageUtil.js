/**
 * 消息回执类
 * <AUTHOR>
 */
class ReceiptMessageUtil {
  ackMap = new Map(); // 等待回执的消息map

  uploadMap = new Map(); // 上传文件map

  /**
   * 开始上传后，等待回执
   * @param data {{seq}} 数据
   */
  addUploadReceipt = (data) => {
    this.uploadMap.set(data.seq, data);
  };

  /**
   * 上传成功，删除回执
   */
  removeUploadReceipt = (seq) => {
    this.uploadMap.delete(seq);
  };

  /**
   * 是否存在正在上传的
   */
  existUpload = (seq) => {
    return this.uploadMap.has(seq);
  };

  /**
   * 发送消息后，等待回执
   * @param data {{seq,receiptType,message}} 数据
   */
  addMessageReceipt = (data) => {
    this.ackMap.set(data.seq, data);
  };

  /**
   * 是否存在等待回执的消息，即正在发送中
   */
  exist = (seq) => {
    return this.ackMap.has(seq);
  };

  /**
   * 获取等待回执的消息
   * @return {{seq,receiptType,message}}
   */
  getMessageByReceipt = async (seq) => {
    const data = this.ackMap.get(seq);
    if (!data) {
      // TODO: 本地数据库查询
      logger.warn('未查到消息回执', seq);
      return {};
    }
    this.ackMap.delete(seq);
    return data;
  };
}

export default new ReceiptMessageUtil();
