let tableExist = new Set();

/**
 * 数据库工具类
 * <AUTHOR>
 */
class DatabaseUtil {
  insert = async ({ db, tableName, fields, data }) => {
    const sql = this.getInsertSql({ tableName, fields });
    return db.executeSql(sql, this.getInsertValues({ fields, data }));
  };

  getInsertSql = ({ tableName, fields, onConflict = ' OR REPLACE ' }) => {
    return `INSERT${onConflict}INTO ${tableName} (${fields.join(',')}) VALUES (${new Array(
      fields.length
    )
      .fill('?')
      .join(',')});`;
  };

  getInsertValues = ({ fields, data }) => {
    return fields.slice().map((field) => {
      return typeof data[field] === 'undefined' ? null : data[field];
    });
  };

  getUpdateFieldSql = ({ fields, data }) => {
    const values = [];
    const updateFieldSql = fields
      .map((item) => {
        values.push(data[item]);
        return `${item}=?`;
      })
      .join(',');
    return { values, updateFieldSql };
  };

  itemMapFun = (item) => item;

  getArray = (res, reverse, itemMapFun = this.itemMapFun) => {
    const arr = [];
    const { rows } = Array.isArray(res) ? res[0] : res;
    const length = rows.length;
    if (length === 0) {
      return arr;
    }
    if (reverse) {
      for (let i = length - 1; i > -1; i--) {
        arr.push(itemMapFun(rows.item(i)));
      }
    } else {
      for (let i = 0; i < length; i++) {
        arr.push(itemMapFun(rows.item(i)));
      }
    }
    return arr;
  };

  getOne = (res) => {
    const arr = this.getArray(res);
    if (arr.length > 1) {
      throw new Error('本该查到一条，结果查到多条数据');
    }
    return arr.length ? arr[0] : null;
  };

  deleteTableName = (tableName) => tableExist.delete(tableName);

  openDatabaseAndCheckTable = async (db, tableName, createTableSqlFun) => {
    if (!tableExist.has(tableName)) {
      await this.getAllTables(db);
      if (!tableExist.has(tableName)) {
        await db.executeSql(createTableSqlFun(tableName));
      }
      tableExist.add(tableName);
    }
    return { db, tableName };
  };

  /**
   * 检查批量查询中的表是否已创建，并且创建
   */
  checkAndCreateTableByBatchQuery = async (db, arr, options) => {
    const tableSet = new Set();
    const regExp = new RegExp(` FROM\\s+(${options.tablePrefix || ''}\\w+)`);
    let matchArr;
    arr.forEach((item) => {
      matchArr = item.sql.match(regExp);
      if (matchArr) {
        tableSet.add(matchArr[1]);
      }
    });
    await this.checkAndCreateTables(db, tableSet, options.createTableSqlFun);
  };

  /**
   * 检查表是否已创建，并且创建
   */
  checkAndCreateTables = async (db, tableSet, createTableSqlFun) => {
    if (typeof createTableSqlFun !== 'function' || !tableSet.size) return;
    await this.getAllTables(db);
    const tableArr = [];
    tableSet.forEach((tableName) => {
      if (!tableExist.has(tableName)) {
        tableArr.push(tableName);
      }
    });
    if (!tableArr.length) return;
    await db.transaction((tx) => {
      tableArr.forEach((tableName) => {
        tx.executeSql(createTableSqlFun(tableName));
      });
    });
    tableArr.forEach((tableName) => {
      tableExist.add(tableName);
    });
  };

  isNotExistTable = async (db, tableName) => {
    const res = await db.executeSql('SELECT name FROM Sqlite_master WHERE type=? AND name=?', [
      'table',
      tableName,
    ]);
    return res[0].rows.length !== 1;
  };

  getTableNames = async (db, prefix) => {
    const res = await db.executeSql(
      `SELECT name FROM sqlite_master WHERE type ='table' AND name LIKE '${prefix}%' AND instr(name,'_')!=0`
    );
    return this.getArray(res);
  };

  getAllTables = async (db, isRefresh) => {
    if (tableExist.size && !isRefresh) return tableExist;
    const res = await db.executeSql(`SELECT name FROM sqlite_master WHERE type ='table'`);
    const tables = this.getArray(res, false, (item) => item.name);
    tableExist = new Set(tables);
    return tableExist;
  };
}

export default new DatabaseUtil();
