import sqliteService from '../sqliteService';
import databaseUtil from '../databaseUtil';
import ChatSession from '../chatSession';
import constant from '../../store/constant';
import friendDao from './friendDao';
import chatGroupDao from './chatGroupDao';
import userStore from '../../store/stores/user';
import chatMessageUtil from '../chatMessageUtil';
import groupMemberDao from './groupMemberDao';
import sentryUtil from '../../util/sentryUtil';
import messageDao from './messageDao';
import chatStore from '../../store/stores/chatStore';
import applicationAction from '../../store/actions/application';
import _ from 'lodash';
import util from '../../util';

const tableName = 't_session';

const fields = [
  'sessionId',
  'ownerId',
  'msgId',
  'title',
  'content',
  'lastMsgTime',
  'setTopTime',
  'isDisturb',
  'isTempSession',
  'setTempTopTime',
  'isDelete',
  'unReadNum',
  'isSelfSend',
  'isGroup',
  'atCount',
  'jobId',
  'jobTitle',
  'resumeId',
  'extra',
];

const updateFields = [
  'msgId',
  'title',
  'content',
  'lastMsgTime',
  'unReadNum',
  'isSelfSend',
  'atCount',
  'jobId',
  'jobTitle',
  'resumeId',
  'extra',
];

const updateSessionTypeSet = new Set([
  constant.messageType.sendResume,
  constant.messageType.changeJob,
  constant.messageType.job,
  constant.messageType.resume,
  constant.messageType.inviteInterview,
]);

/**
 * 聊天会话DAO
 */
class ChatSessionDao {
  /**
   * 保存会话
   * @param message {ChatMessage} 最新的未读消息
   * @param unReadNum {number} 未读消息量
   */
  saveSessionByMsg = async (message, unReadNum = 1, resetRead) => {
    console.log('ChatSessionDao saveSessionByMsg 开始');
    let chatSession = await this.getChatSession(message);
    const isUpdate = !!chatSession;
    if (!chatSession) {
      chatSession = new ChatSession();
      chatSession.sessionId = message.sessionId;
      chatSession.ownerId = message.ownerId;
      chatSession.isGroup = message.isGroup;
    }
    chatSession.msgId = message.msgId;
    chatSession.lastMsgTime = message.msgTime;
    chatSession.isSelfSend = message.isSelfSend;
    chatSession.jobId = message.jobId || chatSession.jobId;
    chatSession.resumeId = message.resumeId || chatSession.resumeId;
    chatSession.jobTitle = message.jobTitle || chatSession.jobTitle;
    if (message.isSelfSend || message.isRead || resetRead) {
      chatSession.unReadNum = 0;
      chatSession.atCount = 0;
    } else {
      chatSession.unReadNum = (chatSession.unReadNum || 0) + unReadNum;
      if (chatMessageUtil.isAtMe(message)) {
        chatSession.atCount = (chatSession.atCount || 0) + 1;
      }
    }
    await this.setSessionTitle(chatSession, isUpdate, message);
    await this.setSessionContent(chatSession, message);
    if (isUpdate) {
      await this.updateSession(chatSession);
    } else {
      await this.addSessionByMsg(chatSession);
    }
    console.log('ChatSessionDao saveSessionByMsg 完成', message);
  };

  saveSessionByMsgList = async (messageList, index = 0) => {
    if (messageList?.length > index && messageList[index]) {
      if (messageList[index].notSaveMsg) {
        this.saveSessionByMsgList(messageList, index + 1);
      } else {
        await this.saveSessionByMsg(messageList[index], 0, true);
        global.emitter.emit(constant.event.chatSessionChange, {});
      }
    }
  };

  /**
   * 设置会话的最新消息内容
   * @param chatSession
   * @param message
   */
  setSessionContent = async (chatSession, message) => {
    chatSession.content = JSON.stringify({ content: message.content, isLanguage: false });
    try {
      switch (message.type) {
        /*case constant.messageType.transfer:
        case constant.messageType.groupTransfer:
        case constant.messageType.transferReturn:
        case constant.messageType.transferAccept:
          chatSession.content = JSON.stringify({
            content: 'page_transfer_title',
            isLanguage: true,
          });
          // chatSession.content = JSON.parse(message.content).imId === userStore.imId ? ;
          break;
        case constant.messageType.receiveCreate:
        case constant.messageType.receivePaid:
          chatSession.content = JSON.stringify({ content: 'page_receive_title', isLanguage: true });
          break;
        case constant.messageType.redPacket:
        case constant.messageType.redPacketAccept:
          chatSession.content = JSON.stringify({ content: 'feat_red_pocket', isLanguage: true });
          break;*/
        case constant.messageType.image:
          chatSession.content = JSON.stringify({ content: 'feat_image', isLanguage: true });
          break;
        case constant.messageType.audio:
          chatSession.content = JSON.stringify({ content: 'feat_audio', isLanguage: true });
          break;
        case constant.messageType.video:
          chatSession.content = JSON.stringify({ content: 'feat_video', isLanguage: true });
          break;
        case constant.messageType.file:
          chatSession.content = JSON.stringify({ content: 'feat_file', isLanguage: true });
          break;
        case constant.messageType.none:
          chatSession.content = JSON.stringify({ content: '', isLanguage: false });
          break;
        /*case constant.messageType.notice:
          chatSession.content = JSON.stringify({ content: 'feat_notice', isLanguage: true });
          break;
        case constant.messageType.businessCard:
          chatSession.content = JSON.stringify({
            content: 'page_message_title_business_card',
            isLanguage: true,
          });
          break;*/
        case constant.messageType.job:
          chatSession.content = JSON.stringify({
            content: 'util_index_msg_job',
            isLanguage: true,
          });
          break;
        case constant.messageType.resume:
          chatSession.content = JSON.stringify({
            content: 'page_resume_text_title',
            isLanguage: true,
          });
          break;
        case constant.messageType.reqResume:
          chatSession.content = JSON.stringify({
            content: 'page_chat_req_resume',
            isLanguage: true,
          });
          break;
        case constant.messageType.sendResume:
          chatSession.content = JSON.stringify({
            content: 'page_resume_text_title',
            isLanguage: true,
          });
          break;
        case constant.messageType.inviteInterview:
          chatSession.content = JSON.stringify({
            content: 'page_resume_text_interview',
            isLanguage: true,
          });
          break;
        case constant.messageType.changeJob:
          chatSession.content = JSON.stringify({
            content: 'feat_change_job',
            isLanguage: true,
          });
          break;
        case constant.messageType.companyBusinessCard:
          chatSession.content = JSON.stringify({
            content: 'feat_company_business_card',
            isLanguage: true,
          });
          break;
        /*case constant.messageType.newFriend:
          chatSession.content = JSON.stringify({
            content: 'page_message_tips_has_new_friend',
            isLanguage: true,
          });
          break;*/
        case constant.messageType.call:
          {
            const extra = sentryUtil.parse(message.extra, 'csd ssc c') || {};
            chatSession.extra = JSON.stringify({ isLanguage: true });
            chatSession.content = JSON.stringify({
              content: extra.callType === 1 ? 'page_call_type_voice' : 'page_call_type_video',
              isLanguage: true,
            });
          }
          break;
        case constant.messageType.tip:
          {
            const extra = (message.extra && sentryUtil.parse(message.extra, 'csd ssc t')) || null;
            if (extra?.tipType === 'redPacket') {
              if (message.isSelfSend) {
                if (extra.fromImId === message.ownerId) {
                  chatSession.content = JSON.stringify({
                    content: 'page_message_tips_be_received_self',
                    isLanguage: true,
                  });
                } else {
                  chatSession.content = JSON.stringify({
                    content: 'page_message_tips_receive',
                    isLanguage: true,
                    params: { name: extra.fromName },
                  });
                }
              } else if (!message.isGroup || extra.fromImId === message.ownerId) {
                chatSession.content = JSON.stringify({
                  content: 'page_message_tips_be_received',
                  isLanguage: true,
                  params: { name: extra.openName },
                });
              } else {
                chatSession.content = JSON.stringify({
                  content: 'page_message_tips_be_received_group',
                  isLanguage: true,
                  params: {
                    openName: extra.openName,
                    fromName: extra.fromName,
                  },
                });
              }
            }
          }
          break;

        case constant.messageType.groupAnnouncement: // 群公告
          chatSession.content = JSON.stringify({
            content: 'page_chat_text_group_notice',
            isLanguage: true,
          });
          break;
      }
      if (chatSession.isGroup && !message.isSelfSend) {
        const friend = await friendDao.getFriend({ imId: message.senderId });
        let nickname = friend?.memo;
        if (!nickname) {
          const member = await groupMemberDao.getGroupMember({
            groupId: chatSession.sessionId,
            imId: message.senderId,
          });
          nickname = member?.memo || member?.nickname || friend?.nickname;
        }
        if (nickname) {
          const content = sentryUtil.parse(chatSession.content, 'csd ssc m');
          chatSession.content = JSON.stringify({
            nickname: `${nickname}:`,
            ...content,
          });
        }
      }
    } catch (e) {
      logger.warn('ChatSessionDao setSessionContent', e, message);
    }
  };

  setSessionTitle = async (chatSession, isUpdate, message) => {
    if (isUpdate && chatSession.title) {
      return;
    }
    if (chatSession.isGroup) {
      const chatGroup = await chatGroupDao.getGroup({
        ownerId: chatSession.ownerId,
        groupId: chatSession.sessionId,
      });
      console.log('setSessionTitle chatGroup', chatGroup);
      if (!isUpdate && !chatGroup) return Promise.reject({ message: 'group not exist' });
      chatSession.isDisturb = chatGroup?.isDisturb || 0;
      chatSession.title = chatGroup?.title || '';
    } else {
      const friend = await friendDao.getFriend({
        ownerId: chatSession.ownerId,
        imId: chatSession.sessionId,
      });
      console.log('setSessionTitle friend', friend);
      if (!isUpdate && !friend) {
        // friend为null，或者非好友 不保存会话，仅 有一方是管理员 或者 转账消息 才保存
        return Promise.reject({ message: 'friend not exist' });
      }
      chatSession.isDisturb = friend?.isDisturb || 0;
      chatSession.title = friend?.memo || friend?.nickname || '';
    }
  };

  /**
   * 添加会话
   * @param chatSession {ChatSession} 会话
   */
  addSessionByMsg = async (chatSession) => {
    console.log('ChatSessionDao addSessionByMsg 开始', chatSession);
    const db = await sqliteService.openDatabase();
    chatSession.atCount = chatSession.atCount || 0;
    await databaseUtil.insert({ db, tableName, fields, data: chatSession });
    global.emitter.emit(constant.event.chatSessionChange, { chatSession });
    console.log('ChatSessionDao addSessionByMsg 完成', chatSession);
  };

  /**
   * 收到发送成功的回执消息时，更新消息id、时间、状态
   */
  updateSession = async (chatSession) => {
    console.log('ChatSessionDao updateSession 开始', chatSession);
    chatSession.atCount = chatSession.unReadNum ? chatSession.atCount || 0 : 0;
    const { updateFieldSql, values } = databaseUtil.getUpdateFieldSql({
      fields: updateFields,
      data: chatSession,
    });
    values.push(chatSession.ownerId);
    values.push(chatSession.sessionId);
    values.push(chatSession.isGroup ? 1 : 0);
    values.push(chatSession.lastMsgTime || 0);
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET ${updateFieldSql} WHERE ownerId=? AND sessionId=? AND isGroup=? AND lastMsgTime<=?`,
      values
    );
    global.emitter.emit(constant.event.chatSessionChange, { chatSession });
    console.log('ChatSessionDao updateSession 完成', chatSession);
  };

  /**
   * 修改未读消息数量
   */
  updateUnReadNum = async ({ ownerId = userStore.imId, sessionId, isGroup = 0, unReadNum = 0 }) => {
    console.log('ChatSessionDao updateUnReadNum 开始');
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET unReadNum=?, atCount=0 WHERE ownerId=? AND sessionId=? AND isGroup=?`,
      [unReadNum, ownerId, sessionId, isGroup || 0]
    );
    global.emitter.emit(constant.event.chatSessionChange, {});
    console.log('ChatSessionDao updateUnReadNum 完成');
  };

  /**
   * 修改未读消息数量
   */
  updateUnReadNumBatch = async ({ arr }) => {
    console.log('ChatSessionDao updateUnReadNumBatch 开始');
    arr = arr.map((session) => {
      return {
        sql: `UPDATE ${tableName} SET unReadNum=0, atCount=0 WHERE ownerId=? AND sessionId=? AND isGroup=? AND unReadNum>0`,
        values: [session.ownerId, session.sessionId, session.isGroup ? 1 : 0],
      };
    });
    let count = 0;
    await sqliteService.batchQuery(arr, (results, index) => {
      console.log('ChatSessionDao updateUnReadNumBatch item', index, results.rowsAffected, results);
      count += results.rowsAffected;
      return results;
    });
    if (count > 0) {
      global.emitter.emit(constant.event.chatSessionChange, {});
    }
    console.log('ChatSessionDao updateUnReadNumBatch 完成', count);
  };

  /**
   * 是否免打扰
   */
  isDisturb = async ({ ownerId = userStore.imId, sessionId, isGroup }) => {
    console.log('ChatSessionDao isDisturb 开始');
    let res = await sqliteService.executeSql(
      'SELECT isDisturb FROM t_session WHERE ownerId=? AND sessionId=? AND isGroup=?',
      [ownerId, sessionId, isGroup ? 1 : 0]
    );
    res = databaseUtil.getOne(res);
    console.log('ChatSessionDao isDisturb 完成', res);
    return res?.isDisturb;
  };

  /**
   * 获取会话, 如果传了session（一般是建立新会话本地数据库可能没有的情况下），将进行merge
   */
  getChatSession = async ({
    ownerId = userStore.imId,
    session,
    sessionId = session?.sessionId,
    isGroup = session?.isGroup,
  }) => {
    console.log('ChatSessionDao getChatSession 开始');
    let res;
    if (isGroup) {
      res = await sqliteService.executeSql(
        `SELECT 
       a.*,
       c.avatar AS groupAvatar,
       c.title AS groupTitle,
       c.isVip AS groupIsVip,
       c.count AS groupMemberCount
    FROM t_session a
    LEFT JOIN t_group c ON a.ownerId=c.ownerId AND a.sessionId=c.groupId
    WHERE a.ownerId=? AND a.sessionId=? AND a.isGroup=?`,
        [ownerId, sessionId, isGroup ? 1 : 0]
      );
      res = databaseUtil.getOne(res);
      if (res) {
        this._handleSession(res);
      }
    } else {
      res = await sqliteService.executeSql(
        `SELECT 
       a.*,
       b.userType AS userType,
       b.avatar AS friendAvatar,
       b.nickname AS friendNickname,
       b.extra AS friendExtra,
       b.memo AS friendMemo
    FROM t_session a
    LEFT JOIN t_friend b ON a.ownerId=b.ownerId AND a.sessionId=b.imId
    WHERE a.ownerId=? AND a.sessionId=? AND a.isGroup=?`,
        [ownerId, sessionId, isGroup ? 1 : 0]
      );
      res = databaseUtil.getOne(res);
      if (res) {
        this._handleSession(res);
      }
    }
    chatMessageUtil.handleSession(res);
    console.log('ChatSessionDao getChatSession 完成', res);
    if (session) {
      return res ? _.merge(res, session) : session;
    }
    return res;
  };

  _handleSession = (item) => {
    item.avatar = item.groupAvatar || item.friendAvatar;
    item.title = util.handleDisplayName(
      item.groupTitle || item.friendMemo || item.friendNickname || item.title
    );
    item.friendExtra = sentryUtil.parseSafe(item.friendExtra);
    if (item.friendExtra?.contact?.name) {
      item.company = item.friendExtra.company || item.friendNickname;
      const position = applicationAction.getPositionById(item.friendExtra.contact.positionId);
      item.positionName = position?.label;
    }
    if (!item.lastMsgTime) {
      item.lastMsgTime = 0;
    }
    item.sortTime = Math.max(item.lastMsgTime, item.setTopTime || 0);
    chatMessageUtil.handleSession(item);
    return item;
  };

  /**
   * 查询会话列表
   * @return {Promise<ChatSession[]>}
   */
  querySessionList = async ({ ownerId = userStore.imId, excludeSelf } = {}) => {
    console.log('ChatSessionDao querySessionList 开始', ownerId);
    const values = [ownerId];
    let sql = '';
    if (excludeSelf) {
      sql = ' AND a.sessionId!=?';
      values.push(ownerId);
    }
    const res = await sqliteService.executeSql(
      `SELECT 
       a.*, 
       c.avatar AS groupAvatar, 
       c.title AS groupTitle, 
       c.isVip AS groupIsVip,
       c.count AS groupMemberCount,
       b.userType AS userType,
       b.avatar AS friendAvatar, 
       b.nickname AS friendNickname,
       b.extra AS friendExtra,
       b.memo AS friendMemo 
    FROM t_session a 
    LEFT JOIN t_friend b ON a.isGroup = 0 AND a.ownerId = b.ownerId AND a.sessionId = b.imId 
    LEFT JOIN t_group c ON a.isGroup = 1 AND a.ownerId = c.ownerId AND a.sessionId = c.groupId
    WHERE a.ownerId = ? ${sql} ORDER BY setTopTime DESC, lastMsgTime DESC`,
      values
    );
    const result = databaseUtil.getArray(res, false, this._handleSession);
    result.sort((a, b) => {
      if (a.userType === constant.userType.manage) {
        return -1;
      }
      if (b.userType === constant.userType.manage) {
        return 1;
      }
      if (a.setTopTime && !b.setTopTime) {
        return -1;
      }
      if (!a.setTopTime && b.setTopTime) {
        return 1;
      }
      return b.sortTime - a.sortTime;
    });
    console.log('ChatSessionDao querySessionList 完成', result);
    return result;
  };

  /**
   * 更新聊天设置 消息免打扰，置顶时间
   */
  updateSet = async (chatSession) => {
    console.log('ChatSessionDao updateSet 开始', chatSession);
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET isDisturb=?, setTopTime=? WHERE ownerId=? AND sessionId=? AND isGroup=?`,
      [
        chatSession.isDisturb || 0,
        chatSession.setTopTime || 0,
        chatSession.ownerId,
        chatSession.sessionId,
        chatSession.isGroup,
      ]
    );
    global.emitter.emit(constant.event.chatSessionChange, { chatSession });
    console.log('ChatSessionDao updateSet 完成', chatSession);
  };

  /**
   * 删除会话
   */
  deleteSession = async ({ ownerId = userStore.imId, sessionId, isGroup }) => {
    console.log('ChatSessionDao deleteSession 开始', sessionId, isGroup);
    await sqliteService.executeSql(
      `DELETE FROM ${tableName} WHERE ownerId=? AND sessionId=? AND isGroup=?`,
      [ownerId, sessionId, isGroup ? 1 : 0]
    );
    console.log('ChatSessionDao deleteSession 完成', sessionId);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 假删除会话
   */
  falseDeleteSession = async ({ ownerId = userStore.imId, sessionId, isGroup, content }) => {
    console.log('ChatSessionDao deleteSession 开始', sessionId, isGroup);
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET isDelete=1, content=?, unReadNum=unReadNum+1 WHERE ownerId=? AND sessionId=? AND isGroup=?`,
      [content, ownerId, sessionId, isGroup ? 1 : 0]
    );
    console.log('ChatSessionDao deleteSession 完成', sessionId);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 假删除会话extras
   */
  falseDeleteSessionExtra = async ({ ownerId = userStore.imId, sessionId, isGroup, content }) => {
    console.log('ChatSessionDao deleteSession 开始', sessionId, isGroup);
    await sqliteService.executeSql(
      'UPDATE t_session SET isDelete=1, content=? WHERE ownerId=? AND sessionId=? AND isGroup=?',
      [content, ownerId, sessionId, isGroup ? 1 : 0]
    );
    console.log('ChatSessionDao deleteSession 完成', sessionId);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 清空所有会话内容
   */
  clearAllSessionMsg = async () => {
    console.log('ChatSessionDao deleteAllSession 开始');
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET unReadNum=0,atCount=0,content=? WHERE ownerId=?`,
      ['', userStore.imId]
    );
    console.log('ChatSessionDao deleteAllSession 完成');
  };

  /**
   * 清空会话内容
   */
  clearSessionMsg = async ({ ownerId = userStore.imId, sessionId, isGroup }) => {
    console.log('ChatSessionDao deleteSession 开始', sessionId, isGroup);
    await sqliteService.executeSql(
      `UPDATE ${tableName} SET unReadNum=0,atCount=0,content=? WHERE ownerId=? AND sessionId=? AND isGroup=?`,
      ['', ownerId, sessionId, isGroup ? 1 : 0]
    );
    console.log('ChatSessionDao deleteSession 完成', sessionId);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  updateMessageWithSession = async (message, isAddMessage, isNoSession) => {
    if (isNoSession) return;
    console.log(
      'ChatSessionDao updateMessageWithSession',
      isAddMessage,
      chatStore.currentSession?.sessionId,
      message
    );
    const msgPromise = isAddMessage
      ? messageDao.addMessage(message)
      : messageDao.updateMessageBySeq(message);
    const notUpdateSession =
      chatStore.currentSession?.sessionId === message.sessionId &&
      !updateSessionTypeSet.has(message.type);
    if (notUpdateSession) {
      await msgPromise;
    } else {
      await Promise.all([msgPromise, this.saveSessionByMsg(message)]);
    }
  };

  /**
   * 获取未读数量
   */
  totalUnReadNum = async ({ ownerId = userStore.imId } = {}) => {
    console.log('ChatSessionDao totalUnReadNum 开始', ownerId);
    let res = await sqliteService.executeSql(
      `SELECT SUM(unReadNum) AS totalCount FROM t_session WHERE ownerId=? AND isDisturb=0`,
      [ownerId]
    );
    res = databaseUtil.getOne(res);
    console.log('ChatSessionDao totalUnReadNum 完成', res);
    return res?.totalCount || 0;
  };
}

export default new ChatSessionDao();
