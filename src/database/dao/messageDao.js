import { getChatMessageTableName, getCreateChatMessageTableSQL } from '../initSQL';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';
import sqliteService from '../sqliteService';
import sentryUtil from '../../util/sentryUtil';

const fields = [
  'msgId',
  'seq',
  'originSeq',
  'broadcastId',
  'content',
  'referTo',
  'msgTime',
  'type',
  'isRead',
  'isArrived',
  'readExtra',
  'timeLength',
  'sendState',
  'isStar',
  'imagePath',
  'senderId',
  'quoteId',
  'isSelfSend',
  'isGroup',
  'extra',
  'localExtra',
  'jobId',
  'resumeId',
];

const updateFields = [
  'msgId',
  'seq',
  'originSeq',
  'broadcastId',
  'content',
  'referTo',
  'msgTime',
  'type',
  'timeLength',
  'sendState',
  'isStar',
  'imagePath',
  'quoteId',
  'extra',
];

/**
 * 聊天消息DAO，由于seq是主键，优先使用seq作为查询条件，seq基本和msgId一样
 */
class MessageDao {
  /**
   * 添加消息
   * @param message 消息，需额外包含：ownerId(当前用户的imId)、sessionId(单聊用户的imId 或 群聊的groupId)
   */
  addMessage = async (message) => {
    console.log('MessageDao addMessage 开始', message);
    if (!message.ownerId || !message.sessionId) {
      logger.warn(
        `MessageDao addMessage 缺少 ownerId: ${message.ownerId} sessionId: ${message.sessionId}`
      );
      return;
    }
    const { db, tableName } = await this.openDatabase(message.ownerId, message.sessionId);
    // 收到别人的消息，没有seq，只有msgId
    if (message.seq) {
      message.originSeq = message.seq;
    } else {
      message.seq = message.msgId;
    }

    await databaseUtil.insert({ db, tableName, fields, data: message });
    console.log('MessageDao addMessage 完成', message);
    await this.updateRefMessage(message);
  };

  checkRefExp = /^{.*"msgIds".*}$/;

  checkRefMessage = (message) => {
    if (!message.msgId || !this.checkRefExp.test(message.content)) return false;
    const contentObj = sentryUtil.parse(message.content, 'md urm');
    if (!contentObj?.msgIds?.length) return false;
    const set = new Set(contentObj.msgIds);
    set.add(message.msgId);
    console.log('MessageDao updateRefMessage 开始', set.size, message);
    contentObj.msgIds = [...set];
    message.content = JSON.stringify(contentObj);
    message.refMsgIds = contentObj.msgIds;
    return true;
  };

  updateRefMessage = async (message) => {
    if (this.checkRefMessage(message)) {
      await this.updateMessageContent({
        ownerId: message.ownerId,
        sessionId: message.sessionId,
        content: message.content,
        msgIds: message.refMsgIds,
      });
      console.log('MessageDao updateRefMessage 完成');
    }
  };

  /**
   * 修改消息内容
   */
  updateMessageContent = async ({ ownerId, sessionId, content, msgIds }) => {
    msgIds = msgIds.join(',');
    console.log('MessageDao updateMessageContent 开始', msgIds, content);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    await db.executeSql(`UPDATE ${tableName} SET content=? WHERE seq IN (${msgIds})`, [content]);
    console.log('MessageDao updateMessageContent 完成', msgIds, content);
  };

  /**
   * 修改本地额外信息
   */
  updateLocalExtra = async ({ ownerId, sessionId, seq, localExtra }) => {
    console.log('MessageDao updateLocalExtra 开始', sessionId, seq, localExtra);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    await db.executeSql(`UPDATE ${tableName} SET localExtra=? WHERE seq=?`, [localExtra, seq]);
    console.log('MessageDao updateLocalExtra 完成', sessionId, seq);
  };

  /**
   * 收到发送成功的回执消息时，更新消息id、时间、状态
   */
  updateMessageBySeq = async (message) => {
    const { db, tableName } = await this.openDatabase(message.ownerId, message.sessionId);
    const { seq } = message;
    if (message.msgId) {
      if (message.msgId !== seq) {
        message.seq = message.msgId;
      }
    } else {
      message.originSeq = seq;
    }
    const { updateFieldSql, values } = databaseUtil.getUpdateFieldSql({
      fields: updateFields,
      data: message,
    });
    values.push(seq);
    await db.executeSql(`UPDATE ${tableName} SET ${updateFieldSql} WHERE seq=?`, values);
    await this.updateRefMessage(message);
  };

  /**
   * 清空聊天记录
   */
  clearMessageList = async ({ ownerId = userStore.imId, sessionId }) => {
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    await db.executeSql(`DELETE FROM ${tableName} `);
  };

  /**
   * 获取当前用户所有消息表
   */
  getMessageTableList = async (db) => {
    console.log('MessageDao getMessageTableList 开始');
    if (!db) {
      db = await sqliteService.openDatabase();
    }
    const result = await databaseUtil.getTableNames(db, `t_message_${userStore.imId}_`);
    console.log('MessageDao getMessageTableList 完成', result.length, result);
    return result;
  };

  /**
   * 清空所有聊天记录
   */
  clearAll = async () => {
    const db = await sqliteService.openDatabase();
    const result = await databaseUtil.getTableNames(db, `t_message_${userStore.imId}_`);
    console.log('MessageDao clearAll', result.length, result);
    if (result.length) {
      await db.transaction((tx) => {
        console.log('SqliteService clearAll 事务开始');
        for (const item of result) {
          tx.executeSql(`DROP TABLE IF EXISTS ${item.name};`);
          databaseUtil.deleteTableName(item.name);
        }
        console.log('SqliteService clearAll 事务完成');
      });
    }
    console.log('MessageDao clearAll 完成');
  };

  /**
   * 查询聊天记录
   */
  queryHistoryMessage = async ({ ownerId = userStore.imId, sessionId, content }) => {
    console.log('MessageDao queryHistoryMessage 开始', ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const res = await db.executeSql(
      `SELECT *, ${sessionId} AS sessionId, ${ownerId} AS ownerId FROM ${tableName} WHERE type IN ('text', 'referTo') AND content like '%${content}%' ORDER BY msgTime DESC, seq DESC `
    );
    const result = databaseUtil.getArray(res, false, this.handleMessage);
    console.log('MessageDao queryHistoryMessageList 完成', result);
    return result;
  };

  /**
   * 查询聊天记录数量
   */
  queryHistoryMessageCount = async ({ ownerId = userStore.imId, sessionId, content }) => {
    console.log('MessageDao queryHistoryMessageCount 开始', ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const res = await db.executeSql(
      `SELECT COUNT(*) AS count FROM ${tableName} WHERE type IN ('text', 'referTo') AND content LIKE ?`,
      [`%${content}%`]
    );
    const result = databaseUtil.getOne(res);
    console.log('MessageDao queryHistoryMessageCount 完成', result);
    return result.count;
  };

  /**
   * 查询消息列表
   */
  queryMessageList = async ({ ownerId, sessionId, msgTime, limit, minMsgTime }) => {
    console.log('MessageDao queryMessageList 开始', msgTime, ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const values = [msgTime, limit];
    if (minMsgTime) {
      values.splice(1, 0, minMsgTime);
    }
    const res = await db.executeSql(
      `SELECT *, ${sessionId} AS sessionId, ${ownerId} AS ownerId FROM ${tableName} WHERE msgTime < ? ${
        minMsgTime ? ' And msgTime >= ? ' : ''
      } ORDER BY msgTime DESC, seq DESC LIMIT ?`,
      values
    );
    const result = databaseUtil.getArray(res, false, this.handleMessage);
    console.log('MessageDao queryMessageList 完成', result);
    return result;
  };

  handleMessage = (it) => {
    if (it && !it.isSelfSend) {
      // 处理未发送已读状态成功的能够重新发送
      it.isRead = !it.isRead ? 0 : 1;
      it.isArrived = !it.isArrived ? 0 : 1;
    }
    return it;
  };

  /**
   * 查询消息列表
   * @return {Promise<ChatMessage[]>}
   */
  queryMessageListByMsgIds = async ({ ownerId, sessionId, msgIds }) => {
    console.log('MessageDao queryMessageListByMsgIds 开始', msgIds, ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const inIds = msgIds.join(',');
    const res = await db.executeSql(
      `SELECT *, ${sessionId} AS sessionId, ${ownerId} AS ownerId FROM ${tableName} WHERE seq IN (${inIds})`
    );
    const result = databaseUtil.getArray(res, false, this.handleMessage);
    console.log('MessageDao queryMessageListByMsgIds 完成', result);
    return result;
  };

  /**
   * 获取消息
   */
  getMessageById = async ({ ownerId = userStore.imId, sessionId, msgId }) => {
    console.log('MessageDao getMessageById 开始', msgId, ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const res = await db.executeSql(
      `SELECT *, ${sessionId} AS sessionId, ${ownerId} AS ownerId FROM ${tableName} WHERE seq=?`,
      [msgId]
    );
    const result = databaseUtil.getOne(res);
    console.log('MessageDao getMessageById 完成', result);
    return this.handleMessage(result);
  };

  /**
   * 获取未读消息列表
   */
  getUnreadMessageIdList = async (arr) => {
    console.log('MessageDao getUnreadMessageIdList 开始', arr.length);
    const { imId } = userStore;
    const sqlArr = arr.map((it) => {
      return {
        sql: `SELECT isGroup,msgId,${imId} AS ownerId, ${
          it.sessionId
        } AS sessionId FROM ${getChatMessageTableName(
          imId,
          it.sessionId
        )} WHERE isRead!=1 AND isSelfSend=1 AND msgTime<=?`,
        values: [it.msgTime],
      };
    });
    function handleResult(res) {
      return databaseUtil.getArray(res);
    }
    const result = await sqliteService.batchQuery(sqlArr, handleResult, {
      createTableSqlFun: getCreateChatMessageTableSQL,
    });
    console.log('MessageDao getUnreadMessageIdList 完成', result.length);
    return result;
  };

  /**
   * 批量查询获取消息已读额外信息
   */
  getMessageReadExtraByBatch = async (arr) => {
    console.log('MessageDao getMessageReadExtraByBatch 开始', arr.length);
    const { imId } = userStore;
    const sqlArr = arr.map((it) => {
      return {
        sql: `SELECT readExtra,isGroup,msgId,${imId} AS ownerId, ${
          it.sessionId
        } AS sessionId FROM ${getChatMessageTableName(imId, it.sessionId)} WHERE seq IN (${[
          ...it.msgIdMap.keys(),
        ].join(',')})`,
        values: [],
      };
    });
    function handleResult(res) {
      return databaseUtil.getArray(res);
    }
    const result = await sqliteService.batchQuery(sqlArr, handleResult, {
      createTableSqlFun: getCreateChatMessageTableSQL,
    });
    console.log('MessageDao getMessageReadExtraByBatch 完成', result.length);
    return result;
  };

  /**
   * 批量修改已读额外信息
   */
  updateMessageReadExtraByBatch = async (arr) => {
    console.log('MessageDao updateMessageReadExtraByBatch 开始', arr.length);
    if (!arr.length) return;
    const db = await sqliteService.openDatabase();
    const tableSet = new Set(arr.map((it) => getChatMessageTableName(it.ownerId, it.sessionId)));
    await databaseUtil.checkAndCreateTables(db, tableSet, getCreateChatMessageTableSQL);
    let rows = 0;
    await db.transaction((tx) => {
      arr.forEach((it) => {
        let w;
        if (it.msgIds?.length) {
          w = `seq IN (${it.msgIds.join(',')})`;
        } else {
          w = `seq=${it.msgId}`;
        }
        tx.executeSql(
          `UPDATE ${getChatMessageTableName(
            it.ownerId,
            it.sessionId
          )} SET isRead=?,isArrived=?,readExtra=? WHERE ${w}`,
          [it.isRead, it.isArrived, it.readExtra],
          (_, results) => {
            if (results.rowsAffected > 0) {
              rows += results.rowsAffected;
            }
          }
        );
      });
    });
    console.log('MessageDao updateMessageReadExtraByBatch 完成', rows, arr.length);
  };

  /**
   * 修改消息已读额外信息
   */
  updateMessageRead = async ({
    ownerId = userStore.imId,
    sessionId,
    msgIds,
    isRead,
    isArrived,
  }) => {
    console.log('MessageDao updateMessageRead 开始', sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const w = `msgId IN (${msgIds.join(',')})`;
    let updateFieldSql = 'isArrived=?';
    const values = [isArrived];
    if (isRead) {
      updateFieldSql += ',isRead=?';
      values.push(isRead);
    }
    await db.executeSql(`UPDATE ${tableName} SET ${updateFieldSql} WHERE ${w}`, values);
    console.log('MessageDao updateMessageRead 完成');
  };

  /**
   * 消息是否存在
   */
  existById = async ({ ownerId = userStore.imId, sessionId, msgId }) => {
    console.log('MessageDao existById 开始', msgId, ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const res = await db.executeSql(`SELECT msgId FROM ${tableName} WHERE seq=?`, [msgId]);
    const b = res[0].rows.length > 0;
    console.log('MessageDao existById 完成', msgId, ownerId, sessionId, b);
    return b;
  };

  /**
   * 查询图片和视频消息类型
   */
  queryMediasMessageList = async ({ ownerId = userStore.imId, sessionId }) => {
    console.log('MessageDao queryMediasMessageList 开始', ownerId, sessionId);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const res = await db.executeSql(
      `SELECT *, ${sessionId} AS sessionId, ${ownerId} AS ownerId FROM ${tableName} WHERE type='image' ORDER BY msgTime DESC, seq DESC`
    );
    const result = databaseUtil.getArray(res, false, this.handleMessage);
    console.log('MessageDao queryMediasMessageList 完成', result);
    return result;
  };

  batchSaveMessage = async (ownerId, sessionId, messages) => {
    console.log('MessageDao batchSaveMessage 开始', sessionId, messages.length);
    const { db, tableName } = await this.openDatabase(ownerId, sessionId);
    const successList = [];
    await db.transaction((tx) => {
      messages.forEach((message) => {
        const sql = databaseUtil.getInsertSql({ tableName, fields, onConflict: ' OR IGNORE ' });
        tx.executeSql(
          sql,
          databaseUtil.getInsertValues({ fields, data: message }),
          (_, results) => {
            if (results.rowsAffected > 0) {
              successList.push(message);
            }
          }
        );
        if (message.refMsgIds?.length) {
          tx.executeSql(
            `UPDATE ${tableName} SET content=? WHERE seq IN (${message.refMsgIds.join(',')})`,
            [message.content]
          );
        }
      });
    });
    console.log('MessageDao batchSaveMessage 完成', sessionId, messages.length, successList.length);
    return successList;
  };

  /**
   * 批量查询消息ID本地是否已存在
   * @param sessionMsgMap
   * @returns {Promise<*>}
   */
  queryMsgIds = async (sessionMsgMap) => {
    console.log('MessageDao queryMsgIds 开始', sessionMsgMap.size);
    const { imId } = userStore;
    const sqlArr = [];
    sessionMsgMap.forEach((arr, sessionId) => {
      sqlArr.push({
        sql: `SELECT seq AS msgId, ${sessionId} AS sessionId FROM ${getChatMessageTableName(
          imId,
          sessionId
        )} WHERE seq IN (${arr.map((it) => it.msg_id).join(',')})`,
        values: [],
      });
    });
    function handleResult(res) {
      return databaseUtil.getArray(res, false, (it) => `${it.sessionId}_${it.msgId}`);
    }
    const result = await sqliteService.batchQuery(sqlArr, handleResult, {
      createTableSqlFun: getCreateChatMessageTableSQL,
    });
    console.log('MessageDao queryMsgIds 完成', result.length);
    return result;
  };

  openDatabase = async (ownerId, sessionId) => {
    const db = await sqliteService.openDatabase();
    return databaseUtil.openDatabaseAndCheckTable(
      db,
      getChatMessageTableName(ownerId, sessionId),
      getCreateChatMessageTableSQL
    );
  };
}

export default new MessageDao();
