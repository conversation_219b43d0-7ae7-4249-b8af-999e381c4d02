import sqliteService from '../sqliteService';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';
import constant from '../../store/constant';

const tableName = 't_friend';

const fields = [
  'imId',
  'paymentUserId',
  'ownerId',
  'nickname',
  'memo',
  'phone',
  'countryCode',
  'account',
  'tags',
  'avatar',
  'isDisturb',
  'isBlacked',
  'beDeleted',
  'isHide',
  'relationType',
  'userType',
  'extra',
  'identityKey',
  'preKey',
  'signedPreKey',
  'signedPreKeyId',
  'signedPreKeySignature',
];

export function handleFriend(friend, isForce) {
  if (friend.account && (isForce || !friend.paymentUserId)) {
    friend.paymentUserId = friend.account.replace(/[a-zA-Z_]*/, '');
  }
}

function hideSQL(includeHide) {
  return includeHide ? '' : ' AND isHide!=1';
}

/**
 * 好友DAO
 */
class FriendDao {
  /**
   * 同步保存服务端好友数据
   * @param data {{insert: Friend[],update: Friend[],delete: Friend[]}}
   * @param ownerId
   * @return {Promise<void>}
   */
  saveFriends = async ({ data, ownerId = userStore.imId }) => {
    console.log('FriendDao saveFriends 开始', data);
    if (!data.insert.length && !data.update.length && !data.delete.length) {
      console.log('FriendDao saveFriends 完成 没有好友');
      return;
    }
    const db = await sqliteService.openDatabase();
    await db.transaction((tx) => {
      if (data.insert.length) {
        const sql = databaseUtil.getInsertSql({ tableName, fields });
        data.insert.forEach((friend) => {
          tx.executeSql(sql, databaseUtil.getInsertValues({ fields, data: friend }));
        });
      }
      if (data.update.length) {
        const sql =
          'UPDATE t_friend SET nickname=?, avatar=?, memo=?, phone=?, countryCode=?, paymentUserId=?, account=?, tags=?, beDeleted=?, isHide=?, relationType=?, extra=? WHERE imId=? AND ownerId=?';
        data.update.forEach((friend) => {
          tx.executeSql(sql, [
            friend.nickname,
            friend.avatar,
            friend.memo,
            friend.phone,
            friend.countryCode,
            friend.paymentUserId,
            friend.account,
            friend.tags,
            friend.beDeleted,
            friend.isHide,
            friend.relationType,
            friend.extra,
            friend.imId,
            ownerId,
          ]);
        });
      }
      if (data.delete.length) {
        const imIds = data.delete
          .filter((item) => !item.isHide)
          .map((item) => {
            item.beDeleted = 1;
            return item.imId;
          })
          .join(',');
        tx.executeSql(`UPDATE t_friend SET beDeleted=1 WHERE imId IN (${imIds}) AND ownerId=?`, [
          ownerId,
        ]);
      }
    });
    console.log('FriendDao saveFriends 完成');
  };

  /**
   * 保存更新好友信息
   * @param friend {Friend} 好友
   */
  saveFriend = async (friend) => {
    console.log('FriendDao saveFriend 开始', friend);
    if (!friend.action) {
      const local = await this.getFriend(friend);
      friend.action = local ? 'update' : 'insert';
    }
    if (friend.action === 'insert') {
      const db = await sqliteService.openDatabase();
      const sql = databaseUtil.getInsertSql({ tableName, fields });
      await db.executeSql(sql, databaseUtil.getInsertValues({ fields, data: friend }));
    } else {
      await this.updateFriend(friend);
    }
    console.log('FriendDao saveFriend 完成');
  };

  /**
   * 修改好友信息
   * @param friend {Friend}
   */
  updateFriend = async (friend) => {
    console.log('FriendDao updateFriend 开始', friend);
    await sqliteService.executeSql(
      `UPDATE t_friend SET nickname=?, memo=?, phone=?, countryCode=?, avatar=?, isDisturb=?, isBlacked=?, beDeleted=?, isHide=?, relationType=?, account=?, paymentUserId=?, tags=?, extra=? WHERE ownerId=? AND imId=?`,
      [
        friend.nickname,
        friend.memo,
        friend.phone,
        friend.countryCode,
        friend.avatar,
        friend.isDisturb,
        friend.isBlacked,
        friend.beDeleted,
        friend.isHide,
        friend.relationType,
        friend.account,
        friend.paymentUserId,
        friend.tags,
        friend.extra,
        friend.ownerId,
        friend.imId,
      ]
    );
    console.log('FriendDao updateFriend 完成');
  };

  /**
   * 获取好友
   * @return {Promise<Friend>}
   */
  getFriend = async ({ ownerId = userStore.imId, imId, includeHide = true }) => {
    console.log('FriendDao getFriend 开始');
    let res = await sqliteService.executeSql(
      `SELECT * FROM t_friend WHERE ownerId=? AND imId=?${hideSQL(includeHide)}`,
      [ownerId, imId]
    );
    res = databaseUtil.getOne(res);
    console.log('FriendDao getFriend 完成', res);
    return res;
  };

  /**
   * 通过支付用户ID查找好友
   * @return {Promise<Friend>}
   */
  getFriendByPaymentUserId = async ({ ownerId = userStore.imId, paymentUserId, includeHide }) => {
    console.log('FriendDao getFriendByPaymentUserId 开始');
    let res = await sqliteService.executeSql(
      `SELECT * FROM t_friend WHERE ownerId=? AND paymentUserId=?${hideSQL(includeHide)}`,
      [ownerId, paymentUserId]
    );
    res = databaseUtil.getOne(res);
    console.log('FriendDao getFriendByPaymentUserId 完成', res);
    return res;
  };

  /**
   * 获取好友列表
   * @return {Promise<Friend[]>}
   */
  getAllFriends = async ({ ownerId, includeHide, excludeSelf }) => {
    console.log('FriendDao getAllFriends 开始', ownerId);
    const values = [ownerId];
    let sql = hideSQL(includeHide);
    if (excludeSelf) {
      sql += ' AND imId!=?';
      values.push(ownerId);
    }
    const res = await sqliteService.executeSql(
      `SELECT * FROM ${tableName} WHERE ownerId=? ${sql}`,
      values
    );
    const result = databaseUtil.getArray(res);
    console.log('FriendDao getAllFriends 完成', result);
    return result;
  };

  deleteFriend = async ({ ownerId = userStore.imId, imId, isDelete }) => {
    console.log('FriendDao deleteFriend 开始', imId);
    if (isDelete) {
      await sqliteService.executeSql(`DELETE FROM t_friend WHERE ownerId=? AND imId=?`, [
        ownerId,
        imId,
      ]);
    } else {
      await sqliteService.executeSql(`UPDATE t_friend SET beDeleted=1 WHERE ownerId=? AND imId=?`, [
        ownerId,
        imId,
      ]);
    }
    console.log('FriendDao deleteFriend 完成', imId);
  };

  /**
   * 搜索好友
   * @return {Promise<Friend[]>}
   */
  queryFriends = async ({ ownerId = userStore.imId, searchValue, limit, includeHide }) => {
    console.log('FriendDao queryFriends 开始', ownerId);
    searchValue = `%${searchValue}%`;
    const res = await sqliteService.executeSql(
      `SELECT * FROM t_friend WHERE ownerId=? AND (nickname LIKE ? OR memo LIKE ?) ${hideSQL(
        includeHide
      )} LIMIT ?`,
      [ownerId, searchValue, searchValue, limit]
    );
    const result = databaseUtil.getArray(res);
    console.log('FriendDao queryFriends 完成', result);
    return result;
  };

  /**
   * 获取管理员类型的用户id
   */
  getManageImIds = async () => {
    console.log('FriendDao getManageImIds 开始');
    const res = await sqliteService.executeSql(
      `SELECT imId FROM t_friend WHERE ownerId=? AND userType=?`,
      [userStore.imId, constant.userType.manage]
    );
    const result = databaseUtil.getArray(res);
    console.log('FriendDao getManageImIds 完成', result);
    return result;
  };
}

export default new FriendDao();
