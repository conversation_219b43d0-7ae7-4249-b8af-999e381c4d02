import sqliteService from '../sqliteService';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';

const tableName = 't_friend_req';

/**
 * 联系人相关DAO
 */
class ContactDao {
  /**
   * 添加联系人申请
   * @param model 联系人数据，需额外包含：ownerId(当前用户的imId)
   */
  insertNewContact = async (model) => {
    console.log('ContactDao insertNewContact 开始');
    if (!model.ownerId) {
      logger.warn(`contactDao insertNewContact 缺少 ownerId: ${model.ownerId}`);
      return;
    }
    const db = await this.openDatabase();
    const fields = [
      'imId',
      'ownerId',
      'nickname',
      'avatar',
      'status',
      'message',
      'requestAt',
      'notifyId',
      'isRead',
      'addBy',
      'addFrom',
      'isBlacked',
      'reqId',
      'isSelfReq',
      'isDeleteBy',
    ];
    await databaseUtil.insert({ db, tableName, fields, data: model });
    console.log('ContactDao insertNewContact 完成', model);
  };

  /**
   * 收到发送成功的回执消息时，更新状态
   */
  updateContact = async (model) => {
    console.log('ContactDao updateContact 开始');

    const db = await this.openDatabase(tableName);
    const updateFields = [
      'status',
      'isSelfReq',
      'isDeleteBy',
      'nickname',
      'avatar',
      'requestAt',
      'addFrom',
      'addBy',
      'isBlacked',
      'reqId',
      'isRead',
      'notifyId',
    ];
    if (model.message) {
      updateFields.push('message');
    }
    const { updateFieldSql, values } = databaseUtil.getUpdateFieldSql({
      fields: updateFields,
      data: model,
    });
    values.push(model.imId);
    values.push(userStore.imId);
    await db.executeSql(
      `UPDATE t_friend_req SET ${updateFieldSql} WHERE imId=? AND ownerId=? `,
      values
    );
    console.log('ContactDao updateContact 完成');
  };

  updateContactStatus = async ({ status, imId, ownerId = userStore.imId }) => {
    console.log('ContactDao updateContactStatus 开始');
    await sqliteService.executeSql(`UPDATE t_friend_req SET status=? WHERE imId=? AND ownerId=? `, [
      status,
      imId,
      ownerId,
    ]);
    console.log('ContactDao updateContactStatus 完成');
  };

  /**
   * 删除好友
   */
  deleteContact = async (model) => {
    const db = await this.openDatabase(tableName);
    await db.executeSql('DELETE FROM t_friend_req WHERE imId=? AND ownerId=?', [
      model.imId,
      userStore.imId,
    ]);
  };

  /**
   * 查询单个好友
   */
  findContact = async ({ imId }) => {
    const db = await this.openDatabase(tableName);
    const res = await db.executeSql('SELECT * FROM t_friend_req WHERE imId=? AND ownerId=?', [
      imId,
      userStore.imId,
    ]);
    return databaseUtil.getOne(res);
  };

  /**
   * 查询好友申请列表
   */
  queryReqContactList = async ({ limit }) => {
    console.log('ContactDao queryReqContactList 开始');
    const db = await this.openDatabase();
    const res = await db.executeSql(
      'SELECT req.*, tf.relationType FROM t_friend_req req LEFT JOIN t_friend tf ON req.ownerId = tf.ownerId AND req.imId = tf.imId WHERE req.ownerId=? ORDER BY req.requestAt LIMIT ?',
      [userStore.imId, limit]
    );
    const result = databaseUtil.getArray(res, true, (item) => {
      if (item.relationType === 1) {
        item.status = 1;
        this.updateContactStatus(item);
      }
      return item;
    });
    console.log('ContactDao queryReqContactList 完成', result);
    return result;
  };

  /**
   * 全部标记已读
   */
  allMarkRead = async () => {
    console.log('ContactDao allMarkRead 开始');
    const db = await this.openDatabase();
    await db.executeSql('UPDATE t_friend_req SET isRead=1 WHERE ownerId=?', [userStore.imId]);
    console.log('ContactDao allMarkRead 完成');
  };

  openDatabase = async () => {
    return sqliteService.openDatabase();
  };

  /**
   * 获取未读数量
   */
  totalUnReadNum = async ({ ownerId = userStore.imId } = {}) => {
    console.log('ContactDao totalUnReadNum 开始', ownerId);
    let res = await sqliteService.executeSql(
      `SELECT COUNT(*) AS count FROM t_friend_req WHERE ownerId=? AND isSelfReq=1 AND status=0 AND isRead=0`,
      [ownerId]
    );
    res = databaseUtil.getOne(res);
    console.log('ContactDao totalUnReadNum 完成', res);
    return res.count;
  };
}

export default new ContactDao();
