import sqliteService from '../sqliteService';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';
import constant from '../../store/constant';

const tableName = 't_broadcast';

const fields = ['ownerId', 'lastMsgTime', 'userIds', 'userNames', 'content', 'type'];

/**
 * 广播DAO
 */
class ChatBroadcastDao {
  /**
   * 新增广播
   * @param {Broadcast} broadcast
   */
  addBroadcast = async (broadcast) => {
    console.log('ChatBroadcastDao addBroadcast 开始', broadcast);
    const db = await sqliteService.openDatabase();
    await databaseUtil.insert({
      db,
      tableName,
      fields,
      data: {
        ...broadcast,
        ownerId: userStore.imId,
        type: constant.messageType.text,
        userIds: JSON.stringify(broadcast.userIds),
        userNames: JSON.stringify(broadcast.userNames),
      },
    });
    console.log('ChatBroadcastDao addBroadcast 完成');
  };

  /**
   * 获取广播列表
   * @return {Promise<Broadcast[]>}
   */
  getBroadcasts = async ({ ownerId = userStore.imId, id, limit }) => {
    console.log('ChatBroadcastDao getBroadcasts 开始', ownerId);
    const res = await sqliteService.executeSql(
      `SELECT * FROM ${tableName} WHERE ownerId=? AND id < ? AND type=? ORDER BY id DESC LIMIT ?`,
      [ownerId, id, constant.messageType.text, limit]
    );
    const result = databaseUtil.getArray(res, false, (item) => {
      item.userIds = JSON.parse(item.userIds);
      item.userNames = JSON.parse(item.userNames);
      return item;
    });
    console.log('ChatBroadcastDao getBroadcasts 完成', result);
    return result;
  };

  /**
   * 删除广播
   */
  deleteBroadcast = async (id) => {
    console.log('ChatBroadcastDao deleteBroadcast 开始');
    await sqliteService.executeSql(`DELETE FROM ${tableName} WHERE id=?`, [id]);
    console.log('ChatBroadcastDao deleteBroadcast 完成');
  };
}

export default new ChatBroadcastDao();
