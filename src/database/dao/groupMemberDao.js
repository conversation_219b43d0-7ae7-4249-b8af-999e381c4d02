import { getCreateGroupMemberTableSQL, getGroupMemberTableName } from '../initSQL';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';
import sqliteService from '../sqliteService';

const fields = [
  'imId',
  'nickname',
  'memo',
  'avatar',
  'role',
  'isDisturb',
  'noSpeaking',
  'joinAt',
  'isDelete',
];

/**
 * 群成员DAO
 */
class GroupMemberDao {
  /**
   * 批量保存更新群成员信息
   */
  saveGroupMembers = async ({ data, ownerId, groupId }) => {
    console.log('GroupMemberDao saveGroupMembers 开始', data);
    if (!data.insert.length && !data.update.length && !data.delete.length) {
      console.log('GroupMemberDao saveGroupMembers 完成 没有群成员');
      return;
    }
    const { db, tableName } = await this.openDatabase(ownerId, groupId);
    await db.transaction((tx) => {
      if (data.insert.length) {
        const sql = databaseUtil.getInsertSql({ tableName, fields });
        data.insert.forEach((groupMember) => {
          tx.executeSql(sql, databaseUtil.getInsertValues({ fields, data: groupMember }));
        });
      }
      if (data.update.length) {
        const sql = `UPDATE ${tableName} SET nickname=?, avatar=?, memo=?, role=?, joinAt=?, isDelete=0 WHERE imId=?`;
        data.update.forEach((groupMember) => {
          groupMember.isDelete = 0;
          tx.executeSql(sql, [
            groupMember.nickname,
            groupMember.avatar,
            groupMember.memo,
            groupMember.role,
            groupMember.joinAt,
            groupMember.imId,
          ]);
        });
      }
      if (data.delete.length) {
        const imIds = data.delete
          .map((item) => {
            item.isDelete = 1;
            return item.imId;
          })
          .join(',');
        tx.executeSql(`UPDATE ${tableName} SET isDelete=1 WHERE imId IN (${imIds})`);
      }
    });
    console.log('GroupMemberDao saveGroupMembers 完成');
  };

  /**
   * 获取群成员列表
   * @return {Promise<GroupMember[]>}
   */
  getGroupMembers = async ({ ownerId, groupId, isAll }) => {
    console.log('GroupMemberDao getGroupMembers 开始', ownerId, groupId);
    const { db, tableName } = await this.openDatabase(ownerId, groupId);
    const res = await db.executeSql(
      `SELECT *, ${groupId} AS groupId, ${ownerId} AS ownerId FROM ${tableName}${
        isAll ? '' : ' WHERE isDelete=0'
      }`
    );
    const result = databaseUtil.getArray(res);
    console.log('GroupMemberDao getGroupMembers 完成', result);
    return result;
  };

  /**
   * 获取群成员
   * @return {Promise<GroupMember>}
   */
  getGroupMember = async ({ ownerId = userStore.imId, groupId, imId }) => {
    console.log('GroupMemberDao getGroupMember 开始', ownerId, groupId, imId);
    const { db, tableName } = await this.openDatabase(ownerId, groupId);
    const res = await db.executeSql(
      `SELECT *, ${groupId} AS groupId, ${ownerId} AS ownerId FROM ${tableName} WHERE imId=?`,
      [imId]
    );
    const result = databaseUtil.getOne(res);
    console.log('GroupMemberDao getGroupMember 完成', result);
    return result;
  };

  /**
   * 清空群成员
   */
  cleanGroupMember = async ({ ownerId = userStore.imId, groupId }) => {
    console.log('GroupMemberDao cleanGroupMember 开始', ownerId, groupId);
    const { db, tableName } = await this.openDatabase(ownerId, groupId);
    await db.executeSql(`DELETE FROM ${tableName}`);
    console.log('GroupMemberDao cleanGroupMember 完成');
  };

  openDatabase = async (ownerId, groupId) => {
    const db = await sqliteService.openDatabase();
    return databaseUtil.openDatabaseAndCheckTable(
      db,
      getGroupMemberTableName(ownerId, groupId),
      getCreateGroupMemberTableSQL
    );
  };
}

export default new GroupMemberDao();
