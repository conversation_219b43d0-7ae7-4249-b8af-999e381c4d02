import sqliteService from '../sqliteService';
import databaseUtil from '../databaseUtil';
import userStore from '../../store/stores/user';
import constant from '../../store/constant';

const tableName = 't_group';

const fields = [
  'groupId',
  'groupOwnerId',
  'creatorId',
  'ownerId',
  'title',
  'avatar',
  'isPrivate',
  'count',
  'memberHash',
  'isDisturb',
  'setTopTime',
  'notice',
  'noticeTime',
  'canInvite',
  'joinType',
  'canAddFriend',
  'updateTime',
  'noSpeaking',
  'memberVisible',
  'enableMedia',
  'isVip',
  'isDisbanded',
];

const updateFields = [
  'groupOwnerId',
  'title',
  'avatar',
  'isPrivate',
  'count',
  'memberHash',
  'isDisturb',
  'setTopTime',
  'notice',
  'noticeTime',
  'canInvite',
  'joinType',
  'canAddFriend',
  'updateTime',
  'noSpeaking',
  'memberVisible',
  'enableMedia',
  'isVip',
  'isDisbanded',
];

/**
 * 群聊DAO
 */
class ChatGroupDao {
  /**
   * 批量保存更新群聊
   */
  saveGroups = async (data) => {
    console.log('ChatGroupDao saveGroups 开始', data);
    if (!data.insert.length && !data.update.length && !data.delete.length) {
      console.log('ChatGroupDao saveGroups 完成 没有群聊');
      return;
    }
    const db = await sqliteService.openDatabase();
    await db.transaction((tx) => {
      if (data.insert.length) {
        const sql = databaseUtil.getInsertSql({ tableName, fields });
        data.insert.forEach((chatGroup) => {
          tx.executeSql(sql, databaseUtil.getInsertValues({ fields, data: chatGroup }));
        });
      }
      if (data.update.length) {
        const sql = `UPDATE ${tableName} SET title=?, avatar=?, count=?, isVip=?, memberHash=? WHERE ownerId=? AND groupId=?`;
        data.update.forEach((chatGroup) => {
          tx.executeSql(sql, [
            chatGroup.title,
            chatGroup.avatar,
            chatGroup.count,
            chatGroup.isVip,
            chatGroup.memberHash,
            chatGroup.ownerId,
            chatGroup.groupId,
          ]);
        });
      }
      if (data.delete.length) {
        const groupIds = data.delete.map((item) => item.groupId).join(',');
        tx.executeSql(`DELETE FROM t_group WHERE ownerId=? AND groupId IN (${groupIds})`, [
          data.delete[0].ownerId,
        ]);
      }
    });
    console.log('ChatGroupDao saveGroups 完成');
  };

  updateGroup = async (chatGroup) => {
    console.log('ChatGroupDao updateGroup 开始', chatGroup);
    chatGroup.updateTime = new Date().getTime();
    const db = await sqliteService.openDatabase();
    const { updateFieldSql, values } = databaseUtil.getUpdateFieldSql({
      fields: updateFields,
      data: chatGroup,
    });
    values.push(chatGroup.ownerId);
    values.push(chatGroup.groupId);
    await db.executeSql(
      `UPDATE t_group SET ${updateFieldSql} WHERE ownerId=? AND groupId=?`,
      values
    );
    global.emitter.emit(constant.event.chatSessionChange, {});
    console.log('ChatGroupDao updateGroup 完成');
  };

  /**
   * 新增群聊
   */
  addGroup = async (chatGroup) => {
    console.log('ChatGroupDao addGroup 开始', chatGroup);
    chatGroup.updateTime = new Date().getTime();
    const db = await sqliteService.openDatabase();
    await databaseUtil.insert({ db, tableName, fields, data: chatGroup });
    console.log('ChatGroupDao addGroup 完成');
  };

  /**
   * 获取群聊列表
   * @return {Promise<ChatGroup[]>}
   */
  getGroups = async ({ ownerId = userStore.imId }) => {
    console.log('ChatGroupDao getGroups 开始', ownerId);
    const res = await sqliteService.executeSql('SELECT * FROM t_group WHERE ownerId = ?', [
      ownerId,
    ]);
    const result = databaseUtil.getArray(res);
    console.log('ChatGroupDao getGroups 完成', result);
    return result;
  };

  /**
   * 获取群聊
   * @return {Promise<ChatGroup>}
   */
  getGroup = async ({ ownerId = userStore.imId, groupId }) => {
    console.log('ChatGroupDao getGroup 开始');
    let res = await sqliteService.executeSql(
      'SELECT * FROM t_group WHERE ownerId = ? AND groupId = ?',
      [ownerId, groupId]
    );
    res = databaseUtil.getOne(res);
    console.log('ChatGroupDao getGroup 完成', res);
    return res;
  };

  /**
   * 删除群聊
   */
  deleteGroup = async ({ ownerId = userStore.imId, groupId }) => {
    console.log('ChatGroupDao deleteGroup 开始');
    await sqliteService.executeSql('DELETE FROM t_group WHERE ownerId=? AND groupId=?', [
      ownerId,
      groupId,
    ]);
    console.log('ChatGroupDao deleteGroup 完成');
  };

  /**
   * 搜索群聊
   * @return {Promise<ChatGroup[]>}
   */
  queryGroups = async ({ ownerId = userStore.imId, searchValue, limit }) => {
    console.log('ChatGroupDao queryGroups 开始', ownerId);
    searchValue = `%${searchValue}%`;
    const res = await sqliteService.executeSql(
      'SELECT * FROM t_group WHERE ownerId = ? AND title LIKE ? LIMIT ?',
      [ownerId, searchValue, limit]
    );
    const result = databaseUtil.getArray(res);
    console.log('ChatGroupDao queryGroups 完成', result);
    return result;
  };
}

export default new ChatGroupDao();
