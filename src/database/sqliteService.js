import SQLite from 'react-native-sqlite-storage';
import { getCreateTableSQL } from './initSQL';
import databaseUtil from './databaseUtil';
import promiseUtil from '../util/promiseUtil';

SQLite.DEBUG(__DEV__);
SQLite.enablePromise(true);

const databaseName = 'my.db';
const databaseVersion = '1.0'; // 这个版本号不用改
const databaseDisplayName = 'my db';
const databaseSize = 200000;

const currentVersion = 15; // 数据库升级使用

/**
 * SQLite服务
 */
class SqliteService {
  openDatabase = async () => {
    let { db } = this;
    if (db) return db;
    console.log('SqliteService openDatabase', this.openDatabaseLock);
    if (this.openDatabaseLock) {
      await promiseUtil.sleep(200);
      return this.openDatabase();
    }
    this.openDatabaseLock = true;
    try {
      db = await SQLite.openDatabase(
        databaseName,
        databaseVersion,
        databaseDisplayName,
        databaseSize
      );
      console.log('SqliteService openDatabase', db);
      if (!this.isCheckVersion) {
        await this.checkVersion(db);
        this.isCheckVersion = true;
      }
      this.db = db;
      this.openDatabaseLock = false;
      return db;
    } catch (e) {
      this.openDatabaseLock = false;
      logger.warn('SqliteService openDatabase', e);
      throw new Error('初始化数据库失败');
    }
  };

  closeDatabase = async () => {
    const { db } = this;
    console.log('SqliteService closeDatabase 开始', db);
    this.db = null;
    // if (db) {
    //   await db.close();
    // }
    console.log('SqliteService closeDatabase 完成');
  };

  /**
   * 执行SQL
   * @param sql
   * @param values
   * @return {Promise<*>} [{rows:{item}}]
   */
  executeSql = async (sql, values) => {
    const db = await this.openDatabase();
    return db.executeSql(sql, values);
  };

  /**
   * 批量执行查询SQL
   */
  batchQuery = async (arr, handleResult, options) => {
    if (!arr?.length) return Promise.resolve([]);
    const db = await this.openDatabase();
    if (options?.createTableSqlFun) {
      await databaseUtil.checkAndCreateTableByBatchQuery(db, arr, options);
    }
    return new Promise((resolve) => {
      const res = new Array(arr.length);
      let count = 0;
      db.transaction((tx) => {
        arr.forEach((item, index) => {
          tx.executeSql(
            item.sql,
            item.values,
            (rx, results) => {
              console.log('batchQuery res', index, results);
              res[index] = {
                value: handleResult ? handleResult(results, index) : results,
                status: 'fulfilled',
              };
              if (++count >= arr.length) {
                resolve(res);
              }
            },
            (e) => {
              console.warn('batchQuery error', index, e);
              res[index] = {
                reason: e,
                status: 'rejected',
              };
              if (++count >= arr.length) {
                resolve(res);
              }
            }
          );
        });
      });
    });
  };

  /**
   * 检查数据库版本
   */
  checkVersion = async (db) => {
    console.log('SqliteService checkVersion 开始');
    const isNotExist = await databaseUtil.isNotExistTable(db, 't_version');
    if (isNotExist) {
      await this.onCreate(db);
      console.log('SqliteService checkVersion 完成');
      return;
    }
    const res = await db.executeSql('SELECT version FROM t_version LIMIT 1');
    const oldVersion = res[0].rows.item(0).version;
    console.log('SqliteService checkVersion oldVersion', oldVersion, currentVersion);
    if (oldVersion < currentVersion) {
      await this.onUpgrade(db, oldVersion);
    }
    console.log('SqliteService checkVersion 完成');
  };

  /**
   * 数据库创建时，初始化表和数据
   */
  onCreate = async (db) => {
    console.log('SqliteService onCreate 开始');
    await db.transaction((tx) => {
      console.log('SqliteService onCreate 事务开始');
      getCreateTableSQL().forEach((item) => {
        tx.executeSql(item.sql);
      });
      tx.executeSql('INSERT INTO t_version (version) VALUES (?);', [currentVersion]);
      console.log('SqliteService onCreate 事务完成');
    });
    console.log('SqliteService onCreate 完成');
  };

  /**
   * 数据库升级时，新增修改表
   */
  onUpgrade = async (db, oldVersion) => {
    console.log('SqliteService onUpgrade 开始', oldVersion, currentVersion);
    await db.executeSql('UPDATE t_version SET version=?', [currentVersion]);
    console.log('SqliteService onUpgrade 完成', oldVersion, currentVersion);
  };
}

export default new SqliteService();
