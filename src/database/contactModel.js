/**
 * 联系人申请数据模型
 * <AUTHOR>
 */
export default class ContactModel {
  imId = 0;

  ownerId = 0;

  nickname = null;

  avatar = null;

  status = 1; // 0等待验证 1已同意 2已拒绝

  message = '';

  requestAt = 0; // 请求时间

  notifyId = 0;

  isRead = 0;

  addBy = '';

  addFrom = '';

  isBlacked = 0;

  reqId = 0; // 请求设备id+timestamp

  isSelfReq = 0; // 0 我发起的  1 别人发起的

  isDeleteBy = 0; // 是否被好友删除 0未删除  1被删除过
}
