import constant from '../store/constant';
import sendMessageUtil from './sendMessageUtil';
import { Keyboard, Clipboard } from '../components';
import navigationService from '../navigationService';
import I18n from '../i18n';
import Configs from '../configs';

const hideReadTypes = [
  constant.messageType.call,
  constant.messageType.redPacket,
  constant.messageType.redPacketAccept,
  constant.messageType.transfer,
  constant.messageType.groupTransfer,
  constant.messageType.transferReturn,
  constant.messageType.transferAccept,
  constant.messageType.receiveCreate,
  constant.messageType.receivePaid,
];

/**
 * 聊天消息长按操作
 * <AUTHOR>
 */
class ChatMessageOptions {
  isSupportCopy = (message) => {
    return (
      message.type === constant.messageType.text || message.type === constant.messageType.referTo
    );
  };

  onCopy = (message) => {
    Clipboard.setString(message.text);
  };

  /**
   * 是否支持撤回
   */
  isSupportWithdraw = (message, memberMap, groupInfo, session) => {
    if (
      session.isGPT ||
      !message.isSelfSend ||
      message.isGroup ||
      message.type === constant.messageType.redPacket ||
      message.type === constant.messageType.receiveCreate ||
      message.type === constant.messageType.transfer
    )
      return false;
    // 3分钟内的支持撤回
    const remind = new Date().getTime() - message.msgTime;
    return remind > 0 && remind < 180000;
  };

  /**
   * 撤回
   */
  onWithdraw = async (message) => {
    try {
      await sendMessageUtil.sendWithdraw(message);
    } catch (e) {
      logger.warn('onWithdraw', e, message);
      if (e?.message) {
        toast.show(e.message);
      }
    }
  };

  /**
   * 是否显示已读状态
   */
  isShowRead = (message, isGroup) => {
    if (Configs.disableMessageRead(isGroup)) return false;
    return message.isSelfSend && !hideReadTypes.includes(message.type);
  };

  /**
   * 是否显示查看已读列表
   */
  isSupportViewRead = (message, memberMap, groupInfo, session) => {
    if (session.isGPT) return false;
    return this.isShowRead(message, groupInfo) && message.sessionId !== message.ownerId;
  };

  /**
   * 查看已读列表
   */
  onViewRead = async (message, memberMap, groupInfo, session) => {
    navigationService.navigate('messageReadList', { message, memberMap, session });
  };

  /**
   * 是否支持转发
   */
  isSupportForward = ({ type }) => {
    // TODO 暂时禁用转发，需修改转发列表页面
    return false;
    /*return !(
      type === constant.messageType.audio ||
      type === constant.messageType.redPacket ||
      type === constant.messageType.redPacketAccept ||
      type === constant.messageType.receiveCreate ||
      type === constant.messageType.receivePaid ||
      type === constant.messageType.transfer ||
      type === constant.messageType.transferReturn ||
      type === constant.messageType.transferAccept ||
      type === constant.messageType.groupTransfer ||
      type === constant.messageType.call
    );*/
  };

  /**
   * 转发
   */
  onForward = async (message) => {
    navigationService.navigate('chatForwardSelectList', { message });
  };

  /**
   * 是否支持引用/回复
   */
  isSupportQuote = ({ type, msgId }, memberMap, groupInfo, session) => {
    return !(
      !msgId ||
      session?.isGPT ||
      type === constant.messageType.audio ||
      type === constant.messageType.redPacket ||
      type === constant.messageType.redPacketAccept ||
      type === constant.messageType.receiveCreate ||
      type === constant.messageType.receivePaid ||
      type === constant.messageType.transfer ||
      type === constant.messageType.transferReturn ||
      type === constant.messageType.transferAccept ||
      type === constant.messageType.groupTransfer ||
      type === constant.messageType.call
    );
  };

  /**
   * 引用/回复
   */
  onQuote = async (message) => {
    global.emitter.emit(constant.event.quoteMessage, { message });
  };

  isNoSpeaking = (message, memberMap, groupInfo) => {
    if (message.isGroup && groupInfo && memberMap) {
      const member = memberMap.get(message.ownerId);
      const isMember = member?.role === 'member';
      return isMember && (groupInfo.noSpeaking || member.noSpeaking);
    }
    return false;
  };

  /**
   * 长按显示可用操作
   */
  msgLongPressOptions = (giftedChat, message, memberMap, groupInfo, session) => {
    console.log('showMsgOptions', message);
    const options = [
      {
        title: I18n.t('page_MessageReadList_title'),
        isSupport: this.isSupportViewRead,
        onPress: this.onViewRead,
      },
      {
        title: I18n.t('page_chat_option_label_forward'),
        isSupport: this.isSupportForward,
        onPress: this.onForward,
        checkNoSpeaking: true,
      },
      {
        title: I18n.t('page_chat_option_label_copy'),
        isSupport: this.isSupportCopy,
        onPress: this.onCopy,
      },
      {
        title: I18n.t('page_chat_option_label_withdraw'),
        isSupport: this.isSupportWithdraw,
        onPress: this.onWithdraw,
      },
      {
        title: I18n.t('page_chat_option_label_quote'),
        isSupport: this.isSupportQuote,
        onPress: this.onQuote,
        checkNoSpeaking: true,
      },
    ];
    const availableOptions = options.filter((item) => {
      if (item.checkNoSpeaking && this.isNoSpeaking(message, memberMap, groupInfo)) {
        return false;
      }
      return item.isSupport(message, memberMap, groupInfo, session);
    });
    if (!availableOptions.length) return;
    Keyboard.dismiss();
    const optionTitles = availableOptions.map((item) => item.title);
    optionTitles.push(I18n.t('op_cancel_title'));
    console.log('msgLongPressOptions', optionTitles);
    giftedChat
      .getChildContext()
      .actionSheet()
      .showActionSheetWithOptions(
        {
          options: optionTitles,
          cancelButtonIndex: optionTitles.length - 1,
        },
        (index) => {
          if (index < availableOptions.length) {
            availableOptions[index].onPress(message, memberMap, groupInfo, session);
          }
        }
      );
  };
}

export default new ChatMessageOptions();
