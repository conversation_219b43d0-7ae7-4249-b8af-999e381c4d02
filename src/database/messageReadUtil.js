import messageDao from './dao/messageDao';
import constant from '../store/constant';
import sentryUtil from '../util/sentryUtil';
import ChatMessageReadExtra from './chatMessageReadExtra';
import userStore from '../store/stores/user';
import Configs from '../configs';
import chatStore from '../store/stores/chatStore';
import CollectionUtil from '../util/collectionUtil';
import chatMessageUtil from './chatMessageUtil';
import chatSessionDao from './dao/chatSessionDao';
import chatAction from '../store/actions/chatAction';

/**
 * 消息已读工具类
 * <AUTHOR>
 */
class MessageReadUtil {
  /**
   * 收到消息已读通知，支持批量处理
   * {"event":"msg_p2p","data":{"sender_id":10103,"receiver_id":10102,"msg":{"data":{"im_id":10103,"notice_type":"MessageRead","rim_id":10102,"timestamp":1634810114045},"type":"notice"},"msg_id":1634810113843310,"msg_time":1634810113843,"send_self":2}}
   */
  onMessageRead = async (dataArr, isReceive) => {
    try {
      if (!Array.isArray(dataArr)) {
        dataArr = [dataArr];
      }
      let readArr = [];
      const oldReadMap = new Map();
      const { imId } = userStore;
      for (const data of dataArr) {
        if (Configs.disableMessageRead(data.msg.data.group_id)) continue;
        if (data.sender_id === imId || data.receiver_id !== imId) continue;

        if (!data.msg.data.msg_ids) {
          // 这里主要兼容旧版私聊
          data.sessionId = data.msg.data.group_id || data.msg.data.im_id;
          let value = oldReadMap.get(data.sessionId);
          console.log(
            'MessageReadUtil onMessageRead 兼容旧版',
            data.msg.data.timestamp,
            value?.msg.data.timestamp
          );
          if (!value || data.msg.data.timestamp > value.msg.data.timestamp) {
            data.msg.data.msg_ids = [];
            oldReadMap.set(data.sessionId, data);
          }
          if (isReceive) {
            chatMessageUtil.cacheSocketMsg(data);
          }
          continue;
        }

        data.msg.data.msg_ids.forEach((msgId) => readArr.push({ data, msgId, isReceive }));
      }
      if (readArr.length) {
        this.updateMessageReadExtraByDelay(readArr);
      }

      if (!oldReadMap.size) {
        return;
      }
      const oldParam = [...oldReadMap.values()].map((data) => {
        return {
          sessionId: data.msg.data.group_id || data.msg.data.im_id,
          msgTime: data.msg.data.timestamp,
        };
      });
      const batchRes = await messageDao.getUnreadMessageIdList(oldParam);
      batchRes.forEach((res) => {
        // 查询失败的
        if (res.reason) {
          console.warn('MessageReadUtil onMessageRead 查询失败', res.reason);
          return;
        }
        res.value.forEach((it) => {
          oldReadMap.get(it.sessionId).msg.data.msg_ids.push(it.msgId);
        });
      });
      readArr = [];
      oldReadMap.forEach((data) => {
        if (data.msg.data.msg_ids.length) {
          readArr.push({
            data,
            msgId: data.msg.data.msg_ids[0],
            msgIds: data.msg.data.msg_ids,
          });
        }
      });
      console.log('MessageReadUtil onMessageRead old readArr.length', readArr.length);
      if (readArr.length) {
        this.updateMessageReadExtraByDelay(readArr);
      }
    } catch (e) {
      console.warn('ReceiveMessageUtil onMessageRead', e);
    }
  };

  batchTask = {
    data: [],
    batchTime: Date.now(),
    isProcessing: false, // 是否处理中
    delay: 500,
    delayTask: null,
  };

  /**
   * 延迟500ms批量更新已读
   */
  updateMessageReadExtraByDelay = async (param) => {
    const time = Date.now();
    // 如果任务列表为空，这是第一个任务，需等待一段时间再执行
    if (!this.batchTask.data.length) {
      this.batchTask.batchTime = time;
    }
    if (param) {
      if (param.data) {
        this.batchTask.data.push(param);
      } else if (Array.isArray(param) && param.length) {
        this.batchTask.data = this.batchTask.data.concat(param);
      }
    }
    if (this.batchTask.isProcessing || !this.batchTask.data.length) {
      return;
    }
    const remain = this.batchTask.delay - (time - this.batchTask.batchTime);
    if (remain > 0) {
      if (!this.batchTask.delayTask) {
        this.batchTask.delayTask = setTimeout(() => {
          this.batchTask.delayTask = null;
          this.updateMessageReadExtraByDelay();
        }, remain);
      }
      return;
    }
    if (this.batchTask.delayTask) {
      clearTimeout(this.batchTask.delayTask);
      this.batchTask.delayTask = null;
    }
    this.batchTask.batchTime = time;
    this.batchTask.isProcessing = true;
    const dataArr = this.batchTask.data;
    this.batchTask.data = [];

    await this.updateMessageReadExtraByList(dataArr);

    this.batchTask.isProcessing = false;
    this.updateMessageReadExtraByDelay();
  };

  updateMessageReadExtraByList = async (dataArr) => {
    const time = Date.now();
    try {
      const sessionMap = new Map();
      let session;
      dataArr.forEach((it) => {
        // let { data, msgId, msgIds } = it;
        // const senderId = data.sender_id; // 发送已读消息的人
        const isGroup = !!it.data.msg.data.group_id;
        const sessionId = isGroup ? it.data.msg.data.group_id : it.data.msg.data.im_id; // 所属会话
        session = sessionMap.get(sessionId);
        if (!session) {
          session = {
            isGroup,
            sessionId,
            msgIdMap: new Map(),
          };
          sessionMap.set(sessionId, session);
        }
        CollectionUtil.mapArrayAdd(session.msgIdMap, it.msgId, it);
      });
      let batchRes = await messageDao.getMessageReadExtraByBatch([...sessionMap.values()]);
      const updateArr = [];
      batchRes.forEach((res) => {
        // 查询失败的
        if (res.reason) {
          console.warn('MessageReadUtil updateMessageReadExtraByList 查询失败', res.reason);
          return;
        }
        res.value.forEach((readInfo) => {
          // readInfo: readExtra, msgId, sessionId, ownerId
          session = sessionMap.get(readInfo.sessionId);
          session.msgIdMap.get(readInfo.msgId).forEach((msg) => {
            // msg: data, msgId, msgIds
            this.handleReadInfo(readInfo, msg);
          });
          updateArr.push(readInfo);
        });
      });

      // 更新当前聊天页面
      const { currentSession } = chatStore;
      if (currentSession) {
        const readInfoArr = updateArr.filter((it) => currentSession.sessionId === it.sessionId);
        if (readInfoArr.length) {
          global.emitter.emit(constant.event.chatMessageRead, {
            sessionId: currentSession.sessionId,
            isGroup: currentSession.isGroup,
            readInfo: readInfoArr,
          });
        }
      }
      // 批量更新到数据库
      await messageDao.updateMessageReadExtraByBatch(updateArr);
      // 缓存来自socket的msg_id
      dataArr.forEach((it) => {
        if (it.isReceive) {
          chatMessageUtil.cacheSocketMsg(it.data);
        }
      });
    } catch (e) {
      console.warn('MessageReadUtil updateMessageReadExtraByList error', e);
    }
    console.log(`MessageReadUtil updateMessageReadExtraByList execTime: ${Date.now() - time}`);
  };

  handleReadInfo = (readInfo, param) => {
    const { data, msgIds } = param;
    const senderId = data.sender_id; // 发送已读消息的人
    let readExtra = readInfo.readExtra && sentryUtil.parse(readInfo.readExtra, 'mru umre');
    if (!readExtra?.readMap) {
      readExtra = new ChatMessageReadExtra();
    }
    if (!readInfo.isGroup && !readExtra.receiverIds?.length) {
      readExtra.receiverIds = [senderId];
    }
    let readObj = readExtra.readMap[senderId] || readExtra.readObj || {};
    readObj.imId = senderId;
    readExtra.readMap[senderId] = readObj;
    const timestamp = Math.min(data.msg.data.timestamp, data.msg_time);
    if (data.msg.data.notice_type === constant.messageNoticeType.messageArrived) {
      readObj.arrivedTime = timestamp; // 送达时间
    } else {
      readObj.readTime = timestamp; // 已读时间
      if (!readObj.arrivedTime) {
        readObj.arrivedTime = timestamp;
      }
    }

    let readNum = 0;
    let arrivedNum = 0;
    readExtra.receiverIds?.forEach((it) => {
      if (readExtra.readMap[it]?.readTime) {
        readNum++;
      }
      if (readExtra.readMap[it]?.arrivedTime) {
        arrivedNum++;
      }
    });
    readExtra.readNum = readNum;
    readExtra.arrivedNum = arrivedNum;
    // 0未读、1全部已读、2部分已读
    const receiverNum = readExtra.receiverIds?.length || 0;
    readExtra.isRead = readNum ? (receiverNum === readNum ? 1 : 2) : 0;
    readExtra.isArrived = arrivedNum ? (receiverNum === arrivedNum ? 1 : 2) : 0;

    readInfo.isRead = readExtra.isRead;
    readInfo.isArrived = readExtra.isArrived;
    readInfo.readExtra = JSON.stringify(readExtra);
    readInfo.msgIds = msgIds;
    readInfo.isSelfSend = true;
  };

  onReadByMessageList = (messageList, { readInfo, isGroup }) => {
    if (Configs.disableMessageRead(isGroup)) return null;
    const updateAt = new Date().getTime();

    const msgIdMap = new Map();
    function handleItem(it) {
      if (it.msgIds?.length) {
        it.msgIds.forEach((msgId) => msgIdMap.set(msgId, it));
      } else {
        msgIdMap.set(it.msgId, it);
      }
    }
    if (Array.isArray(readInfo)) {
      readInfo.forEach(handleItem);
    } else {
      handleItem(readInfo);
    }

    let isChange = false;
    messageList.forEach((item) => {
      readInfo = msgIdMap.get(item.msgId);
      if (readInfo) {
        isChange = true;
        item.isRead = typeof readInfo.isRead === 'number' ? readInfo.isRead : item.isRead;
        item.isArrived =
          typeof readInfo.isArrived === 'number' ? readInfo.isArrived : item.isArrived;
        item.readExtra = readInfo.readExtra;
        if (readInfo.isSelfSend) {
          item.received = !!item.isRead;
          item.sent = true;
        }
        item.updateAt = updateAt;
      }
    });
    return isChange ? messageList.slice() : null;
  };

  /**
   * 发送已读消息
   */
  getSendMessageRead = ({
    message,
    messages,
    session,
    notice_type = constant.messageNoticeType.messageRead,
  }) => {
    if (message) {
      messages = [message];
    }
    console.log('MessageReadUtil getSendMessageRead 1', messages?.length, notice_type);
    if (!messages?.length) return;
    const map = new Map();
    let sessionId, key, value;
    messages.forEach((it) => {
      if (
        !it.isSelfSend &&
        !it.isRead &&
        it.msgId &&
        (notice_type !== constant.messageNoticeType.messageArrived || !it.isArrived)
      ) {
        if (notice_type === constant.messageNoticeType.messageArrived) {
          it.isArrived = 2;
        } else {
          it.isRead = 2;
        }
        sessionId = it.isGroup ? it.sessionId || session?.sessionId : it.senderId;
        key = `${it.senderId}_${sessionId}`;
        value = map.get(key);
        if (!value) {
          value = {
            receiver_id: it.senderId,
            group_id: it.isGroup ? sessionId : null,
            messages: [],
            isGroup: it.isGroup,
            sessionId,
            notice_type,
          };
          map.set(key, value);
        }
        value.messages.push(it);
      }
    });
    console.log('MessageReadUtil getSendMessageRead 3', map.size);
    if (!map.size) return;
    return [...map.values()];
  };

  /**
   * 发送已读失败时重置进行中标识
   */
  sendMessageReadFail = (data) => {
    try {
      const key =
        data.notice_type === constant.messageNoticeType.messageRead ? 'isRead' : 'isArrived';
      data.messages.forEach((it) => {
        if (it[key] !== 1) {
          it[key] = 0;
        }
      });
    } catch (e) {
      console.warn('MessageReadUtil sendMessageReadFail', e);
    }
  };

  /**
   * 发送已读成功后的回执
   */
  onMsgReadReceipt = async (data, message) => {
    if (Configs.disableMessageRead(message.isGroup)) return;
    const { sessionId, isGroup, notice_type, messages } = message;
    const param = { sessionId, msgIds: [] };
    if (notice_type === constant.messageNoticeType.messageRead) {
      param.isRead = 1;
      param.isArrived = 1;
    } else if (notice_type === constant.messageNoticeType.messageArrived) {
      param.isArrived = 1;
    } else {
      chatMessageUtil.cacheSocketMsg({ msg_id: data.data?.msg_id, sessionId: message.sessionId });
      return;
    }
    messages.forEach((it) => {
      param.msgIds.push(it.msgId);
      it.isArrived = param.isArrived;
      it.isRead = param.isRead || it.isRead;
    });
    await messageDao.updateMessageRead(param);
    global.emitter.emit(constant.event.chatMessageRead, { sessionId, isGroup, readInfo: param });
    // 群聊的已读使用私聊方式发送，所以使用receiver_id
    chatMessageUtil.cacheSocketMsg({ msg_id: data.data?.msg_id, sessionId: message.receiver_id });
  };

  sessionUnreadMap = new Map();
  sessionUnreadDelay = 1000; // 延迟时间，毫秒

  /**
   * 根据其他端发送的已读消息，清除对应会话的未读数量
   */
  updateSessionUnreadNum = (data) => {
    const { imId } = userStore;
    if (data.sender_id !== imId || data.receiver_id === imId) return;
    const isGroup = !!data.msg.data.group_id;
    const sessionId = isGroup ? data.msg.data.group_id : data.receiver_id; // 所属会话
    // 如果在当前会话，则跳过
    if (chatAction.isCurrentSessionAndActive({ sessionId, isGroup })) {
      return;
    }
    // const readTime = data.msg.data.timestamp; // 已读时间
    const readTime = data.msg_time; // 已读时间

    const key = sessionId + '_' + isGroup;
    let session = this.sessionUnreadMap.get(key);
    if (!session) {
      session = {
        isGroup,
        sessionId,
        readTime,
        ownerId: imId,
      };
      this.sessionUnreadMap.set(key, session);
    } else if (session.readTime < readTime) {
      session.readTime = readTime;
    }
    this.updateSessionUnreadNumDelay();
  };

  /**
   * 至少间隔sessionUnreadDelay执行一次
   */
  updateSessionUnreadNumDelay = async (isExec) => {
    if (!this.sessionUnreadMap.size) return;
    if (!isExec) {
      if (this.updateSessionUnreadNumDelayTask) return;
      this.updateSessionUnreadNumDelayTask = setTimeout(() => {
        this.updateSessionUnreadNumDelay(true);
      }, this.sessionUnreadDelay);
      return;
    }
    try {
      const arr = [];
      const keyArr = [];
      const time = Date.now() - this.sessionUnreadDelay + 1;
      this.sessionUnreadMap.forEach((session, key) => {
        if (session.readTime < time) {
          arr.push(session);
          keyArr.push(key);
        }
      });
      if (arr.length) {
        await chatSessionDao.updateUnReadNumBatch({ arr });
        keyArr.forEach((key) => this.sessionUnreadMap.delete(key));
      }
    } catch (e) {
      console.warn('updateSessionUnreadNumDelay', e);
    }
    this.updateSessionUnreadNumDelayTask = null;
    this.updateSessionUnreadNumDelay();
  };
}

export default new MessageReadUtil();
