/**
 * 群聊类
 * <AUTHOR>
 */
export default class ChatGroup {
  groupId = 0;

  ownerId = 0;

  groupOwnerId = 0;

  creatorId = 0;

  title = '';

  avatar = '';

  isPrivate = 1; // 是否是私密的

  count = 0;

  memberHash = '';

  isDisturb = 0;

  setTopTime = 0;

  notice = 0;

  noticeTime = 0;

  canInvite = 1;

  joinType = 0;

  canAddFriend = 1;

  updateTime = 1;

  noSpeaking = 0; // 是否禁言

  memberVisible = 0; // 是否允许群成员查看其它成员信息

  enableMedia = 0; // 是否支持发送图片、文件、链接、语音等，否则仅能发送文本和表情

  isVip = 0;

  isDisbanded = 0;
}
