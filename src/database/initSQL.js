/**
 * 创建固定表名SQL
 * @return {{sql: string}[]}
 */
export function getCreateTableSQL() {
  return [
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_session (' + // 会话表
        'sessionId INTEGER NOT NULL,' + // 群聊id 或 私聊imId
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'msgId INTEGER NOT NULL,' + // 消息id
        'title TEXT,' + // 标题
        'content TEXT,' + // 内容
        'lastMsgTime INTEGER,' + // 最新收到的消息
        'atCount INTEGER DEFAULT 0,' + // @艾特我的未读数量
        'setTopTime INTEGER,' + // 设置置顶的时间
        'isDisturb INTEGER,' + // 是否设置免打扰默认为false
        'isTempSession INTEGER,' + // 是否是临时会话默认为false
        'setTempTopTime INTEGER,' + // 设置临时会话置顶的时间
        'isDelete SMALLINT,' + // 是否删除会话
        'unReadNum SMALLINT DEFAULT 0,' + // 未读数
        'isSelfSend SMALLINT,' + // 发送方是否是自己
        'isGroup SMALLINT DEFAULT 0,' + // 是否群聊
        'jobId TEXT,' + // 职位ID
        'jobTitle TEXT,' + // 职位名称
        'resumeId TEXT,' + // 简历ID
        'extra TEXT,' + // 额外信息
        'PRIMARY KEY(ownerId, sessionId, isGroup)' + // 联合主键
        ');',
    },
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_friend (' + // 好友表
        'imId INTEGER NOT NULL,' + // 好友imId
        'paymentUserId INTEGER,' + // 支付系统userId
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'nickname TEXT,' + // 昵称
        'memo TEXT,' + // 别名
        'phone VARCHAR(50),' + // 手机号
        'countryCode VARCHAR(50),' + // 手机号地区码
        'account TEXT,' + // 账号
        'avatar TEXT,' + // 头像
        'tags TEXT,' + // 分组/标签
        'isDisturb SMALLINT,' + // 是否设置免打扰模式
        'isBlacked SMALLINT DEFAULT 0,' + // 0：未拉黑,1：已拉黑好友,2：好友已拉黑你,3：相互拉黑
        'beDeleted SMALLINT,' + // 是否被对方删除, 1：是，0：否
        'isHide SMALLINT,' + // 是否隐藏（不显示在好友列表）, 1：是，0：否
        'relationType SMALLINT,' + // 0:非好友，1:好友，2:有一方已删除
        'userType INTEGER,' + // 用户类型
        'extra TEXT,' + // 拓展字段，json字符串
        'identityKey TEXT,' +
        'preKey TEXT,' +
        'signedPreKey TEXT,' +
        'signedPreKeyId TEXT,' +
        'signedPreKeySignature TEXT,' +
        'PRIMARY KEY(ownerId, imId)' + // 联合主键
        ');',
    },
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_friend_req (' + // 好友申请记录表
        'imId INTEGER NOT NULL,' + // 好友imId
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'nickname TEXT,' + // 昵称
        'avatar TEXT,' + // 头像
        'status SMALLINT,' + // request/receive/reject/pass
        'message TEXT,' + // 验证消息
        'requestAt INTEGER,' + // 请求时间
        'notifyId INTEGER,' + // 通知id
        'isRead SMALLINT,' + // 是否已读
        'addBy TEXT,' + // -
        'addFrom TEXT,' + // -
        'isSelfReq SMALLINT,' + // 是否是自己请求0自己 1别人
        'isDeleteBy SMALLINT,' + // 是否被好友删除 0未删除  1被删除过
        'isBlacked SMALLINT DEFAULT 0,' + // 0：未拉黑,1：已拉黑好友,2：好友已拉黑你,3：相互拉黑
        'reqId INTEGER,' + // -
        'PRIMARY KEY(ownerId, imId)' + // 联合主键
        ');',
    },
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_group (' + // 群聊表
        'groupId INTEGER NOT NULL,' + // 群Id
        'groupOwnerId INTEGER NOT NULL,' + // 群主id
        'creatorId INTEGER NOT NULL,' + // 创建人id
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'title TEXT,' + // 群标题
        'avatar TEXT,' + // 群头像
        'count INTEGER DEFAULT 0,' + // 群成员数量
        'isVip SMALLINT DEFAULT 0,' + // 是否vip群
        'memberHash TEXT,' + // 成员列表hash，值为md5("userid1"+"userid2"+"userid3")id的升序
        //----管理员对群的设置
        'isDisturb SMALLINT,' + // 是否设置免打扰模式
        'setTopTime INTEGER DEFAULT 0,' + // 设置置顶的时间
        'notice TEXT,' + // 群公告
        'noticeTime INTEGER,' + // 群公告更新时间
        'isPrivate SMALLINT,' + // 是否是私密的
        //----成员权限
        'canInvite INTEGER DEFAULT 1,' + // 成员是否可邀请好友入群 1：是，0：否
        'joinType INTEGER,' + // 入群验证方式
        'canAddFriend INTEGER DEFAULT 1,' + // 群内是否可进行好友相互添加
        'updateTime INTEGER,' + // 更新时间，使用时判断时间如果超过一周重新拉取完整信息
        'noSpeaking SMALLINT,' + // 是否禁言，如果禁言只允许管理员发言
        'memberVisible SMALLINT,' + // 是否允许群成员查看其它成员信息
        'enableMedia SMALLINT,' + // 是否支持发送图片、文件、链接、语音等，否则仅能发送文本和表情
        'isDisbanded SMALLINT,' + //0: 正常，1: 已解散
        'PRIMARY KEY(ownerId, groupId)' + // 联合主键
        ');',
    },
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_group_req (' + // 入群申请记录表
        'imId INTEGER NOT NULL,' + // 申请用户imId
        'inviteId INTEGER NOT NULL,' + // 邀请人用户id
        'groupId INTEGER NOT NULL,' + // 群id
        'groupName TEXT,' + // 群名称
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'nickname TEXT,' + // 昵称
        'avatar TEXT,' + // 群头像
        'status TEXT,' + // request/receive/reject/pass
        'joinType TEXT,' + // 请求方式 1：invite/2：qrcode/3：hotGroups
        'message TEXT,' + // 验证消息
        'requestAt INTEGER,' + // 请求时间
        'notifyId INTEGER,' + // 通知id
        'isRead SMALLINT,' + // 是否已读
        'reqId INTEGER,' + // 。
        'PRIMARY KEY(ownerId, groupId, imId)' + // 联合主键
        ');',
    },
    {
      sql:
        'CREATE TABLE IF NOT EXISTS t_broadcast (' + // 广播表
        'id INTEGER PRIMARY KEY AUTOINCREMENT,' + // 自增Id
        'ownerId INTEGER NOT NULL,' + // 当前登录用户imId
        'content TEXT NOT NULL,' + // 广播内容
        'type VARCHAR(50) NOT NULL,' + // 广播内容类型
        'lastMsgTime INTEGER,' + // -
        'userIds TEXT,' + // -
        'userNames TEXT' + // -
        ');',
    },
    {
      sql: 'CREATE TABLE IF NOT EXISTS t_version (version INTEGER PRIMARY KEY NOT NULL);',
    },
  ];
}

/**
 * 消息表名
 * @param ownerId 当前登录用户imId
 * @param sessionId 群id 或 好友imId
 */
export function getChatMessageTableName(ownerId, sessionId, noError) {
  if (!ownerId || !sessionId) {
    if (noError) return null;
    throw new Error(`无效的消息表名称，ownerId: ${ownerId} sessionId: ${sessionId}`);
  }
  return `t_message_${ownerId}_${sessionId}`;
}

/**
 * 创建消息表SQL
 * @return {{sql: string}[]}
 */
export function getCreateChatMessageTableSQL(tableName) {
  return (
    'CREATE TABLE IF NOT EXISTS ' +
    tableName +
    ' (' +
    'seq INTEGER PRIMARY KEY,' + // 请求id，收到消息时，将msgId保存到seq
    'msgId INTEGER DEFAULT 0,' + // 消息id
    'originSeq INTEGER DEFAULT 0,' + // 发送时的请求id
    // 'groupId INTEGER DEFAULT 0,' + // 群id
    'broadcastId INTEGER DEFAULT 0,' + // 广播id
    'content TEXT,' + // 内容
    'referTo TEXT,' + // @功能
    'msgTime INTEGER NOT NULL,' + // 时间
    'type VARCHAR(50) NOT NULL,' + // 消息类型
    'isRead SMALLINT NOT NULL,' + // 0未读、1全部已读、2部分已读
    'isArrived SMALLINT,' + // 0未送达、1全部送达、2部分送达
    'timeLength REAL,' + // 语音时长
    'sendState INTEGER NOT NULL,' + // 发送的状态
    'isStar SMALLINT,' + // 消息是否添加星标
    'imagePath TEXT,' + // 图片、语音、视频等文件本地路径
    'senderId INTEGER,' + // 发送者的imId
    // 'imId INTEGER,' + // 发送者的imId
    'quoteId INTEGER,' + // 引用消息ID
    'isSelfSend SMALLINT,' + // 是否我发送的
    'extra TEXT,' + // 额外信息，JSON字符串
    'localExtra TEXT,' + // 本地额外信息，不会发送给对方，JSON字符串
    'jobId TEXT,' + // 职位ID
    'resumeId TEXT,' + // 简历ID
    'readExtra TEXT,' + // 已读额外信息，JSON字符串
    'isGroup SMALLINT' + // 是否群聊
    // 'PRIMARY KEY(seq, msgId)' + // 联合主键
    ');'
  );
}

/**
 * 群成员表名
 * @param ownerId 当前登录用户imId
 * @param groupId 群id
 */
export function getGroupMemberTableName(ownerId, groupId) {
  if (!ownerId || !groupId) {
    throw new Error(`无效的群成员表名称，ownerId: ${ownerId} groupId: ${groupId}`);
  }
  return `t_group_member_${ownerId}_${groupId}`;
}

/**
 * 创建群成员表SQL
 * @return {{sql: string}[]}
 */
export function getCreateGroupMemberTableSQL(tableName) {
  return (
    'CREATE TABLE IF NOT EXISTS ' +
    tableName +
    ' (' +
    'imId INTEGER PRIMARY KEY,' + // 群成员id
    'nickname TEXT,' + // 用户名称
    'memo TEXT,' + // 群昵称
    'avatar TEXT,' + // 头像
    'role TEXT NOT NULL,' + // 角色：owner：群主，admin：管理员，member
    'isDisturb SMALLINT,' + // 是否设置免打扰模式
    'noSpeaking INTEGER,' + // 是否禁言，0:关闭，1:开启，其它：解除时间戳
    'joinAt INTEGER DEFAULT 0,' + // 加入时间
    'isDelete SMALLINT' + // 是否删除
    ');'
  );
}
