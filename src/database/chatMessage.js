import constant from '../store/constant';

/**
 * 聊天消息类
 * <AUTHOR>
 */
export default class ChatMessage {
  seq = 0;

  msgId = 0;

  broadcastId = 0;

  content = null;

  referTo = null;

  msgTime = 0;

  type = constant.messageType.text;

  isRead = 0; // 0未读、1全部已读、2部分已读

  isArrived = 0; // 0未送达、1已送达、2部分送达

  readExtra = null;

  timeLength = 0;

  sendState = constant.messageSendState.sending;

  isStar = 0;

  imagePath = null;

  senderId = 0;

  quoteId = 0;

  isSelfSend = 1;

  isGroup = 0;

  sessionId = 0;

  ownerId = 0;

  extra = null; // 额外信息

  updateAt = 0; // 最后修改时间，目前仅用于UI渲染

  quoteMessage = null; // 引用的消息，仅用于UI渲染

  localExtra = null; // 本地额外信息，不会发送给对方

  jobId = ''; // 职位ID

  resumeId = ''; // 简历ID
}
