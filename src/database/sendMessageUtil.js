import { AppState, NativeModules, Platform } from 'react-native';
import userStore from '../store/stores/user';
import messageDao from './dao/messageDao';
import imWebSocket from '../api/imWebSocket';
import constant from '../store/constant';
import ChatMessage from './chatMessage';
import receiptMessageUtil from './receiptMessageUtil';
import chatSessionDao from './dao/chatSessionDao';
import contactDao from './dao/contactDao';
import CotactModel from './contactModel';
import deviceInfo from '../util/deviceInfo';
import imAction from '../store/actions/imAction';
import promiseUtil from '../util/promiseUtil';
import fileUtil from '../util/fileUtil';
import I18n from '../i18n';
import ImService from '../api/imService';
import sentryUtil from '../util/sentryUtil';
import messageReadUtil from './messageReadUtil';
import ChatMessageReadExtra from './chatMessageReadExtra';
import Configs from '../configs';
import chatMessageUtil from './chatMessageUtil';
import chatStore from '../store/stores/chatStore';
import chatAction from '../store/actions/chatAction';

/**
 * 处理发送聊天消息类
 * <AUTHOR>
 */
class SendMessageUtil {
  /**
   * 发送消息
   * @param type 消息类型
   * @param action
   * @param content
   * @param sessionId
   * @return {Promise<void>}
   */
  sendMessage = async ({ type, isGroup, content, sessionId, session, mustSave, ...rest }) => {
    console.debug('sendMessage session1', session);
    if (!session && chatStore.currentSession?.sessionId === sessionId) {
      session = chatStore.currentSession;
      console.debug('sendMessage session2', session);
    }
    const { seq, timestamp, ownerId } = this.getSendInfo();
    const action = isGroup ? constant.messageAction.msgGroup : constant.messageAction.msgP2P;
    // 保存在本地的数据
    const message = new ChatMessage();
    Object.assign(message, rest);
    message.seq = seq;
    message.content = content;
    message.msgTime = timestamp;
    message.type = type;
    message.senderId = ownerId;
    message.isSelfSend = 1;
    message.isGroup = isGroup ? 1 : 0;
    message.sessionId = sessionId;
    message.ownerId = ownerId;
    message.sendState = constant.messageSendState.sending;
    message.msgId = 0;
    message.broadcastId = 0;
    message.isRead = !isGroup && sessionId === ownerId ? 1 : 0;
    message.isArrived = !isGroup && sessionId === ownerId ? 1 : 0;
    message.isStar = 0;
    message.updateAt = timestamp;
    if (session) {
      message.jobId = message.jobId || session.jobId || '';
      message.resumeId = message.resumeId || session.resumeId || '';
      message.jobTitle = message.jobTitle || session.jobTitle || '';
    }

    let isTransitInGroup = '';
    let toUser = null;

    const { disableSend, notSaveMsg } = await this.isDisableSend({ message, sessionId, mustSave });

    console.log('sendMessageUtil sendMessage isDisableSend', disableSend, notSaveMsg);
    if (type === 'transfer') {
      isTransitInGroup = JSON.parse(content).isTransitInGroup;
      toUser = JSON.parse(content).toUser;
    } else {
      if (disableSend) return;
    }
    await this.setReceiverIds(message).catch((e) => (mustSave ? null : Promise.reject(e)));
    message.notSaveMsg = notSaveMsg;
    await chatSessionDao.updateMessageWithSession(message, true, notSaveMsg);
    if (this.isRequestUpload(message)) {
      global.emitter.emit(constant.event.receiveMessage, { message });
      await this.upload(message);
      await messageDao.updateMessageBySeq(message);
    }
    const sendData = this.getSendDataByMessage({ message, action, sessionId });

    if (isTransitInGroup === 'groupChat' && type === 'transfer') {
      sendData.action = constant.messageAction.msgGroup;
      sendData.data.group_id = sessionId;
      sendData.data.msg.type = 'groupTransfer';
      sendData.data.from = ownerId;
      sendData.data.to = toUser;
      delete sendData.data.sender_id;

      console.log('sendData =====================> ', sendData);
    }

    if (sessionId === constant.fixedSessionId.gpt) {
      message.originSeq = message.seq;
      message.msgTime = message.seq;
      message.msgId = message.msgTime * 1000;
      message.sendState = constant.messageSendState.successful;
      message.isRead = 1;

      const resMsg = new ChatMessage();
      resMsg.sessionId = message.sessionId;
      resMsg.ownerId = message.ownerId;
      resMsg.content = I18n.t('page_chat_gpt_wait');
      resMsg.msgTime = message.msgTime + 1;
      resMsg.msgId = resMsg.msgTime * 1000;
      resMsg.seq = resMsg.msgId;
      resMsg.isSelfSend = 0;
      resMsg.sendState = constant.messageSendState.successful;
      try {
        await Promise.all([messageDao.updateMessageBySeq(message), messageDao.addMessage(resMsg)]);
        global.emitter.emit(constant.event.receiveMessage, {
          message: [message, resMsg],
          sessionId,
        });
        const res = await imAction.sendGPT({
          content: message.content,
        });
        console.debug('sendGPT', res);
        if (res.data?.length) {
          resMsg.content = res.data[0].content;
          message.originSeq = message.dateTime;
          await chatSessionDao.updateMessageWithSession(resMsg, false);
          global.emitter.emit(constant.event.receiveMessage, { message: resMsg });
        }
      } catch (e) {
        console.warn('sendGPT', e);
        resMsg.content = I18n.t('page_chat_gpt_error');
        await chatSessionDao.updateMessageWithSession(resMsg, false);
        global.emitter.emit(constant.event.receiveMessage, { message: resMsg });
      }
      return;
    }
    if (session?.isSelfManage) {
      sendData.data.msg.isBroadCast = true;
      const data = {
        seq: new Date().getTime(),
        action: constant.messageAction.msgP2P,
        data: {
          send_way: 1,
          receiver_ids: [],
          msg: sendData.data.msg,
          service_notification: true,
          proxy_id: 0,
          parent_id: 0,
        },
      };
      const res = await ImService.sendBroadcast(data);
      console.log('sendBroadcast 发送公告', res);
      if (res.data?.msg_id) {
        message.msgTime = res.data.msg_time;
        message.msgId = res.data.msg_id;
      }
      message.sendState = constant.messageSendState.successful;
      await messageDao.updateMessageBySeq(message);
      global.emitter.emit(constant.event.receiveMessage, { message });
      return;
    }

    await this.encryptMessae(sendData);

    if (imWebSocket.send(sendData)) {
      receiptMessageUtil.addMessageReceipt({
        seq,
        receiptType: sendData.action,
        message,
      });
    } else {
      message.sendState = constant.messageSendState.failed;
    }
    if (notSaveMsg) return;
    global.emitter.emit(constant.event.receiveMessage, { message });
  };

  setReceiverIds = async (message) => {
    if (Configs.disableMessageRead(message.isGroup)) return;
    const readExtra = new ChatMessageReadExtra();
    if (message.isGroup) {
      const { groupMembers } = await imAction.getGroupMemberList(message.sessionId, true, false);
      readExtra.receiverIds = groupMembers
        .filter((it) => it.imId !== message.ownerId)
        .map((it) => it.imId);
    } else {
      readExtra.receiverIds = [message.sessionId];
    }
    message.readExtra = JSON.stringify(readExtra);
    console.log('SendMessageUtil setReceiverIds', message.readExtra);
  };

  isDisableSend = async ({ message, sessionId = message.sessionId, isResend, mustSave }) => {
    if (
      isResend &&
      (message.type === constant.messageType.text || message.type === constant.messageType.referTo)
    ) {
      const errMsg = await chatMessageUtil.checkSensitiveWords(message.content);
      if (errMsg) {
        return Promise.reject({ message: errMsg });
      }
    }
    if (sessionId === constant.fixedSessionId.gpt) {
      return { disableSend: false };
    }
    const { disableSend, invalidMsgType } = await imAction
      .isDisableSend({
        sessionId,
        isGroup: message.isGroup,
        type: message.type,
        isSend: true,
      })
      .catch((e) => {
        if (mustSave) {
          return { disableSend: false };
        }
        return Promise.reject(e);
      });
    if (invalidMsgType === constant.invalidMsgType.onlySendNotSave) {
      // 是否发送，但不保存
      return { notSaveMsg: true, disableSend: false };
    }
    if (!disableSend) return { disableSend };
    message.sendState = constant.messageSendState.failed;
    const disableMsg = {
      ...message,
      sendState: constant.messageSendState.successful,
      type: constant.messageType.tip,
    };
    if (isResend) {
      const { seq, timestamp } = this.getSendInfo();
      message.msgTime = timestamp;
      disableMsg.seq = seq;
      disableMsg.msgTime = timestamp + 1;
    } else {
      disableMsg.seq = message.seq + 1;
      disableMsg.msgTime = message.msgTime + 1;
    }
    switch (invalidMsgType) {
      case constant.invalidMsgType.noSpeakingGroup:
      case constant.invalidMsgType.noSpeakingGroupMember:
        disableMsg.content = I18n.t('page_group_tips_no_speaking');
        break;
      case constant.invalidMsgType.notGroup:
        disableMsg.content = I18n.t('page_group_tips_not_member');
        break;
      case constant.invalidMsgType.notFriend:
        disableMsg.content = I18n.t('page_group_tips_not_friend');
        disableMsg.extra = JSON.stringify({ tipType: 'requestAddFriend' });
        break;
      case constant.invalidMsgType.blackedFriend:
        disableMsg.content = I18n.t('page_group_tips_black');
        break;
      default:
        disableMsg.content = I18n.t('page_group_tips_mute');
        break;
    }
    await Promise.all([
      isResend ? messageDao.updateMessageBySeq(message) : messageDao.addMessage(message),
      chatSessionDao.updateMessageWithSession(disableMsg, true),
    ]);
    global.emitter.emit(constant.event.receiveMessage, {
      message: [message, disableMsg],
      sessionId,
    });
    return { disableSend };
  };

  getSendDataByMessage = ({ message, action, sessionId = message.sessionId }) => {
    const extra = (message.extra && sentryUtil.parse(message.extra, 'sm gsd')) || {};
    const sendData = {
      action:
        action ||
        (message.isGroup ? constant.messageAction.msgGroup : constant.messageAction.msgP2P),
      data: {
        msg: {
          content: message.content,
          fileName: extra.fileName || '',
          fileSize: extra.fileSize || 0.0,
          msgId: 0,
          timeLength: message.timeLength || 0.0,
          type: message.type,
        },
        parent_id: 0,
        proxy_id: 0,
      },
      seq: message.seq,
    };
    if (message.signalExt) {
      sendData.data.msg.signalExt = message.signalExt;
    }
    if (message.noticeMsg) {
      sendData.data.msg = {
        data: message.noticeMsg,
        type: constant.messageType.notice,
      };
    }
    if (message.referTo) {
      sendData.data.msg.referTo = message.referTo;
    }
    if (message.quoteId) {
      sendData.data.msg.quoteId = message.quoteId;
    }
    if (message.jobId) {
      sendData.data.msg.jobId = message.jobId;
    }
    if (message.resumeId) {
      sendData.data.msg.resumeId = message.resumeId;
    }
    if (message.jobTitle) {
      sendData.data.msg.jobTitle = message.jobTitle;
    }
    if (message.isGroup) {
      sendData.data.group_id = sessionId;
      sendData.data.sender_id = message.ownerId;
    } else {
      sendData.data.receiver_id = sessionId;
      // 发送消息给自己时
      if (sessionId === message.ownerId) {
        // sendData.data.send_self = 1; // 发送的消息，又会发给自己
        extra.deviceId = deviceInfo.getUniqueID();
        extra.deviceType = Platform.OS.toLowerCase();
        extra.seq = sendData.seq;
        message.extra = JSON.stringify(extra);
      }
    }
    if (message.extra) {
      sendData.data.msg.extra = message.extra;
    }
    return sendData;
  };

  encryptMessae = async (sendData) => {
    const type = sendData.data.msg.type;
    if (
      type === constant.messageType.redPacket ||
      type === constant.messageType.redPacketAccept ||
      type === constant.messageType.transfer ||
      type === constant.messageType.groupTransfer ||
      type === constant.messageType.transferReturn ||
      type === constant.messageType.transferAccept ||
      type === constant.messageType.receiveCreate ||
      type === constant.messageType.receivePaid ||
      type === constant.messageType.audioCall ||
      type === constant.messageType.videoCall ||
      type === constant.messageType.notice
    ) {
      console.log('该类型消息不进行加密', type);
      return;
    }
    if (!sendData.data.msg.content) {
      console.log(`消息内容为空不进行加密:[${sendData.data.msg.content}]`);
      return;
    }
    console.log('encryptMessage 11111112222', sendData);
    const msg = await NativeModules.SignalMagager.aesEncryptMessage(
      sendData.data.msg.content,
      sendData.data.receiver_id || 0,
      userStore.imId,
      sendData.data.group_id?.toString()
    );
    if (msg) {
      sendData.data.msg.content = msg.content;
      sendData.data.msg.aesExt = msg.aesExt;
    } else {
      sentryUtil.captureMessage(
        `smu aem c: ${sendData.data.msg.content}:${sendData.data.receiver_id}:${userStore.imId}:${sendData.data.group_id}`
      );
    }
    console.log('encryptMessae 11111112222333', sendData);
  };

  /**
   * 将发送失败的重新发送
   */
  resend = async ({ message, sessionId = message.sessionId }) => {
    try {
      const { disableSend, notSaveMsg } = await this.isDisableSend({
        message,
        sessionId,
        isResend: true,
      });
      if (disableSend) return;
      const { timestamp, ownerId } = this.getSendInfo();
      message.sendState = constant.messageSendState.sending;
      message.sessionId = sessionId;
      message.ownerId = ownerId;
      message.msgTime = timestamp;
      message.msgId = 0;
      await this.setReceiverIds(message);
      if (this.isRequestUpload(message)) {
        global.emitter.emit(constant.event.receiveMessage, { message });
        await this.upload(message);
      }
      // 发送给服务器的数据
      const sendData = this.getSendDataByMessage({ message, sessionId });
      await chatSessionDao.updateMessageWithSession(message, false, notSaveMsg);
      await this.encryptMessae(sendData);
      if (imWebSocket.send(sendData)) {
        receiptMessageUtil.addMessageReceipt({
          seq: message.seq,
          receiptType: sendData.action,
          message,
        });
      } else {
        message.sendState = constant.messageSendState.failed;
      }
      if (notSaveMsg) return;
      global.emitter.emit(constant.event.receiveMessage, { message });
    } catch (e) {
      logger.warn('sendMessageUtil resend', e);
    }
  };

  /**
   * 发送好友请求 或 同意好友请求
   */
  sendContactSocket = async ({ noticeType, contact }) => {
    const { seq, timestamp, ownerId, reqId } = this.getSendInfo();
    const isAdd =
      noticeType === constant.messageNoticeType.addContact ||
      noticeType === constant.messageNoticeType.AddedContact;
    const data = {
      im_id: ownerId,
      notice_type: noticeType,
      rim_id: contact.im_id,
      timestamp,
    };
    if (isAdd) {
      data.req_id = reqId;
      data.message = contact.message;
      data.add_by = 'account';
    }
    // 发送给服务器的数据
    const sendData = {
      action: constant.messageAction.msgP2P,
      data: {
        msg: {
          data,
          type: constant.messageAction.notice,
        },
        persistence: 1,
        receiver_id: data.rim_id,
        send_self: 0,
        sender_id: ownerId,
      },
      seq,
    };
    // 保存在本地的Contact数据
    const model = new CotactModel();
    model.imId = data.rim_id;
    model.ownerId = ownerId;
    model.nickname = contact.nickname || contact.account_alias;
    model.avatar = contact.avatar;
    model.status = contact.status;
    model.message = contact.message;
    model.requestAt = timestamp;
    model.notifyId = 0;
    model.isRead = 0;
    model.addBy = 'account';
    model.addFrom = 'phone';
    model.isBlacked = 0;
    model.reqId = reqId;
    model.isSelfReq = contact.isSelfReq;
    model.isDeleteBy = 0;
    const localContact = await contactDao.findContact(model);
    if (!localContact) {
      await contactDao.insertNewContact(model);
    } else {
      await contactDao.updateContact(model);
    }
    if (imWebSocket.send(sendData)) {
      // receiptMessageUtil.addMessageReceipt(model);
    } else {
      logger.warn('发送添加申请失败');
    }
  };

  /**
   * 发送在线消息通知
   */
  sendOnLinePing = () => {
    const { seq, timestamp, ownerId, reqId } = this.getSendInfo();
    return {
      action: constant.messageAction.msgP2P,
      data: {
        msg: {
          data: {
            id: reqId,
            im_id: ownerId,
            notice_type: constant.messageNoticeType.onLinePing,
            online: 1,
            platform: IS_ANDROID ? 'android' : 'ios',
            timestamp,
          },
          type: constant.messageType.notice,
        },
        persistence: 0,
        receiver_id: 0,
        send_self: 0,
        sender_id: ownerId,
      },
      seq,
    };
  };

  /**
   * 检查是否在聊天窗口且APP在前台，发送消息已读
   */
  sendMsgReadByCheck = async (data) => {
    if (data.session?.sessionId === constant.fixedSessionId.gpt) {
      return;
    }
    if (!data.notice_type) {
      data.notice_type = constant.messageNoticeType.messageRead;
    }
    if (chatAction.isCurrentSessionAndActive(data.session)) {
      // app在聊天页面且是前台情况下，不发送送达，因为会发送已读
      if (data.notice_type === constant.messageNoticeType.messageArrived) {
        console.warn('sendMessageUtil sendMsgReadByCheck 1', data.notice_type, data);
        return;
      }
      return this.sendMsgRead(data);
    }
    if (data.notice_type === constant.messageNoticeType.messageRead) {
      console.warn('sendMessageUtil sendMsgReadByCheck 2', data.notice_type, data);
      data.notice_type = constant.messageNoticeType.messageArrived;
    }
    return this.sendMsgRead(data);
  };

  /**
   * 发送消息已读
   */
  sendMsgRead = async (data) => {
    if (Configs.disableMessageRead(data.session?.isGroup)) return;
    if (!data.ownerId) {
      data.ownerId = userStore.imId;
      data.retryCount = 0;
    } else if (data.ownerId !== userStore.imId) {
      return;
    }
    if (!imWebSocket.isAvailable) {
      if (++data.retryCount > 5) return;
      await promiseUtil.sleep(2000);
      return this.sendMsgRead(data);
    }
    if (this.sendMsgReading) {
      if (++data.retryCount > 20) return;
      await promiseUtil.sleep(50);
      return this.sendMsgRead(data);
    }
    this.sendMsgReading = true;
    const values = messageReadUtil.getSendMessageRead(data);
    if (values) {
      for (let x of values) {
        this._sendP2PMsgRead(x);
        await promiseUtil.sleep(1);
      }
    }
    this.sendMsgReading = false;
  };

  /**
   * 发送消息已读
   */
  _sendP2PMsgRead = (data) => {
    try {
      if (!imWebSocket.isAvailable) {
        messageReadUtil.sendMessageReadFail(data);
        return;
      }
      const { notice_type } = data;
      const { seq, timestamp, ownerId } = this.getSendInfo();
      const sendData = {
        action: constant.messageAction.msgP2P,
        data: {
          msg: {
            data: {
              im_id: ownerId,
              notice_type,
              rim_id: data.receiver_id,
              timestamp,
              msg_ids: data.messages.map((it) => it.msgId),
              // group_id: data.group_id,
            },
            type: constant.messageType.notice,
          },
          // persistence: 1,
          receiver_id: data.receiver_id,
          // send_self: 2,
          // sender_id: ownerId,
        },
        seq,
      };
      if (data.group_id) {
        sendData.data.msg.data.group_id = data.group_id;
      }
      if (imWebSocket.send(sendData)) {
        receiptMessageUtil.addMessageReceipt({
          seq,
          receiptType: sendData.action,
          message: data,
        });
        return;
      }
    } catch (e) {
      console.warn('SendMessageUtil _sendP2PMsgRead error', e);
    }
    messageReadUtil.sendMessageReadFail(data);
  };

  /**
   * 发送转账消息, 对方接收转账后退还时，修改content里面的status和originMsgId（发送转账消息的msgId），其它数据不用改
   * @param content {object} {orderId: 1, amount: '100', status: 0, remark: '备注', imId: 1, msgIds}//status 1待接收 2已接收 -1已退回 -2已过期
   * @param sessionId {number} 会话ID 即单聊的用户imId
   * @param type {string}
   * @param isGroup {boolean}
   * @return {Promise<void>}
   */
  sendTransferMessage = async ({
    sessionId,
    type = constant.messageType.transfer,
    isGroup,
    ...content
  }) => {
    await this.sendMessage({
      type,
      content: JSON.stringify(content),
      isGroup,
      sessionId,
      extra: JSON.stringify({ fromName: content?.fromUserName, amount: content.amount }),
      mustSave: true,
    });
  };

  sendReceiveMessage = async ({
    sessionId,
    type = constant.messageType.receiveCreate,
    isGroup = false,
    ...content
  }) => {
    await this.sendMessage({
      type,
      content: JSON.stringify(content),
      isGroup,
      sessionId,
      extra: JSON.stringify({
        fromName: userStore.userName,
        amount: content.amount,
        desc: content?.remark,
      }),
      mustSave: true,
    });
  };

  sendRedPacketMessage = async ({ type = constant.messageType.redPacket, ...content }) => {
    await this.sendMessage({
      type,
      content: JSON.stringify(content),
      isGroup: content.isGroup || false,
      sessionId: content.sessionId,
      extra: JSON.stringify({
        fromName: content.fromUserName,
        amount: content.amount,
        desc: content.remark,
      }),
      mustSave: true,
    });
  };

  sendP2PCallMessage = async (sessionId, type, state, channel) => {
    const { seq, timestamp, ownerId, reqId } = this.getSendInfo();

    // 发送给服务器的数据
    const sendData = {
      action: constant.messageAction.msgP2P,
      data: {
        msg: {
          im_id: ownerId,
          rim_id: sessionId,
          timestamp,
          req_id: reqId,
          state,
          channel,
          type,
          extra: JSON.stringify({
            fromName: userStore.userName,
          }),
        },
        persistence: 1,
        receiver_id: sessionId,
        send_self: 0,
        sender_id: ownerId,
      },
      seq,
    };
    console.log('1112222222', sendData);
    if (imWebSocket.send(sendData)) {
    } else {
      logger.warn('发送语音通话失败');
    }
  };

  sendMeetingCallMessage = async (sessionId, state, channel, meetingInfo) => {
    const { disableSend } = await imAction.isDisableSend({
      sessionId,
      isGroup: false,
      isSend: true,
    });
    if (disableSend) {
      return;
    }
    const { seq, timestamp, ownerId, reqId } = this.getSendInfo();

    // 发送给服务器的数据
    const sendData = {
      action: constant.messageAction.msgP2P,
      data: {
        msg: {
          im_id: ownerId,
          rim_id: sessionId,
          timestamp,
          req_id: reqId,
          state,
          channel,
          type: constant.messageType.meetingCall,
          meetingInfo,
          extra: JSON.stringify({
            fromName: userStore.userName,
          }),
        },
        persistence: 1,
        receiver_id: sessionId,
        send_self: 0,
        sender_id: ownerId,
      },
      seq,
    };
    console.log('1112222222', sendData);
    if (imWebSocket.send(sendData)) {
    } else {
      console.warn('发送语音通话失败');
    }
  };

  /**
   * 会否需要上传
   * @param message
   * @return {boolean}
   */
  isRequestUpload = (message) => {
    switch (message.type) {
      case constant.messageType.video:
      case constant.messageType.image:
      case constant.messageType.file:
      case constant.messageType.audio:
        return !message.content;
    }
    return false;
  };

  upload = async (message) => {
    receiptMessageUtil.addUploadReceipt(message);
    try {
      if (this.isRequestUpload(message)) {
        const { fileUrl } = await imAction.uploadFile(message.imagePath);
        message.content = fileUrl;
      }
      receiptMessageUtil.removeUploadReceipt(message.seq);
    } catch (e) {
      receiptMessageUtil.removeUploadReceipt(message.seq);
      logger.warn('sendMessageUtil upload error', e);
      message.sendState = constant.messageSendState.failed;
      await messageDao.updateMessageBySeq(message);
      global.emitter.emit(constant.event.receiveMessage, { message });
      return Promise.reject({ message: I18n.t('msg_upload_fail') });
    }
  };

  sendImages = async (images, session) => {
    console.log('sendMessageUtil sendImages', session.sessionId, session.isGroup, images);
    for (let image of images) {
      if (fileUtil.isVideo(image.type)) {
        this.sendVideo(image, session);
      } else {
        this.sendImage(image, session);
      }
      await promiseUtil.sleep(10);
    }
  };

  sendImage = async (image, session) => {
    try {
      console.log('sendMessageUtil sendImage', session.sessionId, session.isGroup, image);
      // { size: 75786,
      //         height: 1100,
      //         width: 1650,
      //         original_uri: 'file://content://media/external/file/85',
      //         uri: 'file:///storage/emulated/0/Android/data/com.wallet.dev/files/Pictures/IMG_753062422E7E0B6439F645B405AD0A.jpeg',
      //         type: 'image' }
      const extra = {
        fileSize: image.size,
        height: image.height,
        width: image.width,
        fromName: userStore.userName,
      };
      await this.sendMessage({
        type: constant.messageType.image,
        content: '',
        isGroup: session.isGroup,
        sessionId: session.sessionId,
        imagePath: image.uri,
        extra: JSON.stringify(extra),
        session,
      });
    } catch (e) {
      logger.warn('sendMessageUtil sendImage error', e);
      return Promise.reject(e);
    }
  };

  /*sendVideos = async (videos, session) => {
    console.log('sendMessageUtil sendVideos', session.sessionId, session.isGroup, videos);
    for (let video of videos) {
      this.sendVideo(video, session);
      await promiseUtil.sleep(10);
    }
  };*/

  sendVideo = async (video, session) => {
    try {
      console.log('sendMessageUtil sendVideo', session.sessionId, session.isGroup, video);
      const extra = {
        fileSize: video.fileSize, // 视频大小
        duration: video.duration, // 视频时长
        fromName: userStore.userName,
      };
      let { thumbnailInfo } = video;
      if (!thumbnailInfo) {
        if (video.thumbnail) {
          thumbnailInfo = await fileUtil.getImageInfo(video.thumbnail);
        } else {
          thumbnailInfo = await fileUtil.createVideoThumbnail(video.uri);
        }
      }
      extra.posterHeight = thumbnailInfo.height;
      extra.posterWidth = thumbnailInfo.width;
      extra.posterSize = thumbnailInfo.size;

      const { fileUrl } = await imAction.uploadFile(thumbnailInfo.path);
      extra.posterUrl = fileUrl;
      await this.sendMessage({
        type: constant.messageType.video,
        content: '',
        isGroup: session.isGroup,
        sessionId: session.sessionId,
        imagePath: video.uri,
        extra: JSON.stringify(extra),
        session,
      });
    } catch (e) {
      logger.warn('sendMessageUtil sendVideo error', e);
      return Promise.reject(e);
    }
  };

  sendFile = async (file, session) => {
    const fileType = fileUtil.getFileType(file.type);
    const destPath = await fileUtil.getMsgFilePath({
      ownerId: userStore.imId,
      sessionId: session.sessionId,
      type: fileType,
      suffix: fileUtil.getSuffix(file.name),
    });
    const fileCopyUri = await fileUtil.copyToCache(file.uri, destPath);
    console.log('sendMessageUtil sendFile', fileType, fileCopyUri, file);
    const uri = `file://${fileCopyUri}`;
    if (fileUtil.isVideo(file.type)) {
      return this.sendVideo({ uri }, session);
    }
    if (fileUtil.isImage(file.type)) {
      const wh = await fileUtil.getImageSize(uri);
      return this.sendImage({ ...wh, uri, size: file.size }, session);
    }
    try {
      console.log('sendMessageUtil sendFile', session.sessionId, session.isGroup, file);
      const extra = {
        fileSize: file.size,
        fileName: file.name,
        fileType,
        type: file.type,
        fromName: userStore.userName,
      };
      await this.sendMessage({
        type: constant.messageType.file,
        content: '',
        isGroup: session.isGroup,
        sessionId: session.sessionId,
        imagePath: uri,
        extra: JSON.stringify(extra),
        session,
      });
    } catch (e) {
      logger.warn('sendMessageUtil sendFile error', e);
      return Promise.reject(e);
    }
  };

  sendAudio = async (audio, session) => {
    try {
      console.log('sendMessageUtil sendAudio', session.sessionId, session.isGroup, audio);
      const extra = {
        fileSize: audio.fileSize,
        fromName: userStore.userName,
      };
      await this.sendMessage({
        type: constant.messageType.audio,
        content: '',
        isGroup: session.isGroup,
        sessionId: session.sessionId,
        imagePath: audio.filePath,
        timeLength: audio.timeLength,
        extra: JSON.stringify(extra),
        session,
      });
    } catch (e) {
      logger.warn('sendMessageUtil sendAudio error', e);
      return Promise.reject(e);
    }
  };

  sendGroupInvite = async ({ groupId, imIds, targetNames }) => {
    try {
      console.log('sendMessageUtil sendGroupInvite', groupId, imIds);
      const noticeMsg = {
        charter_member: userStore.imId,
        group_id: groupId,
        member_ids: imIds,
        notify_type: constant.messageNoticeType.groupInvite,
        notice_type: constant.messageNoticeType.groupInvite,
      };
      // noticeMsg
      await this.sendMessage({
        type: constant.messageType.tip,
        content: I18n.t('page_group_tips_you_invite_other', { name: targetNames }),
        isGroup: 1,
        sessionId: groupId,
        noticeMsg,
      });
    } catch (e) {
      logger.warn('sendMessageUtil sendGroupInvite error', e);
      return Promise.reject(e);
    }
  };

  sendWithdraw = async (message) => {
    if (message.msgId) {
      const { seq } = this.getSendInfo();
      const data = this.getSendDataByMessage({ message });
      data.data.msg = {
        content: '',
        fileSize: 0.0,
        msgId: message.msgId,
        timeLength: 0.0,
        type: constant.messageType.withdraw,
      };
      data.seq = seq;
      if (!imWebSocket.send(data)) {
        throw new Error(I18n.t('msg_socket_error'));
      }
      receiptMessageUtil.addMessageReceipt({
        seq,
        receiptType: constant.messageType.withdraw,
      });
    }
    message.content = I18n.t('page_chat_withdraw_you');
    message.type = constant.messageType.tip;
    await chatSessionDao.updateMessageWithSession(message, false);
    global.emitter.emit(constant.event.receiveMessage, { message });
  };

  getSendInfo = () => {
    const timestamp = new Date().getTime();
    const { imId } = userStore;
    return {
      seq: timestamp,
      timestamp,
      ownerId: imId,
      reqId: `${deviceInfo.getUniqueID()}${imId}${timestamp}`,
    };
  };
}

export default new SendMessageUtil();
