import constant from '../store/constant';

/**
 * 好友
 * <AUTHOR>
 */
export default class Friend {
  imId = 0;

  ownerId = 0;

  nickname = null;

  memo = null;

  phone = null;

  countryCode = null;

  account = null;

  avatar = null;

  isDisturb = 0;

  isBlacked = 0;

  beDeleted = 0;

  isHide = 0; // 是否隐藏，非好友

  relationType = 0; // 0:非好友，1:好友，2:有一方已删除

  paymentUserId = 0;

  userType = constant.userType.im;

  isSelf = false;

  extra = null; // 拓展字段，json字符串
}
