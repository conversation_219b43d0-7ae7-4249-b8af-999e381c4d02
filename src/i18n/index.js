import I18n from 'react-native-i18n';
import DeviceInfo from '../util/deviceInfo';
import en from './locales/en.json';
import zh from './locales/zh.json';
import km from './locales/km.json';
import ko from './locales/ko.json';
import th from './locales/th.json';
import vi from './locales/vi.json';
import Session from '../api/session';
import stores from '../store';
import I18nUtil from '../util/I18nUtil';
import constant from '../store/constant';

// 首选默认语言
I18n.defaultLocale = 'en';

// 按这样的（en_US en.js）顺序去查找文件
I18n.fallbacks = true;

I18n.translations = { en, zh, km, ko, th, vi };

/*function checkKey(translations) {
  // 查看国际化文集的差异部分
  const allKey = [...new Set(Object.values(translations).flatMap((item) => Object.keys(item)))];
  console.log('I18n allKey', allKey.length);
  Object.keys(translations).forEach((name) => {
    const keys = Object.keys(translations[name]);
    const result = allKey.filter((item) => !keys.includes(item));
    console.log('I18n 差集', name, keys.length, result);
  });
}
checkKey(I18n.translations);*/

/**
 * 获取系统语言
 */
function getDeviceLocale() {
  const locale = DeviceInfo.getDeviceLocale();
  if (locale.toLowerCase().indexOf('km') >= 0 || locale.toLowerCase().indexOf('kh') >= 0) {
    return 'km';
  }
  if (locale.toLowerCase().indexOf('en') >= 0) {
    return 'en';
  }
  if (locale.toLowerCase().indexOf('zh') >= 0) {
    return 'zh';
  }
  if (locale.toLowerCase().indexOf('ko') >= 0) {
    return 'ko';
  }
  if (locale.toLowerCase().indexOf('th') >= 0) {
    return 'th';
  }
  if (locale.toLowerCase().indexOf('vi') >= 0) {
    return 'vi';
  }
  return 'en';
}

/**
 * 初始化用户首先语言环境
 * 参考 https://www.jianshu.com/p/4dc5612854eb
 */
export function initLanguage() {
  return Session.getLanguage().then((language) => {
    if (language) {
      I18n.locale = language;
    } else {
      I18n.locale = getDeviceLocale();
    }
    if (global.IS_IOS) {
      I18nUtil.getDefaultLanguage(I18n.locale, () => {});
    }
    // 存储到本地
    Session.setLanguage(I18n.locale);
    stores.settingsAction.setLanguage(I18n.locale);
    return Promise.resolve(true);
  });
}

export async function initConstantsEnum() {
  await stores.applicationAction.initConstants();
  stores.resumeAction.queryConstants();
  stores.jobAction.queryConstants();
  stores.userAction.queryConstants();
}

/**
 * 切换语言
 * @param {*} language
 */
export function setLanguage(language) {
  I18n.locale = language;
  stores.settingsAction.setLanguage(language);
  // 切换语言需要重新load一下应用常量
  initConstantsEnum();
  // 存储到本地
  Session.setLanguage(language);
}

export function getLanguage() {
  return I18n.locale;
}

export function getAcceptLanguage() {
  return constant.languageMap[getLanguage()] || constant.languageMap.en;
}

export function getAcceptLanguageByIM() {
  return constant.imLanguageMap[getLanguage()] || constant.imLanguageMap.en;
}

export default I18n;
