import qs from 'qs';
import Restful from './restful';

class DynamicService {
  /**
    * 获取所有的动态列表
    * request: /v1.0.0/users/twitters
    * method: GET
    * @param param - param
    */
  async queryDynamics(param) {
    const data = await Restful.get(`/users/twitters?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
    return data;
  }

  /**
   * 发布动态
   * request: /v1.0.0/users/twitters
   * method: POST
   * @param twitter - twitter
   */
  async publishDynamic(twitter) {
    const data = await Restful.post('/users/twitters', twitter);
    return data;
  }

  /**
   * 修改动态
   * request: /v1.0.0/users/twitters/{twitterId}
   * method: PUT
   * @param twitterId - twitterId
   * @param twitter - twitter
   */
  async modifyDynamic(twitterId, twitter) {
    const data = await Restful.put(`/users/twitters/${twitterId}`, twitter);
    return data;
  }

  /**
   * 删除动态
   * request: /v1.0.0/users/twitters/{twitterId}
   * method: DELETE
   * @param twitterId - twitterId
   */
  async deleteDynamic(twitterId) {
    const data = await Restful.delete(`/users/twitters/${twitterId}`);
    return data;
  }

  /**
  * 发表评论
  * request: /v1.0.0/users/twitters/{twitterId}/comments
  * method: POST
  * @param twitterId - twitterId
  * @param twitterComment - twitterComment
  */
  async publishComment(twitterId, twitterComment) {
    const data = await Restful.post(`/users/twitters/${twitterId}/comments`, twitterComment);
    return data;
  }

  /**
   * 获取动态的评论列表
   * request: /v1.0.0/users/twitters/{twitterId}/comments
   * method: GET
   * @param param - param
   */
  async queryComments(twitterId, param) {
    const data = await Restful.get(`/users/twitters/${twitterId}/comments?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
    return data;
  }

  /**
  * 修改评论
  * request: /v1.0.0/users/twitters/{twitterId}/comments/{twitterCommentId}
  * method: PUT
  * @param twitterId - twitterId
  * @param twitterCommentId - twitterCommentId
  * @param twitterComment - twitterComment
  */
  async modifyComment(twitterId, twitterCommentId, twitterComment) {
    const data = await Restful.put(`/users/twitters/${twitterId}/comments/${twitterCommentId}`, twitterComment);
    return data;
  }

  /**
  * 删除评论
  * request: /v1.0.0/users/twitters/{twitterId}/comments/{twitterCommentId}
  * method: DELETE
  * @param twitterId - twitterId
  * @param twitterCommentId - twitterCommentId
  */
  async deleteComment(twitterId, twitterCommentId) {
    const data = await Restful.delete(`/users/twitters/${twitterId}/comments/${twitterCommentId}`);
    return data;
  }

  /**
   * 点赞
   * request: /v1.0.0/users/twitters/{twitterId}/likes
   * method: POST
   * @param twitterId - twitterId
   */
  async likes(twitterId) {
    const data = await Restful.post(`users/twitters/${twitterId}/likes`);
    return data;
  }

  /**
   * 取消点赞
   * request: /v1.0.0/users/twitters/{twitterId}/dislikes
   * method: DELETE
   * @param twitterId - twitterId
   */
  async dislikes(twitterId) {
    const data = await Restful.delete(`/users/twitters/${twitterId}/dislikes`);
    return data;
  }

  /**
  * 上传动态图片
  * request: /v1.0.0/users/twitters/images
  * method: POST
  * @param file - file
  */
  async uploadDynamicImage(file) {
    const data = await Restful.upload('/users/twitters/images', file);
    return data;
  }

  /**
   * 拉黑用户
   * request: /v1.0.0/users/twitters/block/{blockedId}
   * method: POST
   * @param blockedId - blockedId
   */
  async blockedUser(blockedId) {
    const data = await Restful.post(`/users/twitters/block/${blockedId}`);
    return data;
  }

  /**
  * 取消拉黑用户
  * request: /v1.0.0/users/twitters/unblock/{blockedId}
  * method: POST
  * @param blockedId - blockedId
  */
  async unblockedUser(blockedId) {
    const data = await Restful.post(`/users/twitters/unblock/${blockedId}`);
    return data;
  }

  /**
   * 举报用户
   * request: /v1.0.0/users/twitters/reports/{reportedId}
   * method: POST
   * @param reportedId - reportedId
   * @param reportParam - reportParam
   */
  async reportedUser(reportedId, reportParam) {
    const data = await Restful.post(`/users/twitters/reports/${reportedId}`, reportParam);
    return data;
  }
}

export default new DynamicService();
