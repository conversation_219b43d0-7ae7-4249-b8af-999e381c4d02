import axios from 'axios';
import qs from 'qs';
import _ from 'lodash';
import configs from '../configs';
import { getAcceptLanguage } from '../i18n';
import deviceInfo from '../util/deviceInfo';
import Session from './session';

// 创建一个自定义的axios
const request = axios.create();

// api url
request.defaults.baseURL = configs.serverURL;
request.defaults.headers.post['Content-Type'] = 'application/json';
request.defaults.headers.put['Content-Type'] = 'application/json';

/**
 * 请求拦截器
 * 处理token
 */
/*request.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => Promise.reject(error)
);*/

/**
 * 响应拦截器
 * 处理响应错误
 * 处理重新登录
 * 401, 403
 * 等等
 */
request.interceptors.response.use(
  (response) => {
    if (configs.printHttpResponse) {
      const { status, data, headers, config } = response;
      console.debug('response =====>', status, config.method, config.url);
      console.debug('response request headers', config.headers);
      console.debug('response request data', config.data);
      console.debug('response headers', headers);
      console.debug('response data', data);
      console.debug('response <=====');
    }
    return response;
  },
  (error) => {
    if (!error?.response) {
      console.warn('response error', error?.message, error?.request?._response, error);
    } else {
      const { status, data, headers, config } = error.response;
      if (configs.printHttpResponseError) {
        console.warn('response error =====>', status, config.method, config.url);
        console.warn(
          'response error request headers',
          config.headers && JSON.stringify(config.headers)
        );
        if (typeof config.data === 'string') {
          console.warn('response error request data', config.data);
        } else {
          console.warn('response error request data', config.data && JSON.stringify(config.data));
        }
        console.warn('response error headers', headers && JSON.stringify(headers));
        console.warn('response error data', data && JSON.stringify(data));
        console.warn('response error <=====');
      }
      if (data && !data.message && data.fieldErrors) {
        const message = Object.keys(data.fieldErrors)
          .map((key) => `${key}${data.fieldErrors[key]}`)
          .join(';');
        error.response.data = {
          message,
          ...data,
        };
      }
    }

    return Promise.reject(error);
  }
);

async function getConfig(token, config, headers) {
  const imToken = await Session.getImAccessToken();
  headers = {
    Authorization: token,
    'Accept-Language': getAcceptLanguage(),
    'X-APPLICATION-ID': configs.im.appId,
    'X-APPLICATION-VERSION': deviceInfo.getVersion(),
    'x-im-token': imToken?.token || '',
    ...(headers || {}),
  };
  if (config) {
    return _.merge({ headers }, config);
  }
  return { headers };
}

/**
 * get request
 */
async function get(url, token, config) {
  config = await getConfig(token, config);
  return request.get(url, config);
}

/**
 * post request
 */
async function post(url, data, token, config) {
  config = await getConfig(token, config);
  return request.post(url, data, config);
}

/**
 * delete request
 */
async function del(url, token, config) {
  config = await getConfig(token, config);
  return request.delete(url, config);
}

/**
 * put request
 */
async function put(url, data, token, config) {
  config = await getConfig(token, config);
  return request.put(url, data, config);
}

/**
 * post form表单提交
 */
async function formPost(url, data, token, config) {
  config = await getConfig(token, config, { 'Content-Type': 'application/x-www-form-urlencoded' });
  return request.post(url, qs.stringify(data, { arrayFormat: 'repeat' }), config);
}

/**
 * get form表单提交
 */
async function formGet(url, token, config) {
  config = await getConfig(token, config, { 'Content-Type': 'application/x-www-form-urlencoded' });
  return request.get(url, config);
}

/**
 * 文件上传
 */
async function upload(url, formData, token, config) {
  config = await getConfig(token, config, {
    'Content-Type': 'multipart/form-data',
    Accept: 'application/json',
  });
  return request.post(url, formData, config);
}

class Ajax {
  constructor() {
    this.token = '';
  }

  setToken(token) {
    this.token = token || '';
    return this;
  }

  get(url, config) {
    return get(url, this.token, config);
  }

  post(url, data, config) {
    return post(url, data, this.token, config);
  }

  delete(url, config) {
    return del(url, this.token, config);
  }

  put(url, data, config) {
    return put(url, data, this.token, config);
  }

  formPost(url, data, config) {
    return formPost(url, data, this.token, config);
  }

  formGet(url, config) {
    return formGet(url, this.token, config);
  }

  upload(url, formData, config) {
    return upload(url, formData, this.token, config);
  }
}

export default new Ajax();
