import Restful from './restful';

class CityService {
  async queryHotLocations() {
    // return Restful.get('/maps/hot/locations');
    return Restful.get('/map/places/hot');
  }

  async getLocalCity(lng, lat) {
    // return Restful.get(`/maps/cities/longitude/${lng}/latitude/${lat}`);
    return Restful.get(`/map/places/longitude/${lng}/latitude/${lat}`);
  }

  async getAllPlaceTree(lng, lat) {
    return Restful.get(`/map/places/tree`);
  }
}

export default new CityService();
