import Restful from './restful';
import qs from 'qs';

class ApplicationService {
  async getConstants() {
    return Restful.get('/application/constants');
  }

  async getConstantsByName(name) {
    return Restful.get(`/application/constants/${name}`);
  }

  async getConstantsFlatByName(name) {
    return Restful.get(`/application/constants/${name}/flat`);
  }

  /**
   * 获取最新的APP版本号
   * request: /v1.0.0/apps/versions/last
   * method: GET
   */
  async getLastestAPPVersion(appType) {
    return Restful.get(`/apps/versions/last?appType=${appType}`);
  }

  /**
   * 分页查询 APP版本号信息
   * request: /v1.0.0/admins/apps/versions
   * method: GET
   */
  async getAllAPPVersions(param) {
    return Restful.get(`/admins/apps/versions?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 新增 APP版本号信息
   * request: /v1.0.0/admins/apps/versions/{id}
   * method: POST
   * @param appVersion - appVersion
   */
  async addAppVersion(appVersion) {
    return Restful.post('/admins/apps/versions', appVersion);
  }

  /**
   * 删除 APP版本号信息
   * request: /v1.0.0/admins/apps/versions/{id}
   * method: DELETE
   * @param id - id
   */
  async deleteResume(id) {
    return Restful.delete(`/admins/apps/versions/${id}`);
  }
}

export default new ApplicationService();
