import constant from '../store/constant';

/**
 * WebSocket
 * <AUTHOR>
 */
export default class BaseWebSocket {
  constructor(url) {
    this.url = url;
    this.typeMap = new Map();
    this.id = new Date().getTime();
    this.state = constant.imSocketState.init;
  }

  get isAvailable() {
    return this.state === constant.imSocketState.opened;
  }

  _changeState = state => {
    console.log(`BaseWebSocket[${this.id}] _changeState`, this.state, state);
    this.state = state;
  };

  /**
   * 开始连接 WebSocket
   */
  connect = () => {
    try {
      console.log(`BaseWebSocket[${this.id}] _connect`);
      this._changeState(constant.imSocketState.connecting);
      const socket = new WebSocket(this.url);
      socket.addEventListener('open', this._onOpen);
      socket.addEventListener('message', this._onMessage);
      socket.addEventListener('error', this._onError);
      socket.addEventListener('close', this._onClose);
      this._socket = socket;
      return true;
    } catch (e) {
      logger.warn(`BaseWebSocket[${this.id}] _connect`, e);
      this._changeState(constant.imSocketState.error);
      return false;
    }
  };

  /**
   * 关闭连接
   */
  close = () => {
    console.log(`BaseWebSocket[${this.id}] close ======`);
    this._changeState(constant.imSocketState.close);
    this._socket?.close();
  };

  /**
   * 发送数据
   * @param data
   */
  send = data => {
    if (this.isAvailable) {
      console.log(`BaseWebSocket[${this.id}] send`, data);
      this._socket.send(typeof data === 'string' ? data : JSON.stringify(data));
      return true;
    }
    logger.warn(`BaseWebSocket[${this.id}] send 失败`, this.state, data);
    return false;
  };

  addEventListener = (type, listener) => {
    this.typeMap.set(type, listener);
  };

  clearEventListener = () => {
    this.typeMap.clear();
  };

  onResponse = (type, event) => {
    const listener = this.typeMap.get(type);
    if (typeof listener === 'function') {
      listener(event, this);
    }
  };

  /**
   * 当一个 WebSocket 连接成功时触发
   */
  _onOpen = event => {
    console.log(`BaseWebSocket[${this.id}] _onOpen`, { ...event });
    this._changeState(constant.imSocketState.opened);
    this.onResponse('open', event);
  };

  /**
   * 当通过 WebSocket 收到数据时触发
   */
  _onMessage = event => {
    console.log(`BaseWebSocket[${this.id}] _onMessage`, event.data);
    this.onResponse('message', event);
  };

  /**
   * 当一个 WebSocket 连接因错误而关闭时触发，例如无法发送数据时
   */
  _onError = event => {
    logger.warn(`BaseWebSocket[${this.id}] _onError`, { ...event });
    this._changeState(constant.imSocketState.error);
    this.onResponse('error', event);
  };

  /**
   * 当一个 WebSocket 连接被关闭时触发
   */
  _onClose = event => {
    console.log(`BaseWebSocket[${this.id}] _onClose`, { ...event });
    this._socket = null;
    this._changeState(constant.imSocketState.close);
    this.onResponse('close', event);
  };
}
