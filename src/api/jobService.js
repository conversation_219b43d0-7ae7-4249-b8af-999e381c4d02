import Restful from './restful';
import globalStore from '../store/stores/global';
import qs from 'qs';

class JobService {
  /**
   * 用户端查询职位
   * Restful: /v1.0.0/jobs
   * method: GET
   * @param company -           公司名称
   * @param employerId -        公司Id
   * @param jobTitle -          职位标题
   * @param jobTitleOrCompany - 职位标题或公司名称
   * @param limit -
   * @param locationId -        外键，城市Id，常量：LOCATION
   * @param now -
   * @param offline -           是否下线
   * @param offset -
   * @param online -            是否上线
   * @param order -
   * @param page -
   * @param query -
   * @param size -
   * @param status -            常量：JOB_STATUS
   * @param urgent -            是否紧急
   * eg.
   */
  async queryJobs(param) {
    return Restful.get(`/jobs?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 查询公司列表
   */
  async queryEmployers(param) {
    return Restful.get(`/employers?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 分页查询推荐信息
   */
  async queryRecommends(param) {
    return Restful.get(`/cms/recommendations?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 查询已沟通的职位
   */
  async queryCommunicatedJobs(param) {
    return Restful.get(`/users/jobs?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 查询已投递、面试的职位
   */
  async queryDeliveredJobs(param) {
    return Restful.get(
      `/users/jobs/applications?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 用户端查询已申请的职位 （单条）
   */
  async querySingleJobs(jobId) {
    return Restful.get(`/users/jobs/${jobId}`);
  }

  /**
   * 用户端查询职位 （单条）
   */
  async queryJob(jobId) {
    return Restful.get(`/jobs/${jobId}`);
  }

  /**
   * 用户端查询单条公司详情
   */
  async querySingleCompany(employerId) {
    return Restful.get(`/employers/${employerId}/public`);
  }

  /**
   * 标记已沟通的职位
   */
  async flagCommunicated(jobId) {
    return Restful.put(`/users/jobs/${jobId}/communication`);
  }

  /**
   * 申请职位==投递简历
   * request: /v1.0.0/users/jobs/{jobId}/applications
   * method: POST
   * @param jobId - jobId
   * @param jobApply - jobApply
   */
  async sendResume(jobId, jobApply) {
    return Restful.post(`/users/jobs/${jobId}/applications`, jobApply);
  }

  /**
   * 更新求职意向
   * request: users/resumes/intentions
   * method: PUT
   * @param intention - intention
   */
  async updateIntentions(intention) {
    return Restful.put(`/users/resumes/intentions`, intention);
  }

  /**
   * 更新求职状态
   * request: users/resumes/intentions/work/{status}
   * method: PUT
   * @param status - status
   */
  async updateWorkStatus(status) {
    return Restful.put(`/users/resumes/intentions/work/${status}`);
  }

  /**
   * 获取求职意向列表
   * request: /users/resumes/intentions
   * method: GET
   */
  async getIntensions() {
    return Restful.get('/users/resumes/intentions');
  }

  /**
   * 获取我的页面统计数字
   * request: /users/jobs/statistics'
   * method: GET
   */
  async getJobStatistics() {
    if (globalStore.isEnterprise) return {};
    return Restful.get('/users/jobs/statistics');
  }

  /**
   * 广告点击
   * request: /ad/click/promotion-msg
   * method: POST
   * @param adId - adId
   */
  async adJobClick(clickData) {
    return Restful.get(`/ad/click/promotion-msg?data=${clickData}`);
  }
}

export default new JobService();
