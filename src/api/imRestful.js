/**
 * 接口请求
 * 基本http请求
 * 支持token刷新
 * 返回接口处理
 * 错误处理
 * 请求头处理
 * author: moke
 */
// import NetInfo from '@react-native-community/netinfo';
import qs from 'qs';
import configs from '../configs';
import Ajax from '../common/ajax';
import Session from './session';
import I18n, { getAcceptLanguageByIM } from '../i18n';
import constant from '../store/constant';
import promiseUtil from '../util/promiseUtil';
import deviceInfo from '../util/deviceInfo';

/**
 * 获取结果
 * 成功： 响应数据 data，消息 message
 * 失败： 响应状态码 statusCode，消息 message
 */
function getResult(res) {
  return res.data;
}

function sessionTimeout(data) {
  data.isCleanStore = true;
  global.emitter.emit(constant.event.imSessionTimeout);
}

function showMessage(i18nKey, options, message) {
  if (options?.showToast !== false) {
    toast.show(i18nKey ? I18n.t(i18nKey) : message);
  }
}

/**
 * 错误处理
 * token过期处理
 */
function handlerError(err, options) {
  if (!err || !err.response) {
    /*NetInfo.fetch().then(({ isConnected }) => {
      console.warn('isConnected', isConnected);
      if (!isConnected) {
        showMessage('msg_network_connect_fail', options);
      }
    });*/
    return Promise.reject(err);
  }
  const { status, data } = err.response;
  const dataStatus = data && data.status;
  if (dataStatus) {
    data.httpStatus = status;
  }

  if (status === 403) {
    // 多端登录，被踢下线  需要处理退出逻辑，广播超时事件回到init app里面处理
    // {"timestamp":"2021-01-05T22:43:45.183+0000","status":433,"error":"Forbidden","message":"您已经从另一台设备登录","successful":false}
    /*if (dataStatus === 433) {
      showMessage(null, options, data.message);
      sessionTimeout(data);
    }*/
  } else if (status === 401) {
    // 刷新token失败，refresh token已经失效
    // {"successful":false,"status":100003,"message":"无效的TOKEN"}
    // {"timestamp":"2021-01-08T07:46:16.899+0000","status":100004,"error":"Unauthorized","message":"请您先登录","successful":false}
    /*if (dataStatus === 100003 || dataStatus === 100004) {
      sessionTimeout(data);
    }*/
  } else if (status <= 504 && status >= 500) {
    // 服务器异常
    // showMessage(status === 502 ? 'msg_server_error_502' : 'server_is_error', options);
  } else if (status >= 404 && status < 422) {
    // 这种情况是不允许出现的，所以，这个消息只会给开发者提醒用的
    // 使用代理可能出现410
    // showMessage(null, options, I18n.t('msg_api_not_exist', { status }));
  }
  return Promise.reject(data);
}

/**
 * 请求API，把结果展平再返回
 */
class Restful {
  constructor() {
    this.ajaxMap = new Map();
  }

  getAjax(url) {
    let ajax = this.ajaxMap.get(url);
    if (!ajax) {
      ajax = new Ajax(url);
      this.ajaxMap.set(url, ajax);
    }
    return ajax;
  }

  async getAuthorization(options) {
    if (options?.noAuthorization) return null;
    const token = await this.getImToken(options?.skipRefreshToken);
    if (!token) return null;
    return 'Bearer ' + token;
  }

  async getImToken(skipRefreshToken) {
    const accessToken = await Session.getImAccessToken();
    if (!accessToken) {
      return null;
    }
    if (skipRefreshToken || accessToken.isValid()) {
      console.log('token----isValid', skipRefreshToken);
      return accessToken.token;
    }
    return this.freshImAccessToken(accessToken.token);
  }

  /**
   * 刷新IM access token
   */
  async freshImAccessToken(token, defaultToken = token) {
    console.log('freshImAccessToken 0');
    if (this.freshAccessTokenRes?.token === token) {
      if (this.freshAccessTokenRes.locked) {
        console.log('freshImAccessToken sleep');
        await promiseUtil.sleep(100);
      }
      console.log('freshImAccessToken sleep 1', this.freshAccessTokenRes);
      if (this.freshAccessTokenRes.newToken) return this.freshAccessTokenRes.newToken;
      if (this.freshAccessTokenRes.error) return Promise.reject(this.freshAccessTokenRes.error);
      return this.freshImAccessToken(token, defaultToken);
    }
    try {
      global.emitter.emit(constant.event.imAccessTokenChange, null);
      this.freshAccessTokenRes = {
        error: null,
        newToken: null,
        token,
        locked: true,
      };
      const url = '/auth?server=warm';
      const headers = await this.getHeaders();
      headers.Authorization = `Bearer ${token}`;
      const ajax = this.getAjax(configs.im.serverImURL);
      const res = await ajax.get({ url, headers }).then(getResult, handlerError);
      console.log('freshImAccessToken res', res);
      if (res?.data?.im_token) {
        const newAccessToken = await Session.setImAccessToken(res.data.im_token, res.data.im_exp);
        this.freshAccessTokenRes.newToken = newAccessToken.token;
      } else if (res?.message) {
        // token过期或无效
        if ([404, -403, -402, -401].includes(res.code)) {
          sessionTimeout(res);
        }
        this.freshAccessTokenRes.error = res;
        return Promise.reject(res);
      } else {
        this.freshAccessTokenRes.newToken = defaultToken;
      }
      return this.freshAccessTokenRes.newToken;
    } catch (e) {
      console.warn('freshImAccessToken e', e);
      this.freshAccessTokenRes.error = e;
      return Promise.reject(e);
    } finally {
      this.freshAccessTokenRes.locked = false;
    }
  }

  async getHeaders() {
    return {
      'X-APPLICATION-ID': configs.im.appId,
      'X-APPLICATION-VERSION': deviceInfo.getVersion(),
      'Accept-Language': getAcceptLanguageByIM(),
    };
  }

  /**
   * 基本请求
   */
  async baseRequest(requestParams) {
    let { method, options, retryCount, ...param } = requestParams;
    if (retryCount) console.log('baseRequest retry count', retryCount, param.url);
    const authorization = await this.getAuthorization(options);
    param.headers = await this.getHeaders();
    if (authorization) {
      param.headers.Authorization = authorization;
    }
    try {
      let url;
      if (options?.useBaseUrl) {
        url = configs.im.serverBaseURL;
      } else if (options?.useImUrl) {
        url = configs.im.serverImURL;
      } else if (options?.isGPT) {
        url = configs.gpt.serverURL;
      }
      console.log('baseRequest', method, `${url}${param.url}`, retryCount);
      const ajax = this.getAjax(url);
      const res = await ajax[method].call(ajax, param);
      const { data } = res;
      if (options && (options.useBaseUrl || options.useImUrl)) {
        const code = data?.code;
        if (code === (options?.isIm ? 0 : 200)) {
          return data;
        }
        console.warn('restful im res error', data, res.config?.url);
        if (code === 404) {
          if (options?.skipRefreshToken) {
            // token过期，启动app时
            data.isCleanStore = true;
          }
        } else if ([-403, -402, -401].includes(code)) {
          // token过期或无效，非刷新token接口返回
          data.message = I18n.t('msg_token_expired');
          sessionTimeout(data);
        } else if (code && code !== -801) {
          showMessage(null, options, data.message || data.msg);
        }
        if (data?.msg) {
          data.message = data.msg;
        }
        return Promise.reject(data);
      }
      return data;
    } catch (e) {
      let responseData = e?.response?.data || e?.request?._response;
      if (typeof responseData === 'string') {
        // console.warn('baseRequest error', retryCount, param.url, responseData);
        if (responseData.match(/^\s*token expire , unauthorized/)) {
          responseData = { code: -30004, message: responseData };
          sessionTimeout(responseData);
          return Promise.reject(responseData);
        }
        // 请求失败，自动延迟重试，最多重试两次
        if (
          responseData === 'need wait db load,please wait' ||
          responseData.startsWith('unexpected end of stream on')
        ) {
          if (!requestParams.retryCount) {
            requestParams.retryCount = 0;
          }
          if (++requestParams.retryCount < 3) {
            await promiseUtil.sleep(500 * requestParams.retryCount);
            return this.baseRequest(requestParams);
          }
          return Promise.reject({ code: -3000, message: responseData });
        }
      }
      return handlerError(e, options);
    }
  }

  async get(url, param, options) {
    if (param && !url.includes('?')) {
      url = `${url}?${qs.stringify(param, { arrayFormat: 'repeat' })}`;
    }
    return this.baseRequest({ url, options, method: 'get' }).then((res) => res.data || res);
  }

  async delete(url, data, options) {
    return this.baseRequest({ url, data, options, method: 'delete' });
  }

  async post(url, data, options) {
    return this.baseRequest({ url, data, options, method: 'post' });
  }

  async put(url, data, options) {
    return this.baseRequest({ url, data, options, method: 'put' });
  }

  async patch(url, data, options) {
    return this.baseRequest({ url, data, options, method: 'patch' });
  }

  async formPost(url, data, options) {
    return this.baseRequest({ url, data, options, method: 'formPost' });
  }

  async formGet(url, options) {
    return this.baseRequest({ url, options, method: 'formGet' }).then((res) => res.data || res);
  }

  async upload(url, formData, options) {
    return this.baseRequest({ url, formData, options, method: 'upload' });
  }

  async uploadProgress(url, formData, onUploadProgress, options) {
    return this.baseRequest({ url, formData, onUploadProgress, options, method: 'uploadProgress' });
  }
}

export default new Restful();
