import Restful from './imRestful';

/**
 * im相关API
 */
class ImUserService {
  /**
   * 刷新token
   */
  async refreshAuthToken() {
    return Restful.get(`/auth?server=warm`, undefined, { skipRefreshToken: true, useImUrl: true });
  }

  /**
   * 手机号注册 - 获取验证码
   * {"phone":"***********","country_code":"+86","vcode_type":"login"}
   */
  async sendImPhoneCode(param) {
    return Restful.post(`/sms/${param.phone}/vcodes`, param, {
      useImUrl: true,
      skipRefreshToken: true,
    });
  }

  /**
   * 手机号注册 - 提交
   *{"phone":"***********","country_code":"+86","login_type":"vcode","account":"account","v_code":"0124","platform":"android","pwd":"96e79218965eb72c92a549dd5a330112","device":{"name":"unknown","model":"RMX3700","mac":"333658d7d4fe0615","version":"1.4.54","registration_id":"160a3797c91a9e623aa","platform":"android"}}
   */
  async registerAuth(param) {
    return Restful.post(`/auth`, param, { useImUrl: true, skipRefreshToken: true });
  }

  /**
   * 忘记密码 - 验证验证码
   * @param {*} param
   * code=4711&country_code=%2B86&phone=***********
   * @returns
   */
  async recoverValidateCode(param) {
    return Restful.formPost(`/passport/recover/validate`, param, {
      useImUrl: true,
      skipRefreshToken: true,
    });
  }

  /**
   * 忘记密码 - 设置密码
   * @param {*} param
   * recoverToken=e5650b37-10bd-497c-8684-c4dd8e5b4de5&password=96e79218965eb72c92a549dd5a330112
   * @returns
   */
  async recoverSetPassword(param) {
    return Restful.formPost(`/passport/recover/password`, param, {
      useImUrl: true,
      skipRefreshToken: true,
    });
  }

  /**
   * 修改/设置登录密码
   * @param {*} param
   *{"old_password":"96e79218965eb72c92a549dd5a330112","new_password":"e3ceb5881a0a1fdaad01296d7554868d"}
   * @returns
   */
  async setLoginPassword(param) {
    return Restful.post(`/users/${param.imId}/password"`, param, {
      useImUrl: true,
    });
  }

  /**
   * 退出登录im
   */
  async logoutIM() {
    return Restful.delete('/auth', undefined, { useImUrl: true });
  }

  /**
   * 获取当前用户相关更新信息
   * {"code":200,"message":"","data":{"login_request_id":"","settings":{"setting":1634781613,"chat":1634781613},"contacts":{"list":1635303223},"groups":{"list":1635756095},"res":[{"type":"settings","update_at":1634781613},{"type":"chat_settings","update_at":1634781613},{"type":"contacts","update_at":1635303223},{"type":"groups","update_at":1635756095}]}}
   */
  async getUserInfoRes(userId) {
    return Restful.get(`/users/${userId}/res`, null, { useBaseUrl: true });
  }

  /**
   * 获取当前用户相关更新信息
   * {"code":200,"message":"","data":{"login_request_id":"","settings":{"setting":1634781613,"chat":1634781613},"contacts":{"list":1635303223},"groups":{"list":1635756095},"res":[{"type":"settings","update_at":1634781613},{"type":"chat_settings","update_at":1634781613},{"type":"contacts","update_at":1635303223},{"type":"groups","update_at":1635756095}]}}
   */
  async getConfigByDHWL() {
    return Restful.get(`/config/${IS_ANDROID ? 'android' : 'iphone'}?channel=dhwl`, null, {
      useBaseUrl: true,
    });
  }

  /**
   * 修改im用户信息 (设置语言等)
   */
  async updateImUser(userId, param) {
    if (!param.nickname) {
      delete param.nickname;
    }
    if (!param.avatar) {
      delete param.avatar;
    }
    if (!userId || `${userId}` === '0') {
      return Promise.reject();
    }
    return Restful.post(`/users/${userId}/`, param, { useBaseUrl: true });
  }

  /**
   * 获取当前登录im用户信息
   * {"code":200,"message":"","data":{"account":"r0who99410","account_alias":"","im_id":10104,"nickname":"R4080","platform":"android","country_code":"+86","phone":"***********","sex":"","birthday":"","avatar":"","address":"","signature":"","password":true,"extension_info":{"wangyiyunxin":{"token":""}}}}
   */
  async getImUserInfo(userId) {
    return Restful.get(`/users/${userId}/`, null, { useBaseUrl: true });
  }

  /**
   * 修改im用户头像
   */
  async updateImAvatar(uri) {
    const formData = new FormData();
    const file = { uri, type: 'multipart/form-data', name: 'image.jpg' };
    formData.append('file', file);
    return Restful.post('/files', formData, { useBaseUrl: true });
  }

  /**
   * 修改im 手机号
   */
  async updateImPhone(userId, param) {
    return Restful.post(`/users/${userId}/phones`, param, { useBaseUrl: true });
  }
}

export default new ImUserService();
