import { autorun } from 'mobx';
import { AppState } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import Session from './session';
import userStore from '../store/stores/user';
import settingsStore from '../store/stores/settings';
import imAction from '../store/actions/imAction';
import constant from '../store/constant';
import receiveMessageUtil from '../database/receiveMessageUtil';
import configs from '../configs';
import chatStore from '../store/stores/chatStore';
import sendMessageUtil from '../database/sendMessageUtil';
import Debounce from 'debounce-decorator';
import BaseWebSocket from './baseWebSocket';
import receiptMessageUtil from '../database/receiptMessageUtil';
import I18n from '../i18n';

const SocketState = constant.imSocketState;

const PING_TIME = 50000;

const heartbeat = '{"action":"heartbeat","Data":{}}';

/**
 * IM WebSocket
 * <AUTHOR>
 */
class ImWebSocket {
  state = SocketState.close;

  get isAvailable() {
    const b = this.isNetworkConnected && !!this._socket?.isAvailable;
    logger.debug(
      `ImWebSocket isAvailable:${b} isNetworkConnected:${this.isNetworkConnected} state:${this._socket?.state}`
    );
    return b;
  }

  get hasConnectImSocket() {
    return userStore.hasConnectImSocket;
  }

  _changeState = (state) => {
    if (this.state !== state) {
      logger.info(`ImWebSocket _changeState this.state:${this.state} state:${state}`);
    }
    this.state = state;
    chatStore.changeImSocketState(state);
  };

  /**
   * 登录状态时自动连接socket
   */
  init = async () => {
    logger.info('ImWebSocket init ======');
    this.requestConnect = true;
    this.autorunDisposer = autorun(this.autorunConnect);
    this.unsubscribeNetInfo = NetInfo.addEventListener((state) => {
      logger.info(`ImWebSocket NetInfo isConnected:${state.isConnected} type:${state.type}`);
      this._reconnectByNetworkChange(state.isConnected);
    });
    global.emitter.on(constant.event.imAccessTokenChange, this.onImAccessTokenChange);
  };

  autorunConnect = () => {
    const { hasConnectImSocket } = userStore;
    logger.info(
      `ImWebSocket init autorun hasConnectImSocket:${hasConnectImSocket} requestConnect:${this.requestConnect}`
    );
    if (hasConnectImSocket) {
      this._connectDelay();
    } else {
      this._disconnect();
    }
  };

  onImAccessTokenChange = (token) => {
    this._disconnect();
    if (token) {
      this.autorunConnect();
    }
  };

  @Debounce(300)
  _reconnectByNetworkChange(isNetworkConnected) {
    this.isNetworkConnected = isNetworkConnected;
    if (isNetworkConnected) {
      this._reconnect(this.state, 0);
    } else {
      this._disconnect();
    }
  }

  @Debounce(30)
  _connectDelay() {
    this._connect();
  }

  /**
   * 关闭 - 退出程序时
   */
  close = () => {
    logger.info('ImWebSocket close ======');
    this.requestConnect = false;
    this._disconnect();
    this.autorunDisposer?.();
    this.unsubscribeNetInfo?.();
    global.emitter.off(constant.event.imAccessTokenChange, this.onImAccessTokenChange);
  };

  /**
   * 发送数据
   * @param data
   */
  send = (data) => {
    if (this.isAvailable) {
      logger.info('ImWebSocket send', data);
      this._socket.send(typeof data === 'string' ? data : JSON.stringify(data));
      return true;
    }
    if (!/"action":"heartbeat"/.test(data)) {
      toast.show(I18n.t('op_send_error'));
    }
    logger.warn('ImWebSocket send 失败 {this.state} {data}', this.state, data);
    this._reconnect(SocketState.error, 0);
    return false;
  };

  /**
   * 初始化 WebSocket
   */
  _connect = async () => {
    try {
      logger.info('ImWebSocket _connect');
      if (
        !this.requestConnect ||
        !this.hasConnectImSocket ||
        !this.isNetworkConnected ||
        this.isAvailable
      ) {
        logger.warn(
          'ImWebSocket _connect 中断连接 {requestConnect} {hasConnectImSocket} {isNetworkConnected} {isAvailable}',
          this.requestConnect,
          this.hasConnectImSocket,
          this.isNetworkConnected,
          this.isAvailable
        );
        return;
      }
      imAction.getAllOfflineMessageList();
      this._changeState(SocketState.init);
      const token = await Session.getImAccessToken();
      if (!token?.isValid()) {
        logger.warn('ImWebSocket _connect token无效');
        this._reconnect(SocketState.error);
        return;
      }
      this._changeState(SocketState.connecting);
      const socket = new BaseWebSocket(`${configs.im.imWebSocketURL}?token=${token.token}`);
      socket.addEventListener('open', this._onOpen);
      socket.addEventListener('close', this._onClose);
      socket.addEventListener('error', this._onError);
      socket.addEventListener('message', this._onMessage);
      socket.connect();
      this._socket = socket;
    } catch (e) {
      logger.warn('ImWebSocket _connect {@error}', e);
      this._reconnect(SocketState.error);
    }
  };

  notCurrentSocket = (socket) => {
    const isNot = this._socket && this._socket !== socket;
    console.log('ImWebSocket notCurrentSocket', isNot, socket?.id, this._socket?.id);
    return isNot;
  };

  _reconnect = (state, delay = 2000) => {
    if (
      this.requestConnect &&
      this.hasConnectImSocket &&
      this.isNetworkConnected &&
      !this.isAvailable
    ) {
      logger.info(`ImWebSocket _reconnect 定时${delay}ms后重连...`);
      this._changeState(SocketState.reconnecting);
      setTimeout(this._connect, delay);
    } else {
      this._changeState(state);
    }
  };

  /**
   * 断开连接
   */
  _disconnect = () => {
    this._clearPingTask();
    this._changeState(SocketState.close);
    if (this._socket) {
      this._socket.clearEventListener();
      this._socket.close();
    }
    this._socket = null;
  };

  /**
   * 开始ping，主要是外部调用，刷新ping的间隔等
   */
  startPing = (appState) => {
    console.log('ImWebSocket startPing', appState, AppState.currentState);
    this._startPing(appState);
  };

  /**
   * 定时ping
   */
  _startPing = (appState = AppState.currentState) => {
    console.log('ImWebSocket _startPing', appState);
    this._clearPingTask();
    if (this.isAvailable) {
      // activePing：app在前台时的心跳时间
      // backgroundPing：app在后台时的心跳时间
      // serverTimeout：app在后台时的服务器连接超时时间
      const { activePing = PING_TIME, backgroundPing = 3000, serverTimeout = 5 } =
        settingsStore.imSocketConfig || {};
      if (appState === 'active') {
        console.log('ImWebSocket _startPing', activePing);
        this.send(heartbeat);
        this.pingTask = setTimeout(this._startPing, activePing);
      } else {
        console.log('ImWebSocket _startPing', backgroundPing, serverTimeout);
        this.send(`{"action":"heartbeat","Data":{},"timeout":${serverTimeout}}`);
        this.pingTask = setTimeout(this._startPing, backgroundPing);
      }
    }
  };

  _clearPingTask = () => {
    if (this.pingTask) {
      clearTimeout(this.pingTask);
      this.pingTask = null;
    }
  };

  /**
   * 当一个 WebSocket 连接成功时触发
   */
  _onOpen = (event, socket) => {
    if (this.notCurrentSocket(socket)) return;
    this._changeState(SocketState.opened);
    logger.info('ImWebSocket _onOpen {event}', { ...event });
    this._startPing();
  };

  /**
   * 已连接成功，第一次收到心跳返回
   * @private
   */
  _onConnected = () => {
    const { state } = this;
    this._changeState(SocketState.connected);
    if (state === SocketState.connected) return;
    const data = sendMessageUtil.sendOnLinePing();
    if (this.send(data)) {
      receiptMessageUtil.addMessageReceipt({
        seq: data.seq,
        receiptType: constant.messageNoticeType.onLinePing,
      });
    }
    imAction.getAllOfflineMessageList();
  };

  /**
   * 当通过 WebSocket 收到数据时触发
   */
  _onMessage = (event, socket) => {
    if (this.notCurrentSocket(socket)) return;
    logger.info('ImWebSocket _onMessage {data}', event.data);
    const message = JSON.parse(event.data);
    if (message.id && (message.id === 'error.unauthorized' || message.id === 'error.kicked_out')) {
      global.emitter.emit(constant.event.imSessionTimeout, { isSocket: true });
      this._disconnect();
      return;
    }
    this._onConnected();
    receiveMessageUtil.receiveMessage(message);
  };

  /**
   * 当一个 WebSocket 连接因错误而关闭时触发，例如无法发送数据时
   */
  _onError = (event, socket) => {
    if (this.notCurrentSocket(socket)) return;
    this._changeState(SocketState.error);
    logger.warn('ImWebSocket _onError {event}', { ...event });
    if (__DEV__ && event.message !== 'Software caused connection abort') {
      toast.show(`WebSocket出错：${event.message}`);
    }
  };

  /**
   * 当一个 WebSocket 连接被关闭时触发
   */
  _onClose = (event, socket) => {
    if (this.notCurrentSocket(socket)) return;
    logger.info('ImWebSocket _onClose {event}', { ...event });
    if (event.code === 1000) {
      this._changeState(SocketState.close);
      return;
    }
    const delay = event.message === 'Software caused connection abort' ? 0 : undefined;
    this._reconnect(SocketState.close, delay);
  };
}

export default new ImWebSocket();
