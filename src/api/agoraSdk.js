import RtcEngine from 'react-native-agora';
import callStore from '../store/stores/callStore';
import callAction from '../store/actions/callAction';
import imAction from '../store/actions/imAction';
import meetingAction from '../store/actions/meetingAction';
import constant from '../store/constant';
import configs from '../configs';

const StartStatus = {
  init: 0, // 默认 或 启动失败
  starting: 1, // 启动中
  finish: 2, // 启动完成
};

/**
 * agora
 * <AUTHOR>
 */
class AgoraSdk {
  init = async () => {
    this.engine = await RtcEngine.create(configs.im.agoraAppId);
    await this.engine.enableAudio();
    await this.engine.enableVideo();

    // 注册 UserJoined 回调。
    // 远端用户成功加入频道时，会触发该回调，并返回该用户的 id。
    this.engine.addListener('UserJoined', (uid) => {
      logger.info(
        'VideoCall 加入频道 回调 userId:{@userId} channel:{@channel}',
        uid,
        callStore.channel
      );
      if (callStore.callType === constant.callType.meetingCall) {
        meetingAction.onReceiveAccept(uid);
      } else {
        if (uid === callStore.peerId) {
          callAction.onReceiveAccept(uid);
        }
      }
    });

    // 注册 UserOffline 回调。
    // 远端用户离开频道时，会触发该回调，并返回该用户的 id。
    this.engine.addListener('UserOffline', (uid, reason) => {
      logger.info(
        'VideoCall 离开频道 回调 userId:{@userId} channel:{@channel} reason:{@reason}',
        uid,
        callStore.channel,
        reason
      );
      if (callStore.callType === constant.callType.meetingCall) {
        meetingAction.onReceiveEndCall(uid);
      } else {
        callAction.onReceiveEndCall(uid);
      }
    });

    // 注册 JoinChannelSuccess 回调。
    // 本地用户成功加入频道时，会触发该回调。
    this.engine.addListener('JoinChannelSuccess', (channel, uid, elapsed) => {
      logger.info(
        'VideoCall 本地用户 加入频道 回调 userId:{@userId} channel:{@channel}',
        uid,
        channel
      );
      this.joinChannelStatus = { channel, uid, elapsed, status: StartStatus.finish };
    });

    this.engine.addListener('UserMuteVideo', (uid, mute) => {
      logger.info('VideoCall 用户UserMuteVideo 回调 userId:{@userId} mute:{@mute}', uid, mute);
      if (callStore.callType === constant.callType.meetingCall) {
        meetingAction.onReceiveMuteVideo(uid, mute);
      }
    });

    this.engine.addListener('UserMuteAudio', (uid, mute) => {
      logger.info('VideoCall 用户UserMuteAudio 回调 userId:{@userId} mute:{@mute}', uid, mute);

      if (callStore.callType === constant.callType.meetingCall) {
        meetingAction.onReceiveMuteAudio(uid, mute);
      }
    });

    this.engine.addListener('RemoteVideoStateChanged', (uid, state) => {
      logger.info(
        'VideoCall 用户RemoteVideoStateChanged 回调 userId:{@userId} state:{@state} local:{@local}',
        uid,
        state,
        callStore.localInfo,
        callStore.callType
      );
      if (callStore.callType === constant.callType.meetingCall) {
        if (state === 0) {
          //stop
          meetingAction.onReceiveMuteVideo(uid, true);
        } else if (state === 1) {
          // Starting
          meetingAction.onReceiveMuteVideo(uid, false);
        }
      } else if (callStore.callType === constant.callType.singleVideoCall) {
        callAction.onReceiveMuteVideo(uid, state === 0);
      }
    });

    this.engine.addListener('RemoteAudioStateChanged', (uid, state) => {
      logger.info(
        'VideoCall 用户RemoteAudioStateChanged 回调 userId:{@userId} state:{@state} local:{@local}',
        uid,
        state,
        callStore.localInfo
      );
      if (callStore.callType === constant.callType.meetingCall) {
        if (state === 0) {
          //stop
          meetingAction.onReceiveMuteAudio(uid, true);
        } else if (state === 1) {
          // Starting
          meetingAction.onReceiveMuteAudio(uid, false);
        }
      }
    });

    this.engine.addListener('AudioVolumeIndication', (speakers) => {
      if (callStore.callType === constant.callType.meetingCall) {
        meetingAction.onReceiveIsSpeaking(speakers);
      }
    });

    this.engine.addListener('Warning', (warn) => {
      logger.warn('VideoCall sdk警告 {@warn}', warn);
    });

    this.engine.addListener('Error', (err) => {
      logger.error('VideoCall sdk错误 {@err}', err);
    });
  };

  startPreview = async () => {
    console.debug('agoraSdk startPreview', this.previewStatus);
    this.previewStatus = StartStatus.starting;
    try {
      await this.engine.startPreview();
      this.previewStatus = StartStatus.finish;
    } catch (e) {
      console.warn('agoraSdk startPreview', e);
      return Promise.reject(e);
    }
  };

  stopPreview = async () => {
    console.debug('agoraSdk stopPreview', this.previewStatus);
    if (!this.previewStatus) return;
    try {
      await this.engine.stopPreview();
      this.previewStatus = StartStatus.init;
    } catch (e) {
      this.previewStatus = StartStatus.init;
      console.warn('agoraSdk stopPreview', e);
      return Promise.reject(e);
    }
  };

  setupCall = async () => {
    const { callType } = callStore;
    if (callType === constant.callType.singleVideoCall) {
      await this.startPreview();
    } else if (callType === constant.callType.meetingCall) {
      await this.startPreview();
      // 开启通话音量检测
      await this.engine.enableAudioVolumeIndication(1000, 3, false);
    }
  };

  joinChannel = async (channel, userId) => {
    const res = await imAction.getAgoraToken(channel, userId);
    console.debug(
      `MeetingCall 加入频道 开始 userId:${userId} channel:${channel} token:${res.token}`
    );
    await this.setupCall();
    if (IS_IOS) {
      await this.engine.setParameters('{"che.audio.keep.audiosession":true}');
    }
    this.joinChannelStatus = {
      channel,
      status: StartStatus.starting,
      uid: userId,
    };
    await this.engine.joinChannel(res.token, channel, null, userId);
    logger.debug('MeetingCall 加入频道 结束');
  };

  leaveChannel = async () => {
    console.debug('agoraSdk leaveChannel', this.joinChannelStatus);
    if (this.joinChannelStatus?.status) {
      try {
        await this.engine.leaveChannel();
      } catch (e) {
        console.warn('agoraSdk leaveChannel', e);
      }
      this.joinChannelStatus = null;
    }
    this.stopPreview();
  };
}

export default new AgoraSdk();
