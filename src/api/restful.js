import NetInfo from '@react-native-community/netinfo';
import Ajax from './ajax';
import I18n from '../i18n';
import Session from './session';
import loginAction from '../store/actions/login';
import qs from 'qs';

/**
 * 获取数据
 * 主要是get请求数据
 * 1. 常量没有data
 * 2. 登录没有data
 * 3. 其他情况下都会有data
 */
function getData(res) {
  if (res && res.data) {
    return res.data.data ? res.data.data : res.data;
  }
  return null;
}

/**
 * 获取结果
 * 主要是post、put、delete等操作结果
 */
function getResult(res) {
  return res.data;
}

function showMessage(i18nKey, options, message) {
  if (options?.showToast !== false) {
    toast.show(i18nKey ? I18n.t(i18nKey) : message);
  }
}

/**
 * 错误处理
 */
function handlerError(err, options) {
  if (!err.response) {
    if (err.config.url.indexOf('/users/resumes/attachment?fileNameEncode=true') !== -1) {
      return Promise.reject(null);
    }

    NetInfo.fetch().then((state) => {
      if (state.isConnected) {
        showMessage('network_is_bad', options);
      } else {
        showMessage('page_network_bad_tips', options);
      }
    });
    return Promise.reject(null);
  }
  const { status } = err.response;
  const body = err.response.data;
  if (status === 403 || status === 401) {
    // Alert.alert(I18n.t('page_tips_withdraw_title'), body.message);
    loginAction.loginFail();
    return Promise.resolve(body);
  } else if (status <= 504 && status >= 500) {
    showMessage('network_is_bad', options);
    return Promise.resolve(body);
  } else if (status >= 404 && status < 422) {
    // 这种情况是不允许出现的，所以，这个消息只会给开发者提醒用的
    // Alert.alert(I18n.t('page_tips_withdraw_title'), `状态：${status}，请确认API地址是否存在！`);
    return Promise.resolve(body);
  } else if (status === 400) {
    // 400状态码返回页面处理
    return Promise.resolve(body);
  }
  // 其他状态码不处理
  // Alert.alert('提示', '状态：' + status + '，网络异常！');
  return Promise.resolve(body);
}

/**
 * 初始化access token
 */
async function ensureAccessToken() {
  // 调用用户传入的获取token的异步方法，获得token之后使用（并缓存它）。
  const accessToken = await Session.getAccessToken();
  if (!accessToken || !accessToken.token) {
    return;
  }
  if (accessToken.isValid()) {
    return accessToken.token;
  }
  return freshAccessToken(accessToken);
}

/**
 * 刷新access token
 * 不判断refreash token是否过期，如果过期刷新会失败，返回401
 * 因为这里有一点点小问题，store 获取为 undefined
 */
async function freshAccessToken(accessToken) {
  const url = '/passport/token';
  const freshToken = await Session.getRefreshToken();
  if (!freshToken) {
    return '';
  }
  const params = {
    access_token: accessToken.token,
    refresh_token: freshToken.token,
  };
  const data = await Ajax.post(url, params).then(
    (res) => getResult(res),
    (err) => handlerError(err)
  );
  await Session.saveRefreshToken(data.refresh_token);
  const newAccessToken = await Session.saveAccessToken(data.access_token, data.expires_in);
  return newAccessToken.token;
}

/**
 * 请求API，把结果展平再返回
 */
class Restful {
  async get(url, param, config) {
    if (param) {
      url = `${url}?${qs.stringify(param, { arrayFormat: 'repeat' })}`;
    }
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .get(url, config)
      .then(
        (res) => getData(res),
        (err) => handlerError(err, config)
      );
  }

  async delete(url) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .delete(url)
      .then(
        (res) => getResult(res),
        (err) => handlerError(err)
      );
  }

  async post(url, data, config) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .post(url, data, config)
      .then(
        (res) => getResult(res),
        (err) => handlerError(err, config)
      );
  }

  async put(url, data) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .put(url, data)
      .then(
        (res) => getResult(res),
        (err) => handlerError(err)
      );
  }

  async formPost(url, data) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .formPost(url, data)
      .then(
        (res) => getResult(res),
        (err) => handlerError(err)
      );
  }

  async formGet(url) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .formGet(url)
      .then(
        (res) => getData(res),
        (err) => handlerError(err)
      );
  }

  async upload(url, formData) {
    const token = await ensureAccessToken();
    return Ajax.setToken(token)
      .upload(url, formData)
      .then(
        (res) => getResult(res),
        (err) => handlerError(err)
      );
  }
}

export default new Restful();
