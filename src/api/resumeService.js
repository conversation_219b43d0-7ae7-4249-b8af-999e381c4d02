import Restful from './restful';
import qs from 'qs';

class ResumeService {
  async getResumes() {
    return Restful.get('users/resumes');
  }

  /**
   * 新增简历
   * request: /v1.0.0/users/resumes
   * method: POST
   * @param language - language
   * @param resumeId - resumeId
   */
  async addResume(resume) {
    return Restful.post('/users/resumes', resume);
  }

  /**
   * 删除我的简历
   * request: /v1.0.0/users/resumes/{resumeId}
   * method: DELETE
   * @param resumeId - resumeId
   */
  async deleteResume(resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}`);
  }

  /**
   * 获取简历详情
   */
  async getResumeDetail(resumeId) {
    return Restful.get(`/users/resumes/${resumeId}`);
  }

  /**
   * 更新个人信息
   * request: /v1.0.0/users/resumes/{resumeId}/profile
   * method: PUT
   * @param resumeId - resumeId
   * @param resumeProfile - resumeProfile
   */
  async updateResumeProfile(resumeId, resumeProfile) {
    return Restful.put(`/users/resumes/${resumeId}/profile`, resumeProfile);
  }

  /**
   * 添加兴趣爱好，培训经历，自我评价，简历完整度
   * request: /v1.0.0/users/resumes/{resumeId}/attributes
   * method: PUT
   * @param attributes - resume
   * @param resumeId - resumeId
   */
  async updateResumeAttributes(resumeId, attributes) {
    return Restful.put(`/users/resumes/${resumeId}/attributes`, attributes);
  }

  /**
   * 添加工作经历
   * request: /v1.0.0/users/resumes/{resumeId}/experiences
   * method: POST
   * @param experience - experience
   * @param resumeId - resumeId
   */
  async addExperience(resumeId, experience) {
    return Restful.post(`/users/resumes/${resumeId}/experiences`, experience);
  }

  /**
   * 更新工作经历
   * request: /v1.0.0/users/resumes/{resumeId}/experiences/{experienceId}
   * method: PUT
   * @param experience - experience
   * @param experienceId - experienceId
   * @param resumeId - resumeId
   */
  async updateExperiences(experienceId, resumeId, experience) {
    return Restful.put(`/users/resumes/${resumeId}/experiences/${experienceId}`, experience);
  }

  /**
   * 删除工作经历
   * request: /v1.0.0/users/resumes/{resumeId}/experiences/{experienceId}
   * method: DELETE
   * @param experienceId - experienceId
   * @param resumeId - resumeId
   */
  async deleteExperiences(experienceId, resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}/experiences/${experienceId}`);
  }

  /**
   * 添加教育背景
   * request: /v1.0.0/users/resumes/{resumeId}/educations
   * method: POST
   * @param education - education
   * @param resumeId - resumeId
   */
  async addEducation(resumeId, education) {
    return Restful.post(`/users/resumes/${resumeId}/educations`, education);
  }

  /**
   * 更新教育背景
   * request: /v1.0.0/users/resumes/{resumeId}/educations/{eduId}
   * method: PUT
   * @param education - education
   * @param eduId - eduId
   * @param resumeId - resumeId
   */
  async updateEducation(eduId, resumeId, education) {
    return Restful.put(`/users/resumes/${resumeId}/educations/${eduId}`, education);
  }

  /**
   * 删除教育背景
   * request: /v1.0.0/users/resumes/{resumeId}/educations/{eduId}
   * method: DELETE
   * @param eduId - eduId
   * @param resumeId - resumeId
   */
  async deleteEducation(eduId, resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}/educations/${eduId}`);
  }

  /**
   * 新增语言水平
   * request: /v1.0.0/users/resumes/{resumeId}/languageLevels
   * method: POST
   * @param language - language
   * @param resumeId - resumeId
   */
  async addLanguage(resumeId, language) {
    return Restful.post(`/users/resumes/${resumeId}/languageLevels`, language);
  }

  /**
   * 更新语言水平
   * request: /v1.0.0/users/resumes/{resumeId}/languageLevels/{languageLevelId}
   * method: PUT
   * @param language - language
   * @param languageLevelId - languageLevelId
   * @param resumeId - resumeId
   */
  async updateLanguage(languageLevelId, resumeId, language) {
    return Restful.put(`/users/resumes/${resumeId}/languageLevels/${languageLevelId}`, language);
  }

  /**
   * 删除语言水平
   * request: /v1.0.0/users/resumes/{resumeId}/languageLevels/{languageLevelId}
   * method: DELETE
   * @param languageLevelId - languageLevelId
   * @param resumeId - resumeId
   */
  async deleteLanguage(languageLevelId, resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}/languageLevels/${languageLevelId}`);
  }

  /**
   * 更新职业概况
   * request: /v1.0.0/users/resumes/{resumeId}/career/profile
   * method: PUT
   * @param careerProfile - careerProfile
   * @param resumeId - resumeId
   */
  async updateCareerProfile(resumeId, careerProfile) {
    return Restful.put(`/users/resumes/${resumeId}/career/profile`, careerProfile);
  }

  /**
   * 添加技能
   * request: /v1.0.0/users/resumes/{resumeId}/skills
   * method: POST
   * @param resumeId - resumeId
   * @param skill - skill
   */
  async addSkill(resumeId, skill) {
    return Restful.post(`/users/resumes/${resumeId}/skills`, skill);
  }

  /**
   * 更新技能
   * request: /v1.0.0/users/resumes/{resumeId}/skills/{skillId}
   * method: PUT
   * @param skill - skill
   * @param skillId - skillId
   */
  async updateSkill(skillId, resumeId, skill) {
    return Restful.put(`/users/resumes/${resumeId}/skills/${skillId}`, skill);
  }

  /**
   * 删除技能
   * request: /v1.0.0/users/resumes/{resumeId}/skills/{skillId}
   * method: DELETE
   * @param skillId - skillId
   */
  async deleteSkill(skillId, resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}/skills/${skillId}`);
  }

  /**
   * 添加证书
   * request: /v1.0.0/users/resumes/{resumeId}/qualifications
   * method: POST
   * @param qualification - qualification
   * @param resumeId - resumeId
   */
  async addQualification(resumeId, qualification) {
    return Restful.post(`/users/resumes/${resumeId}/qualifications`, qualification);
  }

  /**
   * 更新证书
   * request: /v1.0.0/users/resumes/{resumeId}/qualifications/{qualificationId}
   * method: PUT
   * @param qualification - qualification
   * @param qualificationId - qualificationId
   * @param resumeId - resumeId
   */
  async updateQualification(qualificationId, resumeId, qualification) {
    return Restful.put(
      `/users/resumes/${resumeId}/qualifications/${qualificationId}`,
      qualification
    );
  }

  /**
   * 删除证书
   * request: /v1.0.0/users/resumes/{resumeId}/qualifications/{qualificationId}
   * method: DELETE
   * @param qualificationId - qualificationId
   * @param resumeId - resumeId
   */
  async deleteQualification(qualificationId, resumeId) {
    return Restful.delete(`/users/resumes/${resumeId}/qualifications/${qualificationId}`);
  }

  /**
   * 隐藏简历
   * request: users/resumes/{resumeId}/hiden
   * method: PUT
   * @param resumeId - resumeId
   */
  async hideResume(resumeId, level) {
    return Restful.put(`/users/resumes/${resumeId}/pirvacy/${level}`);
  }

  /**
   * 刷新简历
   * request: users/resumes/{resumeId}/refresh
   * method: PUT
   * @param resumeId - resumeId
   */
  async refreshResume(resumeId) {
    return Restful.put(`/users/resumes/${resumeId}/refresh`);
  }

  /**
   * 复制简历
   * request: users/resumes/copy
   * method: PUT
   * @param resumeId - resumeId
   */
  async copyResume(resume) {
    return Restful.post('/users/resumes/copy', resume);
  }

  /**
   * 分享简历
   * request: users/resumes/{resumeId}/sharing
   * method: PUT
   * @param resumeId - resumeId
   */
  async shareResume(resumeId) {
    return Restful.post(`/users/resumes/${resumeId}/sharing`);
  }

  /**
   * 证书上传
   * request: /v1.0.0/users/resumes/{resumeId}/images
   * method: POST
   * @param resumeId - resumeId
   * @param file - file
   */
  async uploadImage(resumeId, file) {
    return Restful.upload(`/users/resumes/${resumeId}/images`, file);
  }

  /**
   * 谁看过我
   * @param {*} params
   */
  async getResumesViewers(params) {
    return Restful.get(
      `/users/resumes/employers/viewers?${qs.stringify(params, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 设置默认简历
   * request: users/resumes/{resumeId}/default/{defaultCv}
   * method: PUT
   * @param resumeId - resumeId
   */
  async setDefaultResume(resumeId, defaultCv) {
    return Restful.put(`/users/resumes/${resumeId}/default/${defaultCv}`);
  }

  /**
   * 新增附件简历
   * @param {*} params
   */
  async addAnnexResume(params) {
    return Restful.upload('/users/resumes/attachment?fileNameEncode=true', params);
  }

  /**
   * 获取附件简历
   * @param {*} params
   */
  async getAnnexResumes() {
    return Restful.get('/users/resumes/attachment');
  }

  /**
   * 获取所有简历
   * @param {*} params
   */
  async getAllResumes() {
    return Restful.get('/users/resumes/simple');
  }
}

export default new ResumeService();
