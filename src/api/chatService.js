/**
 * 聊天相关接口
 * author: sxw
 */
import Restful from './imRestful';

class ChatService {
  /**
   * 查询联系人 通过手机号
   * @param {*} phone
   * @returns
   */
  async searchContactByPhone(phone) {
    const res = await Restful.get(`/user/phone/${phone}`, {}, { useBaseUrl: true });
    return res?.contacts?.length && res.contacts[0];
  }

  /**
   * 查询联系人 通过用户ID
   * @param {*} userId
   * @param {*} isLimit 如果为true有次数限制
   * @returns
   */
  async searchContactByUserID(userId, isLimit) {
    const res = await Restful.get(`/user/${isLimit ? 'search' : 'id'}/${userId}`, null, {
      useBaseUrl: true,
    });
    return res?.contacts?.length && res.contacts[0];
  }

  /**
   * 添加联系人 (直接添加成功)
   * @param {*} id
   * @param {*} cid
   * @returns
   */
  async addContact(id, cid) {
    return Restful.post(`/users/${id}/contacts/${cid}`, {}, { useBaseUrl: true });
  }

  /**
   * 设置联系人tags分组
   * @param data {object[]} [{friend_id:1,tags:'["朋友"]'}]
   */
  async setContactTags(id, data) {
    return Restful.put(`/users/${id}/contacts/tags`, data, { useBaseUrl: true });
  }

  /**
   * 添加联系人 (需要对方同意)
   * @param {*} id
   * @param {*} params
   * @returns
   */
  async addContactReq(id, params) {
    return Restful.post(`/users/${id}/add_reqs`, params, { useBaseUrl: true });
  }

  /**
   * 处理添加好友请求
   * @param {*} id
   * @param {*} req_id
   * @param {*} params
   * @returns
   */
  async agreeContactReq(id, req_id, params) {
    return Restful.post(`/users/${id}/add_reqs/${req_id}`, params, { useBaseUrl: true });
  }

  /**
   * 获取好友添加请求
   * @param {*} id
   * @param {*} params
   * @returns
   */
  async getContactReq(id, req_id) {
    return Restful.get(`/users/${id}/add_reqs/${req_id}`, {}, { useBaseUrl: true });
  }

  /**
   * 删除联系人
   * @param {*} id
   * @param {*} cid
   * @returns
   */
  async deleteContact(id, cid) {
    return Restful.delete(`/users/${id}/contacts/${cid}`, {}, { useBaseUrl: true });
  }

  /**
   * 通讯录列表
   * @param {*} id
   * @returns
   */
  async getContacts(id) {
    const res = await Restful.get(`/users/${id}/contacts`, {}, { useBaseUrl: true });
    return res.contacts;
  }

  /**
   * 聊天转账
   * @param {*}
   * @returns
   */
  async chatTransfer(params) {
    return Restful.post(`/wallet/transfer`, params, { useBaseUrl: true });
  }

  /**
   * 聊天转账计算手续费
   * @param {*}
   * @returns
   */
  async chatTransferFee(amount) {
    return Restful.get(`/wallet/fee?amount=${amount}`, {}, { useBaseUrl: true });
  }

  /**
   * 接收转账
   * @param {*}
   * @returns
   */
  async acceptTransfer(id) {
    return Restful.post(`/wallet/transfer/${id}/accept`, {}, { useBaseUrl: true });
  }

  /**
   * 退回转账
   * @param {*} id
   * @returns
   */
  async returnTransfer(id) {
    return Restful.post(`/wallet/transfer/${id}/return`, {}, { useBaseUrl: true });
  }

  /**
   * 转账详情
   * @param {*} id
   * @returns
   */
  async getTransfer(id) {
    return Restful.get(`/wallet/transfer/${id}`, {}, { useBaseUrl: true });
  }

  /**
   * 创建红包
   * @param {*}{ "amount": 100, "size": 3, "im_id": 10119, "rp_type": 2, "desc": "大吉大利", "rev_id": 1634199677110001, "is_group":true }
   * @returns
   */
  async addRedPacket(params) {
    return Restful.post(`/wallet/red_packets`, params, { useBaseUrl: true });
  }

  /**
   * 抢红包
   * @param {*}
   * @returns
   */
  async openRedPacket(id) {
    return Restful.post(`/wallet/red_packets/${id}/open`, {}, { useBaseUrl: true });
  }

  /**
   * 红包详情
   * @param {*}
   * @returns
   */
  async getRedPacketInfo(id) {
    return Restful.get(`/wallet/red_packets/${id}`, {}, { useBaseUrl: true });
  }

  /**
   * 红包我发出的
   * @param {*}
   * @returns
   */
  async getSendRedPacketList(params) {
    return Restful.get(`/wallet/red_packet_records/sent`, params, { useBaseUrl: true });
  }

  /**
   * 红包我收到的
   * @param {*}
   * @returns
   */
  async getReceivedRedPacketList(params) {
    return Restful.get(`/wallet/red_packet_records/received`, params, { useBaseUrl: true });
  }

  /**
   * 创建个人收款
   * @param {*}{ "payer_id": 10125, "amount": 50, "desc": "电动车修理费" }
   * @returns
   */
  async addReceive(params) {
    return Restful.post(`/wallet/spilt_bills`, params, { useBaseUrl: true });
  }

  /**
   * 收款详情
   * @param {*} id
   * @returns
   */
  async getReceive(id) {
    return Restful.get(`/wallet/spilt_bills/${id}`, {}, { useBaseUrl: true });
  }

  /**
   * 支付收款
   * @param {*} id
   * @returns
   */
  async payReceive(id, itemId, params) {
    return Restful.post(`/wallet/spilt_bills/${id}/pay/${itemId}`, params, {
      useBaseUrl: true,
    });
  }

  /**
   * 创建群收款
   * @param {*}{ "group_id":1634199677110001,
                "b_type":1,
                "amount":300,
                "desc":"聚餐费用",
                "items":[{
                    "payer_id":10126,
                    "amount":100
                },{
                    "payer_id":10127,
                    "amount":100
                },{
                    "payer_id":10125,
                    "amount":100
                }]
            }
   * @returns
   */
  async addGroupReceive(params) {
    return Restful.post(`/wallet/group_spilt_bills`, params, { useBaseUrl: true });
  }

  /**
   * 我发出的收款
   * @param {*}
   * @returns
   */
  async getSendReceiveList(params) {
    return Restful.get(`/wallet/my_spilt_bills`, params, { useBaseUrl: true });
  }

  /**
   * 我支付的收款
   * @param {*}
   * @returns
   */
  async getReceivedList(params) {
    return Restful.get(`/wallet/my_pay_spilt_bills`, params, { useBaseUrl: true });
  }

  /**
   * 发送GPT消息
   */
  async sendGPT(params) {
    return Restful.post(`/chat`, params, { isGPT: true, showToast: false });
  }

  /**
   * 判断GPT是否可用
   */
  async isGPTEnabled() {
    return Restful.get(`/chat`, null, { isGPT: true, showToast: false });
  }
}

export default new ChatService();
