import qs from 'qs';
import Restful from './restful';
import Md5 from 'crypto-js/md5';
import DeviceInfo from '../util/deviceInfo';
import configs from '../configs';
class LoginService {
  /**
   * 手机快速登录
   * 登录并保存token、user信息到本地storage
   * @param {*} phone    电话 required
   * @param {*} code     短信验证码 required
   */
  async loginByPhone({ phone, code, regionCode }) {
    return this.authorizeByPhone({ phone, code, regionCode });
  }

  /**
   * 账号密码登录
   * 登录并保存token、user信息到本地storage
   * @param {*} name         用户名或邮箱 required
   * @param {*} password     密码 required
   */
  async loginByAccount({ name, password, captcha, signature }) {
    return this.authorizeByEmail({
      name,
      password,
      captcha,
      signature,
    });
  }

  /**
   * 退出登录并清除本地storage的token信息、user信息
   */
  async logout() {
    return true;
  }

  /**
   * 发送登录短信验证码
   */
  async sendLoginCode(data) {
    const nonce = DeviceInfo.getUniqueID();
    data = {
      ...data,
      nonce,
      sign: Md5(data?.regionCode + data?.mobile + nonce + configs.smsCodeSalt).toString(),
      imageCaptcha: data?.imageCaptcha || '',
    };

    return Restful.formPost('/users/sms/verify-code', data);
  }

  /**
   * 手机、短信验证码获取token
   */
  async authorizeByPhone({ phone, code, regionCode }) {
    return Restful.formPost('/users/authorize/mobile', {
      mobile: phone,
      verificationCode: code,
      regionCode,
    });
  }

  /**
   * 邮箱、账号获取token
   * @param {*} name    账号（邮箱或手机）
   * @param {*} password   短信验证码
   */
  async authorizeByEmail({ name, password, captcha, signature }) {
    return Restful.formPost('/users/authorize', {
      name,
      password,
      captcha,
      signature,
    });
  }

  /**
   * 手机注册账号
   * @param {*} code              验证码
   * @param {*} phone             电话号码
   * @param {*} password          密码
   */
  async registerByPhone({ code, phone, password, regionCode }) {
    try {
      if (!code || !phone || !password || !regionCode) {
        return Promise.reject('参数错误');
      }
      return await Restful.formPost('/users/register/mobile', {
        regionCode,
        verificationCode: code,
        mobile: phone,
        password,
      });
    } catch (e) {
      return Promise.reject(e);
    }
  }

  /**
   * name注册账号
   * @param {*} name             name
   * @param {*} password          密码
   */
  async registerByEmail(name, pwd) {
    try {
      if (!name || !pwd) {
        return Promise.reject('参数错误');
      }
      return await Restful.formPost('/users/register', { username: name, password: pwd });
    } catch (e) {
      return Promise.reject(e);
    }
  }

  /**
   * 注册短信验证码
   */
  async sendRegisterCode(data) {
    return this.sendLoginCode(data);
  }

  /**
   * 忘记密码，手机号码找回
   * @param {*} code          验证码
   * @param {*} phone         电话号码
   * @param {*} password   新密码
   */
  async forgotPasswordByPhone({ code, phone, password, regionCode }) {
    return Restful.post(`/findpassword/1/${regionCode}/${phone}`, {
      verificationCode: code,
      phone,
      password,
      regionCode,
    });
  }

  /**
   * 忘记密码，邮箱找回
   * @param {*} code          验证码
   * @param {*} email         邮箱
   * @param {*} password   新密码
   */
  async forgotPasswordByEmail({ code, email, password }) {
    return Restful.post(`/findpassword/1/${email}`, {
      verificationCode: code,
      email,
      password,
    });
  }

  /**
   * 发送忘记密码短信验证码
   * @param {*} phone 手机号码
   */
  async sendForgotCodeByPhone(data) {
    const nonce = DeviceInfo.getUniqueID();
    const params = {
      nonce,
      sign: Md5(data?.regionCode + data?.mobile + nonce + configs.smsCodeSalt).toString(),
      imageCaptcha: data?.imageCaptcha || '',
    };
    return Restful.get(`/findpassword/1/${data.regionCode}/${data.mobile}?${qs.stringify(params)}`);
  }

  /**
   * 发送忘记密码邮箱验证码
   * @param {*} email 找回邮箱
   */
  async sendForgotCodeByEmail(email) {
    return Restful.get(`/findpassword/${email}`);
  }

  /**
   * /v1.0.0/users/name/{name}/exists
   * 判断用户名是否存在
   * @param {*} name 判断用户名是否存在
   */
  async isUsernameExist(name) {
    return Restful.get(`/users/name/${name}/exists`);
  }

  /**
   * /v1.0.0/users/social/{provider}/accesstoken/login
   * 第三方登录- linkedin/facebook
   * @param {*} provider 平台
   */
  async thirdParyLogin(provider, accessToken) {
    return Restful.get(`/users/social/${provider}/accesstoken/login?accessToken=${accessToken}`);
  }

  /**
   * /v1.0.0/users/social/{provider}/login
   * 第三方登录- 微信
   * @param {*} provider 平台
   */
  async thirdParyLoginWithCode(provider, code) {
    return Restful.get(`/users/social/${provider}/login?code=${code}`);
  }

  /**
   * 企业端账号密码登录
   * @param {*} name    账号（邮箱或手机）
   * @param {*} password   短信验证码
   */
  async epLogin({ name, password, captcha, signature }) {
    return Restful.formPost('/employers/authorize', {
      name,
      password,
      captcha,
      signature,
    });
  }

  unregisterForEnterPrise = async (data) => {
    data.password = Md5(data.password).toString();
    return Restful.post(`/employers/close/account`, data);
  };

  unregisterForPersonal = async (data) => {
    data.password = Md5(data.password).toString();
    return Restful.post(`/users/close/account`, data);
  };
}

export default new LoginService();
