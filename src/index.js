import { createAppContainer } from 'react-navigation';
import { createStackNavigator } from 'react-navigation-stack';
import CardStyleInterpolators from './util/cardStyleInterpolators';
import Pages from './pages';
import EnterprisePages from './pages/enterprise';
import Tabbar from './tabbar';

const RootStack = createStackNavigator(
  {
    ...Pages,
    ...EnterprisePages,
    main: Tabbar,
  },
  {
    initialRouteName: 'first',
    headerMode: 'none',
    navigationOptions: () => {
      return {};
    },
    mode: 'card',
    defaultNavigationOptions: {
      cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
    },
  }
);

export default createAppContainer(RootStack);
