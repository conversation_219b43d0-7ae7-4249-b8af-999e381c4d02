
import { RVW, RFT, RPX, deviceWidth, footerHeight } from '../common';
import { titleColor, subTitleColor, bgColor, desColor, baseRedColor, baseBlueColor } from './base';

export const dynamicDetailStyle = {
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  top: {
    flex: 1,
    flexDirection: 'row',
    // justifyContent: 'space-between',
    paddingBottom: 12,
    backgroundColor: '#fff',
  },
  left: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 17,
    color: titleColor,
    marginLeft: 15,
    marginTop: 15,
  },
  time: {
    fontSize: 14,
    color: desColor,
    marginRight: 15,
    marginLeft: 15,
  },
  icon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginLeft: 15,
    marginTop: 15,
  },
  imagesContent: {
    marginLeft: 15,
    marginRight: 10,
    marginTop: 0,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignContent: 'space-between',
    flexWrap: 'wrap',
  },
  likeImagesContent: {
    marginRight: 8,
    marginTop: 0,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignContent: 'space-between',
    flexWrap: 'wrap',
  },
  images: {
    margin: 10,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  image: {
    height: (deviceWidth - 119) / 3,
    width: (deviceWidth - 119) / 3,
    marginTop: 5,
    marginRight: 5,
    backgroundColor: bgColor,
  },
  content: {
    fontSize: 14,
    color: subTitleColor,
    lineHeight: 20,
  },
  rightContent: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    paddingRight: 15,
  },
  commentItemtContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 5,
    marginBottom: 6,
    paddingVertical: 8,
    marginHorizontal: 12,
    backgroundColor: '#fff',
  },
  commentItemLeft: {
    paddingLeft: 4,
  },
  commentIcon: {
    height: 38,
    width: 38,
    borderRadius: 19,
    backgroundColor: '#eee',
  },
  commentItemRight: {
    flex: 1,
    marginLeft: 10,
  },
  commentItemtTitleContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  commentName: {
    fontSize: 16,
    color: baseBlueColor,
  },
  commentTime: {
    fontSize: 12,
    color: desColor,
    textAlign: 'right',
    paddingBottom: 4,
  },
  commentContent: {
    flex: 1,
    fontSize: 14,
    color: titleColor,
  },
  likeContainer: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  commentCon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexShrink: 100,
  },
  inputText: {
    height: 30,
    marginVertical: 10,
    marginHorizontal: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: bgColor,
    paddingLeft: 8,
  },
  inputTextExtra: {
    height: 80,
    marginTop: 10,
    marginRight: 15,
    marginLeft: 15,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: bgColor,
    fontSize: 14,
    paddingLeft: 8,
  },
  commentInputView: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    height: 30,
    backgroundColor: 'white',
    justifyContent: 'center',
  },
  commentInputText: {
    margin: 0,
    color: desColor,
    fontSize: 14,
  },
  inputBoxWrapper: {
    height: 130,
    width: '100%',
  },
  inputBox: {
    flex: 1,
    backgroundColor: bgColor,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  likeConText: {
    fontSize: 14,
    color: subTitleColor,
    marginLeft: 6,
  },
  deleteText: {
    fontSize: 14,
    color: subTitleColor,
    flexShrink: 100,
  },
  deleteTextContainer: {
    padding: 6,
    paddingLeft: 0,
    backgroundColor: bgColor,
    marginTop: 12,
    marginLeft: 15,
    marginRight: 20,
    borderRadius: 4,
    flexShrink: 100,
  },
};
