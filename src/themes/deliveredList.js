/**
 * 已投递职位列表
 */
import { baseBlueColor, bgColor, titleColor, subTitleColor, desColor } from './base';

export const deliveredListStyle = {
  deliveredContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  listContainer: {
    padding: 10,
    flexDirection: 'column',
    backgroundColor: '#fff',
    borderBottomColor: bgColor,
    borderBottomWidth: 0.5,
    paddingLeft: 20,
    paddingRight: 12,
  },
  namePannel: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  jobWages: {
    fontSize: 12,
    color: baseBlueColor,
    marginTop: 4,
  },
  companySubcribe: {
    fontSize: 14,
    color: titleColor,
    lineHeight: 18,
  },
  nameSubcribe: {
    fontSize: 12,
    paddingVertical: 5,
    color: subTitleColor,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  noData: {
    fontSize: 14,
    color: desColor,
    textAlign: 'center',
    marginTop: 88,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: '#f5f5f5',
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
