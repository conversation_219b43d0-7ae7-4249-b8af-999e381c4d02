/**
 * 沟通过的职位列表
 */
import { baseBlueColor, bgColor, titleColor, desColor } from './base';
import { RVW } from '../common';

export const communicatedListStyle = {
  communicatedContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  listContainer: {
    padding: 10,
    flexDirection: 'column',
    backgroundColor: '#fff',
    borderBottomColor: bgColor,
    borderBottomWidth: 8,
    paddingLeft: 20,
    paddingRight: 12,
  },
  companyPannel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  namePannel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 10,
  },
  companyDetail: {
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: 10,
    width: RVW * 80,
    paddingVertical: 6,
  },
  jobName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: titleColor,
  },
  companyName: {
    fontSize: 14,
    color: titleColor,
    width: RVW * 72,
  },
  companyNameCont: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  jobWages: {
    fontSize: 16,
    color: baseBlueColor,
  },
  companySubcribe: {
    fontSize: 12,
    color: desColor,
    marginTop: 4,
  },
  nameSubcribe: {
    fontSize: 14,
    width: RVW * 72,
    lineHeight: 18,
    color: desColor,
    marginTop: 8,
  },
  requireJob: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16,
    marginLeft: 75,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  spaceLine: {
    backgroundColor: desColor,
    width: 1,
    height: 10,
    marginHorizontal: 6,
  },
  locationsStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'wrap',
    marginTop: 6,
  },
  companyInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: '90%',
    marginTop: 4,
  },
  noData: {
    fontSize: 14,
    color: desColor,
    textAlign: 'center',
    marginTop: 88,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: '#f5f5f5',
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
