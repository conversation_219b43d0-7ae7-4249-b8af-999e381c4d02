import { titleColor } from './base';
import { footerHeight } from '../common';

export const annexResumeStyle = {
  listContainer: {
    flex: 1,
    marginBottom:
      footerHeight + 20 + 40 + 12 + 30
  },
  tipsContainer: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#D0E9FF',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  tipsText: {
    color: '#32A5E7',
    fontSize: 14,
    flex: 1
  },
  tipsClose: {
    marginLeft: 10,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  bottomContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: footerHeight + 20,
    paddingHorizontal: 15
  },
  remarkText: {
    color: '#999999',
    fontSize: 12,
    flex: 1,
    marginVertical: 6
  },
  itemContainer: {
    marginHorizontal: 15,
    paddingVertical: 25,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1
  },
  nameContainer: {
    flex: 1,
    marginLeft: 18,
    justifyContent: 'space-between',
  },
  nameText: {
    flex: 1,
    color: titleColor, fontSize: 14
  },
  timeText: {
    marginTop: 10,
    color: '#999999', fontSize: 12
  }
};
