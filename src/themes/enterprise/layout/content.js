/**
 * 内容模块样式
 * author: moke
 */
export function getContentStyle(theme) {
  return {
    container: {
      flex: 1,
      paddingVertical: 0,
      backgroundColor: theme.primaryListBgColor,
    },
    center: {
      justifyContent: 'center',
      alignItems: 'center',
      alignContent: 'center',
    },
    page: {
      flex: 1,
      backgroundColor: theme.mediumBgColor,
      flexDirection: 'column',
      justifyContent: 'flex-start',
    },
  };
}
