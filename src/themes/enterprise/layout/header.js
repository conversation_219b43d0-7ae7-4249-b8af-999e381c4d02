/**
 * 页面头部样式
 * author: moke
 */
import { RFT, RVW, headerHeight, statusBarHeight } from '../../../common';

export function getHeaderStyle(theme) {
  return {
    theme,
    bgColor: theme.primaryBgColor,
    tabBar: {
      height: 12 * RVW,
      backgroundColor: '#fff',
    },
    tabLabel: {
      lineHeight: 9 * RVW,
      fontSize: 3.6 * RFT,
    },
    wrapper: {
      height: headerHeight,
      paddingTop: headerHeight - 44,
      borderBottomColor: 'transparent',
      // borderBottomWidth: 0,
      // borderBottomColor: theme.primaryColor,
      // backgroundColor: theme.primaryBgColor,
    },
    center: {
      color: theme.titleFontColor,
      fontSize: 18,
      fontWeight: 'bold',
    },
    centerBlack: {
      color: theme.labelFontColor,
      fontSize: 18,
    },
    rightBtn: {
      color: theme.titleFontColor,
      fontSize: 16,
    },
    icon: {
      width: 30,
    },
    iconSlim: {
      fontSize: 6 * RFT,
    },
    iconContainer: {
      minHeight: headerHeight,
      justifyContent: 'center',
    },
    iconColor: theme.primaryFontColor,
    iconSize: 24,
    buttonGroup: {
      top: 2 * RVW,
      width: 30 * RVW,
      height: 8 * RVW,
      borderRadius: 6,
    },
    secondLineTitleView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingBottom: 12,
      paddingHorizontal: 18,
    },
    secondLineTitle: {
      fontSize: 24,
      color: '#fff',
      fontWeight: 'bold',
    },
    statusView: {
      paddingHorizontal: 10,
      backgroundColor: theme.stressColor,
      borderRadius: 3,
      justifyContent: 'center',
      alignItems: 'center',
    },
    statusValue: {
      fontSize: theme.fontSizeM,
      color: theme.simpleFontColor,
      textAlign: 'center',
      lineHeight: 28,
    },
    containerStyle: {
      backgroundColor: '#fff',
      paddingTop: statusBarHeight,
      paddingHorizontal: 18,
      height: headerHeight + statusBarHeight,
      borderBottomColor: 'transparent',
    },
    bottomLine: {
      borderBottomColor: theme.separatorColor,
    },
    // 高亮样式，为主色调（蓝色），文字为白色
    lightContainerStyle: {
      borderBottomColor: 'transparent',
      backgroundColor: theme.primaryColor,
    },
    lightTitleStyle: {
      color: theme.primaryBgColor,
    },
    lightIconColor: theme.primaryBgColor,

    accountTitleView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    blueContainerStyle: {
      borderBottomColor: theme.blueColor,
      backgroundColor: theme.blueColor,
    },
  };
}
