/**
 * 底部导航tabbar样式
 * author: moke
 */
import { footerHeight } from '../../../common';

const tabHeight = 49;

export function getTabbarStyle(theme) {
  return {
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tabBar: {
      height: footerHeight + tabHeight,
      paddingBottom: footerHeight,
      backgroundColor: '#fff',
      // shadowColor: '#666666',
      // shadowOffset: { x: 5, y: 5 },
      // shadowOpacity: 0.2,
      // shadowRadius: 10,
      // elevation: 0,
    },
    tabBarShadowStyle: {
      // height: 1,
    },
    tabBarScens: {
      paddingBottom: footerHeight + tabHeight,
    },
    icon: {
      width: 26,
      height: 26,
    },
    selectedIcon: {
      width: 26,
      height: 26,
    },
    title: {
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightRegular,
      fontSize: 10,
      lineHeight: 14,
      marginTop: 0,
      marginBottom: 4,
    },
    selectedTitle: {
      // color: '#118654',
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightRegular,
      fontSize: 10,
      lineHeight: 14,
      marginTop: 0,
      marginBottom: 4,
    },
    cryptoPromptMain: {
      position: 'absolute',
      bottom: footerHeight + tabHeight + 13,
      right: 15,
    },
    cryptoPromptIcon: {
      width: 44,
      height: 44,
    },
    badge: {
      position: 'absolute',
      top: 0,
      right: -5,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#f00',
    },
  };
}
