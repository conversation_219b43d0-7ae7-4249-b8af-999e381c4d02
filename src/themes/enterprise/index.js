/**
 * 主题导出功能
 * author: moke
 */

import { theme } from './theme';
import { getHeaderStyle } from './layout/header';
import { getTabbarStyle } from './layout/tabbar';
import { getFooterStyle } from './layout/footer';
import { getBannerStyle } from './layout/banner';
import { getContentStyle } from './layout/content';
import { getGlobalStyle } from './global';
import { getHomeStyle } from './pages/home';
import { getChangePasswordStyle } from './pages/changePassword';
import { getPersonnelDetailStyle } from './pages/personnelDetail';
import { getLoginStyle } from './pages/login';
import { getMineStyle } from './pages/mine';
import { getWebviewStyle } from './pages/webview';
import { getTradeListStyle } from './pages/tradeList';
import { getSecurityStyle } from './pages/security';
import { getVerifyLoginPasswordStyle } from './pages/verifyLoginPassword';
import { getTransferScanStyle } from './pages/transferScan';
import { getGuideStyle } from './pages/guide';
import { getTouchLoginStyle } from './pages/touchLogin';
import { getSetPasswordStyle } from './pages/setPassword';
import { getInterviewStyle } from './pages/interview';
import { getResumeListStyle } from './pages/resumeList';
import { getResumeDetailStyle } from './pages/resumeDetail';
import { getTradeFilterStyle } from './pages/tradeFilter';
import { getPaySuccessStyle } from './pages/paySuccess';
import { getMessageListStyle } from './pages/messageList';

/**
 * 新增的样式文件需要在这里配置一下
 */
const styleMapFunc = {
  theme: () => theme.night,
  global: getGlobalStyle,
  header: getHeaderStyle,
  tabbar: getTabbarStyle,
  footer: getFooterStyle,
  banner: getBannerStyle,
  content: getContentStyle,
  home: getHomeStyle,
  changePassword: getChangePasswordStyle,
  personnelDetail: getPersonnelDetailStyle,
  login: getLoginStyle,
  mine: getMineStyle,
  webview: getWebviewStyle,
  tradeList: getTradeListStyle,
  security: getSecurityStyle,
  verifyLoginPassword: getVerifyLoginPasswordStyle,
  transferScan: getTransferScanStyle,
  guide: getGuideStyle,
  touchLogin: getTouchLoginStyle,
  setPassword: getSetPasswordStyle,
  interview: getInterviewStyle,
  resumeList: getResumeListStyle,
  resumeDetail: getResumeDetailStyle,
  tradeFilter: getTradeFilterStyle,
  paySuccess: getPaySuccessStyle,
  messageList: getMessageListStyle,
};

function getStyles(keys, currTheme) {
  const result = {};
  keys.forEach((key) => {
    const f = styleMapFunc[key];
    result[`${key}Style`] = f(currTheme);
  });
  return result;
}

function getStyle(key, currTheme) {
  const f = styleMapFunc[key];
  if (f) {
    return f(currTheme);
  }
  return {};
}

function get(key) {
  const currTheme = styleMapFunc.theme();
  if (Array.isArray(key)) {
    return getStyles(key, currTheme);
  }
  return getStyle(key, currTheme);
}

export default {
  get,
};
