import { deviceWidth, footerHeight, headerHeight } from '../../../common';

/**
 * 功能：引导页 样式
 * Author:高兆培
 */
export function getGuideStyle(theme) {
  return {
    pageContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    itemContainer: {
      alignItems: 'center',
      justifyContent: 'flex-end',
      flex: 1,
    },
    topContainer: {
      justifyContent: 'center',
      marginTop: headerHeight,
      flex: 1,
    },
    contentView: {
      paddingHorizontal: 60,
      marginBottom: 45,
    },
    guideImg: {
      width: deviceWidth - 40,
      marginHorizontal: 50,
    },
    dotBox: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      marginBottom: 20,
    },
    dotView: {
      width: 4,
      height: 4,
      borderRadius: 2,
      backgroundColor: '#D8D8D8',
      marginHorizontal: 2,
    },
    dotSelectedView: {
      width: 24,
      backgroundColor: theme.primaryColor,
    },
    guideTitle1: {
      fontSize: theme.fontSizeXXL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      lineHeight: 28,
      textAlign: 'center',
    },
    guideContent: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      marginTop: 10,
      lineHeight: 20,
      textAlign: 'center',
    },
    btnContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    btn: {
      minWidth: 150,
    },
    nextText: {
      fontSize: theme.fontSizeM,
      color: theme.stressColor,
      lineHeight: 20,
    },
    spaceH: {
      paddingHorizontal: 24,
    },
    spaceLH: {
      paddingHorizontal: 52,
    },
  };
}
