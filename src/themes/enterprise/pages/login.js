import { statusBarHeight } from '../../../common';

/**
 * 登录界面样式
 */

export function getLoginStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    loginContainer: {
      marginTop: 64,
      paddingLeft: 5,
      paddingRight: 5,
    },
    inputContainer: {
      flex: 1,
      height: 48,
      marginHorizontal: 28,
      // backgroundColor: 'red'
    },
    titleContainer: {
      paddingLeft: 18,
    },
    formInputContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    formTilte: {
      fontSize: 14,
      color: '#8E96A3',
      marginHorizontal: 38,
      lineHeight: 16,
    },
    userIdText: {
      fontSize: 20,
      color: '#333333',
      marginHorizontal: 38,
      lineHeight: 40,
      height: 40,
    },
    userIdTips: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: 38,
    },
    userIdTexttips: {
      fontSize: 14,
      color: '#FFA600',
      lineHeight: 20,
      marginLeft: 10,
    },
    title: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXXL,
      fontWeight: 'bold',
    },
    warnTitleText: {
      color: theme.minorFontColor,
      fontSize: theme.fontSizeS,
    },
    warnText: {
      color: theme.minorFontColor,
      fontSize: theme.fontSizeS,
      marginTop: 4,
    },
    verificationCodeContainer: {
      flex: 1,
      flexDirection: 'row',
    },
    verificationCodeLeftMain: {
      flex: 7,
    },
    verificationCodeRightMain: {
      flex: 3,
      justifyContent: 'center',
      marginTop: 6,
    },
    verificationCodeRightContent: {
      height: 40,
      marginTop: IS_ANDROID ? 10 : 0,
    },
    verificationCodeBtn: {
      backgroundColor: theme.primaryColor,
      borderRadius: 4,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      height: 40,
    },
    leftViewContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 0,
    },
    line: {
      width: 1,
      height: 14,
      backgroundColor: theme.separatorColor,
    },
    leftViewMain: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: 35,
    },
    leftViewText: {
      color: theme.primaryFontColor,
      fontSize: theme.fontSizeS,
      fontWeight: 'normal',
    },
    leftViewIcon: {
      paddingRight: 10,
      paddingLeft: 2,
      color: theme.primaryFontColor,
      fontSize: 15,
    },
    labelStyle: {
      color: theme.primaryFontColor,
      fontSize: theme.fontSizeM,
      paddingVertical: 10,
    },
    btnBox: {
      flex: 1,
      paddingHorizontal: 38,
      marginTop: 32,
    },
    buttonTitle: {
      color: theme.primaryBgColor,
      fontSize: theme.fontSizeM,
    },
    codeContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 17,
    },
    sendCodeLabel: {
      color: theme.labelFontColor,
      fontSize: theme.fontSizeS,
      marginTop: 16,
      paddingLeft: 18,
    },
    sendCodeText: {
      color: theme.primaryColor,
      fontSize: theme.fontSizeS,
      fontWeight: 'bold',
    },
    regionCon: {
      flex: 1,
      paddingHorizontal: 15,
    },
    regionContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomColor: theme.separatorColor,
      borderBottomWidth: 1,
    },
    regionImage: {
      width: 24,
      height: 16,
      marginRight: 6,
      borderRadius: 2,
    },
    regionTextContainer: {
      flexDirection: 'column',
      marginLeft: 8,
    },
    regionTitle: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      fontWeight: 'bold',
    },
    regionLine: {
      height: 4,
      color: theme.primaryBgColor,
    },
    regionSubTitle: {
      fontSize: theme.fontSizeS,
      color: theme.primaryFontColor,
    },
    pwdSpace: {
      // marginTop: 10,
      // marginBottom: 12,
    },
    headerContainer: {
      borderBottomColor: 'transparent',
      backgroundColor: theme.primaryBgColor,
    },
    backgroundCon: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    inputStyle: {
      height: 48,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
    placeholderFontColor: theme.placeholderFontColor,
    passwordInputContainer: {
      flex: 1,
      height: 40,
      marginTop: 12,
      paddingLeft: 28,
      paddingRight: 10,
    },
    passwordInputStyle: {
      paddingLeft: 0,
      fontSize: theme.fontSizeM,
    },
    authenticationlabelBox: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingRight: 28,
      paddingLeft: 28,
    },
    tabBody: {
      marginTop: 30,
      // backgroundColor:'red'
      // flex: 1,
    },
    loginTypeText: {
      fontSize: 15,
      color: theme.titleFontColor,
      textAlign: 'center',
      lineHeight: 17,
      paddingVertical: 15,
    },
    loginTypeText2: {
      color: theme.stressColor,
    },
    bottomContainer: {
      position: 'absolute',
      bottom: 32,
      width: '100%',
    },

    languageContainer: {
      marginTop: statusBarHeight,
      paddingHorizontal: 28,
      height: 40,
      justifyContent: 'center',
    },
    languageTooltipWrap: {
      flexDirection: 'row-reverse',
    },
    languageWrap: {
      flexDirection: 'row-reverse',
      alignItems: 'center',
    },
    language: {
      paddingHorizontal: 5,
      height: 20,
      justifyContent: 'center',
    },
    languageText: {
      fontSize: 14,
      color: '#333',
      lineHeight: 18,
    },
    languageDropdown: {
      justifyContent: 'center',
    },
    iconSwitchLanguage: {
      width: 15,
      height: 20,
    },

    protocolCon: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    sendCodeTitle: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
    },
    logoCon: {
      flexDirection: 'row',
      marginLeft: 38,
      // justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    logo: {
      width: 42,
      height: 42,
      marginRight: 8,
    },
    separatorLine: {
      height: 1,
      backgroundColor: '#C7D0D6',
      // marginVertical: 4,
      marginHorizontal: 38,
    },
    welcomeText: {
      fontSize: theme.fontSizeLg,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      lineHeight: 42,
    },
    registerText: {
      fontSize: theme.fontSizeM,
      color: '#5D6CC1',
      fontWeight: '500',
      paddingVertical: 10,
      lineHeight: 17,
      marginHorizontal: 38,
      alignSelf: 'flex-start',
    },
    spaceZero: {
      marginHorizontal: 0,
    },
    commonInputContainer: {
      // padding: 0,
      // backgroundColor: 'rgba(42, 42, 42, 0.5)',
      // borderRadius: 4,
      borderBottomColor: 'transparent',
      // backgroundColor: 'red'
    },
    flagImage: {
      width: 30,
      height: 20,
      marginRight: 16,
    },
    textColor: {
      color: 'rgba(255, 255, 255, 0.8)',
    },
    setPwdExtra: {
      marginTop: 20,
      marginHorizontal: 0,
    },
    setPwdContainer: {
      height: 54,
      backgroundColor: '#fff',
      borderRadius: 7,
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    setPwdInputStyle: {
      height: 54,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      // paddingLeft: 21,
    },
    setPwdInputContainer: {
      flex: 1,
      height: 54,
      marginTop: 12,
      marginHorizontal: 38,
    },
    setPwdH: {
      marginHorizontal: 38,
    },
    setPwdSendCodeLabel: {
      color: theme.primaryFontColor,
      fontSize: theme.fontSizeM,
      marginTop: 16,
    },
    uploadContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 40,
    },
    uploadImageContainer: {
      width: 154,
      height: 154,
      borderRadius: 77,
    },
    uploadImage: {
      width: 154,
      height: 154,
      borderRadius: 77,
    },
    loginTitleContainer: {
      paddingHorizontal: 44,
      marginTop: 32,
      paddingLeft: 48,
    },
    loginTitleText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXXL,
      fontWeight: theme.fontWeightMedium,
    },
    firstLoginInputContainer: {
      paddingHorizontal: 44,
      marginTop: 35,
    },
    firstInputContainer: {
      backgroundColor: 'transparent',
      borderBottomColor: theme.separatorColor,
    },
    firstInputStyle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
  };
}
