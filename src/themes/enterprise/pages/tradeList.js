/**
 * 功能 交易记录
 * 作者：李鹏飞
 */
export function getTradeListStyle(theme) {
  return {
    listContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 14,
      paddingHorizontal: 18,
      backgroundColor: theme.primaryBgColor,
    },
    itemMain: {
      flexDirection: 'row',
      alignItems: 'center',
      flexShrink: 1000,
      paddingRight: 20,
    },
    itemImg: {
      width: 36,
      height: 36,
      borderRadius: 18,
      marginRight: 14,
    },
    listItem: {
      flexDirection: 'column',
      flexShrink: 1000,
    },
    itemTitle: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 22,
      flexShrink: 1000,
    },
    itemDate: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
      lineHeight: 18,
      marginTop: 5,
    },
    itemRightContainer: {
      alignItems: 'flex-end',
    },
    amountStatusContainer: {
      alignItems: 'flex-end',
    },
    amount: {
      fontSize: theme.fontSizeXL,
      color: theme.stressColor,
      fontWeight: 'bold',
      lineHeight: 25,
    },
    statusText: {
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 16,
      marginTop: 5,
    },
    status: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 18,
      paddingTop: 5,
    },

    headerItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: 46,
      paddingHorizontal: 18,
      backgroundColor: theme.listBgColor,
      marginBottom: -10,
    },
    headDate: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      // justifyContent: 'flex-end'
    },
    headColor: {
      width: 3,
      height: 18,
      backgroundColor: theme.titleLineColor,
      marginRight: 14,
    },
    headMonth: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      fontWeight: 'bold',
    },
    allTrade: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    tradeSend: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
    },
    tradeInconme: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
      marginLeft: 10,
    },
    emptyDetailText: {
      marginTop: 15,
      color: theme.minorFontColor,
      fontSize: theme.fontSizeM,
    },
    emptyButtonContainer: {
      marginTop: 50,
      alignItems: 'center',
    },
    listHeaderBox: {
      backgroundColor: '#DBF8E0',
      // height: 40,
      paddingHorizontal: 16,
      paddingVertical: 8,
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    listHeaderBoxContent: {
      color: '#4FC556',
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
    },
  };
}
