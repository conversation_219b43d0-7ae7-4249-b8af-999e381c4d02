/**
 * 支付成功
 * <AUTHOR>
 */
export function getPaySuccessStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.primaryBgColor,
      flex: 1,
    },
    contentContainer: {
      paddingHorizontal: theme.containerPaddingHorizontal,
    },
    topContainer: {
      alignItems: 'center',
      marginBottom: 10,
    },
    titleText: {
      fontSize: theme.fontSizeXL,
      color: theme.titleFontColor,
      marginTop: 14,
      marginBottom: 80,
      textAlign: 'center',
    },
    pendingText: {
      fontSize: theme.fontSizeXL,
      color: theme.titleFontColor,
      marginTop: 14,
      marginBottom: 20,
      textAlign: 'center',
    },
    subText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      textAlign: 'center',
      marginBottom: 4,
    },
    btnBoxContainer: {
      marginHorizontal: 38,
    },
    pendingBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 20,
    },
  };
}
