/**
 * 扫码转账
 * <AUTHOR>
 */
export function getTransferScanStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: '#F5F8FB',
      flex: 1,
    },
    headerBottomLine: {
      backgroundColor: '#f6f6f6',
      height: 1,
    },
    topContainer: {
      alignItems: 'center',
    },
    avatar: {
      width: 56,
      height: 56,
      borderRadius: 37,
      marginVertical: 14,
    },
    nameContainer: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    nameLabelText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
    },
    nameTypeText: {
      fontSize: 18,
      fontWeight: theme.fontWeightRegular,
      color: '#333333',
      lineHeight: 28,
      // borderColor: theme.minorColor,
      // borderWidth: 1,
      // borderRadius: 4,
      // paddingVertical: 2,
      // paddingHorizontal: 5,
      // marginHorizontal: 5,
    },
    nameText: {
      fontSize: 14,
      fontWeight: '400',
      color: '#4F5866',
      lineHeight: 28,
      marginBottom: 6,
    },
    bottomContainer: {
      flex: 1,
      marginBottom: 0,
      backgroundColor: '#fff',
      borderTopLeftRadius: 30,
      borderTopRightRadius: 30,
      paddingHorizontal: 18,
    },
    mealBox: {
      flexDirection: 'row',
      marginTop: 11,
    },
    mealItemBox: {
      paddingVertical: 5,
      paddingHorizontal: 30,
      backgroundColor: theme.placeholderFontColor,
      borderRadius: 15,
    },
    mealItemActiveBox: {
      backgroundColor: theme.primaryColor,
    },
    mealText: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
      lineHeight: 20,
    },
    mealActive: {
      color: theme.simpleFontColor,
    },
    amountLabelText: {
      fontSize: theme.fontSizeL,
      fontWeight: '500',
      color: '#4F5866',
      lineHeight: 21,
    },
    amountInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      // height: 60,
      // paddingVertical: 5,
      marginTop: 3,
    },
    amountSymbolText: {
      fontSize: theme.fontSizeXXL,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      paddingVertical: 7,
      paddingRight: 5,
      alignItems: 'center',
    },
    unitIcon: {
      width: 20,
      height: 20,
      marginRight: 5,
      // marginTop: 5,
      tintColor: theme.titleFontColor,
    },
    smallUnitIcon: {
      width: 12,
      height: 12,
      marginRight: 0,
      // marginTop: 5,
      tintColor: theme.primaryFontColor,
    },
    amountInput: {
      flex: 1,
      fontSize: 16,
      lineHeight: 22,
      height: 50,
      fontWeight: '400',
      color: theme.titleFontColor,
      paddingHorizontal: 20,
      paddingVertical: 14,
      alignItems: 'center',
      backgroundColor: '#EFF1F3',
      borderRadius: 5,
    },
    inputRightText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: '#5389F5',
      lineHeight: 18,
      paddingVertical: 5,
    },
    amountInputBottomLine: {
      backgroundColor: '#EDEDED',
      height: 1,
    },
    remarkInput: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      paddingVertical: 10,
      paddingHorizontal: 0,
      marginTop: 10,
      // marginBottom: 35,
    },
    remarkPlaceholderTextColor: '#5389F5',
    selectionColor: theme.primaryColor,

    rechargeInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    rechargeInfoLabel: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
    },
    rechargeUserNameView: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rechargeTypeTag: {
      fontSize: theme.fontSizeXS,
      fontWeight: theme.fontWeightRegular,
      color: theme.minorColor,
      borderColor: theme.minorColor,
      borderWidth: 1,
      borderRadius: 4,
      paddingVertical: 2,
      paddingHorizontal: 5,
      marginLeft: 5,
    },
    rechargeInfoValue: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      textAlign: 'right',
    },
    rechargeInfoView: {
      alignItems: 'flex-end',
      marginTop: 5,
      marginBottom: 20,
    },

    mealRechargeHistoryText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      lineHeight: 18,
      padding: 10,
      marginTop: 10,
      alignSelf: 'center',
    },

    balanceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    balanceLeftContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    balanceLabelText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryFontColor,
      lineHeight: 20,
      paddingRight: 10,
    },
    balanceText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryFontColor,
      lineHeight: 20,
      marginRight: 5,
    },
    allWithdrawText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: '#5389F5',
      lineHeight: 20,
      paddingVertical: 10,
    },
    feeText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      lineHeight: 20,
    },

    selectCounterContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: theme.containerPaddingHorizontal,
      paddingTop: 15,
      paddingBottom: 15,
    },
    selectCounterTopContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    selectCounterView: {
      paddingVertical: 5,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      minWidth: 40,
      minHeight: 30,
    },
    selectLabelText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: '#5389F5',
      lineHeight: 20,
      paddingHorizontal: 10,
    },
    counterContainer: {
      flexDirection: 'row',
      paddingVertical: 10,
      paddingHorizontal: 10,
      backgroundColor: '#F6F6F6',
      borderRadius: 5,
      marginTop: 5,
    },
    counterInfoContainer: {
      marginLeft: 6,
      flex: 1,
    },
    counterText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      lineHeight: 22,
    },
    counterPositionContainer: {
      flexDirection: 'row',
      marginTop: 6,
    },
    counterPositionIconContainer: {
      height: 17,
      justifyContent: 'center',
    },
    counterPositionText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: theme.minorFontColor,
      lineHeight: 17,
      // marginLeft: 2,
    },
    warnHintContainer: {
      marginTop: 10,
      marginBottom: 0,
    },
    counterListContainer: {
      alignSelf: 'center',
      marginTop: 25,
    },
    counterListText: {
      paddingHorizontal: 15,
      fontSize: theme.fontSizeM,
      color: '#5389F5',
      fontWeight: theme.fontWeightRegular,
      lineHeight: 16,
    },
    refundText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.blueColor,
      lineHeight: 24,
      textAlign: 'center',
      marginTop: 31,
    },
  };
}
