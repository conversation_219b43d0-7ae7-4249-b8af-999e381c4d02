/**
 * 功能：账号与安全样式
 * 描述：
 * Author:孙宇强
 */

export function getSecurityStyle(theme) {
  return {
    container: {
      // flex: 1,
      backgroundColor: theme.listBgColor,
    },
    itemSeparator: {
      paddingHorizontal: 16,
      backgroundColor: theme.primaryBgColor,
    },
    itemSeparatorContent: {
      height: 1,
      backgroundColor: theme.mediumBgColor,
    },
    cell: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.primaryBgColor,
    },
    celllabelTitle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      flexShrink: 100,
      fontWeight: 'bold',
    },
    cellLabelContent: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 18,
      minHeight: 48,
      backgroundColor: '#fff',
    },
    itemLabelText: {
      color: theme.textBtnColor,
      fontSize: theme.fontSizeM,
      lineHeight: 21,
      flexShrink: 100,
    },
    itemRightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemValueText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
      paddingRight: 10,
      flexShrink: 100,
    },
    switchImg: {
      width: 43,
      height: 23,
    },
    hintText: {
      color: theme.minorFontColor,
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 17,
      marginHorizontal: 18,
      marginTop: 6,
    },
    tipsText: {
      fontSize: theme.fontSizeM,
      color: theme.tradeUsdColor,
      fontWeight: theme.fontWeightRoman,
      paddingHorizontal: 18,
      paddingBottom: 12,
      backgroundColor: '#fff',
    },
    verifyContainer: {
      paddingHorizontal: 18,
      paddingVertical: 18,
      backgroundColor: theme.primaryBgColor,
      marginTop: 10,
      paddingBottom: 0,
    },
    hintContainer: {
      marginTop: 6,
      flexDirection: 'row',
      flexShrink: 1000,
      backgroundColor: theme.cryptoWaringBgColor,
      paddingVertical: 10,
      paddingHorizontal: 10,
      paddingBottom: 11,
      borderRadius: 8,
    },
    hintImg: {
      width: 17,
      height: 17,
      marginRight: 7,
      marginTop: 2,
    },
    hintDescText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.sellColor,
      lineHeight: 20,
      flexShrink: 1000,
    },
    headerText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      lineHeight: 22,
      flexShrink: 1000,
    },
  };
}
