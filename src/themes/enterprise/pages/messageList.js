/**
 * 消息列表
 * <AUTHOR>
 */
export function getMessageListStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    itemContainer: {
      marginHorizontal: theme.containerMarginHorizontal,
      marginTop: 10,
      paddingHorizontal: 16,
      backgroundColor: theme.primaryBgColor,
      borderRadius: 10,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemDateText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 20,
      paddingVertical: 16,
      flex: 1,
    },
    itemLine: {
      height: 1,
      backgroundColor: theme.headerBottomBorderColor,
    },
    itemTitleText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightBold,
      lineHeight: 22,
      marginVertical: 10,
    },
    itemDescText: {
      color: theme.primaryFontColor,
      fontSize: 13,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 20,
      marginBottom: 20,
    },
  };
}
