/**
 * 功能：面试 样式文件
 * 作者： sxw
 * @export
 * @param {*} theme
 * @returns
 */
export function getInterviewStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingTop: 12,
      paddingBottom: 10,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    itemTopRightContainer: {
      marginLeft: 16,
      flex: 1,
      marginTop: 10,
    },
    itemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    itemNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginRight: 6,
    },
    sencondText: {
      fontSize: 13,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    statusText: {
      fontSize: theme.fontSizeS,
      color: '#999999',
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      marginTop: 10,
      borderTopWidth: 1,
      borderTopColor: theme.minorBgColor,
      paddingTop: 7,
    },
    commentedBox: {
      paddingTop: 16,
      paddingBottom: 6,
    },
    commentText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      marginLeft: 5,
    },
    avatarContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
      elevation: 4,
    },
  };
}
