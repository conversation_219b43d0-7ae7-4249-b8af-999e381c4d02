/**
 * 功能：首页 home 样式文件
 * 作者： sxw
 * @export
 * @param {*} theme
 * @returns
 */
import { deviceWidth, statusBarHeight, headerHeight } from '../../../common';

export function getHomeStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: '#f7f7f7',
    },
    scorllViewContainer: {
      backgroundColor: '#f7f7f7',
    },
    headerBox: {
      height: 200,
      width: deviceWidth,
    },
    logoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    iconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      marginTop: statusBarHeight + 10,
      marginBottom: 20,
    },
    logoImg: {
      width: 62,
      height: 20,
    },
    changeImg: {
      width: 16,
      height: 18,
    },
    settingImg: {
      width: 20,
      height: 18,
    },
    companyContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      // marginTop: 20,
    },
    companyLogoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexShrink: 1,
    },
    companyLogoBox: {
      width: 50,
      height: 50,
      position: 'relative',
      backgroundColor: '#f7f7f7',
      borderRadius: 25,
    },
    companyLogo: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    vipIcon: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 16,
      height: 16,
    },
    companyName: {
      marginLeft: 10,
      color: '#333333',
      fontSize: 17,
      fontWeight: 'bold',
      flexShrink: 1,
      // maxWidth: 200,
    },
    balanceContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: -25,
      paddingBottom: 14,
    },
    balanceBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: deviceWidth - 40,
      height: 70,
      borderRadius: 8,
      paddingHorizontal: 13,
    },
    balanceTitle: {
      color: '#fff',
      fontSize: 12,
      flexShrink: 1,
    },
    balanceAmount: {
      color: '#fff',
      fontSize: 20,
      fontWeight: 'bold',
    },
    balanceBtnBox: {
      minWidth: 60,
      paddingHorizontal: 8,
      height: 28,
      borderRadius: 5,
      backgroundColor: '#EF3D48',
    },
    balanceBtn: {
      color: '#fff',
      fontSize: 13,
      textAlign: 'center',
      lineHeight: 28,
    },
    topContainer: {
      marginTop: -30,
      marginHorizontal: 12,
    },
    sectionContainer: {
      backgroundColor: '#fff',
      marginHorizontal: 12,
      paddingBottom: 14,
      borderRadius: 10,
      paddingTop: 17,
    },
    sectionContainerFirst: {
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
    },
    sectionContainerExtra: {
      marginBottom: 10,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingRight: 12,
    },
    sectionLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    sectionHeaderTitle: {
      marginLeft: 10,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightBold,
    },
    sectionLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 12,
      marginTop: 15,
      marginBottom: 17,
    },
    sectionContent: {
      flexDirection: 'row',
      // alignItems: 'center',
      paddingHorizontal: 12,
    },
    sectionContentItem: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'flex-start',
      backgroundColor: '#F7F9FF',
      minHeight: 64,
      borderRadius: 10,
      marginRight: 10,
      paddingHorizontal: 6,
      paddingVertical: 6,
    },
    sectionContentItemLast: {
      marginRight: 0,
    },
    sectionContentItemValue: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      lineHeight: 28,
      textAlign: 'center',
      marginTop: 3,
    },
    sectionContentItemLabel: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeS,
      textAlign: 'center',
    },
    languageBox: {
      justifyContent: 'center',
    },
    languageTooltipWrap: {
      minWidth: 80,
    },
    language: {
      width: 80,
    },
    languageText: {
      fontSize: 14,
      color: '#333',
      lineHeight: 18,
      textAlign: 'center',
    },
    imgSpace: {
      marginHorizontal: 5,
    },
    unVerifiedBox: {
      backgroundColor: '#3299FF',
      borderRadius: 5,
      height: 18,
      paddingHorizontal: 6,
      marginTop: -2,
    },
    unVerified: {
      color: '#ffffff',
      fontSize: 10,
      textAlign: 'center',
      lineHeight: 18,
    },
    msgIconView: {
      height: 36,
      width: 36,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    msgIconNumView: {
      minWidth: 18,
      paddingHorizontal: 4,
      position: 'absolute',
      top: 2,
      height: 13,
      right: 5,
      backgroundColor: '#EF3D48',
      borderRadius: 7,
      borderColor: '#ffffff',
      borderWidth: 1,
    },
    msgIconNum: {
      fontSize: 12,
      color: '#ffffff',
      lineHeight: 12.5,
    },
  };
}
