/**
 * 功能：验证登录密码样式
 * 描述：
 * Author:孙宇强
 */

export function getVerifyLoginPasswordStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    titleText: {
      marginLeft: 42,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXXL,
      fontWeight: 'bold',
    },
    inputContainer: {
      marginTop: 35,
      paddingHorizontal: theme.containerPaddingHorizontal,
    },
    inputStyle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
    forgotText: {
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      alignSelf: 'flex-end',
      marginHorizontal: theme.containerPaddingHorizontal,
      paddingVertical: 10,
    },
    buttonContainer: {
      marginHorizontal: theme.containerPaddingHorizontal,
      marginTop: 30,
    },
  };
}
