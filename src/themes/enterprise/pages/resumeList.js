/**
 * 功能：简历列表 样式文件
 * 作者： sxw
 * @export
 * @param {*} theme
 * @returns
 */
export function getResumeListStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 12,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    itemTopRightContainer: {
      marginLeft: 16,
      flex: 1,
      marginTop: 10,
    },
    itemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    itemNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    flexRowPopver: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      paddingLeft: 16,
      paddingVertical: 10,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginRight: 6,
    },
    sencondText: {
      fontSize: 13,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    statusText: {
      fontSize: theme.fontSizeS,
      color: '#3299FF',
    },
    statusImg: {
      marginLeft: 5,
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    filterStatusBox: {
      backgroundColor: '#FAECEC',
      borderRadius: 5,
      paddingHorizontal: 8,
      justifyContent: 'center',
      marginRight: 16,
      height: 21,
    },
    filterStatusText: {
      fontSize: 11,
      color: '#EF3D48',
      textAlign: 'center',
    },
    filterStatusTextMain: {
      fontSize: 13,
      color: theme.titleFontColor,
      flexShrink: 1,
    },
    filterContainer: {
      backgroundColor: '#fff',
      paddingBottom: 10,
    },
    sectionContent: {
      flexDirection: 'row',
      // alignItems: 'center',
      backgroundColor: '#fff',
      paddingHorizontal: 8,
      paddingTop: 5,
    },
    sectionContentItem: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'flex-start',
      backgroundColor: '#F6F6F6',
      minHeight: 60,
      borderRadius: 5,
      marginRight: 5,
      paddingHorizontal: 6,
      paddingVertical: 6,
    },
    sectionContentItemLast: {
      marginRight: 0,
    },
    sectionContentItemValue: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightBold,
      textAlign: 'center',
    },
    sectionContentItemLabel: {
      color: theme.titleFontColor,
      fontSize: 11,
      textAlign: 'center',
    },
    sectionContentItemActive: {
      backgroundColor: '#FAECEC',
    },
    sectionContentItemLabelActive: {
      color: '#EF3D48',
    },
    filterText: {
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      paddingHorizontal: 12,
      paddingTop: 10,
    },
    filterTextMain: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
    },
    sapartLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginTop: 10,
      marginBottom: 10,
    },
    avatarContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
      elevation: 4,
    },
  };
}
