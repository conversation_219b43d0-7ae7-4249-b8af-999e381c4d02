/**
 * 设置密码界面样式
 */
export function getSetPasswordStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    loginContainer: {
      marginTop: 10,
      paddingHorizontal: theme.containerPaddingHorizontal,
    },
    welcomeText: {
      fontSize: theme.fontSizeXXXL,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      lineHeight: 42,
      marginTop: 40,
      marginBottom: 10,
    },
    title: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightBold,
      lineHeight: 22,
      marginTop: 15,
    },
    setPwdSendCodeLabel: {
      color: theme.primaryFontColor,
      fontSize: 13,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 18,
      marginTop: 10,
    },
    inputContainer: {
      marginTop: 15,
    },
    inputContainerStyle: {
      paddingHorizontal: 0,
    },
    setPwdContainer: {
      height: 48,
      backgroundColor: '#fff',
      borderRadius: 7,
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    setPwdInputStyle: {
      height: 48,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      paddingHorizontal: 15,
    },
    placeholderFontColor: theme.placeholderFontColor,
    btnBox: {
      marginTop: 30,
    },
    switchAccountText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
      alignSelf: 'center',
      marginTop: 10,
      padding: 10,
    },
  };
}
