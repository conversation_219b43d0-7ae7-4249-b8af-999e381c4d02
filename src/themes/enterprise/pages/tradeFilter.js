import { deviceWidth } from '../../../common';

/**
 * 交易记录筛选
 * <AUTHOR>
export function getTradeFilterStyle(themeStyle) {
  return {
    container: {
      flex: 1,
      backgroundColor: themeStyle.listBgColor,
    },
    typeText: {
      marginTop: 15,
      paddingHorizontal: 18,
      fontSize: themeStyle.fontSizeL,
      color: themeStyle.titleFontColor,
    },
    timeBox: {
      marginLeft: 18,
      marginRight: 0,
      marginTop: 0,
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignContent: 'space-between',
      flexWrap: 'wrap',
    },
    timeView: {
      justifyContent: 'center',
      borderWidth: 1,
      borderRadius: 17,
      height: 34,
      width: (deviceWidth - 15 * 2 - 18 * 2) / 3,
      marginRight: 15,
      marginBottom: 15,
      alignItems: 'center',
    },
    timeText: {
      fontSize: themeStyle.fontSizeM,
    },
    divideLine: { height: 1, backgroundColor: themeStyle.payInputBorderColor },
    emptyView1: { height: 10 },
    emptyView2: { height: 20 },
    hosueView: {
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: 17,
      height: 34,
      marginRight: 15,
      marginBottom: 15,
      paddingHorizontal: 12,
      minWidth: (deviceWidth - 15 * 2 - 18 * 2) / 3,
    },
  };
}
