/**
 * 功能：简历详情 样式文件
 * 作者： sxw
 * @export
 * @param {*} theme
 * @returns
 */
export function getResumeDetailStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 18,
      paddingTop: 10,
    },
    nameContainer: {
      justifyContent: 'center',
      marginRight: 15,
      flexShrink: 10,
    },
    nameText: {
      fontSize: 28,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    jobText: {
      fontSize: 14,
      color: theme.titleFontColor,
    },
    avatarContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4,
      shadowRadius: 4,
      elevation: 4,
    },
    avatarImg: {
      width: 66,
      height: 66,
      borderRadius: 33,
    },
    sexImg: {
      position: 'absolute',
      right: -5,
      top: 8,
      width: 16,
      height: 16,
      backgroundColor: '#fff',
      borderRadius: 8,
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      // paddingHorizontal: 14,
      // marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 22,
      marginTop: 10,
    },
    topItemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 15,
      flexShrink: 10,
    },
    topItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 25,
      flexShrink: 10,
    },
    topItemText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginLeft: 10,
    },
    topItemLineText: {
      fontSize: theme.fontSizeM,
      color: '#C0C8D1',
      marginLeft: 15,
      marginRight: 5,
    },
    careerObJContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 5,
    },
    careerObJTitleText: {
      fontSize: theme.memoSize,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      flexShrink: 1,
      marginRight: 10,
    },
    careerObJAmountText: {
      fontSize: theme.fontSizeXL,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
    },
    careerObJText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
    },
    educationDateText: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
    },

    languageContainer: {
      marginBottom: 10,
    },
    languageTitle: {
      fontSize: theme.fontSizeS,
      color: theme.primaryFontColor,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    languagLevel: {
      backgroundColor: '#3299FF',
      height: 5,
      borderRadius: 5,
      flexShrink: 10,
    },
    languagLevelText: {
      fontSize: theme.fontSizeXS,
      color: theme.minorFontColor,
      marginLeft: 4,
    },
    separateInnerLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginVertical: 20,
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      paddingHorizontal: 14,
      position: 'absolute',
      bottom: 30,
      width: '100%',
    },
    bottomItem: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomItemImg: {
      width: 50,
      height: 50,
    },
    bottomItemText: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
      marginTop: 8,
    },
    qualifacationsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    qualifacationsRight: {
      marginLeft: 14,
      flexShrink: 1,
    },
    photoBox: {
      alignItems: 'center',
      justifyContent: 'center',
      width: 80,
      height: 100,
      borderWidth: 0.5,
      borderColor: '#EEEEEE',
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EF3D48',
      height: 32,
      width: 78,
      borderRadius: 5,
      justifyContent: 'center',
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#fff',
    },
    sectionHeaderBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    favImg: {
      width: 20,
      height: 20,
    },
  };
}
