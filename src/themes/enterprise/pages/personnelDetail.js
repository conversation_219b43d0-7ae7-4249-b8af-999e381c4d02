/**
 *
 * 功能：通讯录UI样式js
 * 作者：Rays
 *
 */
export function getPersonnelDetailStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    itemView: {
      backgroundColor: theme.primaryBgColor,
      paddingVertical: 20,
      paddingLeft: 20,
      paddingRight: 20,
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemLabelText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightBold,
    },
    itemValueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      paddingRight: 5,
      paddingLeft: 40,
      flex: 1,
      textAlign: 'right',
    },
    itemValuePhoneText: {
      color: theme.primaryFontColor,
    },
    itemSeparatorView: {
      paddingHorizontal: 20,
      height: 1,
      backgroundColor: theme.primaryBgColor,
    },
    itemSeparatorBg: {
      flex: 1,
      backgroundColor: '#F7F7F7',
    },
    itemSeparator: {
      marginTop: 10,
      height: 1,
      backgroundColor: '#F7F7F7',
    },
    itemAvatarView: {
      marginTop: 10,
      paddingRight: 20,
      paddingVertical: 10,
    },
    itemAvatar: {
      width: 46,
      height: 46,
      borderRadius: 23,
    },
    backButton: {
      position: 'absolute',
      top: 10,
      left: 10,
    },
    phoneItemView: {
      height: 65,
      flexDirection: 'row',
      alignItems: 'center',
    },
    phoneItemTextView: {
      flex: 1,
      paddingBottom: 10,
    },
    inputContainer: {
      backgroundColor: '#fff',
      // paddingVertical: 10,
      paddingHorizontal: 20,
      marginTop: 20,
    },
    inputLabelText: {
      color: '#ADADAD',
      fontSize: theme.fontSizeS,
    },
    inputText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      height: 65,
      // paddingLeft:1011
    },
    footContainer: {
      marginTop: 48,
      paddingHorizontal: 42,
    },
  };
}
