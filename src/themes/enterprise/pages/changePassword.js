/**
 *
 * 功能：修改密码UI样式js
 * 作者：盛宣伟
 *
 */
export function getChangePasswordStyle(theme) {
  return {
    authenticationBox: {
      flex: 1,
      backgroundColor: '#fff',
      marginBottom: 18,
    },
    authenticationlabelBox: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 28,
      height: 40,
      marginTop: 12,
    },
    authenticationlabelBoxEXR: {
      flexDirection: 'column',
      paddingRight: 18,
      paddingLeft: 18,
      backgroundColor: '#fff',
    },
    authenticationNameLeft: {
      flex: 5,
    },
    authenticationNameRight: {
      flex: 5,
    },
    inputLabel: {
      flex: 1,
    },
    nameText: {
      fontWeight: 'normal',
    },
    labelStyle: {
      color: theme.labelFontColor,
      fontSize: theme.fontSizeM,
    },
    backgroundColor: {
      backgroundColor: theme.primaryListBgColor,
      borderBottomWidth: 0,
      borderTopWidth: 0,
      padding: 0,
    },
    inputBackground: {
      backgroundColor: theme.primaryBgColor,
      fontSize: theme.fontSizeS,
      height: 40,
      borderBottomWidth: 0,
      marginTop: 10,
      borderRadius: 4,
      color: theme.mediumFontColor,
      paddingLeft: 10,
    },
    authenticationUsedName: {
      flex: 1,
    },
    remarks: {
      color: theme.remarksFontColor,
      fontSize: theme.fontSizeM,
      paddingLeft: 20,
    },
    wheelSelectorlBox: {
      flex: 1,
      paddingRight: 18,
      paddingLeft: 18,
      marginTop: 16,
      flexDirection: 'column',
    },
    wheelSelectorlLabel: {
      flex: 1,
    },
    wheelSelectorlContent: {
      flex: 1,
      flexDirection: 'row',
    },
    wheelSelectorLeft: {
      flex: 9,
      justifyContent: 'center',
      paddingLeft: 30,
    },
    wheelSelectorLeftText: {
      fontSize: theme.fontSizeL,
      color: theme.mediumFontColor,
    },
    wheelSelectorRight: {
      flex: 1,
      justifyContent: 'center',
    },
    cameraBox: {
      flex: 1,
      position: 'relative',
    },
    cameraContent: {
      width: 80,
      height: 80,
      backgroundColor: theme.primaryBgColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    btnBox: {
      flex: 1,
      paddingRight: 18,
      paddingLeft: 18,
      marginTop: 10,
      marginBottom: 50,
    },
    btnContent: {
      height: 40,
      backgroundColor: theme.primaryColor,
      borderRadius: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    btnText: {
      fontSize: theme.fontSizeM,
      color: theme.mediumFontColor,
    },
    verificationCodeRightContent: {
      height: 40,
    },
    verificationCodeBtn: {
      backgroundColor: '#ccc',
      borderRadius: 4,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      height: 38,
    },
    verifyPhoneCon: {
      flex: 1,
      paddingVertical: 0,
      backgroundColor: theme.primaryBgColor,
    },
    verifyConColor: {
      backgroundColor: theme.listBgColor,
    },
    inputStyle: {
      height: 40,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
    },
    title: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXL,
      fontWeight: 'bold',
    },
    titleContainer: {
      paddingLeft: 18,
    },
    desc: {
      fontSize: theme.fontSizeS,
      color: theme.primaryFontColor,
      marginTop: 10,
    },
    sendCodeTitle: {
      fontSize: theme.fontSizeM,
      color: theme.stressColor,
    },
    separatorLine: {
      height: 1,
      backgroundColor: '#eee',
      marginVertical: 4,
      marginHorizontal: 26,
    },
    separatorLittleMedLine: {
      height: 8,
      backgroundColor: theme.listBgColor,
    },
    separatorMedLine: {
      height: 12,
      backgroundColor: theme.listBgColor,
    },
    separatorHardLine: {
      marginLeft: 36,
      marginRight: 18,
    },
    verticalSpace: {
      padding: 10,
      paddingBottom: 0,
      marginTop: 20,
    },
    regionImage: {
      width: 24,
      height: 16,
      marginRight: 6,
    },
    headerContainer: {
      borderBottomColor: 'transparent',
      backgroundColor: theme.primaryBgColor,
    },
    titleText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXL,
      fontWeight: 'bold',
      paddingBottom: 8,
    },
    phoneView: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 24,
      marginBottom: 8,
    },
    labelTitle: {
      color: theme.minorFontColor,
      fontSize: theme.fontSizeL,
      marginLeft: 4,
      marginRight: 16,
    },
    image: {
      width: 12,
      height: 12,
    },
    addressView: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginLeft: 24,
      marginTop: 8,
      marginBottom: -4,
    },
    addressTop: {
      marginTop: -2,
      marginLeft: 0,
    },
  };
}
