import { deviceWidth, statusBarHeight } from '../../../common';

/**
 *
 * 功能：企业页面样式
 * <AUTHOR>
 */
export function getMineStyle(theme) {
  const scale = deviceWidth / 375;
  const itemHeight = 150;
  const paddingBottom = scale * 20;
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    headerBox: {
      height: 285,
      justifyContent: 'flex-end',
      marginBottom: 10,
      width: deviceWidth,
      position: 'absolute',
      zIndex: -1,
    },
    logoBox: {
      marginLeft: 30,
      marginTop: 8,
      width: 50,
      height: 50,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 1,
      shadowRadius: 8,
      position: 'relative',
    },
    companyLogo: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    vipIcon: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 16,
      height: 16,
    },
    contentBox: {
      flex: 1,
      marginTop: -25,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      position: 'relative',
      zIndex: -1,
    },
    companyInfo: {
      marginTop: 35,
      marginHorizontal: 30,
    },
    companyName: {
      fontSize: 17,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      marginRight: 10,
      flexShrink: 1,
    },
    companyDesc: {
      fontSize: 13,
      color: theme.primaryFontColor,
      lineHeight: 21,
    },
    moreIcon: {
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      width: 60,
    },
    buyGroup: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginHorizontal: 12,
      marginTop: 5,
    },
    buyGroupItem: {
      width: (deviceWidth - 36) / 2,
      height: 140,
      backgroundColor: '#fff',
      borderRadius: 5,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buyGroupItemValue: {
      fontSize: 28,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      lineHeight: 33,
    },
    buyGroupItemLabel: {
      fontSize: 12,
      color: theme.primaryFontColor,
      lineHeight: 17,
      marginBottom: 14,
      marginTop: 8,
    },
    buyBtn: {
      width: 100,
      height: 30,
      borderRadius: 5,
      backgroundColor: '#FFE9EB',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buyBtnText: {
      fontSize: 14,
      color: '#EF3D48',
      lineHeight: 30,
    },
    middleContainer: {
      marginTop: 10,
      borderRadius: 5,
      marginHorizontal: 12,
      overflow: 'hidden',
      backgroundColor: '#fff',
      paddingHorizontal: 14,
      paddingTop: 5,
      paddingBottom: 18,
    },
    middleTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: 48,
      marginBottom: 20,
      borderBottomWidth: 1,
      borderBottomColor: '#EDEDED',
    },
    middleBottom: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    middleBottomItem: {
      alignItems: 'center',
    },
    middleBottomItemText: {
      fontSize: 12,
      color: theme.titleFontColor,
      lineHeight: 17,
      marginTop: 7,
    },
    bottomContainer: {
      marginTop: 10,
      borderRadius: 5,
      marginHorizontal: 12,
      overflow: 'hidden',
    },
    valueTextStyle: {
      fontSize: 14,
    },
    exitContainer: {
      marginTop: 10,
      backgroundColor: '#fff',
      height: 48,
    },
    exitText: {
      fontSize: theme.fontSizeL,
      color: theme.primaryColor,
      lineHeight: 48,
      textAlign: 'center',
    },
    pendingTextStyle: {
      color: '#3299FF',
    },
    unVerifiedBox: {
      backgroundColor: '#3299FF',
      borderRadius: 5,
      height: 18,
      paddingHorizontal: 6,
      marginRight: 10,
    },
    unVerified: {
      color: '#ffffff',
      fontSize: 10,
      textAlign: 'center',
      lineHeight: 18,
    },
    nameContainer: {
      flexDirection: 'row',
      // alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
      marginTop: 2,
    },
    editIconBox: {
      marginRight: -20,
      width: 50,
      justifyContent: 'flex-end',
      flexDirection: 'row',
    },
    editIcon: {
      width: 32,
      height: 26,
    },
  };
}
