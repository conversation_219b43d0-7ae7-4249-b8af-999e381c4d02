/**
 * 指纹、人脸登录
 * <AUTHOR>
 */
export function getTouchLoginStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    contentContainer: {
      alignItems: 'center',
      marginTop: 50,
      flex: 1,
    },
    usernameText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
      marginTop: 10,
    },
    centerContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 40,
    },
    clickContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    clickImage: {
      marginBottom: 6,
      width: 72,
      height: 72,
    },
    clickText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 20,
      marginTop: 10,
    },
    otherContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 10,
    },
    otherText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
      marginRight: 5,
    },
  };
}
