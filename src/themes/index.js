/**
 * 主题设置
 * 组件库以react-native-elements为主，再根据需求微调
 * 请参考：
 * https://github.com/react-native-training/react-native-elements
 * https://react-native-training.github.io/react-native-elements
 */
import { theme } from './theme';

export * from './base';
export * from './global';
export * from './header';
export * from './chat';
export * from './actionBar';
export * from './contact';
export * from './emoji';
export * from './login';
export * from './dynamic';
export * from './register';
export * from './job';
export * from './city';
export * from './jobSearch';
export * from './jobIntention';
export * from './forgot';
export * from './resume';
export * from './resumePre';
export * from './message';
export * from './chatList';
export * from './resumeEdit';
export * from './jobDetail';
export * from './companyDetail';
export * from './seenItem';
export * from './general';
export * from './communicatedList';
export * from './deliveredList';
export * from './interviewList';
export * from './interviewDetail';
export * from './settings';
export * from './modifyPhone';
export * from './favorite';
export * from './userProfile';
export * from './dynamicDetail';
export * from './annexResume';
export * from './uploadAnnex';
export * from './scanUpload';
export * from './resources';

function get(name) {
  if (name === 'theme') {
    return theme.night;
  }
  return {};
}

export default {
  get,
};
