/**
 * 职位、公司搜索
 */
import { titleColor, desColor, bgColor, baseBlueColor } from './base';
import { RVW, footerHeight } from '../common';

export const jobIntensionStyle = {
  con: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  submitButton: {
    position: 'absolute',
    bottom: 0,
    backgroundColor: '#fff',
    width: '100%',
    height: 56 + footerHeight,
    paddingTop: 8,
  },
  buttonStyle: {
    backgroundColor: baseBlueColor,
    width: '90%',
    height: 40,
    marginLeft: '5%',
    elevation: 0,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  title: {
    fontSize: 14,
    color: desColor,
  },
  subTitleContent: {
    marginRight: -10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    // width: 160,
  },
  subTitle: {
    fontSize: 14,
    color: titleColor,
  },
  separateLine: {
    backgroundColor: bgColor,
    height: 1,
    marginHorizontal: 12,
  },
  jobExpectedSearch: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 4 * RVW,
  },
  listContainerStyle: {
    borderBottomColor: '#eee',
    borderBottomWidth: 0.5,
    paddingHorizontal: 12,
    minHeight: 48,
  },
  rightContainerStyle: {
    color: titleColor,
    width: 200,
    textAlign: 'right',
    marginRight: -12,
    fontSize: 14,
  },
  completeContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  titleContainer: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomColor: baseBlueColor,
    borderBottomWidth: 1,
  },
  topTitle: {
    fontSize: 22,
    color: '#000',
    fontWeight: '500',
  },
  topDes: {
    fontSize: 14,
    color: titleColor,
    marginTop: 8,
  },
};
