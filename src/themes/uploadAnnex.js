import { titleColor } from './base';
import { footerHeight } from '../common';

export const uploadAnnexStyle = {
  topContainer: {
    paddingHorizontal: 15, marginTop: 20
  },
  titleText: { color: titleColor, fontSize: 16 },
  subTitleText: { marginTop: 10, color: '#999999', fontSize: 12, marginBottom: 20 },
  uploadContainer: {
    flexDirection: 'row', alignItems: 'center',
    shadowColor: 'black',
    shadowOffset: { height: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
    backgroundColor: '#fff',
    borderRadius: 4,
    marginBottom: 15
  },
  imageContainer: { marginLeft: 30, width: 64, height: 64, marginVertical: 20 },
  uploadText: { color: titleColor, fontSize: 18, fontWeight: 'bold', marginLeft: 35 },
  bottomContainer: { position: 'absolute', left: 0, right: 0, bottom: footerHeight + 24, paddingHorizontal: 15 },
  remarkText: { color: '#999999', fontSize: 12, textAlign: 'center' },

};
