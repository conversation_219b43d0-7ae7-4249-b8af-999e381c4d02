/**
 * 面试详情
 */
import { subTitleColor, desColor, bgColor, baseBlueColor } from './base';
import { footerHeight } from '../common';

export const interviewDetailStyle = {
  companyContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: 16,
    borderBottomColor: bgColor,
    borderBottomWidth: 1,
  },
  companyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  companyName: {
    color: subTitleColor,
    fontSize: 14,
  },
  interviewStatus: {
    color: baseBlueColor,
    fontSize: 12,
  },
  jobInfo: {
    color: desColor,
    marginTop: 10,
    fontSize: 12,
  },
  listItemContainer: {
    marginTop: 6,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
  },
  itemTitle: {
    color: desColor,
    fontSize: 14,
    flexShrink: 1,
  },
  itemSubtitle: {
    color: subTitleColor,
    fontSize: 16,
    marginLeft: 20,
    flexShrink: 100,
    lineHeight: 20,
  },
  otherInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '90%',
    marginTop: 4,
  },
  companySubcribe: {
    fontSize: 12,
    color: desColor,
    lineHeight: 18,
    flexShrink: 1,
  },
  spaceLine: {
    backgroundColor: desColor,
    width: 1,
    height: 10,
    marginHorizontal: 6,
  },
  btnContainer: {
    backgroundColor: '#fff',
    justifyContent: 'center',
    flexDirection: 'row',
    shadowColor: 'black',
    height: 56 + footerHeight,
    shadowOffset: { h: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
  },
  chatBtn: {
    backgroundColor: '#EEF8FD',
    width: '94%',
    marginLeft: '4%',
    height: 40,
    borderRadius: 5,
    borderWidth: 1,
    marginTop: 8,
    borderColor: baseBlueColor,
    elevation: 0,
  },
  sendBtn: {
    backgroundColor: baseBlueColor,
    width: '94%',
    marginLeft: '2%',
    height: 40,
    borderRadius: 5,
    borderWidth: 1,
    marginTop: 8,
    borderColor: baseBlueColor,
    elevation: 0,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
