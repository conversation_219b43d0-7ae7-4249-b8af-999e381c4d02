import { titleColor, bgColor, baseBlueColor } from './base';
import { headerHeight, statusBarHeight, deviceWidth } from '../common';

export const resourcesStyle = {
  header: {
    backgroundColor: baseBlueColor,
  },
  typeContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    // paddingTop: statusBarHeight + 10,
    // height: headerHeight,
  },
  tabBarStyle: { borderWidth: 0, minHeight: 50, minWidth: 180 },
  tabBarTextStyle: { fontWeight: 'bold', minWidth: 50 },
  activeTabTextStyle: {
    fontWeight: 'bold',
    fontSize: 16,
    lineHeight: 24,
  },
  tabStyles: { tab: { minWidth: 50 } },

  listContainer: {
    flex: 1,
    backgroundColor: '#F5F8FB',
    // paddingTop: 10,
  },
  itemContainer: {
    paddingTop: 20,
    paddingHorizontal: 18,
    paddingBottom: 14,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  titleText: {
    fontSize: 18,
    color: '#333333',
    fontWeight: 'bold',
  },
  coinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinImage: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 5,
  },
  nameText: {
    fontSize: 13,
    color: '#4F5866',
    fontWeight: 'normal',
    marginRight: 9,
  },
  timeText: {
    fontSize: 13,
    color: '#8E96A3',
  },
  infoContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 13,
  },
  infoImage: {
    width: 110,
    height: 75,
    borderRadius: 5,
    marginLeft: 14,
  },
  contentContainer: {
    marginTop: 15,
  },
  contentText: {
    color: '#333333',
    fontSize: 15,
    lineHeight: 22,
    flexShrink: 1,
    flexGrow: 1,
  },
  scanTextBox: {
    marginTop: 10,
  },
  scanText: {
    fontSize: 12,
    color: '#8E96A3',
    lineHeight: 17,
  },
  videoContainer: {
    position: 'relative',
    marginTop: 15,
  },
  videoImage: {
    flex: 1,
    height: 192,
    borderRadius: 8,
  },
  iconPlay: {
    width: 18,
    height: 21,
    position: 'absolute',
    left: (deviceWidth - 36 - 18) / 2,
    top: (192 - 21) / 2,
  },
};
