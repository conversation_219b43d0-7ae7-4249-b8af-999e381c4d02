/**
 * 登录界面样式
 */
import { RPX, deviceHeight, headerHeight } from '../common';
import { baseBlueColor, basePaddingHorizontal, titleColor, subTitleColor } from './base';

export const loginStyle = {
  page: {
    flex: 1,
  },
  container: {
    paddingHorizontal: basePaddingHorizontal,
    height: deviceHeight,
    justifyContent: 'space-between',
    backgroundColor: 'rgba(0,0,0,0)',
  },
  languageSection: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  languageTooltipWrap: {
    flexDirection: 'row-reverse',
  },
  languageWrap: {
    flexDirection: 'row-reverse',
  },
  language: {
    paddingHorizontal: 5,
    height: headerHeight,
    justifyContent: 'center',
  },
  languageText: {
    fontSize: 14,
    color: '#fff',
    lineHeight: 18,
  },
  languageDropdown: {
    justifyContent: 'center',
  },
  iconSwitchLanguage: {
    width: 15,
    height: 20,
  },
  titleSection: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 64,
    marginTop: 5,
  },
  title: {
    fontSize: 40,
    color: '#fff',
  },
  tabSection: {
    marginTop: 25,
    backgroundColor: '#fff',
  },
  tabHeader: {
    // flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 1,
    borderColor: '#eee',
    // backgroundColor: 'red',
  },
  tabTitle: {
    // marginTop: 10,
    fontSize: 16,
    color: subTitleColor,
    lineHeight: 50,
  },
  tabActive: {
    borderBottomWidth: 2,
    borderColor: baseBlueColor,
  },
  tabActiveTitle: {
    color: titleColor,
  },
  tabBody: {
    // flex: 1,
  },
  tabContent: {
    backgroundColor: 'white',
  },
  formGroup: {
    // flex: 1,
    flexDirection: 'row',
  },
  formGroupLine: {
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  formControl: {
    width: '100%',
    height: 50,
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  formPhoneSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 50,
  },
  formPhoneAreaWrap: {
    width: 100,
    justifyContent: 'center',
  },
  formPhoneAreaTo: {
    width: 100,
    borderRightWidth: 1,
    borderColor: '#eee',
  },
  formPhoneArea: {
    paddingTop: 14,
    fontSize: 14,
    color: subTitleColor,
    width: 100,
    height: 50,
    paddingLeft: 15,
  },
  formPhoneInputWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  formSendCodeSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    // backgroundColor: 'red',
    height: 50,
  },
  formSendCodeInputWrap: {
    flex: 1,
  },
  formSendCodeBtnWrap: {
    // flex: 1,
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  formControlCode: {
    paddingVertical: 0,
    paddingBottom: 0,
    paddingTop: 0,
    width: '100%',
    height: 49,
    borderWidth: 0,
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  btnSendCode: {
    paddingHorizontal: 20 * RPX,
    height: 38,
    marginRight: 10,
    backgroundColor: baseBlueColor,
  },
  btnSection: {
    justifyContent: 'center',
    alignItems: 'center',
    maxHeight: 40,
    height: 40,
    padding: 0,
    marginTop: 25,
    marginBottom: 15,
  },
  btnLogin: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: 40,
    maxHeight: 40,
    borderRadius: 0,
    borderTopWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderLeftWidth: 1,
    borderStyle: 'solid',
    borderColor: '#fff',
    backgroundColor: 'rgba(0,0,0,0.0)',
  },
  btnLoginText: {
    fontSize: 20,
    color: '#fff',
    textAlign: 'center',
  },
  linkSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  forgotLink: {
    fontSize: 12,
    color: '#fff',
  },
  registerLink: {
    fontSize: 12,
    color: '#fff',
  },
  thirdLoginSection: {
    height: 180,
  },
  thirdLoginHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 30,
    maxHeight: 30,
    marginBottom: 34,
  },
  thirdTitleDivider: {
    width: 100,
    backgroundColor: 'rgba(255,255,255, 0.3)',
  },
  thirdTitle: {
    color: '#fff',
    fontSize: 18,
    marginRight: 24,
    marginLeft: 24,
  },
  thirdBtnGroupWrap: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  facebook: {
    justifyContent: 'center',
    alignItems: 'center',
    // marginRight: 45,
  },
  facebookTitle: {
    color: '#fff',
    fontSize: 18,
  },
  linkedin: {
    justifyContent: 'center',
    alignItems: 'center',
    // marginLeft: 45,
  },
  linkedinTitle: {
    color: '#fff',
    fontSize: 18,
  },
};
