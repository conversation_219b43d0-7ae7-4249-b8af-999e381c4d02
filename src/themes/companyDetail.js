/**
 * 公司详情样式
 */
import { baseBlueColor, titleColor, subTitleColor, bgColor, desColor } from './base';
import { RVW } from '../common';

export const companyDetailStyle = {
  jobContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: '#fff',
  },
  chatBtn: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    backgroundColor: '#fff',
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopColor: bgColor,
    borderTopWidth: 2,
  },
  tagItem: {
    marginTop: 16,
    marginRight: 16,
    borderRadius: 50,
    overflow: 'hidden',
    backgroundColor: bgColor,
  },
  tagText: {
    fontSize: 12,
    color: subTitleColor,
    paddingVertical: 2,
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  listContainer: {
    padding: 10,
    flexDirection: 'column',
    backgroundColor: '#fff',
    paddingLeft: 20,
    paddingTop: 0,
    borderBottomColor: bgColor,
    borderBottomWidth: 0.5,
  },
  companyInfo: {
    flexDirection: 'column',
  },
  companyPannel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  companyPannelContainer: {
    marginTop: 10,
  },
  companyName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: titleColor,
    width: RVW * 60,
  },
  jobWages: {
    fontSize: 16,
    color: baseBlueColor,
  },
  companyDetail: {
    flexDirection: 'column',
    width: RVW * 80,
  },
  companySubcribe: {
    fontSize: 14,
    color: titleColor,
    marginTop: 6,
    width: RVW * 72,
    lineHeight: 18,
  },
  namePannel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  nameContainer: {
    marginTop: 10,
  },
  nameSubcribe: {
    marginLeft: 10,
  },
  scorllContainer: {
    marginBottom: -4,
  },
  lineStyle: {
    width: (RVW * 100) / 6,
    height: 2,
    backgroundColor: baseBlueColor,
    marginLeft: (RVW * 100) / 6,
  },
  textStyle: {
    flex: 1,
    textAlign: 'center',
  },
  jobDescContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  jobDescTitle: {
    fontWeight: 'bold',
    color: titleColor,
    fontSize: 14,
    marginBottom: 14,
  },
  jobDescSub: {
    color: subTitleColor,
    fontSize: 14,
    marginBottom: 10,
    lineHeight: 18,
  },
  companyCardCont: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderRadius: 6,
    backgroundColor: '#fff',
    marginHorizontal: 10,
    marginVertical: 10,
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.4,
    elevation: 3,
  },
  companySubCard: { flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start' },
  companyCardContent: { flexDirection: 'column', marginLeft: 10, flexShrink: 100 },
  cardCompanyName: {
    color: subTitleColor,
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  cardCompanyDesc: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'wrap',
  },
  cardTag: {
    color: desColor,
    fontSize: 10,
  },
  cardVLine: {
    paddingHorizontal: 6,
    color: desColor,
  },
  spaceLine: {
    backgroundColor: bgColor,
    height: 1,
    marginVertical: 10,
  },
  site: {
    fontSize: 12,
    color: baseBlueColor,
    textDecorationLine: 'underline',
    flexShrink: 100,
    marginLeft: 10,
  },
  siteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  siteTitle: {
    fontSize: 12,
    color: titleColor,
  },
  companyAddressCon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    width: '100%',
    justifyContent: 'space-between',
  },
  addressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexShrink: 100,
    marginLeft: 10,
  },
  addressContainer2: {
    width: '80%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  spaceSpaLine: {
    backgroundColor: desColor,
    width: 1,
    height: 10,
    marginHorizontal: 6,
    flexShrink: 100,
  },
  welfareContainer: {
    marginLeft: 6,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexShrink: 100,
  },
  welfareContainerItem: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexShrink: 100,
  },
  welfareContainerItemText: {
    color: desColor,
    fontSize: 12,
    flexShrink: 100,
  },
  expandTitle: {
    fontSize: 12,
    color: baseBlueColor,
    textAlign: 'center',
  },
  lineView: {
    flexShrink: 10,
    flexGrow: 10,
    backgroundColor: '#eee',
    height: 1,
  },
  moreText: {
    flexShrink: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  companyLogo: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: '#f5f5f5',
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
