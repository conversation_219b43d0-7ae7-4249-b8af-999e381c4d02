/**
 * 全局
 */
import { RVW, RFT, RPX } from '../common';
import { bgColor } from './base';

export const globalStyle = {
  container: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  avatarWrapper: {
    width: 9 * RVW + 1,
    height: 9 * RVW + 1,
    borderRadius: (9 * RVW) / 2,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  avatar: {
    width: 9 * RVW,
    height: 9 * RVW,
    borderRadius: (9 * RVW) / 2,
  },
  listItemRight: {
    width: 60 * RVW,
    textAlign: 'right',
  },
  loadingTips: {
    textAlign: 'center',
    paddingVertical: 16,
    color: '#333333',
  },
};
export default globalStyle;
