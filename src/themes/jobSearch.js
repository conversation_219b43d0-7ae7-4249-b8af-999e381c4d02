/**
 * 职位、公司搜索
 */
import { baseBlueColor, bgColor, titleColor, desColor, subTitleColor } from './base';
import { RVW } from '../common';

export const jobSearchStyle = {
  searchBar: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
  },
  cityName: {
    fontSize: 13,
    color: baseBlueColor,
    width: 40,
    marginLeft: 2,
  },
  divider: {
    backgroundColor: 'lightgray',
    height: '85%',
    width: 1,
  },
  jobContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: '#fff',
  },
  jobContainer2: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: '#f2f2f2',
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    paddingLeft: 10 * 2,
    backgroundColor: '#fff',
    paddingBottom: 10,
    // marginLeft: 10 * 2,
  },
  tag: {
    backgroundColor: '#eee',
    paddingHorizontal: 2,
    paddingVertical: 2,
    paddingLeft: 8,
    paddingRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tagItem: {
    marginTop: 16,
    marginRight: 16,
    overflow: 'hidden',
  },
  badgeStyle: {
    height: 'auto',
    borderRadius: 5,
    backgroundColor: bgColor,
    paddingHorizontal: 8,
    paddingVertical: 3,
  },
  listTagItem: {
    marginVertical: 5,
    marginRight: 12,
    overflow: 'hidden',
  },
  listTagBadgeStyle: {
    borderRadius: 6,
    height: 16,
    paddingHorizontal: 8,
    backgroundColor: bgColor,
  },
  tagText: {
    fontSize: 10,
    color: subTitleColor,
    textAlign: 'center',
  },
  recommendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    paddingTop: 10 * 3,
    paddingLeft: 10 * 2,
    paddingRight: 10 * 2.5,
  },
  hotContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    paddingTop: 10 * 2,
    paddingLeft: 10 * 2,
    paddingRight: 10 * 2.5,
    marginTop: 10 * 1.5,
  },
  placeholderTitle: {
    fontSize: 14,
    color: desColor,
  },
  companyInfo: {
    flexDirection: 'column',
  },
  listContainer: {
    padding: 10,
    flexDirection: 'column',
    backgroundColor: '#fff',
    marginTop: 8,
    paddingLeft: 15,
  },
  companyPannel: {
    flexDirection: 'row',
    paddingTop: 6,
  },
  companyPannelContainer: {
    marginTop: 10,
  },
  namePannel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  nameContainer: {
    marginTop: 10,
  },
  companyDetail: {
    flexDirection: 'column',
    width: RVW * 80,
  },
  companyName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: titleColor,
    flexGrow: 1,
    flexShrink: 200,
  },
  jobWages: {
    fontSize: 16,
    flexGrow: 1,
    flexShrink: 1,
    textAlign: 'right',
    paddingLeft: 10,
    color: baseBlueColor,
  },
  companySubcribe: {
    fontSize: 10,
    color: desColor,
    marginTop: 2,
    width: RVW * 72,
    lineHeight: 18,
  },
  nameSubcribe: {
    color: desColor,
    fontSize: 12,
  },
  listTags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: '100%',
  },
  requireJob: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 6,
  },
  container: {
    height: 32,
  },
  lineStyle: {
    width: (RVW * 100) / 6,
    height: 2,
    backgroundColor: '#fff',
    marginLeft: (RVW * 100) / 6,
  },
  textStyle: {
    // flex: 1,
    fontSize: 12,
    textAlign: 'center',
  },
  renderSearchInput: {
    backgroundColor: '#fff',
    height: 30,
    width: 82 * RVW,
    borderRadius: 5,
    borderBottomWidth: 0,
    marginRight: 10,
  },
  spaceLine: {
    color: desColor,
    fontSize: 12,
  },
  locationsStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '84%',
    marginTop: 6,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  locationsCon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 6,
  },
  locationsTitle: {
    backgroundColor: subTitleColor,
    width: 1,
    height: 12,
    marginHorizontal: 6,
  },
  userImage: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: bgColor,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: '#f5f5f5',
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
