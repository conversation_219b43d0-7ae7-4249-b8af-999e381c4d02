/**
 * 面试职位列表
 */
import { baseBlueColor, bgColor, titleColor, subTitleColor } from './base';
import { RVW } from '../common';

export const interviewListStyle = {
  viewContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  listContainer: {
    padding: 10,
    flexDirection: 'column',
    backgroundColor: '#fff',
    borderBottomColor: bgColor,
    borderBottomWidth: 1,
    paddingLeft: 20,
    paddingRight: 12,
  },
  listItemContainer: {
    flexDirection: 'column',
    marginLeft: 10,
    width: '84%',
  },
  jobInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 6,
  },
  wagesInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  namePannel: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  jobWages: {
    fontSize: 12,
    color: baseBlueColor,
    marginTop: 4,
  },
  companySubcribe: {
    fontSize: 14,
    color: titleColor,
    lineHeight: 18,
  },
  nameSubcribe: {
    fontSize: 12,
    lineHeight: 18,
    color: subTitleColor,
  },
  scorllContainer: {
    marginTop: -6,
  },
  lineStyle: {
    width: RVW * 100 / 9,
    height: 2,
    backgroundColor: '#fff',
    marginLeft: RVW * 100 / 9,
  },
  textStyle: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 5,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};

