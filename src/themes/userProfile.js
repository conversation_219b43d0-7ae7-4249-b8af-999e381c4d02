/**
 * 设置、修改密码样式
 */
import { bgColor, titleColor, desColor } from './base';

export const userProfileStyle = {
  container: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: 20,
    paddingHorizontal: 14,
    backgroundColor: '#fff',
  },
  userContainer: {
    flexDirection: 'column',
    marginLeft: 10,
  },
  userTitle: {
    fontSize: 14,
    color: titleColor,
    lineHeight: 20,
  },
  userContent: {
    fontSize: 14,
    color: desColor,
    lineHeight: 20,
  },
  listItemLeftText: {
    fontSize: 14,
    color: titleColor,
  },
  listItemContent: {
    minHeight: 48,
    borderBottomColor: bgColor,
    borderBottomWidth: 0.5,
  },
};
