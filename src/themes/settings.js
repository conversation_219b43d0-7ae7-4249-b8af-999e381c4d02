/**
 * 设置、修改密码样式
 */
import { bgColor, desColor } from './base';

export const settingStyle = {
  settingContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  exitLoingButtonContainer: {
    width: '100%',
    justifyContent: 'center',
    paddingVertical: 36,
  },
  passwordContainer: {
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    flexDirection: 'column',
  },
  inputViewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomColor: bgColor,
    borderBottomWidth: 1,
  },
  inputContainerStyle: {
    backgroundColor: '#fff',
    marginVertical: 10,
    borderBottomWidth: 0,
    borderRadius: 6,
    marginLeft: 0,
  },
  buttonStyle: {
    width: '90%',
    height: 40,
    marginLeft: '5%',
    backgroundColor: '#fff',
    borderColor: '#eee',
    borderWidth: 1,
    borderRadius: 5,
    elevation: 0,
  },
};
