
/**
 * 修改手机号样式
 */
import { baseBlueColor, bgColor, basePaddingHorizontal, titleColor, subTitleColor, deviceHeight } from './base';

export const modifyPhoneStyle = {
  page: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  container: {
    paddingHorizontal: basePaddingHorizontal,
    height: deviceHeight,
    justifyContent: 'space-between',
  },
  titleSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
    maxHeight: 50,
    marginTop: 21,
    marginBottom: 34,
  },
  formSection: {
    flex: 1,
    height: 106,
    backgroundColor: '#fff',
    marginTop: 20,
  },
  emailFormSection: {
    flex: 1,
    maxHeight: 48,
    backgroundColor: '#fff',
  },
  emailPasswordFormSection: {
    flex: 1,
    maxHeight: 97,
    backgroundColor: '#fff',
  },
  formContent: {
    flex: 1,
  },
  formGroup: {
    flex: 1,
    flexDirection: 'row',
  },
  formGroupLine: {
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  formControl: {
    width: '100%',
    height: 48,
    borderWidth: 0,
    borderColor: '#fff',
  },
  formPhoneSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 48,
  },
  formPhoneAreaWrap: {
    width: 100,
  },
  formPhoneAreaTo: {
    width: 100,
    borderRightWidth: 1,
    borderColor: '#eee',
  },
  formPhoneArea: {
    paddingTop: 14,
    fontSize: 14,
    color: subTitleColor,
    width: 100,
    height: 48,
    lineHeight: 48,
    paddingLeft: 40,
  },
  formPhoneInputWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  formSendCodeSection: {
    flex: 1,
    flexDirection: 'row',
    height: 48,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  formSendCodeInputWrap: {
    flex: 1,
  },
  formSendCodeBtnWrap: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    height: 48,
    justifyContent: 'center',
  },
  formControlCode: {
    width: '100%',
    height: 48,
    borderWidth: 0,
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  btnSendCode: {
    paddingHorizontal: 12,
    height: 32,
    marginRight: 10,
    backgroundColor: baseBlueColor,
    paddingTop: 0,
    marginTop: 0,
    elevation: 0,

  },
  resultContainer: {
    flex: 1,
    paddingHorizontal: basePaddingHorizontal,
    backgroundColor: bgColor,
  },
  resultSection: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    paddingTop: 100,
  },
  successResultTitle: {
    fontSize: 24,
    color: titleColor,
    paddingVertical: 50,
  },
  iconResultSuccess: {
    width: 120,
    height: 120,
  },
  remainSeconds: {
    fontSize: 14,
    color: titleColor,
    paddingVertical: 50,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 68,
    justifyContent: 'center',
    borderRightColor: bgColor,
    borderRightWidth: 1,
    marginLeft: 0,
    paddingRight: 6,
  },
  phoneTextDesc: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  buttonExtro: {
    backgroundColor: baseBlueColor,
    borderWidth: 0,
    marginTop: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text1: {
    fontSize: 12,
    color: subTitleColor,
    paddingHorizontal: 16,
    marginTop: 28,
    textAlign: 'center',
  },
  text2: {
    fontSize: 12,
    color: subTitleColor,
    marginTop: 4,
    paddingHorizontal: 16,
    textAlign: 'center',
  },
};
