
/**
 * 消息界面样式
 */
import { RVW, RFT } from '../common';
import { baseBlueColor, bgColor, basePaddingHorizontal, titleColor, desColor, subTitleColor } from './base';

export const chatListStyle = {
  headerCenterComponent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButtonGroup: {
    height: 29,
    width: 180,
    borderRadius: 5,
    borderColor: '#fff',
    backgroundColor: baseBlueColor,
  },
  headerSelectedButton: {
    backgroundColor: '#fff',
  },
  headerBtnText: {
    fontSize: 14,
  },
  searchSection: {
    flexDirection: 'row',
    backgroundColor: bgColor,
    alignItems: 'center',
  },
  searchBar: {
    // height: 54,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderTopColor: bgColor,
    borderBottomColor: bgColor,
    borderColor: bgColor,
    backgroundColor: bgColor,
  },
  searchBarInputWrap: {
    height: 30,
    backgroundColor: '#fff',
  },
  searchBarInput: {
    height: 30,
    paddingVertical: 5,
  },
  container: {
    backgroundColor: bgColor,
  },
  listWrap: {
    flex: 1,
    backgroundColor: bgColor,
  },
  contactItem: {
    flexDirection: 'row',
    paddingVertical: 11,
    width: 100 * RVW,
    height: 90,
    maxHeight: 90,
    borderBottomWidth: 1,
    borderColor: bgColor,
    backgroundColor: '#fff',
  },
  contactLeft: {
    width: 20 * RVW,
    paddingTop: 3,
    paddingLeft: 18,
    paddingRight: 16,
  },
  avatar: {
    width: 42,
    height: 42,
    borderRadius: 21,
  },
  contactMiddle: {
    width: 60 * RVW,
    paddingLeft: 5,
  },
  topWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 5,
  },
  name: {
    fontSize: 16,
    color: titleColor,
    fontWeight: 'bold',
    width: '50%',
  },
  position: {
    fontSize: 12,
    color: desColor,
    marginLeft: 7,
  },
  middleWrap: {
    marginTop: 2,
  },
  company: {
    fontSize: 12,
    color: subTitleColor,
  },
  bottomWrap: {
    paddingVertical: 10,
  },
  message: {
    fontSize: 14,
    color: desColor,
  },
  contactRight: {
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    width: 20 * RVW,
  },
  chatTime: {
    right: 14,
    top: 0,
    fontSize: 12,
    color: desColor,
  },
  badgeContainer: {
    right: 14,
    width: 20,
    height: 20,
    padding: 0,
    // paddingTop: 0,
  },
  badgeStyle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#f00',
  },
  paddingTopConfY: {
    paddingTop: 0,
  },
  paddingTopConfN: {
  },
  badgeText: {
    fontSize: 12,
    color: '#fff',
  },
  unreadMsgBadgeText: {
    color: '#f00',
  },
  unreadMsgBadgeContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    padding: 0,
    backgroundColor: '#f00',
    zIndex: 10,
  },
  standalone: {
    marginTop: 30,
    marginBottom: 30,
  },
  standaloneRowFront: {
    alignItems: 'center',
    backgroundColor: '#CCC',
    justifyContent: 'center',
    height: 50,
  },
  standaloneRowBack: {
    alignItems: 'center',
    backgroundColor: '#8BC645',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
  },
  backTextWhite: {
    color: '#FFF',
  },
  rowFront: {
    alignItems: 'center',
    backgroundColor: '#CCC',
    borderBottomColor: 'black',
    borderBottomWidth: 1,
    justifyContent: 'center',
    height: 50,
  },
  rowBack: {
    alignItems: 'center',
    backgroundColor: '#DDD',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtnLeft: {
    backgroundColor: 'blue',
    right: 75,
  },
  backRightBtnRight: {
    backgroundColor: 'red',
    right: 0,
  },
  controls: {
    alignItems: 'center',
    marginBottom: 30,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 5,
  },
  trash: {
    height: 25,
    width: 25,
  },
  badgeZH: {
    position: 'absolute',
    top: -3,
    right: -3,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#f00',
  },
  badgeEN: {
    position: 'absolute',
    top: 2,
    right: -3,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#f00',
  },
  badgeKM: {
    position: 'absolute',
    top: 0,
    right: -3,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#f00',
  },
};
