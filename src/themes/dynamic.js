import { RVW, RFT, RPX, deviceWidth, footerHeight } from '../common';
import { titleColor, subTitleColor, bgColor, desColor, baseRedColor, baseBlueColor } from './base';

export const dynamicStyle = {
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: 'white',
  },
  top: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  left: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 17,
    color: titleColor,
    marginLeft: 15,
    marginTop: 15,
    marginRight: 15,
    flexShrink: 1,
  },
  line: {
    width: 100,
    height: 1,
    backgroundColor: bgColor,
    marginTop: 0,
  },
  time: {
    fontSize: 14,
    color: desColor,
    marginRight: 15,
    marginTop: 12,
    textAlign: 'right',
  },
  content: {
    fontSize: 14,
    color: subTitleColor,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    marginBottom: 10,
  },

  imagesContent: {
    marginLeft: 15,
    marginRight: 10,
    marginTop: 0,
    marginBottom: 15,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignContent: 'space-between',
    flexWrap: 'wrap',
  },
  images: {
    margin: 10,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  image: {
    height: (deviceWidth - 25 - 15) / 3,
    width: (deviceWidth - 25 - 15) / 3,
    marginTop: 5,
    marginRight: 5,
    backgroundColor: bgColor,
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  bottomView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 4,
  },

  bottomImageColor: {
    color: desColor,
  },
  bottomImageColorRed: {
    color: baseRedColor,
  },

  bottomTitle: {
    lineHeight: 25,
    fontSize: 14,
    color: titleColor,
    marginLeft: 5,
  },
  commentBackGround: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  commentBackContent: {
    height: '70%',
    backgroundColor: 'white',
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
  },
  commentBackTop: {
    height: '30%',
    width: '100%',
  },
  commentBackTopEmpty: {
    width: '100%',
    height: '100%',
  },
  commentItemtContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 5,
    marginLeft: 15,
    marginRight: 15,
    marginBottom: 6,
  },

  commentItemtTitleContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  commentItemLeft: {},
  commentItemRight: {
    flex: 1,
    marginLeft: 10,
  },
  commentTitleCon: {
    marginTop: 10,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  commentTitle: {
    fontSize: 12,
    color: desColor,
    textAlign: 'center',
    paddingBottom: 5,
    paddingTop: 5,
  },
  commentName: {
    fontSize: 16,
    color: desColor,
  },
  commentTime: {
    fontSize: 12,
    color: desColor,
    textAlign: 'right',
    paddingBottom: 5,
    paddingTop: 5,
  },
  commentContent: {
    fontSize: 14,
    color: titleColor,
  },
  commentIcon: {
    height: 38,
    width: 38,
    borderRadius: 19,
  },

  inputBackTop: {
    flex: 1,
    width: '100%',
  },

  inputBoxWrapper: {
    height: 130,
    width: '100%',
  },
  inputBox: {
    flex: 1,
    backgroundColor: bgColor,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  inputText: {
    height: 80,
    marginTop: 10,
    marginRight: 15,
    marginLeft: 15,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: bgColor,
    fontSize: 14,
    paddingLeft: 8,
  },

  inputBtnContent: {
    alignItems: 'center',
    marginLeft: 15,
    marginRight: 15,
    height: 30,
  },

  inputBtnSend: {
    color: subTitleColor,
    textAlign: 'right',
  },
  inputBtnNiChecked: {
    color: baseBlueColor,
    marginLeft: 5,
  },
  inputBtnNiNormal: {
    color: desColor,
    marginLeft: 5,
    lineHeight: 17,
  },
  inputBtnNiContent: {
    backgroundColor: bgColor,
    padding: 0,
    marginLeft: 0,
    borderWidth: 0,
  },
  chatBtn: {
    padding: 0,
    width: 84 * RVW,
    height: 9 * RFT,
    shadowOffset: { h: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
  },
  chatBtnText: {
    textAlign: 'center',
    lineHeight: 9 * RFT,
    fontSize: 3 * RFT,
  },

  editheaderBg: {
    backgroundColor: 'white',
  },
  editInputText: {
    height: 130,
  },
  editContent: {
    flex: 1,
    flexDirection: 'column',
  },

  editTextCount: {
    color: desColor,
    textAlign: 'right',
    marginTop: 10,
    marginRight: 15,
    marginBottom: 10,
  },
  numberCount: {
    color: '#FC7613',
  },
  editInfoContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 60,
    backgroundColor: 'white',
    paddingLeft: 15,
    paddingRight: 15,
  },
  editImageAdd: {
    color: desColor,
  },

  editIcon: {
    color: 'white',
    marginBottom: 5,
    marginLeft: 5,
  },
  icon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginLeft: 15,
    marginTop: 15,
  },
  separatorLine: {
    height: 10,
    backgroundColor: bgColor,
  },
  commentInputBack: {
    marginBottom: footerHeight,
    height: 50,
    backgroundColor: bgColor,
  },
  commentInputView: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    height: 30,
    backgroundColor: 'white',
    justifyContent: 'center',
  },
  commentInputText: {
    margin: 0,
    color: desColor,
    paddingLeft: 6,
    fontSize: 14,
  },
  menuBox: {
    position: 'absolute',
    right: 0,
    top: 14 * RVW,
    width: 40 * RVW,
    backgroundColor: '#fff',
    borderLeftWidth: 2 * RPX,
    borderLeftColor: '#999',
  },
  menuLine: {
    fontSize: 3.6 * RFT,
    textAlign: 'center',
    borderBottomWidth: 2 * RPX,
    borderBottomColor: '#999',
    lineHeight: 10 * RFT,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  msgContainer: {
    backgroundColor: '#fff',
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
  },
  msgContainerBg: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#575757',
    width: 160,
    borderRadius: 4,
    padding: 6,
  },
  msgView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: 118,
    flexShrink: 100,
  },
  noComments: {
    marginTop: 60,
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    color: desColor,
  },
  contentTitle: {
    fontSize: 14,
    color: subTitleColor,
    lineHeight: 20,
  },
  expandContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
    marginBottom: 5,
    marginTop: 6,
  },
  expandTitle: {
    fontSize: 12,
    color: baseBlueColor,
    textAlign: 'center',
  },
  lineView: {
    flexShrink: 10,
    flexGrow: 10,
    backgroundColor: '#eee',
    height: 1,
  },
  moreText: {
    flexShrink: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  deletIcon: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 999,
  },

  editImageBackView: {
    backgroundColor: '#fff',
    height: (deviceWidth - 74) / 3,
    width: (deviceWidth - 74) / 3,
    marginRight: 0,
    marginVertical: 9,
    borderWidth: 1,
    borderColor: '#DDDEE5',
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  editImageBackViewRight: {
    backgroundColor: '#fff',
    height: (deviceWidth - 74) / 3,
    width: (deviceWidth - 74) / 3,
    marginRight: 19,
    marginVertical: 9,
    borderWidth: 1,
    borderColor: '#DDDEE5',
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  eidtImageContent: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  editImageContainer: {
    marginRight: 13,
    paddingRight: 6,
    paddingVertical: 9,
  },
  editImageContainerRight: {
    marginRight: 0,
    paddingRight: 6,
    paddingVertical: 9,
  },
  editImage: {
    height: (deviceWidth - 74) / 3,
    width: (deviceWidth - 74) / 3,
    backgroundColor: '#f2f2f2',
  },
  imageContainer: {
    backgroundColor: '#fff',
    paddingTop: 8,
    paddingLeft: 15,
    paddingBottom: 25,
  },
};
