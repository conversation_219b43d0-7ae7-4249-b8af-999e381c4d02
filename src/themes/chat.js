import { RVW, RFT, headerHeight, footerHeight, statusBarHeight } from '../common';
import {
  baseBlueColor,
  lightBlueColor,
  bgColor,
  basePaddingVertical,
  titleColor,
  desColor,
  subTitleColor,
} from './base';

export const chatStyle = {
  headerWrapper: {
    height: headerHeight + statusBarHeight,
    backgroundColor: '#fff',
    borderBottomWidth: 0,
    paddingTop: statusBarHeight,
    // zIndex: 9,
  },
  headerTitleContainer: {
    justifyContent: 'center',
    alignContent: 'center',
  },
  headerChatUserName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: titleColor,
    textAlign: 'center',
  },
  headerChatHr: {
    fontSize: 10,
    color: subTitleColor,
    textAlign: 'center',
    marginTop: 2,
  },
  headerRightWrap: {
    // position: 'absolute',
    // right: 12,
    // top: headerHeight - 28 - 5,
    // height: 28,
    // zIndex: 9999,
  },
  headerRightBtn: {
    height: 28,
    width: 80,
    borderRadius: 5,
    paddingVertical: 0,
    paddingHorizontal: 5,
    margin: 0,
    backgroundColor: baseBlueColor,
    elevation: 0,
  },
  chatMsgListContainer: {
    // zIndex: 8,
  },
  hrInfoContainer: {
    marginLeft: 15,
    marginRight: 15,
    marginBottom: 10,
  },
  hrInfoWrap: {
    borderRadius: 5,
    paddingTop: 12,
    paddingLeft: 16,
    paddingRight: 13,
    paddingBottom: 10,
    backgroundColor: '#fff',
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.1,
    elevation: 0,
  },
  hrInfoTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hrInfoName: {
    fontSize: 18,
    color: titleColor,
    fontWeight: 'bold',
    width: 60 * RVW,
  },
  hrInfoSalary: {
    fontSize: 16,
    color: baseBlueColor,
    fontWeight: 'bold',
  },
  hrInfoTags: {
    flexDirection: 'row',
    paddingVertical: 12,
  },
  hrInfoTagContainer: {
    marginRight: 10,
  },
  hrInfoTagBadgeStyle: {
    backgroundColor: bgColor,
  },
  hrInfoTag: {
    fontSize: 10,
    color: subTitleColor,
  },
  hrInfoAddress: {
    fontSize: 14,
    color: desColor,
  },
  hrInfoBottom: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: basePaddingVertical,
    marginTop: basePaddingVertical,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  hrInfoAvatarWrap: {
    paddingRight: 16,
  },
  hrInfoAvatar: {
    width: 42,
    height: 42,
    borderRadius: 21,
  },
  hrInfoCompany: {
    fontSize: 14,
    marginRight: 10,
    color: desColor,
  },
  hrInfoCompanyWrap: {
    // flexDirection: 'row',
    flex: 1,
  },
  hrInfoSize: {
    fontSize: 14,
    color: desColor,
    marginTop: 5,
  },
  chatBoxWrapper: {
    margin: 0,
    padding: 0,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#ccc',
    marginBottom: footerHeight,
  },
  chatBox: {
    flexDirection: 'row',
    marginTop: 0,
    paddingLeft: 10,
    paddingRight: 10,
    paddingHorizontal: 15,
  },
  chatUserAvatar: {
    width: 42,
    height: 42,
  },
  chatText: {
    padding: 0,
    // paddingHorizontal: 6,
    borderWidth: 0,
    borderColor: '#ccc',
    borderRadius: 4,
    fontSize: 4.5 * RFT,
    width: 80 * RVW,
    height: 46,
    paddingTop: 13,
    paddingBottom: 13,
  },
  chatBtn: {
    padding: 0,
    width: 84 * RVW,
    // height: 9 * RFT,
    shadowOpacity: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
  },
  chatBtnText: {
    textAlign: 'center',
    lineHeight: 9 * RFT,
    fontSize: 3 * RFT,
  },
  chatItemWraper: {
    backgroundColor: '#f0f0f0',
    borderTopWidth: 1,
    borderTopColor: '#999',
    paddingVertical: 20,
  },
  chatItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  chatItem: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    width: 12 * RVW,
    height: 12 * RVW,
    borderWidth: 1,
    backgroundColor: '#fff',
    borderColor: '#ccc',
    borderRadius: 2 * RVW,
  },
  iconSmall: {
    width: 10 * RFT,
    height: 10 * RFT,
  },
  sendMsgBtnWrap: {},
  sendMsgBtn: {
    height: 28,
    borderRadius: 5,
    paddingVertical: 0,
    paddingHorizontal: 5,
    margin: 0,
    backgroundColor: baseBlueColor,
    elevation: 0,
  },
  wrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 10,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 0,
    paddingRight: 0,
  },
  left: {
    marginLeft: 15,
    justifyContent: 'flex-start',
  },
  right: {
    marginRight: 15,
    justifyContent: 'flex-end',
  },
  center: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  icon: {
    marginTop: 2 * RVW,
    marginRight: 2,
    width: 20,
    height: 20,
  },
  content: {
    padding: 10,
    borderRadius: 6,
    flexWrap: 'wrap',
    maxWidth: 72 * RVW,
  },
  contentWidth: {
    maxWidth: 70 * RVW,
  },
  contentLeft: {
    marginLeft: 10,
    backgroundColor: lightBlueColor,
  },
  contentRight: {
    marginRight: 10,
    backgroundColor: '#fff',
  },
  text: {
    fontSize: 16,
    color: titleColor,
  },
  emoji: {
    width: 16 * RFT,
    height: 16 * RFT,
  },
  play: {
    width: 10 * RFT,
    height: 10 * RFT,
  },
  timetag: {
    padding: 4,
    // backgroundColor: '#f9f9ff',
    color: desColor,
    textAlign: 'center',
    maxWidth: 80 * RVW,
    marginLeft: 10 * RVW,
    marginVertical: 10,
    borderRadius: 4,
  },
  tip: {
    padding: 4,
    backgroundColor: '#f9f9ff',
    textAlign: 'center',
    maxWidth: 40 * RVW,
    marginLeft: 30 * RVW,
    marginVertical: 10,
    borderRadius: 4,
  },
  readMsg: {
    color: '#BDBDBD',
    marginRight: 10,
    marginTop: 5,
    marginLeft: 10,
  },
  unreadMsg: {
    color: '#63C6FF',
    marginRight: 10,
    marginLeft: 10,
    marginTop: 5,
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
  sendBtn: {
    height: 28,
    width: 64,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    backgroundColor: baseBlueColor,
  },
};
