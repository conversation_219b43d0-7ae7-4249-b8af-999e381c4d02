
import { RVW, RFT, headerHeight, statusBarHeight } from '../common';
import { baseBlueColor, titleColor } from './base';

export const headerStyle = {
  tabBar: {
    height: 12 * RVW,
    backgroundColor: '#fff',
  },
  tabLabel: {
    lineHeight: 9 * RVW,
    fontSize: 3.6 * RFT,
  },
  wrapper: {
    height: headerHeight + statusBarHeight,
    backgroundColor: baseBlueColor,
    borderBottomWidth: 0,
    paddingTop: statusBarHeight,
  },
  center: {
    color: '#fff',
    fontSize: 18,
  },
  centerBlack: {
    color: titleColor,
    fontSize: 18,
  },
  rightBtn: {
    color: titleColor,
    fontSize: 15,
  },
  icon: {
    width: 30,
  },
  iconSlim: {
    fontSize: 6 * RFT,
  },
  buttonGroup: {
    top: 2 * RVW,
    width: 30 * RVW,
    height: 8 * RVW,
    borderRadius: 6,
  },
};
