/**
 * 职位样式
 */
import { baseBlueColor, titleColor, desColor, bgColor, subTitleColor } from './base';
import { RVW, deviceWidth } from '../common';

export const jobStyle = {
  searchBar: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
  },
  jobContainer: {
    height: '100%',
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  swiperimage: {
    height: (113 * deviceWidth) / 375,
    width: '100%',
    backgroundColor: bgColor,
  },
  jobHunting: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 16,
    paddingRight: 8,
    alignItems: 'center',
    borderBottomColor: bgColor,
    borderBottomWidth: 1,
    backgroundColor: '#fff',
  },
  jobFilter: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#fff',
    height: 40,
    borderBottomColor: bgColor,
    borderBottomWidth: 1,
  },
  jobFilterFirst: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRightColor: '#eee',
    borderRightWidth: 1,
    width: 35 * RVW,
    backgroundColor: '#fff',
    height: 28,
  },
  jobFilterMiddle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRightColor: '#eee',
    borderRightWidth: 1,
    width: 30 * RVW,
    backgroundColor: '#fff',
    height: 28,
  },
  jobFilterEnd: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 35 * RVW,
    backgroundColor: '#fff',
    height: 28,
  },
  cityName: {
    fontSize: 13,
    color: baseBlueColor,
    width: 40,
    marginLeft: 2,
  },
  divider: {
    backgroundColor: 'lightgray',
    height: '85%',
    width: 1,
  },
  jobFilterText: {
    fontSize: 14,
  },
  listContainer: {
    padding: 15,
    flexDirection: 'column',
    backgroundColor: '#fff',
    paddingBottom: 0,
    borderTopColor: bgColor,
    borderTopWidth: 6,
  },
  mb15: {
    marginBottom: 15,
  },
  companyPannel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  companyDetail: {
    flexDirection: 'column',
    marginLeft: 10,
    width: RVW * 80,
  },
  companyName: {
    fontSize: 14,
    color: titleColor,
    width: RVW * 72,
  },
  companyInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '90%',
    marginTop: 4,
  },
  companySubcribe: {
    fontSize: 10,
    color: subTitleColor,
    marginTop: 4,
  },
  tagContainer: {
    width: RVW * 90,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tagTitle: {
    fontSize: 14,
    color: desColor,
    backgroundColor: bgColor,
    padding: 6,
    marginRight: 12,
  },
  requireJob: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16,
    width: '100%',
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    overflow: 'hidden',
    marginBottom: 6,
    marginTop: 2,
  },
  tag: {
    backgroundColor: '#F8F8FA',
    paddingLeft: 8,
    paddingRight: 8,
    height: 26,
    justifyContent: 'center',
  },
  tagItem: {
    marginRight: 8,
    overflow: 'hidden',
    marginTop: 10,
  },
  tagItemBadgeStyle: {
    borderRadius: 2,
    backgroundColor: bgColor,
    height: 20,
    paddingHorizontal: 8,
  },
  tagText: {
    fontSize: 10,
    color: subTitleColor,
  },
  jobExpectedSearch: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  /**
   * Modal 样式
   */
  modalStyle: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    // alignSelf: 'stretch',
  },
  subView: {
    height: '72%',
    width: '100%',
    backgroundColor: bgColor,
    justifyContent: 'space-between',
  },
  contentText: {
    marginTop: 20,
    marginBottom: 10,
    marginHorizontal: 16,
    fontSize: 12,
    textAlign: 'left',
    color: titleColor,
  },
  buttonView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonStyle: {
    flex: 1,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: baseBlueColor,
  },
  buttonText: {
    fontSize: 14,
    color: titleColor,
    textAlign: 'center',
  },
  requireFilter: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    alignItems: 'center',
    paddingLeft: 18,
  },
  requireFilterBadge: {
    marginVertical: 6,
    marginRight: 13,
    borderRadius: 5,
    overflow: 'hidden',
    paddingHorizontal: 5,
  },
  urgentFilter: {
    marginVertical: 6,
    height: 32,
    marginRight: 13,
    borderRadius: 5,
    overflow: 'hidden',
  },
  companyFilter: {
    marginVertical: 5,
    marginRight: 12,
    overflow: 'hidden',
  },
  badgeStyle: {
    height: 'auto',
    backgroundColor: '#fff',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 5,
  },
  badgeTextStyle: {
    fontSize: 14,
    color: subTitleColor,
    textAlign: 'center',
  },
  renderSearchInput: {
    backgroundColor: '#fff',
    height: 30,
    // width: 92 * RVW,
    borderRadius: 5,
    paddingLeft: 15,
    borderBottomWidth: 0,
  },
  renderSearchView: {
    position: 'absolute',
    backgroundColor: 'transparent',
    height: 30,
    width: 56 * RVW,
    borderRadius: 5,
    left: 26 * RVW,
    borderBottomWidth: 0,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  jobTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: titleColor,
  },
  locationsStyle: {
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'wrap',
  },
  intensionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flexGrow: 20,
    flexShrink: 200,
  },
  intensionBadgeContainerStyle: {
    paddingHorizontal: 0,
    height: 24,
    marginRight: 13,
    overflow: 'hidden',
  },
  intensionBadgeStyle: {
    height: 24,
    backgroundColor: '#fff',
  },
  spaceLine: {
    backgroundColor: desColor,
    width: 1,
    height: 10,
    marginHorizontal: 6,
  },
  locationsCon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 6,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: '#f5f5f5',
  },
  salaryLabel: {
    fontSize: 16,
    color: baseBlueColor,
    flexGrow: 1,
    marginLeft: 10,
    textAlign: 'right',
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
  square: {
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderWidth: 6,
    borderTopColor: 'blue',
    borderLeftColor: '#fff',
    borderBottomColor: '#fff',
    borderRightColor: '#fff',
    marginTop: 8,
    marginLeft: 4,
    transform: [{ rotateX: '90deg' }],
  },
  locationBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopColor: '#E0E0E0',
    borderTopWidth: 0.5,
    borderStyle: 'solid',
    marginTop: 10,
    height: 44,
  },

  locationLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 12,
    color: '#666',
    fontWeight: 400,
    width: '80%',
  },
  locationRight: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 12,
    color: '#666',
    fontWeight: 400,
  },
  contactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 9,
    marginTop: 10,
  },
  contactAvatarWrapper: {
    position: 'relative',
    marginRight: 10,
  },
  contactAvatar: {
    width: 24,
    height: 24,
    borderRadius: 16,
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CD964',
    borderWidth: 1,
    borderColor: '#fff',
  },
  contactInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  contactName: {
    fontSize: 12,
    color: titleColor,
    fontWeight: '500',
  },
  contactDepartment: {
    fontSize: 10,
    color: desColor,
    marginTop: 4,
  },
};
