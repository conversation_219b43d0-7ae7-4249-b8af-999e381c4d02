import { baseBlueColor, bgColor, titleColor, desColor } from './base';
import { deviceWidth } from '../common';

export const resumeStyle = {
  container: {
    flex: 1,
  },
  list: {
    backgroundColor: '#fff',
  },
  titleView: {
    height: deviceWidth / 3,
    alignItems: 'center',
    backgroundColor: baseBlueColor,
    paddingTop: 16,
    // justifyContent: 'space-around',
  },
  headerBgView1: {
    height: 70,
    backgroundColor: baseBlueColor,
  },
  headerBgView2: {
    height: 55,
    backgroundColor: 'white',
  },
  headerView: {
    position: 'absolute',
    marginTop: 7,
    marginLeft: 20,
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: 'white',
    width: deviceWidth - 40,
    height: 113,
    borderRadius: 5,
    shadowColor: 'black',
    shadowOffset: { height: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
  },
  infoView: {
    height: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  separateView: {
    height: 1,
    marginTop: 2,
    marginBottom: 1,
    backgroundColor: bgColor,
  },
  nameText: {
    marginTop: 20,
    fontSize: 16,
    color: 'white',
  },
  itemsView: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    height: 44,
  },
  item: {
    width: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemIcon: {
    // color: baseBlueColor,
  },
  itemText: {
    marginLeft: 5,
    fontSize: 14,
    color: titleColor,
  },

  separateText: {
    fontSize: 12,
    color: desColor,
  },
  listItemContent: {
    minHeight: 48,
    borderBottomColor: bgColor,
    borderBottomWidth: 0.5,
  },
  listItemLeftText: {
    fontSize: 14,
    color: titleColor,
  },
  listItemRightText: {
    width: 150,
    fontSize: 14,
    color: titleColor,
    marginRight: -10,
    textAlign: 'right',
  },
  info: {
    marginLeft: 20,
  },
  avatar: {
    marginTop: 20,
    marginRight: 20,
  },
  editText: {
    color: 'white',
    paddingTop: 10,
    paddingLeft: 5,
  },
  companyText: {
    paddingLeft: 5,
    paddingTop: 5,
    color: 'grey',
  },
  jobText: {
    paddingLeft: 10,
    color: 'grey',
  },
  inputText: {
    height: 44,
    marginRight: -5,
    fontSize: 14,
    color: titleColor,
  },
  tipsText: {
    marginTop: 5,
    marginLeft: 17,
    color: desColor,
  },
  nameInput: {
    textAlign: 'right',
    marginLeft: 10,
    width: 100,
    height: 40,
    color: titleColor,
  },
  phoneInput: {
    height: 45,
    marginTop: 10,
    paddingLeft: 15,
    paddingRight: 15,
    backgroundColor: 'white',
    fontSize: 16,
  },
  inputContainerStyle: {
    backgroundColor: '#fff',
    marginVertical: 10,
    borderBottomWidth: 0,
    borderRadius: 6,
    marginLeft: 0,
  },
  paginationContainer: {
    marginHorizontal: 5,
    marginTop: 8,
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  saveBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  saveText: {
    fontSize: 12,
    color: '#2089DC',
    fontWeight: 'bold',
  },
  draftText: {
    fontSize: 12,
    color: '#F5A623',
  },
};
