import { baseRedColor, titleColor, desColor } from './base';

export const resumeEditStyle = {
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    height: 100,
    // flex: 1,
  },
  timeText: {
    marginTop: 15,
    marginLeft: 15,
    color: desColor,
    fontSize: 15,
  },
  titleText: {
    marginTop: 10,
    marginLeft: 15,
    color: titleColor,
    fontSize: 18,
  },
  desText: {
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 15,
    color: desColor,
    fontSize: 15,
  },
  icon: {
    marginLeft: 15,
    color: baseRedColor,
    alignSelf: 'center',
  },
  editInputText: {
    padding: 10,
    height: 200,
  },
  majorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  spaceLine: {
    backgroundColor: desColor,
    width: 2,
    height: '40%',
    marginHorizontal: 8
  }
};
