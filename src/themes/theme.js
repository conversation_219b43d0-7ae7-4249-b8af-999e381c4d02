/**
 * 主题
 * 包括：背景颜色、文字颜色、按钮颜色
 */

// 主题：黑夜
const night = {
  statusBarColor: '#FFB731', // 主色
  primaryColor: '#2089DC', // 主色
  collocationColor: '#26305F', // 搭配色
  stressColor: '#3299FF', // 强调色
  minorColor: '#B3D0FF', // 辅助色
  secondaryColor: '#F3F3F9', // 辅助色1
  blueColor: '#5389F5', // 蓝色
  negativeColor: '#E4ECFC', // 浅蓝色
  assetsDetailLightColor: '#75A0F7', // 资产详情主色上的浅色
  redPacketColor: '#FE2B60',

  primaryBgColor: '#FFFFFF', // 主背景色
  minorBgColor: '#F5F8FB', // 第二背景色
  listBgColor: '#F7F7F7', // 列表背景色
  advertisementBgColor: '#F8F8FD', // 广告背景色
  inputBgColor: '#F7FAFD', // 输入框背景色

  primaryBorderColor: '#EFF1F3', // 主边框色

  unreadPoint: '#FF4646', // 未读红点
  translucent: 'rgba(0,0,0,0.5)', // 半透明

  mediumBgColor: '#f2f2f2FF',
  checkBoxColor: '#428FE8',
  errorColor: '#FF9492',

  lineColor: '#F56C6C',
  titleLineColor: '#0AC2E8',
  canteenColor: '#F78A40', // 食堂支付颜色
  merchantBgColor: '#00AF66', // 商户背景颜色
  merchantRefund: '#FF4646', // 商户退款颜色
  buyColor: '#0AB28B', // 买入颜色
  sellColor: '#FF4B5A', // 卖出颜色
  listStatusBgColor: '#FDF9E6', // 交易列表状态背景色
  tradeUsdColor: '#9BA7B2',
  tradeLastPriceColor: '#FD3E69',

  // list背景颜色
  primaryListBgColor: '#141E46',
  mediumListBgColor: '#192454',

  // 支付密码背景颜色
  payInputBorderColor: '#DDDDDD',
  payInputBorderColorActive: 'rgba(83,137,245,0.2)',

  // 虚拟币 waring
  cryptoWaringBgColor: '#FFEEEA',
  cryptoWaringTextColor: '#FB5353',
  cryptoDateTextColor: '#FCA6A6',
  cryptoPromptColor: '#FF5633',
  cryptoPromptDotColor: '#D6E9D7',

  // 设备相关颜色
  devicesPrimaryColor: '#171717',
  devicesPanelColor: '#1C1C1D',
  devicesSeparatorColor: '#333333',
  devicesLightingOpenColor: '#5389F5',
  devicesHeaderColor: '#212121',
  devicesMalfunctionColor: '#FF646E', // 故障颜色
  devicesOfflineColor: '#F5AB44', // 停止颜色
  enterpriseColor: '#A97753', // 企业账户详情颜色
  personalPriseColor: '#57B360', //  个人账户详情颜色

  // 分割线
  separatorColor: '#F2F2F2', // 分割线

  // 订单详情分割线
  order_separatorColor_crude: '#7D72E9',
  order_separatorColor_fine: '#D8D8D8',

  // header
  headerRightIconColor: '#333333',

  // list右侧箭头
  arrowColor: '#979797',

  loginNormalColor: '#354CA3',
  loginActiveColor: '#1B2C73',

  // 访客申请 待审核颜色
  visitorApplyColor: '#63A7FE',

  // 访客邀请 待审核颜色
  visitorInviteColor: '#17C8C7',

  // 考勤 缺卡颜色
  missCardColor: '#BC6EFA',

  // 考勤 迟到早退颜色
  beLateColor: '#F6726B',

  // 提醒 颜色
  remindColor: '#F56C6C',
  // 企业支付颜色 rgba(215, 173, 142, 1)
  companyPayColor: '#D7AD8E',
  // 商户支付颜色
  merchantPayColor: '#FF8364',

  // header下边框颜色
  headerBottomBorderColor: '#EDEDED',

  // 字号
  fontSizeXS: 10,
  fontSizeS: 12,
  fontSizeM: 14,
  fontSizeL: 16,
  fontSizeXL: 18,
  fontSizeXXL: 20,
  fontSizeXXXL: 22,
  fontSizeIVX: 24,
  fontSizeLg: 28,
  fontSizeXVII: 32,
  fontSizeXXXVIII: 38,
  memoSize: 15,

  // 字重 enum('normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900')
  fontWeightThin: '100',
  fontWeightExtraLight: '200',
  fontWeightUltraLight: '200',
  fontWeightLight: '300',
  fontWeightRegular: 'normal', // Regular (Normal、Book、Roman) 400
  fontWeightBook: '400',
  fontWeightRoman: '400',
  fontWeightNormal: 'normal',
  fontWeightMedium: 'bold',
  fontWeightSemiBold: '600',
  fontWeightDemiBold: '600',
  fontWeightBold: 'bold', // 700
  fontWeightExtraBold: '800',
  fontWeightBlack: '900',

  // 字体颜色
  titleFontColor: '#333333', // 标题
  primaryFontColor: '#666666', // 正文
  minorFontColor: '#999999', // 次视觉文字
  mediumFontColor: '#8E96A3', // 弱描述性文字
  placeholderFontColor: '#C7D0D6', // 弱描述性文字
  moreFontColor: '#7F7F7F', // more
  inputLabelFontColor: '#4F5866', // 输入框label
  btnFontColor: '#362D1A', // 按钮颜色

  clerkShopHomeBgColor: '#F6F9F3', // 店员商户背景颜色

  stressFontColor: '#F6A46B',
  simpleFontColor: '#FFFFFF',
  labelFontColor: '#8D8DAA',
  pgFontColor: '#999999',
  pfFontColor: '#97A3B4',
  pdFontColor: '#cccccc',
  remarksFontColor: '#545472',
  errorTextColor: '#F81077',
  moneyColor: '#FF6464',
  textBtnColor: '#484848',
  dateCheckColor: '#495DB1',
  iconColor: '#5389F5',
  orderDetailTitleColor: '#191919',
  remindFontColor: '#F56C6C',
  conpanyPayFontColor: '#C49574', // 企业支付文字颜色
  payHintFontColor: '#FF2626', // 支付提示文字颜色

  // padding
  containerPaddingHorizontal: 18, // 容器横线padding
  listItemPaddingHorizontal: 18,
  listItemPaddingVertical: 15,
  listItemSeparatorHeight: 10,

  // margin
  containerMarginHorizontal: 18, // 容器横线margin

  iconSize: 22, // 容器横线padding
};

// 主题：白天
const daytime = {
  statusBarColor: '#FFB731', // 主色
  primaryColor: '#EF3D48', // 主色
  collocationColor: '#26305F', // 搭配色
  stressColor: '', // 强调色
  minorColor: '#B3D0FF', // 辅助色
  secondaryColor: '#F3F3F9', // 辅助色1
  blueColor: '#5389F5', // 蓝色
  negativeColor: '#E4ECFC', // 浅蓝色
  assetsDetailLightColor: '#75A0F7', // 资产详情主色上的浅色
  redPacketColor: '#FE2B60',

  primaryBgColor: '#FFFFFF', // 主背景色
  minorBgColor: '#F5F8FB', // 第二背景色
  listBgColor: '#F7F7F7', // 列表背景色
  advertisementBgColor: '#F8F8FD', // 广告背景色
  inputBgColor: '#F7FAFD', // 输入框背景色

  primaryBorderColor: '#EFF1F3', // 主边框色

  unreadPoint: '#FF4646', // 未读红点
  translucent: 'rgba(0,0,0,0.5)', // 半透明

  mediumBgColor: '#f2f2f2FF',
  checkBoxColor: '#428FE8',
  errorColor: '#FF9492',

  lineColor: '#F56C6C',
  titleLineColor: '#0AC2E8',
  canteenColor: '#F78A40', // 食堂支付颜色
  merchantBgColor: '#00AF66', // 商户背景颜色
  merchantRefund: '#FF4646', // 商户退款颜色
  buyColor: '#0AB28B', // 买入颜色
  sellColor: '#FF4B5A', // 卖出颜色
  listStatusBgColor: '#FDF9E6', // 交易列表状态背景色
  tradeUsdColor: '#9BA7B2',
  tradeLastPriceColor: '#FD3E69',

  // list背景颜色
  primaryListBgColor: '#141E46',
  mediumListBgColor: '#192454',

  // 支付密码背景颜色
  payInputBorderColor: '#DDDDDD',
  payInputBorderColorActive: 'rgba(83,137,245,0.2)',

  // 虚拟币 waring
  cryptoWaringBgColor: '#FFEEEA',
  cryptoWaringTextColor: '#FB5353',
  cryptoDateTextColor: '#FCA6A6',
  cryptoPromptColor: '#FF5633',
  cryptoPromptDotColor: '#D6E9D7',

  // 设备相关颜色
  devicesPrimaryColor: '#171717',
  devicesPanelColor: '#1C1C1D',
  devicesSeparatorColor: '#333333',
  devicesLightingOpenColor: '#5389F5',
  devicesHeaderColor: '#212121',
  devicesMalfunctionColor: '#FF646E', // 故障颜色
  devicesOfflineColor: '#F5AB44', // 停止颜色
  enterpriseColor: '#A97753', // 企业账户详情颜色
  personalPriseColor: '#57B360', //  个人账户详情颜色

  // 分割线
  separatorColor: '#F2F2F2', // 分割线

  // 订单详情分割线
  order_separatorColor_crude: '#7D72E9',
  order_separatorColor_fine: '#D8D8D8',

  // header
  headerRightIconColor: '#333333',

  // list右侧箭头
  arrowColor: '#979797',

  loginNormalColor: '#354CA3',
  loginActiveColor: '#1B2C73',

  // 访客申请 待审核颜色
  visitorApplyColor: '#63A7FE',

  // 访客邀请 待审核颜色
  visitorInviteColor: '#17C8C7',

  // 考勤 缺卡颜色
  missCardColor: '#BC6EFA',

  // 考勤 迟到早退颜色
  beLateColor: '#F6726B',

  // 提醒 颜色
  remindColor: '#F56C6C',
  // 企业支付颜色 rgba(215, 173, 142, 1)
  companyPayColor: '#D7AD8E',
  // 商户支付颜色
  merchantPayColor: '#FF8364',

  // header下边框颜色
  headerBottomBorderColor: '#EDEDED',

  // 字号
  fontSizeXS: 10,
  fontSizeS: 12,
  fontSizeM: 14,
  fontSizeL: 16,
  fontSizeXL: 18,
  fontSizeXXL: 20,
  fontSizeXXXL: 22,
  fontSizeIVX: 24,
  fontSizeLg: 28,
  fontSizeXVII: 32,
  fontSizeXXXVIII: 38,
  memoSize: 15,

  // 字重 enum('normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900')
  fontWeightThin: '100',
  fontWeightExtraLight: '200',
  fontWeightUltraLight: '200',
  fontWeightLight: '300',
  fontWeightRegular: 'normal', // Regular (Normal、Book、Roman) 400
  fontWeightBook: '400',
  fontWeightRoman: '400',
  fontWeightNormal: 'normal',
  fontWeightMedium: 'bold',
  fontWeightSemiBold: '600',
  fontWeightDemiBold: '600',
  fontWeightBold: 'bold', // 700
  fontWeightExtraBold: '800',
  fontWeightBlack: '900',

  // 字体颜色
  titleFontColor: '#333333', // 标题
  primaryFontColor: '#666666', // 正文
  minorFontColor: '#999999', // 次视觉文字
  mediumFontColor: '#8E96A3', // 弱描述性文字
  placeholderFontColor: '#C7D0D6', // 弱描述性文字
  moreFontColor: '#7F7F7F', // more
  inputLabelFontColor: '#4F5866', // 输入框label
  btnFontColor: '#362D1A', // 按钮颜色

  clerkShopHomeBgColor: '#F6F9F3', // 店员商户背景颜色

  stressFontColor: '#F6A46B',
  simpleFontColor: '#FFFFFF',
  labelFontColor: '#8D8DAA',
  pgFontColor: '#999999',
  pfFontColor: '#97A3B4',
  pdFontColor: '#cccccc',
  remarksFontColor: '#545472',
  errorTextColor: '#F81077',
  moneyColor: '#FF6464',
  textBtnColor: '#484848',
  dateCheckColor: '#495DB1',
  iconColor: '#5389F5',
  orderDetailTitleColor: '#191919',
  remindFontColor: '#F56C6C',
  conpanyPayFontColor: '#C49574', // 企业支付文字颜色
  payHintFontColor: '#FF2626', // 支付提示文字颜色

  // padding
  containerPaddingHorizontal: 18, // 容器横线padding
  listItemPaddingHorizontal: 18,
  listItemPaddingVertical: 15,
  listItemSeparatorHeight: 10,

  // margin
  containerMarginHorizontal: 18, // 容器横线margin

  iconSize: 22, // 容器横线padding
};

export const theme = {
  night,
  daytime,
};
