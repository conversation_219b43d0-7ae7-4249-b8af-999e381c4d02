import { statusBarHeight, deviceWidth, deviceHeight, footerHeight } from '../../common';

export function getCallStyle(theme) {
  return {
    container: {
      backgroundColor: 'transparent',
      position: 'absolute',
      width: '100%',
      height: '100%',
      zIndex: 888,
    },
    zoomAudioContentStyle: {
      width: 100,
      height: 50,
      right: 0,
      top: 200,
      backgroundColor: theme.primaryColor,
      borderRadius: 25,
      borderWidth: 5,
      borderColor: '#fff',
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      borderRightWidth: 0,
      shadowColor: '#000000',
      shadowOffset: { h: 10 },
      shadowOpacity: 0.2,
    },
    zoomVideoContentStyle: {
      width: 100,
      height: 140,
      right: 0,
      top: 200,
      backgroundColor: '#fff',
      borderRadius: 15,
      borderWidth: 5,
      borderColor: '#fff',
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      shadowColor: '#000000',
      shadowOffset: { h: 10 },
      shadowOpacity: 0.2,
    },
    topContainer: {
      height: 50,
      width: '100%',
      position: 'absolute',
      zIndex: 1000,
      top: statusBarHeight,
      flexDirection: 'row',
      alignItems: 'center',
    },
    topActionContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
    },
    zoomContainer: {
      // marginLeft: 20,
      // width: 40,
      // height: 40,
      justifyContent: 'center',
    },
    titleText: {
      flex: 1,
      marginLeft: 10,
      marginRight: 10,
      fontSize: 13,
      textAlign: 'center',
      fontWeight: theme.fontWeightRegular,
      color: '#fff',
    },

    centerContainer: {
      width: '100%',
      height: '100%',
    },
    audioCenterContainer: {
      width: 150,
      height: 150,
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: deviceHeight / 4,
      alignSelf: 'center',
    },
    bigVideoContainer: {
      flex: 1,
    },
    videoUserBox: {
      width: '100%',
      height: 170,
      position: 'absolute',
      top: 0,
      left: 0,
      paddingLeft: 20,
      zIndex: 99,
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    videoUserInfo: {
      flexDirection: 'column',
      marginLeft: 10,
    },
    videoUserNameText: {
      fontSize: 28,
      fontWeight: '500',
      color: '#FFFFFF',
      lineHeight: 40,
    },
    videoUserStatusText: {
      fontSize: 14,
      fontWeight: '400',
      color: '#FFFFFF',
      lineHeight: 20,
      marginTop: 6,
    },
    smallVideoContainer: {
      flex: 1,
      position: 'absolute',
      zIndex: 2000,
      width: 100,
      height: 150,
      top: 100,
      right: 0,
    },
    controlContainer: {
      height: 160,
      width: deviceWidth,
      position: 'absolute',
      bottom: footerHeight + 30,
    },
    itemContainer: {
      alignItems: 'center',
      width: 80,
    },
    itemText: {
      marginTop: 10,
      fontSize: 13,
      fontWeight: theme.fontWeightRegular,
      color: '#fff',
    },
    nameText: {
      marginTop: 14,
      fontSize: 28,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    statusText: {
      marginTop: 16,
      fontSize: 16,
      fontWeight: theme.fontWeightRegular,
      color: '#ffffffB3',
      marginBottom: 12,
    },
    timeText: {
      fontSize: 18,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    zoomStateText: {
      fontSize: 12,
      marginLeft: 8,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    zoomTimeText: {
      fontSize: 14,
      marginLeft: 10,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    btnsContainer: {
      flex: 1,
      flexDirection: 'row',
      paddingHorizontal: 20,
      justifyContent: 'space-around',
    },
    tipsContainer: {
      flex: 1,
      // backgroundColor: '#010101B3',
      borderRadius: 5,
      justifyContent: 'center',
    },
    tipsText: {
      color: '#fff',
      textAlign: 'center',
    },

    controlMiniContainer: {
      width: deviceWidth,
      position: 'absolute',
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      shadowColor: 'rgba(20,20,20,0.6)',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 1,
      shadowRadius: 8,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      borderWidth: 1,
      borderColor: 'rgba(20,20,20,0.11)',
      overflow: 'hidden',
    },
    btnsMiniContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    itemMiniIcon: {
      width: 50,
      height: 50,
    },
    boxVideoL: {
      justifyContent: 'center',
      alignItems: 'center',
      height: deviceHeight - statusBarHeight,
      width: '100%',
    },
    boxVideoM: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      width: '100%',
    },
  };
}
