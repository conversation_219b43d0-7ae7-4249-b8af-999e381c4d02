import { statusBarHeight, deviceWidth, deviceHeight, footerHeight } from '../../common';

export function getMeetingStyle(theme) {
  return {
    page: {
      width: '100%',
      height: '100%',
    },
    container: {
      backgroundColor: 'transparent',
      position: 'absolute',
      width: '100%',
      height: '100%',
      zIndex: 888,
    },
    zoomAudioContentStyle: {
      width: 100,
      height: 50,
      right: 0,
      top: 200,
      backgroundColor: theme.primaryColor,
      borderRadius: 25,
      borderWidth: 5,
      borderColor: '#fff',
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      borderRightWidth: 0,
      shadowColor: '#000000',
      shadowOffset: { h: 10 },
      shadowOpacity: 0.2,
    },
    zoomVideoContentStyle: {
      width: 100,
      height: 140,
      right: 0,
      top: 200,
      backgroundColor: '#fff',
      borderRadius: 15,
      borderWidth: 5,
      borderColor: '#fff',
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      shadowColor: '#000000',
      shadowOffset: { h: 10 },
      shadowOpacity: 0.2,
    },
    topContainer: {
      height: 50,
      width: '100%',
      position: 'absolute',
      zIndex: 1000,
      top: statusBarHeight + 20,
      boxSizing: 'border-box',
    },
    topActionContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
    },
    zoomContainer: {
      width: 44,
      height: 44,
    },
    titleText: {
      flex: 1,
      marginLeft: 10,
      marginRight: 50,
      fontSize: 13,
      textAlign: 'center',
      fontWeight: theme.fontWeightRegular,
      color: '#fff',
    },
    centerContainer: {
      width: deviceWidth,
      height: deviceHeight,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#222222',
    },
    audioCenterContainer: {
      width: 150,
      height: 150,
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: deviceHeight / 4,
      alignSelf: 'center',
    },
    bigVideoContainer: {
      flex: 1,
    },
    videoUserBox: {
      width: '100%',
      height: 170,
      position: 'absolute',
      top: 0,
      left: 0,
      paddingLeft: 20,
      zIndex: 99,
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    videoUserInfo: {
      flexDirection: 'column',
      marginLeft: 10,
    },
    videoUserNameText: {
      fontSize: 28,
      fontWeight: '500',
      color: '#FFFFFF',
      lineHeight: 40,
    },
    videoUserStatusText: {
      fontSize: 14,
      fontWeight: '400',
      color: '#FFFFFF',
      lineHeight: 20,
      marginTop: 6,
    },
    smallVideoContainer: {
      flex: 1,
      position: 'absolute',
      zIndex: 2000,
      width: 100,
      height: 150,
      top: 100,
      right: 0,
    },
    controlBox: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    controlMiniContainer: {
      width: deviceWidth,
      position: 'absolute',
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      shadowColor: 'rgba(20,20,20,0.6)',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 1,
      shadowRadius: 8,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      borderWidth: 1,
      borderColor: 'rgba(20,20,20,0.11)',
      overflow: 'hidden',
    },
    btnsMiniContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    controlContainer: {
      height: 260,
      width: deviceWidth,
      position: 'absolute',
      bottom: footerHeight + 30,
    },
    btnsContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    itemContainer: {
      alignItems: 'center',
      width: 80,
    },
    itemMiniIcon: {
      width: 50,
      height: 50,
    },
    itemIcon: {
      width: 60,
      height: 60,
    },
    itemCloseIcon: {
      width: 72,
      height: 72,
    },
    itemText: {
      marginTop: 10,
      fontSize: 13,
      fontWeight: theme.fontWeightRegular,
      color: '#fff',
    },
    nameText: {
      marginTop: 14,
      fontSize: 28,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    statusText: {
      marginTop: 16,
      fontSize: 16,
      fontWeight: theme.fontWeightRegular,
      color: '#ffffffB3',
      marginBottom: 12,
    },
    timeText: {
      fontSize: 18,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    zoomStateText: {
      fontSize: 12,
      marginLeft: 8,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    zoomTimeText: {
      fontSize: 14,
      marginLeft: 10,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    tipsBox: {
      width: deviceWidth,
      position: 'absolute',
      bottom: footerHeight + 180,
    },
    tipsContainer: {
      flex: 1,
      // backgroundColor: '#010101B3',
      borderRadius: 5,
      justifyContent: 'center',
    },
    tipsText: {
      color: '#fff',
      textAlign: 'center',
    },
    headerText: {
      fontSize: 16,
      color: '#ffffff',
    },
    meetingUserContainer: {
      // marginTop: 110,
      height: deviceHeight,
      width: deviceWidth,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    meetingMoreUserContainer: {
      width: deviceWidth,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      flexWrap: 'wrap',
    },
    meetingUserExtra: {
      flexDirection: 'column',
    },
    mettingSubContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    largeScale: {
      width: deviceWidth,
      height: deviceHeight / 2,
      position: 'relative',
    },
    large3Scale: {
      width: deviceWidth / 2,
      height: deviceHeight / 2,
      position: 'relative',
    },
    large4Scale: {
      width: deviceWidth / 2,
      height: deviceHeight / 3,
      position: 'relative',
    },
    large5Scale: {
      width: deviceWidth / 3,
      height: deviceHeight / 4,
      position: 'relative',
    },
    large6Scale: {
      width: deviceWidth / 2,
      height: deviceHeight / 4,
      position: 'relative',
    },
    large7Scale: {
      width: deviceWidth / 3,
      height: deviceHeight / 3,
      position: 'relative',
    },
    large8Scale: {
      width: deviceWidth / 3,
      height: deviceHeight / 4,
      position: 'relative',
    },
    defaultScale: {
      width: deviceWidth / 2,
      height: deviceWidth / 2,
      position: 'relative',
    },
    defaultLittleScale: {
      width: deviceWidth / 3,
      height: deviceWidth / 3,
      position: 'relative',
    },
    defaultMaxScale: {
      height: deviceWidth,
      width: deviceWidth,
      position: 'relative',
    },
    userName: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      backgroundColor: '#00000050',
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 2,
      minWidth: 50,
    },
    userAvatar: {
      width: '100%',
      height: '100%',
      borderRadius: 0,
    },
    userNameText: {
      fontSize: 10,
      color: '#FCFBFB',
      textAlign: 'center',
    },
    inviteState: {
      width: '100%',
      height: '100%',
      backgroundColor: '#1D1D1D99',
      position: 'absolute',
      zIndex: 9,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    inviteStateText: {
      fontSize: 14,
      color: '#FCFBFB',
    },
    busyStateText: {
      fontSize: 14,
      color: '#FCFBFB',
    },
    waitContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    videoStyle: {
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
    voiceBox: {
      position: 'absolute',
      left: 10,
      bottom: 10,
    },
    littleVoiceIcon: {
      width: 20,
      height: 20,
    },
    bigVoiceIcon: {
      width: 28,
      height: 28,
    },
  };
}
