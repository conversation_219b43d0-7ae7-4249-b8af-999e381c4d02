/**
 * 职位详情样式
 */
import { baseBlueColor, titleColor, subTitleColor, desColor, bgColor } from './base';
import { footerHeight } from '../common';

export const jobDetailStyle = {
  jobContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: '#fff',
  },
  btnContainer: {
    backgroundColor: '#fff',
    justifyContent: 'center',
    flexDirection: 'row',
    height: 56 + footerHeight,
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.2,
    // elevation: 3,
  },
  tagItem: {
    marginTop: 14,
    marginRight: 10,
    overflow: 'hidden',
  },
  tagBadgeStyle: {
    height: 20,
    borderRadius: 11,
    paddingHorizontal: 8,
    backgroundColor: bgColor,
  },
  tagText: {
    fontSize: 10,
    color: subTitleColor,
    paddingVertical: 2,
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  jobCardCont: {
    flex: 1,
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderRadius: 6,
    backgroundColor: '#fff',
    marginHorizontal: 10,
    marginVertical: 10,
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.2,
    elevation: 3,
  },
  cardTitleCont: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 18,
    color: titleColor,
    fontWeight: 'bold',
    flexGrow: 20,
    flexShrink: 200,
  },
  cardWages: {
    fontSize: 16,
    color: baseBlueColor,
    textAlign: 'right',
    marginLeft: 10,
    flexGrow: 1,
  },
  spaceLine: {
    backgroundColor: bgColor,
    height: 1,
    marginVertical: 12,
  },
  companyCardCont: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderRadius: 6,
    backgroundColor: bgColor,
    marginHorizontal: 10 * 2,
    marginVertical: 10,
  },
  cardTag: {
    color: desColor,
    fontSize: 10,
  },
  cardVLine: {
    paddingHorizontal: 6,
    color: desColor,
  },
  cardCompanyTitle: {
    color: subTitleColor,
    fontSize: 14,
    fontWeight: 'bold',
    width: '100%',
    flexWrap: 'wrap',
    marginBottom: 6,
  },
  chatBtn: {
    backgroundColor: '#EEF8FD',
    borderRadius: 5,
    borderWidth: 1,
    width: '94%',
    marginLeft: '4%',
    minHeight: 40,
    marginTop: 8,
    borderColor: baseBlueColor,
    elevation: 0,
  },
  chatDisabledBtn: {
    backgroundColor: '#eee',
    borderRadius: 5,
    borderWidth: 1,
    width: '94%',
    marginLeft: '4%',
    minHeight: 40,
    marginTop: 8,
    borderColor: '#eee',
    elevation: 0,
  },
  sendBtn: {
    backgroundColor: baseBlueColor,
    width: '94%',
    marginLeft: '2%',
    minHeight: 40,
    borderWidth: 1,
    marginTop: 8,
    borderRadius: 5,
    borderColor: baseBlueColor,
    elevation: 0,
  },
  userContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  sendUser: {
    fontWeight: 'bold',
    color: titleColor,
    fontSize: 14,
    marginBottom: 14,
  },
  userName: {
    color: subTitleColor,
    fontSize: 16,
    fontWeight: 'bold',
  },
  jobType: {
    // marginLeft: 10,
    color: subTitleColor,
    fontSize: 12,
    marginTop: 5,
  },
  jobTitleDesc: {
    fontWeight: 'bold',
    color: titleColor,
    fontSize: 14,
    marginBottom: 14,
  },
  jobDesc: {
    color: subTitleColor,
    fontSize: 14,
    lineHeight: 18,
  },
  welfareContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexShrink: 100,
  },
  welfareContainerItem: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexShrink: 100,
  },
  welfareContainerItemText: {
    color: desColor,
    fontSize: 12,
    flexShrink: 100,
  },
  verticalLine: {
    backgroundColor: desColor,
    width: 1,
    height: 10,
    marginHorizontal: 6,
    flexShrink: 100,
  },
  scaleIndustyContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    width: '90%',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  companyContentInfo: {
    marginLeft: 10,
    flexDirection: 'column',
    justifyContent: 'center',
    width: '84%',
    flexShrink: 100,
  },
  jobDescCont: {
    paddingHorizontal: 20,
    paddingVertical: 6,
    position: 'relative',
  },
  watchAllColor: {
    position: 'absolute',
    bottom: -12,
    paddingVertical: 10,
    backgroundColor: 'rgba(255,255,255,0.8)',
    width: '100%',
    zIndex: 99999,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  commentBackGround: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  commentBackContent: {
    height: '50%',
    backgroundColor: '#fff',
    position: 'relative',
  },
  commentBackTop: {
    height: '50%',
    width: '100%',
  },
  commentBackTopEmpty: {
    width: '100%',
    height: '100%',
  },
  commentTitle: {
    marginTop: 10,
    marginBottom: 10,
    lineHeight: 20,
    fontSize: 14,
    color: subTitleColor,
    textAlign: 'center',
  },
  locationInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    width: '100%',
    flexWrap: 'wrap',
    marginTop: -4,
    marginLeft: 4,
    flexShrink: 100,
  },
  welfareCon: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexShrink: 100,
    marginTop: 4,
  },
  locationConf: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 6,
    alignItems: 'center',
  },
  expandTitle: {
    fontSize: 12,
    color: baseBlueColor,
    textAlign: 'center',
  },
  lineView: {
    flexShrink: 10,
    flexGrow: 10,
    backgroundColor: '#eee',
    height: 1,
  },
  moreText: {
    flexShrink: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  stop: {
    paddingVertical: 16,
    textAlign: 'center',
    fontSize: 14,
    color: 'gray',
  },
  companyLogo: {
    width: 54,
    height: 54,
    borderRadius: 27,
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  phoneText: {
    fontSize: 12,
    color: baseBlueColor,
    textDecorationLine: 'underline',
    flexShrink: 100,
    marginLeft: 10,
  },
  competitivePower: {
    paddingBottom: 24,
    marginBottom: 5,
    borderColor: '#f2f2f2',
    borderBottomWidth: 1,
  },
  competitivePowerBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
  },
  competitivePowerBar: {
    height: 12,
    width: '25%',
  },
  competitivePowerBarLine: {
    height: 12,
    width: 1,
    backgroundColor: '#fff',
  },
  competitivePowerDesc: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  competitivePowerDescText: {
    fontSize: 14,
    color: '#c6c6c6',
    textAlign: 'center',
    width: '25%',
  },
};
