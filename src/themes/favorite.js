/**
 * 收藏列表
 */
import { bgColor } from './base';
import { RVW } from '../common';

export const favoriteStyle = {
  viewContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  scorllContainer: {
    marginTop: -6,
  },
  lineStyle: {
    width: (RVW * 100) / 6,
    height: 2,
    backgroundColor: '#fff',
    marginLeft: (RVW * 100) / 6,
  },
  textStyle: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 5,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
};
