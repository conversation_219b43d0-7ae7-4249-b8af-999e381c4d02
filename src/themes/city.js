/**
 * 选择城市/地区
 */
import { baseBlueColor, bgColor, titleColor, desColor } from './base';

export const cityStyle = {
  localTags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    marginBottom: 0,
    width: '50%',
  },
  localTag: {
    backgroundColor: baseBlueColor,
    paddingLeft: 12,
    paddingRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    flexShrink: 100,
  },
  tag: {
    backgroundColor: '#eee',
    paddingLeft: 12,
    paddingRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  localTagItem: {
    marginRight: 16,
    overflow: 'hidden',
  },
  localTagBadgeStyle: {
    borderRadius: 5,
    height: 24,
    paddingHorizontal: 8,
    backgroundColor: baseBlueColor,
  },
  tagItem: {
    marginRight: 16,
    marginBottom: 12,
  },
  tagBadgeStyle: {
    borderRadius: 5,
    height: 24,
    paddingHorizontal: 8,
    backgroundColor: bgColor,
  },
  localTagText: {
    fontSize: 14,
    color: '#fff',
    paddingHorizontal: 4,
  },
  tagText: {
    fontSize: 14,
    color: titleColor,
    paddingHorizontal: 4,
  },
  localTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    marginTop: 16,
  },
  hotTitle: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingLeft: 10,
    marginTop: 26,
  },
  placeholderTitle: {
    fontSize: 15,
    color: desColor,
    marginRight: 50,
  },
};
