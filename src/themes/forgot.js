/**
 * 忘记密码界面样式
 */
import { RFT, RPX, statusBarHeight } from '../common';
import { baseBlueColor, basePaddingHorizontal, titleColor, subTitleColor } from './base';

export const forgotStyle = {
  page: {
    flex: 1,
  },
  container: {
    paddingHorizontal: basePaddingHorizontal,
    justifyContent: 'space-between',
  },
  headerSection: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 45,
    maxHeight: 45,
    marginTop: statusBarHeight,
  },
  headerBack: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: 45,
    maxHeight: 45,
    paddingRight: 50 * RPX,
    paddingTop: 12,
  },
  iconBack: {
    width: 20,
    height: 20,
  },
  headerTitle: {
    fontSize: 20,
    color: '#fff',
  },
  titleSection: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 64,
    marginTop: 10,
  },
  title: {
    fontSize: 40,
    color: '#fff',
  },
  tabSection: {
    marginTop: 25,
    backgroundColor: '#fff',
    height: 200,
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  tabTitle: {
    // marginTop: 10,
    fontSize: 16,
    color: subTitleColor,
    lineHeight: 50,
  },
  tabActive: {
    borderBottomWidth: 2,
    borderColor: baseBlueColor,
  },
  tabActiveTitle: {
    color: titleColor,
  },
  tabBody: {
    // flex: 1,
  },
  tabContent: {
    backgroundColor: 'white',
  },
  formGroup: {
    // flex: 1,
    flexDirection: 'row',
  },
  formGroupLine: {
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  formControl: {
    paddingVertical: 0,
    paddingBottom: 0,
    paddingTop: 0,
    width: '100%',
    height: 50,
    borderWidth: 0,
    borderColor: '#fff',
  },
  formPhoneSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 50,
  },
  formPhoneAreaWrap: {
    width: 100,
    justifyContent: 'center',
  },
  formPhoneAreaTo: {
    width: 100,
    borderRightWidth: 1,
    borderColor: '#eee',
  },
  formPhoneArea: {
    paddingTop: 14,
    fontSize: 14,
    color: subTitleColor,
    width: 100,
    height: 50,
    paddingLeft: 15,
  },
  formPhoneInputWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  formSendCodeSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 50,
  },
  formSendCodeInputWrap: {
    flex: 1,
  },
  formSendCodeBtnWrap: {
    // flex: 1,
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  formControlCode: {
    paddingVertical: 0,
    paddingBottom: 0,
    paddingTop: 0,
    width: '100%',
    height: 50,
    borderWidth: 0,
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  btnSendCode: {
    paddingHorizontal: 20 * RPX,
    height: 38,
    marginRight: 10,
    backgroundColor: baseBlueColor,
  },
  btnSection: {
    justifyContent: 'center',
    alignItems: 'center',
    maxHeight: 40,
    height: 40,
    padding: 0,
    marginTop: 20,
  },
  btnSubmit: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: 40,
    maxHeight: 40,
    borderRadius: 0,
    borderTopWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderLeftWidth: 1,
    borderStyle: 'solid',
    borderColor: '#fff',
    backgroundColor: 'rgba(0,0,0,0.0)',
  },
  btnSubmitText: {
    fontSize: 5 * RFT,
    color: '#fff',
    textAlign: 'center',
  },
};
