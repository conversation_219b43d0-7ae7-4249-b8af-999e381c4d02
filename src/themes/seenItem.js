
/**
 * 谁看过我组件样式
 */
import { baseBlueColor, bgColor, titleColor, desColor, subTitleColor } from './base';
import { RVW } from '../common';

export const seenItemStyle = {
  container: {
    paddingVertical: 12,
    paddingHorizontal: 14,
    marginBottom: 10,
    // height: 179,
    // maxHeight: 179,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  position: {
    fontSize: 16,
    color: titleColor,
    fontWeight: 'bold',
    width: '100%',
  },
  salary: {
    fontSize: 16,
    color: baseBlueColor,
  },
  body: {

  },
  company: {
    fontSize: 14,
    color: subTitleColor,
    marginTop: 10,
    marginBottom: 6,
  },
  address: {
    fontSize: 14,
    color: desColor,
    paddingTop: 5,
    paddingBottom: 5,
  },
  hrInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 7,
    paddingBottom: 7,
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  hrName: {
    fontSize: 14,
    color: desColor,
    marginLeft: 8,
  },
  footer: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderColor: '#eee',
  },
  seenTime: {
    fontSize: 14,
    color: desColor,
  },
  listTags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: '100%',
    marginTop: 8,
  },
  tags: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    paddingLeft: 10 * 2,
    backgroundColor: '#fff',
    paddingBottom: 10,
    // marginLeft: 10 * 2,
  },
  tag: {
    backgroundColor: '#eee',
    paddingHorizontal: 2,
    paddingVertical: 2,
    paddingLeft: 8,
    paddingRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tagItem: {
    marginTop: 16,
    marginRight: 16,
    borderRadius: 5,
    overflow: 'hidden',
    backgroundColor: bgColor,
  },
  listTagItem: {
    marginVertical: 5,
    marginRight: 12,
    overflow: 'hidden',
  },
  listTagBadgeStyle: {
    height: 16,
    borderRadius: 6,
    backgroundColor: bgColor,
    paddingHorizontal: 8,
  },
  tagText: {
    fontSize: 10,
    color: subTitleColor,
    textAlign: 'center',
  },
  spaceLine: {
    color: desColor,
    fontSize: 12,
  },
  locationsStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '84%',
    marginTop: 6,
  },
  seenItemCon: {
    flexDirection: 'row',
    paddingTop: 6,
    paddingBottom: 10,
  },
  seenItemSubCon: {
    flexDirection: 'column',
    width: RVW * 80,
    paddingLeft: 10,
  },
  companyLogo: {
    width: 42,
    height: 42,
    borderRadius: 21,
  },
  v2: {
    width: 14,
    height: 14,
    position: 'absolute',
    right: 0,
    bottom: 2,
  },
};
