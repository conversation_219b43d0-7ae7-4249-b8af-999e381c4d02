import { RVW, RFT, RPX } from '../common';

export const emojiStyle = {
  album: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    width: 10 * RVW,
    height: 10 * RVW,
    borderWidth: 2 * RPX,
    borderColor: '#ccc',
    borderLeftWidth: 0,
  },
  emojiWrapper: {
    height: 50 * RVW,
    flexDirection: 'column',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  emoji: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    width: 10 * RVW,
    height: 10 * RVW,
    borderWidth: 1 * RPX,
    borderColor: '#ccc',
    borderLeftWidth: 0,
  },
  pinup: {
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    width: 12.5 * RVW,
    height: 12.5 * RVW,
    borderWidth: 1 * RPX,
    borderColor: '#ccc',
    borderLeftWidth: 0,
  },

};