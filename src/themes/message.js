/**
 * 消息界面样式
 */
import { RVW, RPX, RFT, DWPX } from '../common';
import {
  baseBlueColor,
  bgColor,
  basePaddingHorizontal,
  basePaddingVertical,
  titleColor,
  desColor,
} from './base';

// 页面内边距像素大小
const PAGE_P_H_PX = basePaddingHorizontal * 2;

export const messageStyle = {
  page: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 15,
    backgroundColor: bgColor,
  },
  list: {
    flex: 1,
  },
  item: {
    marginBottom: basePaddingVertical,
    borderRadius: 5,
    backgroundColor: '#fff',
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
  },
  coverWrap: {
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    overflow: 'hidden',
  },
  cover: {
    width: '100%',
    height: 160,
    maxHeight: 160,
  },
  contentWrap: {
    width: '100%',
    paddingHorizontal: 24 * RPX,
    paddingVertical: 24 * RPX,
  },
  content: {
    fontSize: 18,
    color: titleColor,
  },
  footer: {
    flexDirection: 'row-reverse',
    paddingHorizontal: 24 * RPX,
    paddingBottom: 24 * RPX,
  },
  createdAt: {
    fontSize: 16,
    color: desColor,
  },
  loadingStyle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  detailsContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: '#fff',
  },
  avatar: {
    height: 42,
    width: 42,
    borderRadius: 21,
  },
  twitterImage: {
    height: 60,
    width: 60,
  },
  commentTime: {
    fontSize: 12,
    color: desColor,
  },
  rowBack: {
    alignItems: 'center',
    backgroundColor: '#DDD',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 15,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtnLeft: {
    backgroundColor: 'blue',
    right: 75,
  },
  backRightBtnRight: {
    backgroundColor: 'red',
    right: 0,
  },
  backTextWhite: {
    color: '#FFF',
  },
};
