/**
 * 我的页面样式
 */
import { titleColor, bgColor, baseBlueColor } from './base';
import { headerHeight, statusBarHeight } from '../common';

export const generalStyle = {
  generalContainer: {
    flex: 1,
    paddingVertical: 0,
    backgroundColor: bgColor,
  },
  container: {
    flex: 1,
  },
  languageSection: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginTop: statusBarHeight,
    height: headerHeight,
  },
  languageTooltipWrap: {
    flexDirection: 'row',
  },
  languageWrap: {
    flexDirection: 'row-reverse',
  },
  language: {
    paddingRight: 10,
    height: headerHeight,
    justifyContent: 'center',
  },
  languageText: {
    fontSize: 14,
    color: '#fff',
    lineHeight: 18,
  },
  list: {
    marginTop: 0,
    backgroundColor: '#fff',
  },
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  titleViewColumn: {
    height: 168,
    flexDirection: 'column',
    justifyContent: 'space-between',
    backgroundColor: '#2089DC',
  },
  itemView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    height: 60,
    marginBottom: 10,
  },
  info: {
    marginLeft: 16,
  },
  avatar: {
    marginRight: 12,
  },
  itemText: {
    marginTop: 10,
    textAlign: 'center',
    color: titleColor,
  },
  nameText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  editText: {
    color: '#fff',
    marginLeft: 4,
    fontSize: 12,
  },
  listItemCon: {
    borderBottomColor: '#eee',
    borderBottomWidth: 0.5,
    paddingRight: 6,
    zIndex: 9998,
  },
  listItemTitle: {
    color: titleColor,
    fontSize: 14,
  },
  textInputCon: {
    marginHorizontal: 16,
    marginVertical: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 0,
  },
  // unreadMsgBadgeText: {
  //   color: '#f00',
  // },
  unreadMsgBadgeContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 9999,
  },
  unreadMsgBadgeBadgeStyle: {
    // width: 10,
    // height: 10,
    // borderRadius: 5,
    padding: 0,
    backgroundColor: '#f00',
    zIndex: 99999,
  },
  switchEnterprise: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    marginTop: 10,
  },
  switchEnterpriseText: {
    color: baseBlueColor,
    fontSize: 14,
  },
};
