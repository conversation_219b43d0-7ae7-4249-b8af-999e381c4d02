/**
 * 注册界面样式
 */
import { RPX, statusBarHeight } from '../common';
import { baseBlueColor, bgColor, basePaddingHorizontal, titleColor, subTitleColor } from './base';

export const registerStyle = {
  page: {
    flex: 1,
  },
  container: {
    paddingHorizontal: basePaddingHorizontal,
    justifyContent: 'space-between',
  },
  headerSection: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 45,
    maxHeight: 45,
    marginTop: statusBarHeight,
  },
  headerBack: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: 45,
    maxHeight: 45,
    paddingRight: 50 * RPX,
    paddingTop: 12,
  },
  iconBack: {
    width: 20,
    height: 20,
  },
  headerTitle: {
    fontSize: 20,
    color: '#fff',
  },
  titleSection: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 64,
    marginTop: 10,
  },
  title: {
    fontSize: 40,
    color: '#fff',
  },
  formSection: {
    marginTop: 25,
    height: 150,
    backgroundColor: '#fff',
  },
  emailFormSection: {
    marginTop: 25,
    height: 50,
    backgroundColor: '#fff',
  },
  emailPasswordFormSection: {
    marginTop: 25,
    height: 100,
    backgroundColor: '#fff',
  },

  tabBody: {
    // flex: 1,
  },
  formContent: {
    backgroundColor: 'white',
  },
  formGroup: {
    // flex: 1,
    flexDirection: 'row',
  },
  formGroupLine: {
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  formControl: {
    paddingVertical: 0,
    paddingBottom: 0,
    paddingTop: 0,
    width: '100%',
    height: 50,
    borderWidth: 0,
    borderColor: '#fff',
  },
  formPhoneSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 50,
  },
  formPhoneAreaWrap: {
    width: 100,
    justifyContent: 'center',
  },
  formPhoneAreaTo: {
    width: 100,
    borderRightWidth: 1,
    borderColor: '#eee',
  },

  formPhoneArea: {
    paddingTop: 14,
    fontSize: 14,
    color: subTitleColor,
    width: 100,
    height: 50,
    paddingLeft: 15,
  },
  formPhoneInputWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  formSendCodeSection: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: 50,
  },
  formSendCodeInputWrap: {
    flex: 1,
  },
  formSendCodeBtnWrap: {
    // flex: 1,
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  formControlCode: {
    paddingVertical: 0,
    paddingBottom: 0,
    paddingTop: 0,
    width: '100%',
    height: 50,
    borderWidth: 0,
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  btnSendCode: {
    paddingHorizontal: 20 * RPX,
    height: 38,
    marginRight: 10,
    backgroundColor: baseBlueColor,
  },
  btnSection: {
    marginTop: 25,
    marginBottom: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnRegister: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: 40,
    maxHeight: 40,
    borderRadius: 0,
    borderTopWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderLeftWidth: 1,
    borderStyle: 'solid',
    borderColor: '#fff',
    backgroundColor: 'rgba(0,0,0,0.0)',
  },
  btnRegisterText: {
    fontSize: 20,
    color: '#fff',
    textAlign: 'center',
  },
  linkSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // backgroundColor: 'rgba(0,0,0,0.5)'
  },
  agreementCheckbox: {
    padding: 0,
    marginLeft: 0,
    borderWidth: 0,
    backgroundColor: 'rgba(0,0,0,0)',
    position: 'relative',
    left: 0,
    top: 0,
    width: 20,
  },
  agreementLink: {
    color: '#fff',
    fontSize: 12,
  },
  emailRegisterLink: {
    fontSize: 12,
    color: '#fff',
  },
  resultContainer: {
    flex: 1,
    paddingHorizontal: basePaddingHorizontal,
    backgroundColor: bgColor,
  },
  resultSection: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    paddingTop: 100,
  },
  successResultTitle: {
    fontSize: 24,
    color: titleColor,
    paddingVertical: 50 * RPX,
  },
  iconResultSuccess: {
    width: 120,
    height: 120,
  },
  remainSeconds: {
    fontSize: 18,
    color: titleColor,
    paddingVertical: 50 * RPX,
  },
  btnUseContainer: {
    width: '100%',
  },
  btnUse: {
    paddingHorizontal: 20 * RPX,
    height: 40,
    maxHeight: 40,
    backgroundColor: baseBlueColor,
  },
};
