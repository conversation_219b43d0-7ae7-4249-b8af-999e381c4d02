import { baseBlueColor, bgColor, titleColor, desColor, subTitleColor } from './base';
import { deviceWidth } from '../common';

export const resumePreStyle = {
  cell: {
    backgroundColor: 'white',
    marginBottom: 5,
    // paddingBottom: 15,
  },
  cellHeader: {
    backgroundColor: 'white',
    paddingLeft: 15,
    paddingRight: 15,
  },
  cellContent: {
    backgroundColor: 'white',
    paddingLeft: 23,
    paddingRight: 18,
    paddingBottom: 15,
  },

  cellHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 49,
  },
  cellHeaderTag: {
    width: 3,
    height: 14,
    backgroundColor: baseBlueColor,
  },
  cellHeaderText: {
    fontWeight: 'bold',
    fontSize: 16,
    color: titleColor,
    marginLeft: 5,
  },
  cellHeaderSep: {
    height: 1,
    backgroundColor: bgColor,
  },
  flexDirectionRowNormal: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  icon: {
    height: 14,
    width: 14,
  },
  infoCellTop: {
    marginTop: 15,
    marginBottom: 10,
    height: 70,
    flexDirection: 'row',
  },
  infoCellTopRight: {
    marginLeft: 10,
    height: 50,
    justifyContent: 'space-around',
  },
  infoCellTopRightInfo: {
    marginLeft: 10,
    width: deviceWidth / 2,
    height: 20,
    justifyContent: 'space-between',
  },
  infoNameText: {
    fontWeight: 'bold',
    color: 'black',
    marginLeft: 10,
  },
  careerTitleView: {
    marginTop: 5,
  },
  likeJobMoneyText: {
    fontSize: 14,
    color: baseBlueColor,
  },
  likeJobTypeText: {
    fontSize: 12,
    color: desColor,
  },
  likeJobDirectionRowBetween: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    marginTop: 15,
    marginBottom: 6,
  },
  likeJobAreaView: {
    marginTop: 6,
  },
  likeJobAreaText: {
    marginLeft: 0,
    // marginBottom: 15,
  },
  titleText: {
    fontSize: 16,
    color: titleColor,
  },
  subTitleText: {
    fontSize: 14,
    marginLeft: 5,
    color: subTitleColor,
  },
  desText: {
    fontSize: 13,
    marginLeft: 5,
    color: desColor,
  },
  phoneText: {
    marginLeft: 0,
    color: baseBlueColor,
    textDecorationLine: 'underline',
    marginTop: 12,
  },
  emailText: {
    marginLeft: 0,
    marginTop: 12,
  },
  timelineTimeText: {
    fontSize: 12,
    marginLeft: 0,
    marginTop: 10,
    color: desColor,
  },
  timelineTitleText: {
    fontSize: 14,
    marginTop: 13,
    marginLeft: 0,
  },
  timelineSubText: {
    marginTop: 13,
    fontSize: 12,
    marginLeft: 0,
    color: desColor,
  },
  timelineDesText: {
    marginTop: 13,
    fontSize: 14,
    marginLeft: 0,
    color: desColor,
  },
  timelineSeparate: {
    marginBottom: 35,
  },
  separateLine: {
    marginRight: 15,
    marginLeft: 15,
    height: 15,
    width: 1,
    backgroundColor: titleColor,
  },

  readeMoreText: {
    fontSize: 12,
    color: baseBlueColor,
    textAlign: 'right',
    marginTop: 5,
  },
  appraiseText: {
    marginTop: 8,
  },
  footer: {
    height: 55,
    backgroundColor: 'white',
    alignItems: 'center',
    shadowColor: 'black',
    shadowOffset: { h: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
  },
  footerBtn: {
    backgroundColor: baseBlueColor,
    borderRadius: 5,
    height: 40,
  },
  shareIcon: {
    marginLeft: 15,
  },
  footerBtnContain: {
    flex: 1,
    marginLeft: 15,
    marginRight: 15,
  },
};
