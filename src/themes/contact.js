

import { RVW, RFT, RPX } from '../common';

export const contactStyle = {
  item: {
    height: 60,
    textAlignVertical: 'center',
    backgroundColor: '#ffffff',
    color: '#5C5C5C',
    fontSize: 15,
  },
  section: {
    height: 20,
    paddingLeft: 10,
    textAlign: 'left',
    textAlignVertical: 'center',
    backgroundColor: '#bcbec1',
    color: 'white',
    fontSize: 14,
  },
  separatorLine: {
    height: 1,
    backgroundColor: '#bcbec1',
  },
  menuBox: {
    position: 'absolute',
    right: 0,
    top: 14 * RVW,
    width: 40 * RVW,
    backgroundColor: '#fff',
    borderLeftWidth: 2 * RPX,
    borderLeftColor: '#999',
  },
  menuLine: {
    fontSize: 3.6 * RFT,
    textAlign: 'center',
    borderBottomWidth: 2 * RPX,
    borderBottomColor: '#999',
    lineHeight: 10 * RFT,
  },
};