import { NativeEventEmitter, NativeModules } from 'react-native';
import CollectionUtil from '../util/collectionUtil';
import DeviceInfo from '../util/deviceInfo';
import I18n from '../i18n';

export const RingerMode = {
  SILENT: 0, // 静音模式
  VIBRATE: 1, // 震动模式
  NORMAL: 2, // 正常响铃模式
  UNKNOWN: -1, // 未知模式
};

class AppModule {
  keepScreenOnCount = 0;

  /**
   * 设置屏幕是否常亮
   * @param isTrue
   */
  keepScreenOn(isTrue) {
    if (isTrue) {
      if (++this.keepScreenOnCount !== 1) return;
    } else {
      if (--this.keepScreenOnCount !== 0) {
        this.keepScreenOnCount = Math.max(this.keepScreenOnCount, 0);
        return;
      }
    }
    console.log('AppModule keepScreenOn', isTrue);
    NativeModules.AppModule?.keepScreenOn(isTrue === true);
  }

  /**
   * 获取手机当前的铃声模式
   */
  async getRingerMode() {
    const ringerMode = await NativeModules.AppModule.getRingerMode();
    if (typeof ringerMode === 'string') {
      return parseInt(ringerMode, 10);
    }
    return ringerMode;
  }

  // iOS 开启检测是否静音模式
  async startDetectRingerMode() {
    if (IS_IOS) {
      NativeModules.AppModule?.startDetectMuteStatus();
    }
  }

  // iOS 停止检测是否静音模式
  async stopDetectRingerMode() {
    if (IS_IOS) {
      NativeModules.AppModule?.stopDetectMuteStatus();
    }
  }

  /**
   * 启用前台服务
   */
  startForegroundService() {
    /*if (IS_ANDROID) {
      console.log('AppModule startForegroundService');
      NativeModules.AppModule?.startForegroundService(
        I18n.t('foreground_service_notification_title', {
          appName: DeviceInfo.getApplicationName(),
        }),
        I18n.t('foreground_service_notification_content')
      );
    }*/
  }

  /**
   * 关闭前台服务
   */
  stopForegroundService() {
    /*if (IS_ANDROID) {
      console.log('AppModule stopForegroundService');
      NativeModules.AppModule?.stopForegroundService();
    }*/
  }

  /**
   * 移除所有通知
   */
  clearAllNotifications() {
    if (IS_ANDROID) {
      NativeModules.AppModule?.cancelAllNotification();
    }
  }

  async getFcmToken() {
    /*if (IS_ANDROID && NativeModules.AppModule?.getFcmToken) {
      const token = await NativeModules.AppModule.getFcmToken();
      console.log('AppModule getFcmToken', token);
      if (token) {
        return token;
      }
    }*/
    return '';
  }

  async getApnsToken() {
    /*if (IS_IOS && NativeModules.AppModule?.getApnsToken) {
      const token = await NativeModules.AppModule.getApnsToken();
      console.log('AppModule getApnsToken', token);
      if (token) {
        return token;
      }
    }*/
    return '';
  }

  async getVoipToken() {
    /*if (IS_IOS && NativeModules.AppModule?.getVoipToken) {
      const token = await NativeModules.AppModule.getVoipToken();
      console.log('AppModule getVoipToken', token);
      if (token) {
        return token;
      }
    }*/
    return '';
  }

  async getIOSToken() {
    /*const apnsToken = await this.getApnsToken();
    const voipToken = await this.getVoipToken();
    if (apnsToken && voipToken) {
      return `${apnsToken}|${voipToken}`;
    }*/
    return '';
  }

  async getIntent() {
    if (IS_ANDROID && NativeModules.AppModule?.getIntent) {
      return NativeModules.AppModule.getIntent();
    }
    return '';
  }

  eventMap = new Map();

  addEventListener(eventName, listener) {
    CollectionUtil.mapSetAdd(this.eventMap, eventName, listener);
    if (!this.eventListener) {
      const eventEmitter = new NativeEventEmitter(NativeModules.AppModule);
      this.eventListener = eventEmitter.addListener('event', event => {
        console.log('AppModule addEventListener event', event);
        const set = this.eventMap.get(event?._eventName);

        if (set) {
          for (let fun of set) {
            try {
              fun(event);
            } catch (e) {
              console.warn('AppModule addEventListener event', event);
            }
          }
        }
      });
    }
    return {
      remove: () => CollectionUtil.mapSetRemove(this.eventMap, eventName, listener),
    };
  }
}

export default new AppModule();
