import React, { Component } from 'react';
import { Text, View, Image, StyleSheet, Button } from '../index';
import icon from '../../res';
import styles from '../../themes/enterprise';
import I18n from '../../i18n';

const emptyStyles = StyleSheet.create({
  container: {
    paddingVertical: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: 240,
    height: 200,
  },
  noIconText: {
    paddingTop: 20,
    paddingBottom: 20,
  },
  btnContainerStyle: {
    marginTop: 20,
    minWidth: 150,
  },
});

/**
 * Empty页面处理:    主要是No Data、错误界面
 * @params
 * image:           自定义缺省、错误页面图片
 * text:            自定义缺省、错误页面文字内容
 * loading:         页面加载中的状态
 * noIcon:          是否需要图片显示
 * textColor:       自定义缺省、错误页面文字颜色
 * style:           自定义缺省、错误页面外部样式
 * loadError:       是否是加载失败页面 true 是； false 是no data；
 *
 * author: 盛宣伟
 */

export default class NoData extends Component {
  render() {
    const { image, text, loading, noIcon, textColor, style, loadError, onRetry } = this.props;
    const source = image || loadError ? icon.iconErrorData : icon.iconNoData;
    const content = text || (loadError ? I18n.t('list_error_data') : I18n.t('list_no_data'));
    const { themeStyle } = styles.get(['theme']);
    if (loading) {
      return <View />;
    }
    return (
      <View style={[emptyStyles.container, style]}>
        {!noIcon && (
          <View>
            <Image resizeMode="contain" source={source} style={emptyStyles.image} />
          </View>
        )}
        <View style={[noIcon ? emptyStyles.noIconText : {}]}>
          <Text
            style={{
              marginTop: 20,
              fontSize: themeStyle.fontSizeM,
              color: textColor || themeStyle.primaryFontColor,
            }}
          >
            {content}
          </Text>
        </View>
        {onRetry ? (
          <Button
            containerStyle={emptyStyles.btnContainerStyle}
            title={I18n.t('op_retry_title')}
            onPress={onRetry}
            btnSize="sm"
            btnType="primary"
          />
        ) : null}
      </View>
    );
  }
}
