import React, { Component } from 'react';
import { Button, Image, StyleSheet, Text, View } from '../index';
import icon from '../../res';
import styles from '../../themes/enterprise';
import I18n from '../../i18n';

function getComponentStyle(theme) {
  return {
    container: {
      paddingVertical: 60,
      alignItems: 'center',
      justifyContent: 'center',
    },
    image: {
      width: 200,
      height: 142,
    },
    text: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryFontColor,
      lineHeight: 20,
      marginTop: 20,
    },
    btnContainerStyle: {
      marginTop: 20,
      minWidth: 150,
    },
  };
}

/**
 * 加载出错页面
 * <AUTHOR>
 */
export default class LoadError extends Component {
  style = getComponentStyle(styles.get('theme'));

  render() {
    const { show, onRetry, style } = this.props;
    if (!show) {
      return null;
    }
    return (
      <View style={StyleSheet.flatten([this.style.container, style])}>
        <Image resizeMode="contain" source={icon.iconErrorData} style={this.style.image} />
        <Text style={this.style.text}>{I18n.t('list_error_data')}</Text>
        {onRetry ? (
          <Button
            containerStyle={this.style.btnContainerStyle}
            title={I18n.t('op_retry_title')}
            onPress={onRetry}
            btnSize="sm"
            btnType="primary"
          />
        ) : null}
      </View>
    );
  }
}
