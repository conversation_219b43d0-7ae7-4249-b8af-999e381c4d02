import React, { Component } from 'react';
import { StyleSheet, Text } from 'react-native';

function getComponentStyle() {
  return {
    amountText: {
      fontFamily: 'Roboto',
    },
  };
}

/**
 * text组件
 * <AUTHOR>
 */
export default class TextComponent extends Component {
  style = getComponentStyle();

  render() {
    const { textType = 'text', style = {}, children, ...rest } = this.props;
    return (
      <Text
        style={StyleSheet.flatten([textType === 'amount' && this.style.amountText, style])}
        {...rest}
      >
        {children}
      </Text>
    );
  }
}
