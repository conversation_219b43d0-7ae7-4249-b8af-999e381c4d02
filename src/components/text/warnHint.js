import React from 'react';
import { Text, View } from '../index';
import styles from '../../themes';
import Icon from '../icons/icon';

function getComponentStyle(theme) {
  return {
    warnHintContainer: {
      flexDirection: 'row',
      marginBottom: 8,
      marginHorizontal: theme.containerMarginHorizontal,
    },
    warnIconContainer: {
      justifyContent: 'center',
      height: 16,
    },
    warnHintText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryFontColor,
      lineHeight: 16,
      marginLeft: 5,
      flexShrink: 1000,
    },
  };
}

/**
 * 提示文本
 * <AUTHOR>
 */
export default class WarnHint extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  render() {
    const { hint, style } = this.props;
    if (!hint) {
      return null;
    }
    return (
      <View style={[this.style.warnHintContainer, style]}>
        <View style={this.style.warnIconContainer}>
          <Icon name="ios-alert-circle" type="ionicon" size={14} color="#FEB957" />
        </View>
        <Text style={this.style.warnHintText}>{hint}</Text>
      </View>
    );
  }
}
