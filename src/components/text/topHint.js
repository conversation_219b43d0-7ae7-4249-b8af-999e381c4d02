import React from 'react';
import { Image, Text, Touchable, View } from '../index';
import styles from '../../themes';
import RightArrow from '../rightArrow';
import resIcon from '../../res';

function getComponentStyle(theme) {
  return {
    container: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#DBF8E0',
      paddingHorizontal: theme.containerPaddingHorizontal,
      paddingVertical: 10,
    },
    redContainer: {
      backgroundColor: '#FFEEEA',
    },
    hintImg: {
      width: 12,
      height: 12,
      marginTop: 3,
      marginRight: 5,
    },
    hintText: {
      fontSize: theme.fontSizeL,
      color: theme.primaryColor,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
    },
    redHintText: {
      color: theme.cryptoWaringTextColor,
    },
    rightArrow: {
      marginLeft: 10,
    },
  };
}

/**
 * 页面顶部提示
 * <AUTHOR>
 */
export default class TopHint extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  render() {
    const { hint, onPress, isRed, hintStyle } = this.props;
    if (!hint) {
      return null;
    }
    const { style } = this;
    const hasClick = typeof onPress === 'function';
    const content = (
      <View style={[style.container, isRed && style.redContainer]}>
        {isRed ? (
          <Image style={[style.hintImg]} source={resIcon.alertRedWarn} resizeMode="contain" />
        ) : null}
        <Text style={[style.hintText, isRed && style.redHintText, hintStyle]}>{hint}</Text>
        {hasClick ? <RightArrow color={style.hintText.color} style={style.rightArrow} /> : null}
      </View>
    );
    if (hasClick) {
      return <Touchable onPress={onPress}>{content}</Touchable>;
    }
    return content;
  }
}
