import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, StyleSheet, Modal } from 'react-native';
import { Icon, Button } from 'react-native-elements';
import { titleColor, desColor, baseBlueColor } from '../themes';
import I18n from '../i18n';
import { deviceHeight, deviceWidth, headerHeight, statusBarHeight } from '../common';
import res from '../res';
import Image from './image';

const styles = StyleSheet.create({
  bottomResumeAreaModel: {
    height: deviceHeight / 2,
  },
  bottomModelContainer: {
    flex: 1,
  },
  bottomModelTitleWrap: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 5,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  bottomModelTitle: {
    fontSize: 16,
    color: titleColor,
    lineHeight: 40,
  },
  bottomModelClose: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  bottomModelBody: {
    height: deviceHeight,
    position: 'relative',
    opacity: 0,
  },
  buttonStyle: {
    backgroundColor: baseBlueColor,
    width: '60%',
    height: 40,
    borderWidth: 0,
    borderRadius: 5,
    marginLeft: '20%',
    borderColor: baseBlueColor,
    elevation: 0,
  },
  buttonStyleContainer: {
    marginTop: 40,
    paddingVertical: 10,
    width: '100%',
  },
  nullText: {
    fontSize: 14,
    color: desColor,
    textAlign: 'center',
    paddingVertical: 44,
  },
  commentBackTopEmpty: {
    width: '100%',
    height: '100%',
  },
  commentBackContent: {
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  commentBackTop: {
    height: '0%',
    width: '100%',
  },
  commentBackGround: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  swiperimage: {
    height: (113 * deviceWidth) / 375 + headerHeight,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  jobHunting: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 16,
    paddingRight: 8,
    alignItems: 'center',
  },
});

@inject('resumeStore', 'personStore')
@observer
export default class guideModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onClosed = () => {
    this.props.closeModal();
  };

  onSelectIntension = () => {
    this.props.hideLocationModal();
    const { nav } = this.props;
    nav.navigate('jobIntention', {
      onShowModal: () => {
        this.props.onCheckIntensions();
      },
    });
    this.props.hideModal();
  };

  onCompleteResume = () => {
    this.props.hideModal();
    const { nav } = this.props;
    nav.navigate('resumeProfile', {
      onShowModal: () => {
        this.props.onCheckIntensions();
      },
    });
  };

  onBindPhone = () => {
    this.props.hideModal();
    const { nav } = this.props;
    nav.navigate('modifyPhone', {
      onShowModal: () => {
        this.props.onCheckIntensions();
      },
    });
  };

  renderIntensionView = () => (
    <View>
      <View style={styles.jobHunting}>
        <Text style={{ fontSize: 14, flexGrow: 25, flexShrink: 200 }} />
        <View style={{ flexGrow: 1 }}>
          <Icon
            onPress={this.onSelectIntension}
            type="simple-line-icon"
            name="plus"
            size={22}
            color="#fff"
          />
        </View>
      </View>
      <View
        style={{
          marginTop: -12,
          flexDirection: 'row',
          justifyContent: 'flex-end',
          marginRight: 28,
          paddingLeft: 80,
        }}
      >
        <Image
          source={res.guideline}
          style={{
            width: 132,
            height: 42,
            marginLeft: 14,
            marginTop: 25,
            transform: [{ skewY: '-15deg' }],
          }}
        />
      </View>
      <View style={{ marginTop: 14 }}>
        <Text
          style={{
            fontSize: 18,
            color: '#fff',
            lineHeight: 30,
            textAlign: 'center',
          }}
        >
          {I18n.t('page_job_text_set_job')}
        </Text>
        <Text
          style={{
            fontSize: 18,
            color: '#fff',
            lineHeight: 30,
            textAlign: 'center',
          }}
        >
          {I18n.t('page_guide_quickly_view_jobs')}
        </Text>
      </View>
      <View style={styles.buttonStyleContainer}>
        <Button
          title={I18n.t('page_guide_now_setting')}
          buttonStyle={styles.buttonStyle}
          titleStyle={{ fontSize: 16 }}
          onPress={this.onSelectIntension}
        />
      </View>
    </View>
  );

  renderResumeView = () => (
    <View>
      <View>
        <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
          <Image
            source={res.resumeIcon}
            resizeMode="contain"
            style={{
              width: 64,
              height: 112,
            }}
          />
        </View>
        <View style={{ marginTop: 8 }}>
          <Text
            style={{
              fontSize: 18,
              color: '#fff',
              lineHeight: 30,
              textAlign: 'center',
            }}
          >
            {I18n.t('page_guide_now_complete_a_resume')}
          </Text>
          <Text
            style={{
              fontSize: 18,
              color: '#fff',
              lineHeight: 30,
              textAlign: 'center',
            }}
          >
            {I18n.t('page_guide_see_and_send_intereste_job')}
          </Text>
        </View>
      </View>
      <View style={styles.buttonStyleContainer}>
        <Button
          title={I18n.t('page_guide_complete_resume')}
          buttonStyle={styles.buttonStyle}
          titleStyle={{ fontSize: 16 }}
          onPress={this.onCompleteResume}
        />
      </View>
    </View>
  );

  renderBindPhoneView = () => {
    return null;
    return (
      <View>
        <View>
          <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
            <Image
              source={res.resumeIcon}
              resizeMode="contain"
              style={{
                width: 64,
                height: 112,
              }}
            />
          </View>
          <View style={{ marginTop: 8 }}>
            <Text
              style={{
                fontSize: 18,
                color: '#fff',
                lineHeight: 30,
                textAlign: 'center',
              }}
            >
              {I18n.t('page_guide_bind_phone')}
            </Text>
            <Text
              style={{
                fontSize: 18,
                color: '#fff',
                lineHeight: 30,
                textAlign: 'center',
              }}
            >
              {I18n.t('page_guide_not_miss_every_interview')}
            </Text>
          </View>
        </View>
        <View style={styles.buttonStyleContainer}>
          <Button
            title={I18n.t('page_guide_now_bind_phone')}
            buttonStyle={styles.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={this.onBindPhone}
          />
        </View>
      </View>
    );
  };

  renderFinalView = (showType) => {
    if (showType === 1) {
      return this.renderIntensionView();
    }
    if (showType === 2) {
      return this.renderResumeView();
    }
    return this.renderBindPhoneView();
  };

  render() {
    const { showType } = this.props;
    return (
      <Modal
        animationType="fade"
        presentationStyle="overFullScreen"
        transparent
        visible={this.props.isOpen}
        // onRequestClose={this.onClosed}
      >
        <View style={styles.commentBackGround}>
          <View style={styles.commentBackContent}>
            <View style={styles.swiperimage}>
              <Text />
              <Icon
                onPress={this.onClosed}
                type="ionicon"
                name="ios-close-circle-outline"
                iconStyle={{
                  marginTop: statusBarHeight + headerHeight / 2 - 14,
                  marginRight: 15,
                }}
                size={28}
                color="#fff"
              />
            </View>
            {this.renderFinalView(showType)}
          </View>
        </View>
      </Modal>
    );
  }
}
