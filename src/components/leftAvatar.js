import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { globalStyle } from '../themes';
import Image from './image';

export default (props) => {
  if (props.onPress) {
    return (
      <TouchableOpacity style={globalStyle.avatarWrapper} onPress={props.onPress}>
        <Image source={{ uri: props.uri }} style={globalStyle.avatar} />
      </TouchableOpacity>
    );
  }
  return (
    <View style={globalStyle.avatarWrapper}>
      <Image source={{ uri: props.uri }} style={globalStyle.avatar} />
    </View>
  );
};
