import React from 'react';
import Swiper from 'react-native-swiper';
import styles from '../../themes/enterprise';

function getComponentStyle(theme) {
  return {
    bannerPaginationStyle: {
      bottom: 8,
    },
    indexBannerDotStyle: {
      backgroundColor: theme.labelFontColor,
      width: 4,
      height: 4,
      marginTop: 18,
    },
    indexBannerActiveDotStyle: {
      backgroundColor: theme.primaryBgColor,
      width: 8,
      height: 8,
      borderRadius: 4,
      marginTop: 18,
    },
  };
}

/**
 * 轮播组件，https://github.com/leecade/react-native-swiper
 *
 * author: Rays
 */
export default class SwiperComponent extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  onInitSwiper = (ref) => (this.swiper = ref);

  scrollBy = (index) => {
    this.swiper.scrollBy(index);
  };

  render() {
    return (
      <Swiper
        ref={this.onInitSwiper}
        autoplay
        autoplayTimeout={3}
        index={0}
        paginationStyle={[this.style.bannerPaginationStyle]}
        dotStyle={this.style.indexBannerDotStyle}
        activeDotStyle={this.style.indexBannerActiveDotStyle}
        {...this.props}
      >
        {this.props.children}
      </Swiper>
    );
  }
}
