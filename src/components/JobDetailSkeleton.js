import React from 'react';
import { View, Animated, StyleSheet } from 'react-native';

export default class JobDetailSkeleton extends React.Component {
  constructor(props) {
    super(props);
    this.animatedValue = new Animated.Value(0);
  }

  componentDidMount() {
    this.startAnimation();
  }

  startAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(this.animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(this.animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  render() {
    const opacity = this.animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    });

    return (
      <View style={styles.container}>
        {/* 职位信息骨架 */}
        <View style={styles.jobCard}>
          <Animated.View style={[styles.titleBar, { opacity }]} />
          <View style={styles.tagContainer}>
            {[1, 2, 3].map((item) => (
              <Animated.View key={item} style={[styles.tag, { opacity }]} />
            ))}
          </View>
          <Animated.View style={[styles.addressBar, { opacity }]} />
        </View>

        {/* 公司信息骨架 */}
        <View style={styles.companyCard}>
          <Animated.View style={[styles.avatar, { opacity }]} />
          <View style={styles.companyInfo}>
            <Animated.View style={[styles.companyName, { opacity }]} />
            <Animated.View style={[styles.companyDesc, { opacity }]} />
          </View>
        </View>

        {/* 职位描述骨架 */}
        <View style={styles.descCard}>
          <Animated.View style={[styles.descTitle, { opacity }]} />
          {[1, 2, 3].map((item) => (
            <Animated.View key={item} style={[styles.descLine, { opacity }]} />
          ))}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#fff',
  },
  jobCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  titleBar: {
    height: 24,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    marginBottom: 15,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 15,
  },
  tag: {
    width: 60,
    height: 20,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  addressBar: {
    height: 16,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    width: '60%',
  },
  companyCard: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
  },
  avatar: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: '#E1E9EE',
  },
  companyInfo: {
    flex: 1,
    marginLeft: 15,
  },
  companyName: {
    height: 20,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    width: '80%',
    marginBottom: 10,
  },
  companyDesc: {
    height: 16,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    width: '60%',
  },
  descCard: {
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  descTitle: {
    height: 20,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    width: '40%',
    marginBottom: 15,
  },
  descLine: {
    height: 16,
    backgroundColor: '#E1E9EE',
    borderRadius: 4,
    marginBottom: 10,
  },
});