import React from 'react';
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import styles from '../../themes/enterprise';

export default class ScrollableHorizontalTabView extends React.Component {
  render() {
    const { themeStyle } = styles.get(['theme']);
    const { children, initialPage, ...rest } = this.props;
    return (
      <ScrollableTabView
        renderTabBar={() => <DefaultTabBar style={{ borderBottomWidth: 0 }} />}
        style={{ width: '100%', flex: 1 }}
        tabBarBackgroundColor={themeStyle.primaryColor}
        tabBarInactiveTextColor={themeStyle.primaryBgColor}
        tabBarActiveTextColor={themeStyle.primaryBgColor}
        initialPage={initialPage || 0}
        {...rest}
      >
        {children}
      </ScrollableTabView>
    );
  }
}
