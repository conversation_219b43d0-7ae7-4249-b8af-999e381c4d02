import React from 'react';
import ScrollableTabView, {
  DefaultTabBar,
  ScrollableTabBar,
} from 'react-native-scrollable-tab-view';
import styles from '../../themes/enterprise';
import { deviceWidth } from '../../common';
import ScrollableHorizontalTabView from './scrollableHorizontalTabView';

export default class ScrollableTabComponent extends React.Component {
  renderTabBar = () => {
    const { scrollableTabBar = false, tabBarStyle, tabStyles } = this.props;
    if (scrollableTabBar) {
      return <ScrollableTabBar style={tabBarStyle} tabStyle={tabStyles} />;
    }
    return <DefaultTabBar style={tabBarStyle} tabStyle={tabStyles} />;
  };

  render() {
    const { isHorizontalScroll, children, ...rest } = this.props;
    if (isHorizontalScroll) {
      return <ScrollableHorizontalTabView {...rest}>{children}</ScrollableHorizontalTabView>;
    }
    const { themeStyle } = styles.get(['theme']);
    const {
      props,
      props: { underlineColor, initialPage, defaultStyle },
    } = this;
    return (
      <ScrollableTabView
        renderTabBar={this.renderTabBar}
        style={{ width: '100%', flex: 1, ...defaultStyle }}
        tabBarBackgroundColor={themeStyle.primaryColor}
        tabBarInactiveTextColor="rgba(255,255,255,0.8)"
        tabBarActiveTextColor={themeStyle.primaryBgColor}
        scrollWithoutAnimation={true}
        initialPage={initialPage || 0}
        tabBarTextStyle={{
          fontSize: themeStyle.fontSizeL,
          fontWeight: themeStyle.fontWeightBold,
        }}
        tabBarUnderlineStyle={{
          backgroundColor: underlineColor || themeStyle.primaryBgColor,
          width: 13,
          height: 2,
          marginLeft: (deviceWidth / children.length - 13) / 2,
        }}
        {...props}
      >
        {children}
      </ScrollableTabView>
    );
  }
}
