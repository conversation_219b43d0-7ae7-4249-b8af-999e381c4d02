import React from 'react';
import styles from '../../themes/enterprise';
import { Text, Touchable, View } from '../index';

function getComponentStyle(theme) {
  return {
    container: {
      flexDirection: 'row',
    },
    titleText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      lineHeight: 30,
    },
    selectedTitleText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
    },
  };
}

/**
 * tab
 * <AUTHOR>
 */
export default class TopTab extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  render() {
    const { tabContainerStyle, tabs, selectedTab, onPress, textStyle, selectedStyle } = this.props;
    const { style } = this;
    return (
      <View style={[style.container, tabContainerStyle]}>
        {tabs.map((item, index) => {
          return (
            <Touchable key={item.key} onPress={() => onPress(item)}>
              <Text
                style={[
                  style.titleText,
                  index > 0 && { marginLeft: 20 },
                  { ...textStyle },
                  selectedTab.key === item.key && [style.selectedTitleText, selectedStyle],
                ]}
              >
                {item.title}
              </Text>
            </Touchable>
          );
        })}
      </View>
    );
  }
}
