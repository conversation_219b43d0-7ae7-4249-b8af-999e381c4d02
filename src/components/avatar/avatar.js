import React, { Component } from 'react';
import { Image, View, Text } from '../index';
import { inject, observer } from 'mobx-react';
import resIcon from '../../res';
import avatarUtil from '../../util/avatarUtil';

const colors = ['#e67e22', '#2ecc71', '#3498db', '#8e44ad', '#e74c3c', '#1abc9c', '#2c3e50'];

function getAvatarNameWithColor(userName) {
  const result = { avatarName: '', avatarColor: '' };
  if (!userName) return result;
  const name = userName.toUpperCase().split(' ');
  if (name.length === 1) {
    result.avatarName = `${name[0].charAt(0)}`;
  } else if (name.length > 1) {
    result.avatarName = `${name[0].charAt(0)}${name[1].charAt(0)}`;
  } else {
    return result;
  }
  let sumChars = 0;
  for (let i = 0; i < userName.length; i++) {
    sumChars += userName.charCodeAt(i);
  }
  // inspired by https://github.com/wbinnssmith/react-user-avatar
  // colors from https://flatuicolors.com/
  result.avatarColor = colors[sumChars % colors.length];
  return result;
}

/**
 * 头像
 * <AUTHOR>
 */
@inject('userStore')
@observer
export default class Avatar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      avatar: undefined,
      name: undefined,
      avatarName: '',
      avatarColor: '',
      isError: true,
    };
  }

  static getDerivedStateFromProps({ avatar, name }, state) {
    if (avatar !== state.avatar) {
      const isError = !avatar;
      // console.log(name + ' Avatar render', isError, name, avatar);
      return {
        isError,
        avatar,
        name,
        ...getAvatarNameWithColor(isError && name),
      };
    }
    if (name !== state.name) {
      return {
        name,
        ...getAvatarNameWithColor(state.isError && name),
      };
    }
    return null;
  }

  onError = () => {
    const textAvatarInfo = getAvatarNameWithColor(this.props.name);
    textAvatarInfo.isError = true;
    this.setState(textAvatarInfo);
  };

  render() {
    const { size = 80, borderRadius = size / 2.0, style, textStyle } = this.props;
    const { avatarName, avatarColor, avatar, isError } = this.state;
    if (typeof avatar === 'number') {
      return (
        <Image
          source={avatar}
          style={[{ width: size, height: size, borderRadius }, style]}
          resizeMode="contain"
        />
      );
    }
    if (isError && avatarName) {
      return (
        <View
          style={[
            {
              width: size,
              height: size,
              borderRadius,
              backgroundColor: avatarColor,
              justifyContent: 'center',
              alignItems: 'center',
            },
            style,
          ]}
        >
          <Text
            style={[
              { color: '#fff', fontWeight: 'bold', fontSize: avatarName?.length > 1 ? 12 : 16 },
              textStyle,
            ]}
          >
            {avatarName}
          </Text>
        </View>
      );
    }
    return (
      <Image
        onError={this.onError}
        source={{ uri: avatarUtil.handleAvatar(avatar) }}
        defaultSource={resIcon.defaultAvatar}
        style={[{ width: size, height: size, borderRadius }, style]}
        resizeMode="cover"
      />
    );
  }
}
