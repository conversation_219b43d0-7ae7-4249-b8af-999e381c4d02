import React, { Component } from 'react';
import { View, StyleSheet, Modal, Text, TouchableOpacity } from 'react-native';
import { deviceWidth, deviceHeight } from '../common';
import I18n from '../i18n';
import { baseBlueColor, titleColor } from '../themes/base';

const styles = StyleSheet.create({
  commentBackContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  loadingContainerItem: {
    borderRadius: 6,
    width: deviceWidth - 80,
    backgroundColor: '#fff',
  },
  imageBackgroundContiner: {
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
  },
  imageBackgroundStyle: {
    width: '100%',
    height: 64,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  contentDescContainer: {
    paddingHorizontal: 12,
    paddingTop: 12,
    maxHeight: deviceHeight / 2 + 60,
    justifyContent: 'center',
    marginTop: 20,
  },
  contentDesc: {
    fontSize: 16,
    paddingLeft: 10,
    color: titleColor,
    lineHeight: 30,
    textAlign: 'center',
  },
  btngroupContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    width: deviceWidth - 80,
  },
  afterTitle: {
    width: deviceWidth / 2 - 88,
    color: titleColor,
    fontSize: 16,
    paddingVertical: 11,
    textAlign: 'center',
  },
  updateTitle: {
    width: deviceWidth / 2 - 88,
    color: baseBlueColor,
    fontSize: 16,
    paddingVertical: 11,
    textAlign: 'center',
  },
});

export default class locationModal extends Component {
  dismiss = () => {
    this.props.dismiss();
  };

  swithCity = () => {
    // this.props.dismiss();
    this.props.getPositionConfirm();
  };

  selectCity = () => {
    // this.props.dismiss();
    this.props.selectedCity();
  };

  renderNoramlUpdate = () => (
    <View style={styles.btngroupContainer}>
      <TouchableOpacity onPress={this.dismiss}>
        <Text style={styles.afterTitle}>{I18n.t('page_setting_cancel_text')}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={this.props.isSwitchCity ? this.swithCity : this.selectCity}>
        <Text style={styles.updateTitle}>{this.props.confirmTitle}</Text>
      </TouchableOpacity>
    </View>
  );

  render() {
    const { isOpen, memo } = this.props;
    return (
      <Modal
        onRequestClose={() => {
          this.props.dismiss();
        }}
        animationType="fade"
        presentationStyle="overFullScreen"
        transparent
        visible={isOpen}
      >
        <View style={styles.commentBackContent}>
          <View style={styles.loadingContainerItem}>
            <View style={styles.contentDescContainer}>
              <Text style={styles.contentDesc}>{memo}</Text>
            </View>
            {this.renderNoramlUpdate()}
          </View>
        </View>
      </Modal>
    );
  }
}
