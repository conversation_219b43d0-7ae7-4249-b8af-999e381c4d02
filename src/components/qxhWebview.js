/**
 *功能：webiew界面，打开所有的web
 *描述：params:
 *作者：moke
 * @export
 * @class QxhWebView
 * @extends {Component}
 */

import React, { Component } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';

export default class QxhWebView extends Component {
  componentDidMount() {
  }

  componentWillUnmount() {
  }


  render() {
    const { url, showLoading, style, ...rest } = this.props;
    return (
      <View style={[{ flex: 1 }, style]}>
        <WebView
          useWebKit
          startInLoadingState={showLoading || false}
          originWhitelist={['*']}
          source={{ uri: url }}
          allowUniversalAccessFromFileURLs
          javaScriptEnabled
          {...rest}
        />
      </View>
    );
  }
}
