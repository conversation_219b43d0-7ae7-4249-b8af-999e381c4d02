import React from 'react';
import { StyleSheet, Text } from 'react-native';
import RNPopover from 'react-native-popover-view';
import { statusBarHeight } from '../../common';
import Image from '../images/image';
import Touchable from '../touchable';

// export { PopoverPlacement, PopoverMode, Rect, Size } from 'react-native-popover-view';

const styles = StyleSheet.create({
  popoverStyle: {
    backgroundColor: '#545B65',
    borderRadius: 8,
    paddingHorizontal: 28,
    paddingVertical: 14,
  },
  backgroundStyle: {
    backgroundColor: 'transparent',
  },
  menuMain: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    // borderBottomWidth: 1,
    // borderBottomColor: '#fff',
  },
  menuIcon: {
    marginRight: 21,
  },
  menuText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#F0F5F9',
  },
});

const MenuItem = function ({ icon, text, onPress }) {
  return (
    <Touchable style={styles.menuMain} onPress={onPress}>
      {icon ? <Image source={icon} style={styles.menuIcon} /> : null}
      <Text style={styles.menuText}>{text}</Text>
    </Touchable>
  );
};

/**
 * Popover
 * https://github.com/steffeydev/react-native-popover-view
 * <AUTHOR>
 */
class Popover extends React.Component {
  render() {
    const { forwardRef, menus, data, children, ...rest } = this.props;
    return (
      <RNPopover
        ref={forwardRef}
        // arrowSize={{ width: 0, height: 0 }}
        verticalOffset={IS_ANDROID ? -statusBarHeight : 0}
        popoverStyle={styles.popoverStyle}
        backgroundStyle={styles.backgroundStyle}
        {...rest}
      >
        {menus?.length
          ? menus.map((item) => {
              if (typeof item.isAvailable === 'function' && !item.isAvailable(data)) {
                return null;
              }
              return (
                <MenuItem
                  key={item.text}
                  icon={item.icon}
                  text={item.text}
                  onPress={() => item.onPress?.(data)}
                />
              );
            })
          : children}
      </RNPopover>
    );
  }
}

export default React.forwardRef((props, ref) => <Popover forwardRef={ref} {...props} />);
