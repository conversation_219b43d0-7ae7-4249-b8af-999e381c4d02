import React from 'react';
import { KeyboardAvoidingView } from 'react-native';

export default (props) => {
  const { children, style } = props;
  if (global.IS_IOS) {
    return (
      <KeyboardAvoidingView behavior="padding" style={style}>
        {children}
      </KeyboardAvoidingView>
    );
  }
  return (
    <KeyboardAvoidingView behavior="padding" style={style}>
      {children}
    </KeyboardAvoidingView>
  );
};
