import React from 'react';
import { StatusBar, View } from 'react-native';
import { baseBlueColor } from '../themes';
import { statusBarHeight } from '../common';

/**
 * 状态栏组件，设置状态栏的前/背景色，背景色默认为白色，前景色为黑色
 * Android版本：
 * OS_VERSION 19 开始可以更改状态栏为半透明，且内容延伸到状态栏下
 * OS_VERSION 21 开始可以更改状态栏背景色
 * OS_VERSION 23 开始可以更改状态栏前景色风格为黑色
 *
 * 属性参考https://reactnative.cn/docs/statusbar/
 * barStyle 状态栏文本的颜色，enum('default', 'light-content', 'dark-content')
 * backgroundColor 背景色
 * translucent 是否半透明，Android属性，为true时内容区域延伸到状态栏下
 *
 * author: Rays
 */
export default class StatusBarComponent extends React.Component {
  static setTranslucent(translucent) {
    StatusBar.setTranslucent(translucent);
  }

  static setBarStyle(style) {
    StatusBar.setBarStyle(style);
  }

  render() {
    const {
      backgroundColor: bgColor,
      barStyle: cBarStyle,
      translucent = true,
      height0 = false,
      containerStyle,
      ...attributes
    } = this.props;

    let backgroundColor = bgColor || '#fff';
    let barStyle = cBarStyle || 'dark-content';

    if (global.IS_IOS) {
      return (
        <View style={[{ backgroundColor, height: height0 ? 0 : statusBarHeight }, containerStyle]}>
          <StatusBar barStyle={barStyle} {...attributes} />
        </View>
      );
    }

    const height = translucent && !height0 ? statusBarHeight : 0;
    if (OS_VERSION >= 19) {
      if (OS_VERSION < 23) {
        barStyle = 'light-content';
        backgroundColor = bgColor || baseBlueColor;
      }
      return (
        <View style={[{ backgroundColor, height }, containerStyle]}>
          <StatusBar
            barStyle={barStyle}
            backgroundColor={translucent ? 'transparent' : backgroundColor}
            translucent={translucent}
            {...attributes}
          />
        </View>
      );
    }
    return null;
  }
}
