import React from 'react';
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import { baseBlueColor } from '../themes';

export default class ScrollableHorizontalTabView extends React.Component {
  render() {
    const { children, initialPage, ...rest } = this.props;
    return (
      <ScrollableTabView
        renderTabBar={() => <DefaultTabBar style={{ borderBottomWidth: 0 }} />}
        style={{ width: '100%', flex: 1 }}
        tabBarBackgroundColor={baseBlueColor}
        tabBarInactiveTextColor={baseBlueColor}
        tabBarActiveTextColor={baseBlueColor}
        initialPage={initialPage || 0}
        {...rest}
      >
        {children}
      </ScrollableTabView>
    );
  }
}
