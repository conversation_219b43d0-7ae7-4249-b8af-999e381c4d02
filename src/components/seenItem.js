import React, { Component } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Badge } from 'react-native-elements';
import res, { getEmployerAvatarSource } from '../res';
import { seenItemStyle, desColor } from '../themes';
import I18n from '../i18n';
import util from '../util';
import Image from './image';
import ImageBackground from './imageBackground';

/**
 * 之前是显示的职位
 * 现在改成公司
 * 如果需要回滚，1.1.1之前的版本就行
 */
export default class SeenItem extends Component {
  goDetails = (item) => {
    this.props.navigation.navigate('companyDetail', {
      employerId: item.employerId,
    });
  };

  renderTag = (value) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        value={value}
        containerStyle={seenItemStyle.listTagItem}
        textStyle={seenItemStyle.tagText}
        badgeStyle={seenItemStyle.listTagBadgeStyle}
      />
    );
  };

  render() {
    const { item } = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          this.goDetails(item);
        }}
      >
        <View style={seenItemStyle.container}>
          <View style={seenItemStyle.seenItemCon}>
            <ImageBackground
              imageStyle={{ borderRadius: 21 }}
              style={seenItemStyle.companyLogo}
              source={getEmployerAvatarSource(item ? item.logo : '')}
            >
              {item &&
              item.employerQualificationType &&
              item.employerQualificationType.value === 1 ? (
                <Image style={seenItemStyle.v2} source={res.verify} />
              ) : (
                <View />
              )}
            </ImageBackground>
            <View style={seenItemStyle.seenItemSubCon}>
              <View style={seenItemStyle.header}>
                <Text style={seenItemStyle.position} numberOfLines={1}>
                  {item.company}{' '}
                  {item && item.qualificationStatus && item.qualificationStatus.value === 1 ? (
                    <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
                  ) : null}
                </Text>
              </View>
              <View style={seenItemStyle.body}>
                <View style={seenItemStyle.locationsStyle}>
                  <Text style={{ color: desColor, fontSize: 12 }}>
                    {item.locationId && item.locationId.label ? item.locationId.label : ''}
                  </Text>
                  {item && item.address ? (
                    <Text style={seenItemStyle.spaceLine}>/</Text>
                  ) : (
                    <Text style={{ display: 'none' }} />
                  )}
                  <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                    {item ? item.address : ''}
                  </Text>
                </View>
                <View style={seenItemStyle.listTags}>
                  {this.renderTag(item && item.industrialId && item.industrialId.label)}
                  {this.renderTag(item && item.scaleId && item.scaleId.label)}
                  {this.renderTag(item && item.companyType && item.companyType.label)}
                </View>
              </View>
            </View>
          </View>

          <View style={seenItemStyle.footer}>
            <Text style={seenItemStyle.seenTime}>
              {`${util.getDiffBetween(item.viewAt)} ${I18n.t('page_chatlist_text_viewer')}`}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}
