import React, { Component } from 'react';
import { View } from 'react-native';
import { inject, observer } from 'mobx-react';
import TabNavigator from 'react-native-tab-navigator';
import I18n from '../../i18n';
import res from '../../res';
import { baseBlueColor } from '../../themes';
import Job from '../../pages/job';
import Dynamic from '../../pages/dynamic';
import Resume from '../../pages/resume';
import General from '../../pages/general';
import Session from '../../api/session';
import { footerHeight } from '../../common';
import Image from '../image';
import ChatPersonal from '../../pages/chat/chatPersonal';
import Constant from '../../store/constant';
import settingsAction from '../../store/actions/settings';
import BindPhoneModal from '../bindPhoneModal';
import NavigationService from '../../navigationService';

const tabbarStyle = {
  tabBar: {
    borderTopWidth: 1,
    borderTopColor: '#EFEFEF',
    backgroundColor: '#FFFFFF',
    height: footerHeight + 49,
    paddingBottom: footerHeight,
  },
  selectedTitle: {
    color: baseBlueColor,
  },
  icon: {
    width: 24,
    height: 24,
  },
  text: {
    fontSize: 12,
    height: 16,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: -5,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#f00',
  },
};

@inject(
  'settingsStore',
  'dynamicStore',
  'dynamicAction',
  'resumeAction',
  'personStore',
  'applicationAction'
)
@observer
export default class TabbarPersonal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      jobTabTitle: I18n.t('menu_nav_bottom_job'),
      chatTabTitle: I18n.t('menu_nav_bottom_chat'),
      dynamicTabTitle: I18n.t('menu_nav_bottom_dynamic'),
      resumeTabTitle: I18n.t('menu_nav_bottom_resume'),
      // resourcesTitle: I18n.t('page_guide_text_consultant'),
      mineTabTitle: I18n.t('menu_nav_bottom_mine'),
    };
    global.isAppStarted = true;
    console.debug('push isAppStarted true');
  }

  componentDidMount() {
    this.dynamiceTimer = setInterval(() => {
      this.props.dynamicAction.hasNewDynamics();
    }, 60 * 1000);
    global.emitter.on('languageChange', this.onChangeLanguage);
    global.emitter.on('reloadData', this.onReload);
  }

  componentWillUnmount() {
    if (this.dynamiceTimer) {
      clearInterval(this.dynamiceTimer);
    }
    global.emitter.off('languageChange', this.onChangeLanguage);
    global.emitter.off('reloadData', this.onReload);
  }

  onChangeLanguage = () => {
    this.forceUpdate();
    this.initState();
  };

  onReload = () => {
    this.initState();
  };

  initState = () => {
    this.setState({
      jobTabTitle: I18n.t('menu_nav_bottom_job'),
      chatTabTitle: I18n.t('menu_nav_bottom_chat'),
      dynamicTabTitle: I18n.t('menu_nav_bottom_dynamic'),
      resumeTabTitle: I18n.t('menu_nav_bottom_resume'),
      // resourcesTitle: I18n.t('page_guide_text_consultant'),
      mineTabTitle: I18n.t('menu_nav_bottom_mine'),
    });
  };

  selectedTab = (name) => {
    Session.isLogin().then(async (isLogin) => {
      const res = await Session.getPersonInfo();
      console.debug('push isLogin', isLogin, name);
      if (!isLogin && (name === 'Resume' || name === Constant.tabs.chat)) {
        this.props.navigation.navigate('login');
      } else {
        const {
          personStore: { me },
        } = this.props;
        const mobile = me?.mobile || res?.mobile;
        if (isLogin && !mobile && name !== 'General' && name !== 'Job') {
          this.bindPhoneModal.wrappedInstance.show();
          return;
        }
        settingsAction.selectedTab(name);
        if (name === 'Resume') {
          this.props.resumeAction.getResumes();
        }
      }
    });
  };

  /**
   * 主题切换或者语言切换的时候，强制更新tabbar
   */
  changeSettings = () => {
    this.forceUpdate();
  };

  hasNewDynamics = () => {
    const { lastDynamic, preLastDynamic } = this.props.dynamicStore;
    return lastDynamic !== preLastDynamic;
  };

  render() {
    const {
      settingsStore: { selectedTab },
      personStore: { newTwitterMsgData },
    } = this.props;
    const { navigation } = this.props;
    const {
      jobTabTitle,
      chatTabTitle,
      dynamicTabTitle,
      mineTabTitle,
      resumeTabTitle,
      // resourcesTitle,
    } = this.state;
    const BadgeView = () => <View style={tabbarStyle.badge} />;
    // const unreadNimMsgTotal = this.countUnreadNimMsg();
    const hasNewDynamic =
      this.hasNewDynamics() || (newTwitterMsgData && newTwitterMsgData.unreadTwitterNum !== 0);
    return (
      <>
        <TabNavigator
          tabBarStyle={tabbarStyle.tabBar}
          sceneStyle={{ paddingBottom: footerHeight + 49 }}
        >
          <TabNavigator.Item
            selected={selectedTab === 'Job'}
            title={jobTabTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconWork} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => (
              <Image source={res.iconWorkActive} style={tabbarStyle.icon} />
            )}
            onPress={() => {
              this.selectedTab('Job');
            }}
          >
            <Job navigation={navigation} changeSettings={this.changeSettings} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === Constant.tabs.chat}
            title={chatTabTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconChat} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => (
              <Image source={res.iconChatActive} style={tabbarStyle.icon} />
            )}
            renderBadge={() => {
              return this.props.msgBadge ? <BadgeView /> : null;
            }}
            onPress={() => this.selectedTab(Constant.tabs.chat)}
          >
            <ChatPersonal navigation={navigation} changeSettings={this.changeSettings} />
          </TabNavigator.Item>
          {/* <TabNavigator.Item
          selected={selectedTab === 'ChatList'}
          title={chatTabTitle}
          titleStyle={tabbarStyle.text}
          selectedTitleStyle={tabbarStyle.selectedTitle}
          renderIcon={() => <Image source={res.iconChat} style={tabbarStyle.icon} />}
          renderSelectedIcon={() => <Image source={res.iconChatActive} style={tabbarStyle.icon} />}
          renderBadge={() => { return unreadNimMsgTotal > 0 ? <BadgeView /> : <View /> }}
          onPress={() => { this.selectedTab('ChatList'); }}
        >
          <ChatList navigation={navigation} changeSettings={this.changeSettings} />
        </TabNavigator.Item> */}
          <TabNavigator.Item
            selected={selectedTab === 'Dynamic'}
            title={dynamicTabTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconForum} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => (
              <Image
                source={res.iconForumActive}
                style={[tabbarStyle.icon, { width: 26, height: 26 }]}
              />
            )}
            renderBadge={() => {
              return hasNewDynamic ? <BadgeView /> : <View />;
            }}
            onPress={() => {
              this.selectedTab('Dynamic');
            }}
          >
            <Dynamic navigation={navigation} changeSettings={this.changeSettings} />
          </TabNavigator.Item>
          <TabNavigator.Item
            selected={selectedTab === 'Resume'}
            title={resumeTabTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconResume} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => (
              <Image source={res.iconResumeActive} style={tabbarStyle.icon} />
            )}
            onPress={() => {
              this.selectedTab('Resume');
            }}
          >
            <Resume navigation={navigation} changeSettings={this.changeSettings} />
          </TabNavigator.Item>
          {/*<TabNavigator.Item
            selected={selectedTab === 'Resources'}
            title={resourcesTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconChat} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => (
              <Image source={res.iconChatActive} style={tabbarStyle.icon} />
            )}
            onPress={() => {
              this.selectedTab('Resources');
            }}
          >
            <Resources navigation={navigation} />
          </TabNavigator.Item>*/}
          <TabNavigator.Item
            selected={selectedTab === 'General'}
            title={mineTabTitle}
            titleStyle={tabbarStyle.text}
            selectedTitleStyle={tabbarStyle.selectedTitle}
            renderIcon={() => <Image source={res.iconMe} style={tabbarStyle.icon} />}
            renderSelectedIcon={() => <Image source={res.iconMeActive} style={tabbarStyle.icon} />}
            onPress={() => {
              this.selectedTab('General');
            }}
          >
            <General
              navigation={navigation}
              changeSettings={this.changeSettings}
              changeTab={this.selectedTab}
            />
          </TabNavigator.Item>
        </TabNavigator>
        <BindPhoneModal
          ref={(ref) => (this.bindPhoneModal = ref)}
          nav={this.props.navigation}
          onCheckIntensions={() => {}}
        />
      </>
    );
  }
}
