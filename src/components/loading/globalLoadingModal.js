import React, { Component } from 'react';
import { ActivityIndicator, StyleSheet, View } from '../index';
import constant from '../../store/constant';
import GlobalAlert from '../alert/globalAlert';
import AppUtil from '../../util/appUtil';

/**
 * 全局加载框页面
 * <AUTHOR>
 */
export default class GlobalLoadingModal extends Component {
  constructor(props) {
    super(props);
    this.showLoading = false;
    this.state = {
      showLoading: false,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.globalLoading, this.onHandle);
  }

  componentWillUnmount() {
    this.isUnmount = true;
    global.emitter.off(constant.event.globalLoading, this.onHandle);
    AppUtil.addAndroidBackListener(false, this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    return this.state.showLoading;
  };

  onHandle = param => {
    if (!param || this.isUnmount) {
      return;
    }
    const { showLoading, error, delay = 0 } = param;
    this.showErrorMsg(error);

    // 有delay的情况一般是要隐藏loading，但接下来可能又要显示，这时延迟取消，就不会频繁修改状态
    if (this.delayTask) {
      clearTimeout(this.delayTask);
      this.delayTask = null;
    }
    if (delay > 0) {
      this.delayTask = setTimeout(() => {
        this.delayTask = null;
        this.onHandle({ showLoading });
      }, delay);
      return;
    }

    if (this.showLoading === showLoading) return;
    this.showLoading = showLoading;
    this.setState({ showLoading });
  };

  showErrorMsg = error => {
    if (error?.message) {
      console.warn('GlobalLoadingModal showErrorMsg', error);
      toast.show(error.message);
    }
  };

  shouldComponentUpdate(nextProps, nextState, nextContext) {
    return nextState.showLoading !== this.state.showLoading;
  }

  render() {
    const { showLoading } = this.state;
    AppUtil.addAndroidBackListener(showLoading, this.onBackButtonPressAndroid, this.isUnmount);
    return (
      <>
        <GlobalAlert />
        {showLoading ? (
          <View style={loadingStyles.modal}>
            <View style={loadingStyles.loadingContainerItem}>
              <ActivityIndicator animating color="#fff" size="small" />
            </View>
          </View>
        ) : null}
      </>
    );
  }
}

const loadingStyles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 999,
  },
  loadingContainerItem: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(204,204,204,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
});
