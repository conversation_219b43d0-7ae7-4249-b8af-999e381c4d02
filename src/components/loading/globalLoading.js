import React, { Component } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from '../index';
import { inject, observer } from 'mobx-react';

const loadingStyles = StyleSheet.create({
  commentBackContent: {
    flex: 1,
    backgroundColor: 'transparent',
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10000,
  },
  loadingContainerItem: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(51,51,51,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
  activityIndicatorStyle: {
    width: 40,
    height: 40,
  },
  loadingText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
});

/**
 * 全局加载框页面
 * <AUTHOR>
 */
@inject('globalStore')
@observer
export default class GlobalLoadingModal extends Component {
  render() {
    const { showLoading, loadingTips } = this.props.globalStore;
    return (
      <View style={loadingStyles.commentBackContent} pointerEvents={showLoading ? 'auto' : 'none'}>
        {showLoading ? (
          <View style={loadingStyles.loadingContainerItem}>
            <ActivityIndicator animating color="#fff" size="small" />
          </View>
        ) : null}
      </View>
    );
  }
}
