import React from 'react';
import { Switch } from 'react-native';
import { inject, observer } from 'mobx-react';

@inject('stores')
@observer
export default class SwitchComponent extends React.PureComponent {
  render() {
    const { value, ...rest } = this.props;
    console.log('render SwitchComponent', value);
    const { themeStyle } = this.props.stores.userStore;
    return (
      <Switch
        trackColor={{ false: '#767577', true: themeStyle.primaryColor }}
        thumbColor={value ? '#fff' : '#f4f3f4'}
        ios_backgroundColor="#C7D0D6"
        // onValueChange={onSwitch}
        {...rest}
        value={value}
      />
    );
  }
}
