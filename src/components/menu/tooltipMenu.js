import React from 'react';
import { Animated, Modal, StyleSheet, Text, TouchableOpacity, View, Image } from '../index';
import { deviceWidth } from '../../common';

const mapWight = (type) => {
  switch (type) {
    case 'half':
      return {
        width: deviceWidth / 2,
      };
    case 'full':
      return {
        width: deviceWidth * 0.9,
      };
    default:
      return {
        width: deviceWidth / 3,
      };
  }
};

export default class TooltipMenu extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      opacity: new Animated.Value(0),
    };
    this.toggleModal = this.toggleModal.bind(this);
    this.openModal = this.openModal.bind(this);
    this.hideModal = this.hideModal.bind(this);
  }

  toggleModal() {
    const { isModalOpen } = this.state;
    this.setState({ isModalOpen: !isModalOpen });
  }

  openModal() {
    this.toggleModal();
    Animated.timing(this.state.opacity, {
      toValue: 1,
      duration: 300,
    }).start();
  }

  hideModal() {
    Animated.timing(this.state.opacity, {
      toValue: 0,
      duration: 300,
    }).start(this.toggleModal);
  }

  handleClick(onClickItem) {
    const method = this.state.isModalOpen ? this.hideModal : this.openModal;
    method();
    onClickItem();
  }

  render() {
    const {
      buttonComponent,
      items,
      componentWrapperStyle,
      overlayStyle,
      widthType,
      labelContainerStyle,
      touchableItemStyle,
      labelStyle,
      modalButtonStyle,
      positionStyle,
    } = this.props;
    const { isModalOpen } = this.state;
    const { onRequestClose } = this.props;
    const widthStyle = mapWight(widthType);

    const TooltipMenuItem = ({
      onPress,
      containerStyle,
      touchableStyle,
      label,
      labelStyle: textStyle,
      item,
    }) => (
      <View style={containerStyle}>
        <TouchableOpacity style={[styles.container, touchableStyle]} onPress={onPress}>
          <Image
            source={item.icon}
            style={{ width: 20, height: 15, marginRight: 10 }}
            resizeMode="cover"
          />
          {typeof label === 'string' ? <Text style={textStyle}>{label}</Text> : label()}
        </TouchableOpacity>
      </View>
    );

    return (
      <View>
        <View style={componentWrapperStyle}>
          <TouchableOpacity onPress={this.openModal}>
            {!isModalOpen && buttonComponent}
          </TouchableOpacity>
        </View>
        <Modal visible={isModalOpen} transparent onRequestClose={onRequestClose}>
          <View style={[styles.overlay, overlayStyle]}>
            <TouchableOpacity
              activeOpacity={1}
              focusedOpacity={1}
              style={[{ flex: 1 }, modalButtonStyle]}
              onPress={this.hideModal}
            >
              <View style={[styles.component, positionStyle]}>
                <Animated.View
                  style={[styles.tooltipContainer, widthStyle, { opacity: this.state.opacity }]}
                >
                  {items.map((item, index) => {
                    const classes = [labelContainerStyle];

                    if (index !== items.length - 1) {
                      classes.push(styles.tooltipMargin);
                    }

                    return (
                      <TooltipMenuItem
                        key={item.label}
                        label={item.label}
                        onPress={() => this.handleClick(item.onPress)}
                        containerStyle={classes}
                        touchableStyle={touchableItemStyle}
                        labelStyle={labelStyle}
                        item={item}
                      />
                    );
                  })}
                </Animated.View>
                <Animated.View style={[styles.triangle, { opacity: this.state.opacity }]} />
                <TouchableOpacity
                  style={[componentWrapperStyle]}
                  onPress={isModalOpen ? this.hideModal : this.openModal}
                >
                  {buttonComponent}
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
  },
  tooltipMargin: {
    borderBottomWidth: 1,
    borderBottomColor: '#E1E1E1',
  },
  component: {
    position: 'absolute',
    top: 10,
    right: 28,
  },
  tooltipContainer: {
    backgroundColor: 'white',
    borderRadius: 4,
    position: 'absolute',
    top: 30,
    right: 5,
  },
  triangle: {
    position: 'absolute',
    top: 21,
    right: 30,
    width: 10,
    height: 10,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderBottomWidth: 5,
    borderRightWidth: 5,
    borderTopWidth: 0,
    borderLeftWidth: 5,
    borderBottomColor: 'white',
    borderRightColor: 'transparent',
    borderTopColor: 'transparent',
    borderLeftColor: 'transparent',
  },
});
