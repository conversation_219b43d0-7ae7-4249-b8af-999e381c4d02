import React, { Component } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { baseBlueColor, titleColor, subTitleColor } from '../themes';
import I18n from '../i18n';

const styles = StyleSheet.create({
  tabs: {
    height: 44,
    backgroundColor: baseBlueColor,
    justifyContent: 'flex-end',
  },
  tab: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 44,
  },
  tabContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 5,
  },
  tabRight: {
    height: 40,
    width: 40,
  },
  tabItem: {
    alignItems: 'center',
  },
});
export default class commonCustomTabBar extends Component {
  componentDidMount() {
    this.props.scrollValue.addListener(this.setAnimationValue);
  }

  setAnimationValue() {}

  renderNormalItem() {
    return (
      <Text style={{ fontSize: 17, color: 'white' }}>{I18n.t('page_resume_text_my_title')}</Text>
    );
  }

  renderTabOption(tab, i) {
    const fontSize = this.props.currentIndex === i ? 16 : 16; // 判断i是否是当前选中的tab，设置不同的颜色
    const showLine = this.props.currentIndex === i;
    const tabBarInactiveTextColor = this.props.isCompanyDetail
      ? subTitleColor
      : 'rgba(255,255,255,0.8)';
    const tabBarActiveTextColor = this.props.isCompanyDetail ? titleColor : '#fff';
    return (
      <TouchableOpacity onPress={() => this.props.goToPage(i)} key={tab}>
        <View style={styles.tabItem}>
          <Text
            style={{
              fontSize,
              color: showLine ? tabBarActiveTextColor : tabBarInactiveTextColor,
              textAlign: 'center',
            }}
          >
            {tab}{' '}
          </Text>
          {showLine ? (
            <View
              style={{
                backgroundColor: this.props.isCompanyDetail ? baseBlueColor : '#fff',
                height: 2,
                marginTop: 5,
                width: 20,
              }}
            />
          ) : (
            <View
              style={{
                height: 2,
                marginTop: 5,
              }}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    return (
      <View
        style={[
          styles.tabs,
          { backgroundColor: this.props.isCompanyDetail ? '#fff' : baseBlueColor },
        ]}
      >
        <View style={styles.tab}>
          <View style={styles.tabRight} />
          <View style={styles.tabContent}>
            {this.props.tabs.map((tab, i) => this.renderTabOption(tab, i))}
          </View>
          <View style={styles.tabRight}>
            {this.props.tabs.length === 1 ? this.props.renderRight : <View />}
          </View>
        </View>
      </View>
    );
  }
}
