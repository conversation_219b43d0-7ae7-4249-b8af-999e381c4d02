import React from 'react';
import styles from '../themes/enterprise';
import Icon from './icons/icon';
import { Image } from './index';
import resIcon from '../res';

/**
 * 功能：表格右侧箭头
 * 描述：
 * 作者：孙宇强
 */
export default class RightArrow extends React.Component {
  color = styles.get('theme').arrowColor;

  render() {
    const { useImgArrow } = this.props;
    return useImgArrow ? (
      <Image source={resIcon.jobRightArrowEnterprise} />
    ) : (
      <Icon name="chevron-right" size={12} type="font-awesome" color={this.color} {...this.props} />
    );
  }
}
