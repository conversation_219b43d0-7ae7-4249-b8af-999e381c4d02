import React from 'react';
import Image from './image';
import res from '../res';

const avatarSizes = {
  small: 34,
  medium: 50,
  large: 75,
  xlarge: 150,
};

export default class MyAvatar extends React.Component {
  render() {
    let { size = avatarSizes.medium, rounded = true, ...rest } = this.props;
    if (typeof size === 'string') {
      size = avatarSizes[size] || avatarSizes.medium;
    }
    const style = { width: size, height: size };
    if (rounded) {
      style.borderRadius = size / 2;
    }
    return <Image style={style} defaultSource={res.defaultAvatar} {...rest} />;
  }
}
