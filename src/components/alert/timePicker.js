import React, { Component } from 'react';
import Modal from 'react-native-modalbox';
import { BackHandler } from '../index';
import Picker from 'react-native-picker';
import util from '../../util';
import { pickerToolBarHeight } from '../../common';

export default class TimePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
    };
    this.picker = Picker;
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    if (this.isShow) {
      this.hide();
      return true;
    }
    return false;
  };

  getSelectedValue = () => this.selectedValue || this.props.selectedValue;

  /**
   *初始化联动插件
   */
  initPicker = () => {
    const data = util.selectTimes();
    const { title, confirmText, cancelText } = this.props;
    const selectedValue = this.getSelectedValue() || ['09', '00'];
    this.picker.init({
      pickerData: data,
      selectedValue,
      pickerTitleText: title,
      pickerConfirmBtnText: confirmText,
      pickerCancelBtnText: cancelText,
      pickerToolBarHeight,
      pickerToolBarBg: [255, 255, 255, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [83, 137, 245, 1],
      pickerCancelBtnColor: [102, 102, 102, 1],
      pickerTitleColor: [0, 0, 0, 1],
      pickerFontColor: [0, 0, 0, 1],
      onPickerConfirm: this.onPickerConfirm,
      onPickerCancel: this.hide,
    });
  };

  onPickerConfirm = item => {
    this.selectedValue = item;
    this.hide();
    if (this.props.onPickerChanged) {
      this.props.onPickerChanged(item);
    }
  };

  show = () => {
    this.initPicker();
    this.setState({ isOpen: true });
    this.picker.show();
    this.isShow = true;
  };

  hide = () => {
    this.setState({ isOpen: false });
    this.picker.hide();
    this.isShow = false;
  };

  render() {
    return (
      <Modal
        style={{ height: 1 }}
        position="bottom"
        isOpen={this.state.isOpen}
        onClosed={this.hide}
        swipeToClose={false}
        backdropPressToClose={false}
      />
    );
  }
}
