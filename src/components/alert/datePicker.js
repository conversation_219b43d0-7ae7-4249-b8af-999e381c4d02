import React, { Component } from 'react';
import { View } from '../index';
import Picker from 'react-native-picker';
import util from '../../util';
import { pickerToolBarHeight } from '../../common';

export default class DatePicker extends Component {
  constructor(props) {
    super(props);
    this.picker = Picker;
  }

  /**
   *初始化联动插件
   */
  initPicker = () => {
    const that = this;
    const data = util.dates();
    const defaultValue = this.getDefaultValue();
    const { selectedValue, title, confirmText, cancelText } = this.props;
    this.picker.init({
      pickerData: data,
      selectedValue: selectedValue || defaultValue,
      pickerTitleText: title,
      pickerConfirmBtnText: confirmText,
      pickerCancelBtnText: cancelText,
      pickerToolBarHeight,
      pickerToolBarBg: [255, 255, 255, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [83, 137, 245, 1],
      pickerCancelBtnColor: [102, 102, 102, 1],
      pickerTitleColor: [0, 0, 0, 1],
      pickerFontColor: [0, 0, 0, 1],
      onPickerConfirm: (item) => {
        if (that.props.onPickerChanged) {
          that.props.onPickerChanged(item);
        }
      },
      onPickerCancel: () => {
        that.picker.hide();
      },
    });
  };

  show = () => {
    this.initPicker();
    this.picker.show();
  };

  hide = () => {
    this.picker.hide();
  };

  getDefaultValue = () => {
    const date = new Date();
    const y = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    return [y, month, day];
  };

  render() {
    return <View />;
  }
}
