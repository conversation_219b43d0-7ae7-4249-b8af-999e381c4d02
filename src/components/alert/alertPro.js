import React, { Component } from 'react';
import AlertPro from 'react-native-alert-pro';
import DebounceUtil from '../../util/debounceUtil';
import { inject, observer } from 'mobx-react';

const btnTypeFun = (themeStyle) => {
  return {
    blue: {
      buttonConfirm: {
        backgroundColor: themeStyle.blueColor,
      },
      textConfirm: {
        color: themeStyle.primaryBgColor,
      },
      buttonCancel: {
        backgroundColor: themeStyle.negativeColor,
        borderWidth: 0,
      },
      textCancel: {
        color: themeStyle.blueColor,
      },
    },
  };
};

/**
 * 对话框
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class AlertProComponent extends Component {
  isShow = false;

  componentDidMount() {
    this.toggle();
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    this.toggle();
  }

  onRef = (ref) => {
    this.AlertPro = ref;
  };

  onConfirm = () => {
    DebounceUtil.exec(this, this.props.onConfirm);
  };

  onCancel = () => {
    DebounceUtil.exec(this, this.props.onCancel);
  };

  open = () => {
    if (this.AlertPro && !this.isShow) {
      this.isShow = true;
      this.AlertPro.open();
    }
  };

  close = () => {
    if (this.AlertPro && this.isShow) {
      this.isShow = false;
      this.AlertPro.close();
    }
  };

  toggle = () => {
    if (this.props.visible) {
      this.open();
    } else {
      this.close();
    }
  };

  getButtonStyles = (themeStyle) => {
    const { btnType } = this.props;
    const btnTypeObj = btnTypeFun(themeStyle);
    return btnTypeObj[btnType] || {};
  };

  render() {
    const {
      title,
      customButtonStylesCancel,
      customTextStylesCancel,
      customButtonStylesConfirm,
      customTextStylesConfirm,
      messageStyle = {},
      messageAlign = 'center',
      textConfirmStyle = {},
      themeStyle = this.props.stores.userStore.themeStyle,
      ...rest
    } = this.props;
    const {
      buttonCancel = {},
      buttonConfirm = {},
      textCancel = {},
    } = this.getButtonStyles(themeStyle);
    return (
      <AlertPro
        ref={this.onRef}
        // onConfirm={() => this.AlertPro.close()}
        // onCancel={() => this.AlertPro.close()}
        // onClose={() => {}}
        // message="Are you sure to delete the entry?"
        // textCancel="Cancel"
        // textConfirm="Delete"
        showCancel
        showConfirm
        closeOnPressMask={false}
        closeOnPressBack={false}
        customStyles={{
          container: {
            width: '80%',
            marginHorizontal: 0,
            maxWidth: 1000,
          },
          title: {
            fontSize: title ? themeStyle.fontSizeL : 1,
            fontWeight: themeStyle.fontWeightBold,
            color: themeStyle.titleFontColor,
            lineHeight: title ? 28 : 0,
          },
          message: {
            paddingTop: title ? 10 : 0,
            fontSize: themeStyle.fontSizeL,
            fontWeight: themeStyle.fontWeightRegular,
            color: themeStyle.titleFontColor,
            lineHeight: 22,
            textAlign: messageAlign,
            ...messageStyle,
          },
          buttonCancel: {
            height: 36,
            borderRadius: 5,
            justifyContent: 'center',
            minWidth: '35%',
            paddingVertical: 0,
            backgroundColor: '#E8EDFF',
            ...buttonCancel,
            ...customButtonStylesCancel,
          },
          buttonConfirm: {
            backgroundColor: themeStyle.primaryColor,
            height: 36,
            minWidth: '35%',
            paddingVertical: 0,
            borderRadius: 5,
            justifyContent: 'center',
            ...buttonConfirm,
            ...customTextStylesConfirm,
          },
          textCancel: {
            fontSize: themeStyle.fontSizeM,
            fontWeight: themeStyle.fontWeightRegular,
            color: '#4F5866',
            ...textCancel,
            ...customTextStylesCancel,
          },
          textConfirm: {
            fontSize: themeStyle.fontSizeM,
            fontWeight: themeStyle.fontWeightRegular,
            color: '#fff',
            ...textConfirmStyle,
            ...customTextStylesConfirm,
          },
        }}
        {...rest}
        title={title || ''}
        onConfirm={this.onConfirm}
        onCancel={this.onCancel}
      />
    );
  }
}
