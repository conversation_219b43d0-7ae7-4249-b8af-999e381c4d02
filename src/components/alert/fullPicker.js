import React, { Component } from 'react';
import Modal from 'react-native-modalbox';
import Picker from 'react-native-picker';
import util from '../../util';
import { BackHandler } from 'react-native';
import { pickerToolBarHeight } from '../../common';

export default class FullPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    if (this.isShow) {
      this.hide();
      return true;
    }
    return false;
  };

  getSelectedValue = () => this.selectedValue || this.props.selectedValue;

  getMinDate = dateValue => {
    const minDate = new Date();
    if (this.minDate) {
      minDate.setTime(this.minDate.getTime());
    }

    let selectedDate = new Date();
    selectedDate.setTime(minDate.getTime());
    selectedDate = util.convertSelectedDate(dateValue, selectedDate);
    if (selectedDate.getTime() > minDate.getTime()) {
      minDate.setHours(0);
      minDate.setMinutes(0);
    }
    return minDate;
  };

  /**
   * 显示日期选择控件
   */
  showDatePicker = () => {
    const { setAddFour } = this.props;
    const minDate = this.getMinDate();
    const data = util.datePickerData(minDate);
    const defaultValue = this.getDefaultDateValue(minDate, setAddFour ? 4 : 0);
    let selectedValue = this.getSelectedValue();
    selectedValue =
      (selectedValue && selectedValue.length > 2 && selectedValue.slice(0, 3)) || defaultValue;
    this.showPicker(data, selectedValue, this.onDatePickerConfirm);
  };

  /**
   * 显示时间选择控件
   */
  showTimePicker = () => {
    const { setAddFour } = this.props;
    const minDate = this.getMinDate(this.dateValue);
    const data = util.timePickerData(minDate);
    const defaultValue = this.getDefaultTimeValue(minDate, setAddFour ? 4 : 0);
    let selectedValue = this.getSelectedValue();
    selectedValue =
      (selectedValue && selectedValue.length > 4 && selectedValue.slice(3)) || defaultValue;
    this.showPicker(data, selectedValue, this.onTimePickerConfirm);
  };

  showPicker = (pickerData, selectedValue, onPickerConfirm) => {
    const { title, confirmText, cancelText } = this.props;
    Picker.init({
      pickerData,
      selectedValue,
      pickerTitleText: title,
      pickerConfirmBtnText: confirmText,
      pickerCancelBtnText: cancelText,
      pickerToolBarHeight,
      pickerToolBarBg: [255, 255, 255, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [83, 137, 245, 1],
      pickerCancelBtnColor: [102, 102, 102, 1],
      pickerTitleColor: [0, 0, 0, 1],
      pickerFontColor: [0, 0, 0, 1],
      onPickerConfirm,
      onPickerCancel: this.hide,
      pickerFontSize: 20,
    });
    Picker.show();
  };

  onDatePickerConfirm = item => {
    this.dateValue = item;
    Picker.hide();
    this.showTimePicker();
  };

  onTimePickerConfirm = item => {
    this.hide();
    this.selectedValue = this.dateValue.concat(item);
    if (this.props.onPickerChanged) {
      this.props.onPickerChanged(this.selectedValue);
    }
  };

  show = (minDate, selectedValue) => {
    if (selectedValue) {
      this.selectedValue = selectedValue;
    }
    this.minDate = minDate;
    this.showDatePicker();
    this.setState({ isOpen: true });
    Picker.show();
    this.isShow = true;
  };

  hide = () => {
    this.setState({ isOpen: false });
    Picker.hide();
    this.isShow = false;
  };

  getDefaultDateValue = (date = new Date(), addHour = 0) => {
    date.setHours(date.getHours() + addHour);
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    return [date.getFullYear().toString(), month.toString(), day.toString()];
  };

  getDefaultTimeValue = (date = new Date(), addHour = 0) => {
    date.setHours(date.getHours() + addHour);
    const hours = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
    const minutes = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
    return [hours.toString(), minutes.toString()];
  };

  render() {
    return (
      <Modal
        style={{ height: 1 }}
        position="bottom"
        isOpen={this.state.isOpen}
        onClosed={this.hide}
        swipeToClose={false}
        backdropPressToClose={false}
      />
    );
  }
}
