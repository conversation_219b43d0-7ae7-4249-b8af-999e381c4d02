import React, { Component } from 'react';
import AlertPro from './alertPro';
import I18n from '../../i18n';
import constant from '../../store/constant';

/**
 * 全局对话框
 * <AUTHOR>
 */
export default class GlobalAlert extends Component {
  constructor(props) {
    super(props);
    this.state = {
      alertProps: null,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.globalAlert, this.onAlert);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.globalAlert, this.onAlert);
  }

  onAlert = ({ alertProps }) => {
    this.setState({ alertProps });
  };

  onAlertCancel = () => {
    this.setState({ alertProps: null });
  };

  render() {
    const { alertProps } = this.state;
    return (
      <AlertPro
        visible={!!alertProps}
        // title={I18n.t('alert_title_prompt')}
        // message={I18n.t('msg_network_connect_fail')}
        textConfirm={I18n.t('op_confirm_title')}
        textCancel={I18n.t('op_cancel_title')}
        // onConfirm={this.onAlertConfirm}
        onCancel={this.onAlertCancel}
        // showCancel={false}
        {...(alertProps || {})}
      />
    );
  }
}
