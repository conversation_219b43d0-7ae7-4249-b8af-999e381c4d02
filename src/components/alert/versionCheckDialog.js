import React from 'react';
import { ImageBackground, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>View, Text, View } from 'react-native';
import { BaseComponent, Button } from '..';
import { WIDTH } from '../../common';
import I18n from '../../i18n';
import styles from '../../themes';
import { inject, observer } from 'mobx-react';
import resIcon from '../../res';

function getComponentStyle(themeStyle) {
  return {
    commentBackContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
    backContainer: {
      width: WIDTH(300),
      height: WIDTH(422),
    },
    loadingContainerItem: {
      marginTop: WIDTH(197),
      paddingHorizontal: WIDTH(15),
      flex: 1,
    },
    headerTitle: {
      textAlign: 'center',
      fontSize: themeStyle.fontSizeIVX,
      fontWeight: themeStyle.fontWeightMedium,
      color: themeStyle.titleFontColor,
    },
    subTitle: {
      marginTop: WIDTH(3),
      textAlign: 'center',
      fontSize: themeStyle.fontSizeXL,
      fontWeight: themeStyle.fontWeightMedium,
      color: themeStyle.titleFontColor,
    },
    contentDescContainer: {
      marginVertical: WIDTH(10),
      flex: 1,
    },
    contentDesc: {
      fontSize: themeStyle.fontSizeL,
      color: '#4F5866',
      textAlign: 'center',
    },
    btnGroupContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: WIDTH(18),
    },
    btnContainerStyle: {
      flex: 1,
      height: WIDTH(48),
      alignSelf: 'center',
    },
  };
}

@inject('settingsStore', 'settingsAction')
@observer
export default class VersionCheckDialog extends BaseComponent {
  constructor(props) {
    super(props);
    const { themeStyle } = styles.get(['theme']);
    this.style = getComponentStyle(themeStyle);
    this.state = {
      modalVisible: false,
      newVersionInfo: null,
    };
  }

  componentDidMount() {
    if (!this.props.isClickCheck) {
      this.checkVersion();
    }
  }

  hideModal = () => {
    this.setState({ modalVisible: false });
  };

  checkVersion = async () => {
    try {
      const { isClickCheck, timeout = 0 } = this.props;
      const newVersionInfo = await this.props.settingsAction.checkNewVersion(true, timeout);
      console.log('checkNewVersion newVersionInfo', newVersionInfo);
      if (!newVersionInfo.hasNew) {
        if (isClickCheck) {
          this.showRequestResult(I18n.t('page_versionCheck_is_latest'));
        }
        this.onCancel();
        return;
      }
      let modalVisible;
      if (isClickCheck) {
        modalVisible = newVersionInfo.hasNew;
      } else {
        modalVisible = newVersionInfo.hasForce || newVersionInfo.hasPrompt;
        if (!modalVisible) {
          this.onCancel();
        }
      }
      this.setState({ newVersionInfo, modalVisible });
    } catch (e) {
      console.log('checkNewVersion error', e);
      // this.showRequestResult(I18n.t('payment_msg_network'));
      this.onCancel();
    }
  };

  onCancel = () => {
    this.hideModal();
    if (typeof this.props.onCancelUpgrade === 'function') {
      this.props.onCancelUpgrade();
    }
  };

  update = () => {
    this.hideModal();
    this.forcedUpgrade();
  };

  forcedUpgrade = async () => {
    try {
      const { url } = this.state.newVersionInfo;
      await Linking.openURL(url);
    } catch (e) {
      console.warn('forcedUpgrade', e);
    }
  };

  renderNormalUpdate = () => {
    return (
      <View style={this.style.btnGroupContainer}>
        <Button
          containerStyle={this.style.btnContainerStyle}
          title={I18n.t('page_versionCheck_btn_cancel')}
          titleStyle={{ fontSize: 14, color: '#4F5866' }}
          onPress={this.onCancel}
          btnType="light"
        />
        <View style={{ width: WIDTH(15) }} />
        <Button
          containerStyle={this.style.btnContainerStyle}
          title={I18n.t('page_versionCheck_btn_forcedUpgrade')}
          titleStyle={{ fontSize: 14, color: '#362D1A' }}
          onPress={this.update}
        />
      </View>
    );
  };

  renderForceUpdate = () => (
    <View style={this.style.btnGroupContainer}>
      <Button
        containerStyle={this.style.btnContainerStyle}
        title={I18n.t('page_versionCheck_btn_forcedUpgrade')}
        titleStyle={{ fontSize: 14, color: '#362D1A' }}
        onPress={this.forcedUpgrade}
      />
    </View>
  );

  render() {
    const { modalVisible, newVersionInfo } = this.state;
    if (!newVersionInfo) {
      return null;
    }
    const { hasForce, desc, forceDesc, version } = newVersionInfo;
    return (
      <Modal
        animationType="fade"
        presentationStyle="overFullScreen"
        transparent
        visible={modalVisible}
      >
        <View style={this.style.commentBackContent}>
          <ImageBackground style={this.style.backContainer} source={resIcon.iconUpdateBG}>
            <View style={this.style.loadingContainerItem}>
              <Text style={this.style.headerTitle}>{I18n.t('page_versionCheck_title')}</Text>
              <Text style={this.style.subTitle}>{`V ${version}`}</Text>
              <ScrollView style={this.style.contentDescContainer}>
                <Text style={this.style.contentDesc}>{hasForce ? forceDesc : desc}</Text>
              </ScrollView>
              {hasForce ? this.renderForceUpdate() : this.renderNormalUpdate()}
            </View>
          </ImageBackground>
        </View>
      </Modal>
    );
  }
}
