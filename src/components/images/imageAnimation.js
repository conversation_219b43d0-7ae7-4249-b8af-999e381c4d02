import React from 'react';
import Image from './image';

/**
 * 帧动画组件，即定时切换图片
 * <AUTHOR>
 */
export default class ImageAnimation extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      index: 0,
    };
  }

  componentDidMount() {
    this.startAnim(-1);
  }

  componentWillUnmount() {
    this.clearAnimHandle();
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (!this.animHandle) {
      this.startAnim(-1);
    }
  }

  startAnim = (index = this.state.index) => {
    const { images, isStop, interval = 200 } = this.props;
    if (isStop || !images?.length) {
      this.clearAnimHandle();
      return;
    }
    this.setState({ index: ++index % images.length });
    this.animHandle = setTimeout(this.startAnim, interval);
  };

  clearAnimHandle = () => {
    if (this.animHandle) {
      clearTimeout(this.animHandle);
      this.animHandle = null;
    }
  };

  render() {
    const { index } = this.state;
    const { images, isStop, defaultIndex } = this.props;
    return <Image source={images && images[isStop ? defaultIndex : index]} {...this.props} />;
  }
}
