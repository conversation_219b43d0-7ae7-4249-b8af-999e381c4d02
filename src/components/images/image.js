import React from 'react';
import { Image as ImageComponent, ImageBackground } from 'react-native';

/**
 * 图片组件，如果加载图片失败，将显示设置的默认图
 * <AUTHOR>
 */
export default class Image extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isError: true,
      source: null,
      propSource: null,
      status: undefined,
    };
  }

  /**
   * 获取图片的宽高(以像素为单位)
   * @param uri {string} 图片地址
   * @param success {function} (width, height) => {}
   * @param failure
   */
  static getSize(uri, success, failure) {
    return ImageComponent.getSize(uri, success, failure);
  }

  static getDerivedStateFromProps({ source, checkHttp }, state) {
    if (source !== state.propSource) {
      const propSource = source;
      let isError = false;
      let uri, status;
      if (typeof source === 'object' && Reflect.has(source, 'uri')) {
        uri = source.uri;
        isError = !uri || uri.startsWith('/images');
        if (checkHttp) {
          if (
            !isError &&
            uri.startsWith('http:') &&
            !/^http:\/\/(\d+\.\d+\.\d+\.\d+).+/.test(uri)
          ) {
            status = 'https';
            source = { ...source, uri: source.uri.replace('http', 'https') };
          }
        } else if (uri === state.uri && state.isError) {
          isError = true;
        }
      }
      return {
        isError,
        source,
        propSource,
        uri,
        status,
      };
    }
    return null;
  }

  onError = (e) => {
    console.log('Image onError', this.props.source?.uri, e?.nativeEvent);
    this.props.onError?.();
    let { source, status } = this.state;
    if (status === 'https') {
      status = 'http';
      if (source?.uri?.startsWith('https:')) {
        this.setState({ status, source: { ...source, uri: source.uri.replace('https', 'http') } });
        return;
      }
    }
    this.setState({ isError: true });
  };

  render() {
    const { isError, source } = this.state;
    const { defaultSource, isImageBackground, ...rest } = this.props;
    if (isImageBackground) {
      return (
        <ImageBackground
          {...rest}
          onError={this.onError}
          source={isError ? defaultSource || undefined : source}
        />
      );
    }
    return (
      <ImageComponent
        {...rest}
        onError={this.onError}
        source={isError ? defaultSource || undefined : source}
      />
    );
  }
}
