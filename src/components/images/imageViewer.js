import React from 'react';
import Image<PERSON><PERSON>Viewer from 'react-native-image-zoom-viewer';
import {
  DoubleTapZoomStyle,
  SubsamplingScaleImage,
} from '@wuye/react-native-subsampling-scale-image';
import FastImage from 'react-native-fast-image';
import { Image, StyleSheet, Touchable, View } from '../index';
import resIcon from '../../res';
import NavigationService from '../../navigationService';
import fileUtil from '../../util/fileUtil';
import Debounce from 'debounce-decorator';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  close: {
    position: 'absolute',
    left: 20,
    top: 20,
  },
  closeView: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#23232380',
  },
  menu: {
    position: 'absolute',
    bottom: 120,
    right: 20,
    width: 40,
    height: 40,
  },
  download: {
    position: 'absolute',
    bottom: 50,
    right: 20,
    width: 40,
    height: 40,
  },
});

/**
 * 查看多张图片组件，可左右切换查看，支持手势缩放
 * <AUTHOR>
 */
export default class ImageViewer extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      index: 0,
      whStyle: null,
    };
  }

  onImageChange = (index) => {
    this.setState({ index });
  };

  onAllPhotos = () => {
    NavigationService.navigate('chatImages', { images: this.props.imageUrls });
  };

  savePhoto = () => {
    fileUtil.saveToPhotoByUrl(this.props.imageUrls[this.state.index].url);
  };

  onLayoutContainer = (layoutEvent) => {
    console.log('onLayoutContainer', layoutEvent.nativeEvent.layout);
    this.setWH(layoutEvent.nativeEvent.layout);
  };

  @Debounce(200)
  setWH({ width, height }) {
    this.setState({ whStyle: { width, height } });
  }

  static getDerivedStateFromProps({ index = 0 }, state) {
    if (index !== state.pIndex) {
      return {
        pIndex: index,
        index: index || 0,
      };
    }
    return null;
  }

  renderImage = (props) => {
    if (IS_ANDROID) {
      const { whStyle } = this.state;
      console.log('renderImage', props.source);
      return (
        <SubsamplingScaleImage
          zoomEnabled
          panEnabled
          quickScaleEnabled
          maxScale={3}
          minScale={1}
          doubleTapZoomStyle={DoubleTapZoomStyle.ZOOM_FOCUS_FIXED}
          source={props.source}
          style={whStyle}
        />
      );
    }
    return <FastImage {...props} />;
  };

  render() {
    const { whStyle, index } = this.state;
    const { showMore, showDownload, imageUrls, ...rest } = this.props;
    console.log('imageViewer render', index, whStyle);
    return (
      <View style={styles.container} onLayout={this.onLayoutContainer}>
        {whStyle && imageUrls?.length ? (
          <>
            <ImageZoomViewer
              enableImageZoom={IS_IOS}
              saveToLocalByLongPress={false} //是否开启长按保存
              imageUrls={imageUrls}
              // onClick={onClose}
              onChange={this.onImageChange}
              useNativeDriver={true}
              maxOverflow={IS_ANDROID ? whStyle.width / 20 : undefined} // 这里主要处理图片放大后左右滑动到下一页问题，不过也不理想
              useThisWH={IS_ANDROID}
              renderImage={this.renderImage}
              {...rest}
              index={index}
              style={whStyle}
            />
            {showMore ? (
              <Touchable onPress={this.onAllPhotos} style={styles.menu}>
                <Image source={resIcon.chatMenu} />
              </Touchable>
            ) : null}
            {showDownload && imageUrls[index]?.url?.startsWith('http') ? (
              <Touchable onPress={this.savePhoto} style={styles.download}>
                <Image source={resIcon.chatDownload} />
              </Touchable>
            ) : null}
          </>
        ) : null}
      </View>
    );
  }
}
