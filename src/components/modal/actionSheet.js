import React from 'react';
import RNActionSheet from 'react-native-actionsheet';
import I18n from '../../i18n';
import constant from '../../store/constant';

/**
 * 底部选项弹窗
 */
export default class ActionSheet extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      param: null,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.showActionSheet, this.onShow);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.showActionSheet, this.onShow);
  }

  onShow = (param) => {
    try {
      if (!param || this.props.page !== param.page) return;
      this.setState({ param });
    } catch (e) {
      console.warn('actionSheet onShow', e);
    }
  };

  onActionSheetRef = (ref) => {
    ref?.show();
  };

  onPress = (index) => {
    const { param } = this.state;
    const { options } = this;
    this.setState({ param: null });
    if (index < options.length) {
      const onPress = options[index].onPress || param.onPress || this.props.onPress;
      onPress?.(options[index]);
    }
  };

  get options() {
    return this.state.param.options || this.props.options || [];
  }

  render() {
    const { param } = this.state;
    if (!param) return null;
    const options = this.options.map((item) => item.title || item);
    options.push(I18n.t('op_cancel_title'));
    return (
      <RNActionSheet
        ref={this.onActionSheetRef}
        options={options}
        cancelButtonIndex={options.length - 1}
        onPress={this.onPress}
      />
    );
  }
}
