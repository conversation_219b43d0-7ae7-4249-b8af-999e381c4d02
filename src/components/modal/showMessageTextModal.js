import React, { Component } from 'react';
import { Modal, Text, Touchable, View } from '../index';
import styles from '../../themes';
import constant from '../../store/constant';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      flex: 1,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: 18,
    },
    contentText: {
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightRegular,
      fontSize: theme.fontSizeXXL,
    },
  };
}

/**
 * 显示消息文本内容弹窗
 * <AUTHOR>
 */
export default class ShowMessageTextModal extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.state = {
      visible: false,
      hide: true,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.showQuoteMessageText, this.onShow);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.showQuoteMessageText, this.onShow);
  }

  onShow = ({ content }) => {
    this.content = content;
    this.setState({
      visible: true,
      hide: false,
    });
  };

  onRequestClose = () => {
    this.setState({ visible: false }, () => {
      this.content = null;
      this.setState({ hide: true });
    });
  };

  onStartShouldSetResponder = () => true;

  render() {
    const { hide, visible } = this.state;
    if (hide) return null;
    const { content } = this;
    const { style } = this;
    return (
      <Modal
        animationType="fade"
        presentationStyle="fullScreen"
        onRequestClose={this.onRequestClose}
        transparent={false}
        visible={visible}
      >
        <Touchable onPress={this.onRequestClose} style={style.container} withoutFeedback>
          <View style={style.contentContainer} onPress={this.onRequestClose}>
            <Text
              style={style.contentText}
              onStartShouldSetResponder={this.onStartShouldSetResponder}
              selectable
            >
              {content}
            </Text>
          </View>
        </Touchable>
      </Modal>
    );
  }
}
