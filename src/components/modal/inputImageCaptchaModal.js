import React, { Component } from 'react';
import Modal from 'react-native-modalbox';
import { Button, Image, Text, TextInput, Touchable, View } from '../index';
import styles from '../../themes';
import I18n from '../../i18n';
import userService from '../../api/userService';
import uiUtil from '../../util/uiUtil';

function getComponentStyle(theme) {
  return {
    model: {
      width: '90%',
      borderRadius: 5,
    },
    contentContainer: {
      backgroundColor: '#fff',
      marginHorizontal: 15,
      paddingBottom: 20,
      paddingTop: 5,
    },
    inputLabel: {
      fontSize: 15,
      fontWeight: theme.fontWeightMedium,
      color: theme.primaryFontColor,
      marginVertical: 15,
    },
    inputStyle: {
      backgroundColor: '#F7FAFD',
      height: 50,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: '#EFF1F3',
      paddingHorizontal: 10,
      fontSize: 16,
      fontWeight: 'bold',
      flex: 1,
      marginRight: 10,
    },
    selectionColor: '#5D6CC1',
    placeholderTextColor: '#C7D0D6',
    image: { height: 50, width: 100 },
    buttonsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 25,
    },
    btn: { flex: 1 },
  };
}

/**
 * 调用发送手机验证码接口，如果需要输入图片验证码，则弹窗输入
 */
export default class InputImageCaptchaModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.phoneSet = new Set();
    this.state = {
      height: 100,
      isOpen: false,
      isHide: true,
      imageSource: undefined,
    };
  }

  getImageSource = () => {
    this.phoneSet.add(this.phone);
    const uri = userService.getImageCaptchaUrl({
      phone: this.phone,
    });
    console.log('InputImageCaptchaModal getImageSource', uri);
    return { uri };
  };

  onRefreshImage = () => {
    this.setState({ imageSource: this.getImageSource() });
  };

  getPhone = (param) => `${param.regionCode}${param.mobile}`;

  sendImPhoneCode = async (options, func) => {
    let { param } = options;
    // 处理imageCaptcha可能是点按事件的event
    if (typeof param.imageCaptcha !== 'string') {
      param = { ...param };
      delete param.imageCaptcha;
    }
    // 检查同一手机号之前就需要输入图片验证码，此时直接弹窗输入
    if (this.hasImageCaptcha(options)) {
      return Promise.reject({ requestCaptcha: true });
    }
    try {
      uiUtil.showGlobalLoading();
      const res = await func(param);
      this.onCheckError({ ...options, error: res });
      return res;
    } catch (e) {
      console.warn('InputImageCaptchaModal sendImPhoneCode', e);
      if (this.onCheckError({ ...options, error: e })) {
        return Promise.reject({ requestCaptcha: true });
      }
      uiUtil.showRequestResult();
      return Promise.reject(e);
    }
  };

  hasImageCaptcha = (options) => {
    const b = !options.param.imageCaptcha && this.phoneSet.has(this.getPhone(options.param));
    if (b) {
      this.onCheckError(options, true);
    }
    return b;
  };

  onCheckError = (options, isShow) => {
    const { error: e, param } = options;
    console.warn('InputImageCaptchaModal onCheckError', e);
    uiUtil.showRequestResult();

    if (isShow || e?.status === 215001) {
      this.options = options;
      this.phone = this.getPhone(param);
      this.value = null;
      this.setState({ isOpen: true, isHide: false, imageSource: this.getImageSource() });
      return true;
    }
  };

  onConfirm = () => {
    const value = this.value?.trim();
    if (!value) {
      toast.show(I18n.t('page_login_op_code_required'));
      return;
    }
    this.close();
    this.options.onConfirm(value);
  };

  onChangeText = (value) => (this.value = value);

  close = () => this.setState({ isOpen: false });

  onClosed = () => {
    this.options = null;
    this.phone = null;
    this.value = null;
    this.setState({ isOpen: false, isHide: true, imageSource: null });
  };

  onContentLayout = (layoutEvent) => {
    this.setState({ height: layoutEvent.nativeEvent.layout.height });
  };

  render() {
    const { isOpen, isHide, height, imageSource } = this.state;
    if (isHide) return null;
    const { style } = this;
    return (
      <Modal
        style={[style.model, { height }]}
        isOpen={isOpen}
        onClosed={this.onClosed}
        contentHeight={420}
        backdropPressToClose={false}
        swipeToClose={false}
        backButtonClose={false}
      >
        <View style={style.contentContainer} onLayout={this.onContentLayout}>
          <Text style={style.inputLabel}>{I18n.t('page_login_op_code_required')}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TextInput
              style={style.inputStyle}
              onChangeText={this.onChangeText}
              isInput={false}
              maxLength={15}
              placeholderTextColor={style.placeholderTextColor}
              selectionColor={style.selectionColor}
            />
            <Touchable onPress={this.onRefreshImage}>
              <Image source={imageSource} style={style.image} resizeMode="cover" />
            </Touchable>
          </View>
          <View style={style.buttonsContainer}>
            <Button
              title={I18n.t('op_cancel_title')}
              onPress={this.close}
              containerStyle={style.btn}
              outline
              btnType="reset"
            />
            <Button
              title={I18n.t('op_confirm_title')}
              onPress={this.onConfirm}
              containerStyle={[style.btn, { marginLeft: 15 }]}
              btnType={this.props.isEnterprise ? 'login' : 'personalUnregister'}
            />
          </View>
        </View>
      </Modal>
    );
  }
}
