import React, { Component } from 'react';
import { BackHandler, Button, Icon, ScrollView, Text, Touchable, View } from '../index';
import Modal from 'react-native-modalbox';
import styles from '../../themes/enterprise';
import I18n from '../../i18n';
import { reset, setAdjustResize } from '../softInputMode';
import { deviceHeight, footerHeight, statusBarHeight } from '../../common';
import Toast from 'react-native-easy-toast';

function getComponentStyle(theme) {
  return {
    model: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
    },
    headerContainer: {
      height: 60,
      flexDirection: 'row',
      alignItems: 'center',
      paddingRight: 70,
      // backgroundColor: '#F5F8FB',
      backgroundColor: '#fff',
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
    },
    headerTitle: {
      flex: 1,
      paddingHorizontal: 10,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightMedium,
      color: theme.inputLabelFontColor,
      textAlign: 'center',
    },
    headerButtonContainer: {
      width: 70,
      height: 60,
      paddingLeft: theme.containerPaddingHorizontal,
      justifyContent: 'center',
    },
    headerButtonCancelText: {
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryFontColor,
      lineHeight: 60,
    },
    line: {
      backgroundColor: theme.separatorColor,
      height: 1,
    },
    buttonContainer: {
      height: 80,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.containerPaddingHorizontal,
    },
    bottomView: {
      height: footerHeight,
    },
    rightContainer: {
      position: 'absolute',
      top: 0,
      right: theme.containerPaddingHorizontal,
      height: 60,
      justifyContent: 'center',
    },
    rightTitle: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightBold,
      color: theme.stressColor,
    },
  };
}

/**
 *功能：底部弹出框
 *作者：Rays
 */
export default class BottomModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      maxHeight: Number.MAX_VALUE,
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    if (this.props.isOpen) {
      this.props.onClosed();
      return true;
    }
    return false;
  };

  onShow = (tips) => {
    this.myToast.show(tips);
  };

  show = () => {
    this.modal.open();
  };

  close = () => {
    this.modal.close();
  };

  render() {
    const {
      isOpen,
      onClosed, // 当modal完全关闭时候会调用，如果连续多个modal交互需要用到这个属性，不然可能弹不出来第二个
      backdropPressToClose = false,
      handleSoftInput = true,
      contentHeight,
      title,
      headerTitleStyle,
      children,
      buttonTitle,
      buttonAction,
      btnSize = 'md',
      btnType = 'primary',
      btnDisabled = false,
      iconCancel,
      cancelTitle,
      cancelTitleStyle,
      iconSize = 22,
      rightTitle,
      rightAction,
      rightComponent,
      backgroundColor = '#fff',
      coverScreen = false,
      isHead = true,
      buttonComponent,
      useScrollContent = true,
      showCancel = true,
      showBottomView = true,
      positionValue = 0,
    } = this.props;
    const { ...rest } = this.props;

    const { themeStyle } = styles.get(['theme']);
    const modalStyle = getComponentStyle(themeStyle);
    let height = contentHeight + modalStyle.headerContainer.height + modalStyle.bottomView.height;
    if (buttonTitle) {
      height += modalStyle.buttonContainer.height;
    }
    const availableHeight = deviceHeight - statusBarHeight;
    // console.log('BottomModal height', height, availableHeight);
    height = Math.min(height, availableHeight);
    if (handleSoftInput) {
      if (isOpen) {
        setAdjustResize();
      } else {
        reset();
      }
    }
    height = Math.min(this.state.maxHeight, height);
    // console.log('layoutEvent 0', this.state.maxHeight, height, availableHeight);
    return (
      <Modal
        ref={(ref) => (this.modal = ref)}
        style={[modalStyle.model, { height, backgroundColor }]}
        position="bottom"
        isOpen={isOpen}
        onClosed={onClosed}
        swipeToClose={false}
        backdropPressToClose={backdropPressToClose}
        onLayout={(layoutEvent) => {
          // console.log('layoutEvent 1', layoutEvent.nativeEvent.layout, height, availableHeight);
          this.setState({
            maxHeight: layoutEvent.nativeEvent.layout.height - statusBarHeight,
          });
        }}
        coverScreen={coverScreen}
      >
        {isHead ? (
          <View style={modalStyle.headerContainer}>
            <Touchable onPress={this.close} style={modalStyle.headerButtonContainer}>
              {showCancel ? (
                iconCancel ? (
                  <Icon
                    type="antdesign"
                    name="close"
                    size={iconSize}
                    color={modalStyle.headerButtonCancelText.color}
                  />
                ) : (
                  <Text style={[modalStyle.headerButtonCancelText, cancelTitleStyle]}>
                    {cancelTitle || I18n.t('op_cancel_title')}
                  </Text>
                )
              ) : null}
            </Touchable>
            <Text style={[modalStyle.headerTitle, headerTitleStyle]} numberOfLines={1}>
              {title}
            </Text>
            {rightComponent ? (
              <Touchable style={modalStyle.rightContainer} onPress={rightAction}>
                {rightComponent}
              </Touchable>
            ) : null}
            {rightTitle ? (
              <Touchable style={modalStyle.rightContainer} onPress={rightAction}>
                <Text style={modalStyle.rightTitle}>{rightTitle}</Text>
              </Touchable>
            ) : null}
          </View>
        ) : null}
        {/* <View style={modalStyle.line} /> */}
        {useScrollContent ? (
          <ScrollView
            style={{ flex: 1 }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            {...rest}
          >
            {children}
          </ScrollView>
        ) : (
          children
        )}

        {buttonComponent ? <View>{buttonComponent}</View> : null}
        {buttonTitle ? (
          <View style={modalStyle.buttonContainer}>
            <Button
              title={buttonTitle}
              onPress={buttonAction}
              btnSize={btnSize}
              btnType={btnType}
              disabled={btnDisabled}
              containerStyle={btnSize === 'lg' ? { width: '100%' } : { minWidth: 150 }}
              useMinWidth={btnSize !== 'lg'}
            />
          </View>
        ) : null}
        {showBottomView ? <View style={modalStyle.bottomView} /> : null}
        <Toast
          ref={(toast) => (this.myToast = toast)}
          position="top"
          fadeInDuration={750}
          fadeOutDuration={1000}
          positionValue={positionValue || deviceHeight / 3}
        />
      </Modal>
    );
  }
}
