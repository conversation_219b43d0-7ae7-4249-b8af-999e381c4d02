import React from 'react';
import { StyleSheet } from 'react-native';
import Touchable from '../touchable';
import { Button } from 'react-native-elements';
import styles from '../../themes/enterprise';

const { themeStyle } = styles.get(['theme']);

const fsMap = {
  xsm: themeStyle.fontSizeS,
  s30: themeStyle.fontSizeM,
  s32: themeStyle.fontSizeM,
  s36: themeStyle.fontSizeM,
  s44: themeStyle.fontSizeM,
  sm: themeStyle.fontSizeM,
  md: themeStyle.fontSizeL,
  lg: themeStyle.fontSizeL,
};

const btnTypeMap = {
  primary: {
    backgroundColor: themeStyle.primaryColor,
    color: '#fff',
  },
  info: {
    backgroundColor: themeStyle.minorColor,
  },
  danger: {
    color: themeStyle.collocationColor,
    backgroundColor: themeStyle.stressColor,
    lineColor: themeStyle.mediumFontColor,
  },
  white: {
    color: themeStyle.primaryColor,
    backgroundColor: themeStyle.primaryBgColor,
  },
  pay: {
    backgroundColor: themeStyle.companyPayColor,
  },
  merchantPay: {
    backgroundColor: themeStyle.merchantPayColor,
  },
  merchantFilter: {
    backgroundColor: themeStyle.primaryColor,
  },
  guide: {
    backgroundColor: themeStyle.blueColor,
  },
  blue: {
    backgroundColor: themeStyle.blueColor,
  },
  negative: {
    color: themeStyle.blueColor,
    backgroundColor: themeStyle.negativeColor,
  },
  gray: {
    color: themeStyle.primaryFontColor,
    backgroundColor: 'transparent',
  },
  default: {
    color: themeStyle.btnFontColor,
    backgroundColor: themeStyle.primaryColor,
  },
  simple: {
    color: themeStyle.primaryBgColor,
    backgroundColor: themeStyle.primaryBgColor,
  },
  line: {
    color: themeStyle.collocationColor,
    backgroundColor: themeStyle.collocationColor,
    lineColor: themeStyle.mediumFontColor,
  },
  light: {
    backgroundColor: '#E8EDFF',
  },
  success: {
    color: 'white',
    backgroundColor: themeStyle.buyColor,
  },
  buy: {
    color: themeStyle.simpleFontColor,
    backgroundColor: themeStyle.buyColor,
  },
  sell: {
    color: themeStyle.simpleFontColor,
    backgroundColor: themeStyle.sellColor,
  },
  redPacket: {
    color: themeStyle.simpleFontColor,
    backgroundColor: themeStyle.redPacketColor,
  },
  register: {
    color: themeStyle.collocationColor,
    backgroundColor: '#E8EDFF',
  },
  // reset: {
  //   color: themeStyle.stressColor,
  //   backgroundColor: 'transparent',
  // },
  login: {
    color: themeStyle.primaryBgColor,
    backgroundColor: themeStyle.primaryColor,
  },
  reset: {
    color: themeStyle.titleFontColor,
    backgroundColor: '#CCCCCC',
  },
  viewRecord: {
    color: themeStyle.primaryColor,
    backgroundColor: '#FDEEEF',
  },
  sendCode: {
    color: '#384A7C',
    backgroundColor: '#384A7C',
  },
  chatSendResume: {
    color: '#3299FF',
    backgroundColor: '#E7F1FF',
  },
  buyServicePackage: {
    color: themeStyle.primaryColor,
    backgroundColor: 'transparent',
  },
  personalUnregister: {
    color: themeStyle.primaryBgColor,
    backgroundColor: '#2089DC',
  },
};

const sizeMap = {
  xsm: 25,
  s30: 30,
  s32: 32,
  s36: 36,
  sm: 37,
  md: 40,
  s44: 44,
  lg: 48,
};

/**
 * 按钮组件
 * 尺寸： 大、小
 * 类型：solid, outline
 * 背景：primary, info, danger
 * author: Rays
 */
export default class ButtonComponent extends React.Component {
  getBtnTypeObj = () => {
    const { btnType } = this.props;
    if (btnType && btnTypeMap[btnType]) {
      return {
        ...btnTypeMap.default,
        ...btnTypeMap[btnType],
      };
    }
    return btnTypeMap.default;
  };

  getTitleStyle = () => {
    const { btnSize, outline, titleStyle = {} } = this.props;
    const btnTypeObj = this.getBtnTypeObj();
    if (outline) {
      return {
        color: btnTypeObj.color,
        fontSize: fsMap[btnSize] || 16,
        lineHeight: sizeMap[btnSize] || 48,
        paddingTop: 0,
        paddingBottom: 0,
        ...titleStyle,
      };
    }
    return {
      color: btnTypeObj.color,
      fontSize: fsMap[btnSize] || 16,
      lineHeight: sizeMap[btnSize] || 48,
      paddingTop: 0,
      paddingBottom: 0,
      fontWeight: '400',
      ...titleStyle,
    };
  };

  getButtonStyle = () => {
    const { btnSize, outline, borderRadius = 5 } = this.props;
    const btnTypeObj = this.getBtnTypeObj();
    if (outline) {
      return {
        height: sizeMap[btnSize] || 48,
        borderColor: btnTypeObj.lineColor ? btnTypeObj.lineColor : btnTypeObj.backgroundColor,
        borderWidth: 1,
        borderRadius,
        padding: 0,
      };
    }
    return {
      height: sizeMap[btnSize] || 48,
      borderRadius,
      backgroundColor: btnTypeObj.backgroundColor,
      padding: 0,
    };
  };

  getContainerStyle = () => {
    const { containerStyle, useMinWidth = true } = this.props;
    const minWidthStyle = {
      minWidth: 150,
    };
    if (containerStyle && Object.keys(containerStyle).length) {
      return useMinWidth ? StyleSheet.flatten([minWidthStyle, containerStyle]) : containerStyle;
    }
    return useMinWidth ? minWidthStyle : null;
  };

  render() {
    const { outline, disabled } = this.props;
    const btnTypeObj = this.getBtnTypeObj();
    return (
      <Button
        buttonStyle={this.getButtonStyle()}
        containerStyle={this.getContainerStyle()}
        TouchableComponent={Touchable}
        disabledStyle={{ backgroundColor: btnTypeObj.backgroundColor, opacity: 0.6 }}
        disabledTitleStyle={{
          color: btnTypeObj.color,
        }}
        {...this.props}
        disabled={!!disabled}
        titleStyle={this.getTitleStyle()}
        type={outline ? 'outline' : 'solid'}
        titleProps={{ numberOfLines: 1 }}
      />
    );
  }
}
