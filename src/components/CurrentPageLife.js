import React from 'react';
import { View } from './index';
import { setAdjustResize, setAdjustPan } from './softInputMode';
import AppModule from '../modules/AppModule';

/**
 * 当前页面生命感知
 * 页面显示时设置一些系统状态，如屏幕常亮
 * 跳到其他页面时恢复状态，如关闭屏幕常亮
 * <AUTHOR>
 */
export default class CurrentPageLife extends React.Component {
  componentDidMount() {
    if (!this.props.navigation) return;
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.onWillFocus);
    this.willBlurSubscription = this.props.navigation.addListener('willBlur', this.onWillBlur);
  }

  componentWillUnmount() {
    console.log('CurrentPageLife componentWillUnmount');
    if (!this.willFocusSubscription) return;
    this.willFocusSubscription.remove();
    this.willBlurSubscription.remove();
    this.changeState(false);
  }

  onWillBlur = () => {
    console.log('CurrentPageLife onWillBlur');
    this.changeState(false);
  };

  onWillFocus = () => {
    console.log('CurrentPageLife onWillFocus');
    this.changeState(true);
  };

  changeState = (isFocus) => {
    this.setSoftInputMode(isFocus);
    this.setKeepScreenOn(isFocus);
  };

  /**
   * 设置输入法弹出后，页面滚动到输入框位置（默认会将整个页面上移，导致header不可见）
   * @param isFocus
   */
  setSoftInputMode = (isFocus) => {
    if (!this.props.isSoftInputMode) return;
    console.log('CurrentPageLife setSoftInputMode');
    if (isFocus) {
      setAdjustResize();
    } else {
      setAdjustPan();
    }
  };

  /**
   * 设置屏幕常亮
   * @param isFocus
   */
  setKeepScreenOn = (isFocus) => {
    if (!this.props.isKeepScreenOn) return;
    console.log('CurrentPageLife setKeepScreenOn');
    AppModule.keepScreenOn(isFocus);
  };

  render() {
    return <View />;
  }
}
