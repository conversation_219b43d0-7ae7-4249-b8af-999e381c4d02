import React, { Component } from 'react';
import { ScrollView, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ListItem } from 'react-native-elements';
import EvilIcon from 'react-native-vector-icons/EvilIcons';
import Modal from 'react-native-modalbox';
import { titleColor, subTitleColor } from '../themes';
import { deviceHeight, footerHeight } from '../common';
import res from '../res';
import I18n from '../i18n';
import Image from './image';

const countryCodelist = [
  {
    id: 1,
    name: 'Cambodia',
    flag: res.khFlag,
    code: '855',
    label: '+855(0)',
    pattern: /^[1-9]\d{7,8}$/,
  },
  {
    id: 2,
    name: 'China(中国)',
    flag: res.cnFlag,
    code: '86',
    label: '+86',
    pattern: /^1([358][0-9]|4[579]|66|7[0135678]|9[189])[0-9]{8}$/,
  },
  {
    id: 3,
    name: 'Hong Kong, China',
    flag: res.hkFlag,
    code: '852',
    label: '+852',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 4,
    name: 'Macau, China',
    flag: res.moFlag,
    code: '853',
    label: '+853',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 5,
    name: 'Taiwan, China',
    flag: res.twFlag,
    code: '886',
    label: '+886',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 6,
    name: 'Thailand',
    flag: res.thFlag,
    code: '66',
    label: '+66',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 7,
    name: 'Malaysia',
    flag: res.myFlag,
    code: '60',
    label: '+60',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 8,
    name: 'Singapore',
    flag: res.sgFlag,
    code: '65',
    label: '+65',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 9,
    name: 'Myanmar',
    flag: res.etFlag,
    code: '95',
    label: '+95',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 10,
    name: "Lao People's Democratic Republic",
    flag: res.laFlag,
    code: '856',
    label: '+856',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 11,
    name: 'Vietnam',
    flag: res.vnFlag,
    code: '84',
    label: '+84',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 12,
    name: 'Korea',
    flag: res.krFlag,
    code: '82',
    label: '+82',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 13,
    name: 'Japan',
    flag: res.jpFlag,
    code: '81',
    label: '+81',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 14,
    name: 'Indonesia',
    flag: res.mcFlag,
    code: '62',
    label: '+62',
    pattern: /^\d{6,15}$/,
  },
  {
    id: 15,
    name: 'Philippines',
    flag: res.phFlag,
    code: '63',
    label: '+63',
    pattern: /^\d{6,15}$/,
  },
];

export default class CountryCode extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onItemClick = (item) => {
    this.props.onSelected(item);
    this.modal.close();
  };

  onClosed = () => {
    this.props.onSelected();
  };

  render() {
    return (
      <Modal
        style={styles.bottomPhoneAreaModel}
        position="bottom"
        ref={(ref) => {
          this.modal = ref;
        }}
        onClosed={this.onClosed}
        isOpen={this.props.isOpen}
      >
        <View style={styles.bottomModelContainer}>
          <View style={styles.bottomModelTitleWrap}>
            <Text style={styles.bottomModelTitle}>{I18n.t('component_countryCode_title')}</Text>
            <TouchableOpacity
              style={styles.bottomModelClose}
              onPress={() => {
                this.modal.close();
              }}
            >
              <EvilIcon name="close" size={30} color={subTitleColor} />
            </TouchableOpacity>
          </View>
          <View style={styles.bottomModelBody}>
            <ScrollView showsVerticalScrollIndicator={false}>
              {countryCodelist.map((item) => (
                <ListItem
                  onPress={() => {
                    this.onItemClick(item);
                  }}
                  containerStyle={{ padding: 10 }}
                  key={item.id}
                  leftIcon={<Image source={item.flag} style={{ width: 20, height: 15 }} />}
                  title={item.name}
                  titleStyle={{ color: titleColor }}
                  rightTitle={item.label}
                />
              ))}
              <View style={{ height: footerHeight + 36 }} />
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  bottomPhoneAreaModel: {
    height: deviceHeight / 2,
  },
  bottomModelContainer: {
    flex: 1,
  },
  bottomModelTitleWrap: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 5,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  bottomModelTitle: {
    fontSize: 16,
    color: titleColor,
    lineHeight: 40,
  },
  bottomModelClose: {
    position: 'absolute',
    right: 15,
    top: 10,
  },
  bottomModelBody: {
    padding: 15,
  },
});
