import React, { Component } from 'react';
import { View, StyleSheet, Modal, Text, TouchableOpacity, Linking, ScrollView } from 'react-native';
import DeviceInfo from '../util/deviceInfo';
import { deviceWidth, deviceHeight } from '../common';
import { inject, observer } from 'mobx-react';
import I18n from '../i18n';
import res from '../res';
import { baseBlueColor, titleColor } from '../themes/base';
import ImageBackground from './imageBackground';
import util from '../util';
import configs from '../configs';

const styles = StyleSheet.create({
  commentBackContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  loadingContainerItem: {
    marginTop: -36,
    borderRadius: 6,
    width: deviceWidth - 80,
    backgroundColor: '#fff',
  },
  imageBackgroundContiner: {
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
  },
  imageBackgroundStyle: {
    width: '100%',
    height: 64,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  contentDescContainer: {
    paddingHorizontal: 12,
    paddingTop: 12,
    maxHeight: deviceHeight / 2 + 60,
  },
  contentDesc: {
    fontSize: 14,
    paddingLeft: 10,
    color: titleColor,
    lineHeight: 24,
  },
  btngroupContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginVertical: 20,
    width: deviceWidth - 80,
  },
  afterTitle: {
    width: deviceWidth / 2 - 88,
    color: baseBlueColor,
    borderColor: baseBlueColor,
    borderWidth: 1,
    fontSize: 14,
    paddingVertical: 11,
    textAlign: 'center',
  },
  updateTitle: {
    width: deviceWidth / 2 - 88,
    color: '#fff',
    backgroundColor: baseBlueColor,
    borderColor: baseBlueColor,
    borderWidth: 1,
    fontSize: 14,
    paddingVertical: 11,
    textAlign: 'center',
  },
});

@inject(
  'jobStore',
  'jobAction',
  'resumeStore',
  'resumeAction',
  'cityAction',
  'userAction',
  'applicationAction',
  'personStore'
)
@observer
export default class checkVersionModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showVersionModal: false,
      versionDesc: '',
      updateForcedly: false,
      downloadAddress: '',
    };
  }

  componentDidMount() {
    this.checkAppVersionNode();
  }

  checkAppVersionNode = async () => {
    const versionInfo = await this.props.applicationAction.getAppVersion();
    if (!versionInfo || !versionInfo.version) {
      return;
    }
    const onlineVersionArray = versionInfo.version.split('.');
    const currentVersionArray = DeviceInfo.getVersion().split('.');
    if (util.onCheckVersion(onlineVersionArray, currentVersionArray)) {
      this.onChangeAppVersion(versionInfo);
    }
  };

  onChangeAppVersion = (versionInfo) => {
    this.setState({
      showVersionModal: true,
      versionDesc: versionInfo.memo,
      updateForcedly: versionInfo.updateForcedly,
      downloadAddress: versionInfo.downloadAddress,
    });
  };

  dismiss = () => {
    this.setState({ showVersionModal: false });
  };

  update = () => {
    this.dismiss();
    this.forcedUpgrade();
  };

  forcedUpgrade = () => {
    const { downloadAddress } = this.state;
    const defaultDownloadAddress = IS_IOS ? configs.iosStoreUrl : configs.androidStoreUrl;
    Linking.openURL(
      downloadAddress && downloadAddress.indexOf('http') > -1
        ? downloadAddress
        : defaultDownloadAddress
    );
  };

  renderNoramlUpdate = () => (
    <View style={styles.btngroupContainer}>
      <TouchableOpacity onPress={this.dismiss}>
        <Text style={styles.afterTitle}>{I18n.t('page_version_check_next_title')}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={this.update}>
        <Text style={styles.updateTitle}>{I18n.t('page_version_check_download_title')}</Text>
      </TouchableOpacity>
    </View>
  );

  renderForceUpdate = () => (
    <View style={styles.btngroupContainer}>
      <TouchableOpacity onPress={this.forcedUpgrade}>
        <Text style={[styles.updateTitle, { width: deviceWidth - 168 }]}>
          {I18n.t('page_version_check_download_title')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  render() {
    const { showVersionModal, versionDesc, updateForcedly } = this.state;
    return (
      <Modal
        onRequestClose={() => {
          this.props.dismiss();
        }}
        animationType="fade"
        presentationStyle="overFullScreen"
        transparent
        visible={showVersionModal}
      >
        <View style={styles.commentBackContent}>
          <View style={styles.loadingContainerItem}>
            <ImageBackground
              source={res.versionBg}
              imageStyle={styles.imageBackgroundContiner}
              style={styles.imageBackgroundStyle}
            >
              <Text style={styles.headerTitle}>{I18n.t('page_version_check_upadte_title')}</Text>
            </ImageBackground>
            <View style={styles.contentDescContainer}>
              <ScrollView>
                <Text style={styles.contentDesc}>{versionDesc}</Text>
              </ScrollView>
            </View>
            {updateForcedly ? this.renderForceUpdate() : this.renderNoramlUpdate()}
          </View>
        </View>
      </Modal>
    );
  }
}
