import React from 'react';
import Toast from 'react-native-easy-toast';
import I18n from '../i18n';

export function getErrorMessage(error) {
  if (!error) return null;
  try {
    if (error instanceof Error) {
      error = error.message || error.toString();
    } else if (error instanceof Object) {
      error = error.message || JSON.stringify(error);
    }
    if (typeof error === 'string') {
      if (error === 'Network Error') {
        return I18n.t('msg_network_connect_fail');
      }
      return error;
    }
  } catch (e) {
    console.warn('getErrorMessage', e, error);
  }
  return null;
}

export class ToastManager {
  /**
   * 静态toast
   */
  static toast;

  /**
   * 显示toast
   * showToast
   * @param obj
   * @param duration
   * @param callback
   */
  static show(obj, duration = 3000, callback) {
    if (this.toast) {
      obj = getErrorMessage(obj);
      if (obj) {
        this.toast.show(obj, duration, callback);
      }
    }
  }

  /**
   * 关闭toast
   * closeToast
   * @param duration
   */
  static close(duration) {
    if (this.toast) {
      this.toast.close(duration);
    }
  }
}

export default class ToastComponent extends React.Component {
  /**
   * 组件被移除的时候
   */
  componentWillUnmount() {
    ToastManager.toast = null;
  }

  refToast = ref => (ToastManager.toast = ref);

  render() {
    return <Toast ref={this.refToast} position="center" />;
  }
}
