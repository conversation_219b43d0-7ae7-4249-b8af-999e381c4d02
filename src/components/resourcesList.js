import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import res from '../res';
import I18n from '../i18n';
import PageFlatList from './pageFlatList';
import { resourcesStyle } from '../themes';

/**
 * 咨询列表
 */
@inject('resourceAction')
@observer
export default class ResourcesList extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.filterType = props.filterType;
  }

  componentDidMount() {
    global.emitter.on('languageChange', this.onRefresh);
  }

  componentWillUnmount() {
    global.emitter.off('languageChange', this.onRefresh);
  }

  onViewDetail = (item) => {
    console.log('333', item.id);
    this.props.nav.navigate('articleDetail', {
      articleId: item.id,
    });
  };

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  onRefresh = (filterParam) => {
    this.filterParam = filterParam;
    this.pageFlatList && this.pageFlatList.onRefresh();
  };

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        page,
        limit,
        articleType: parseInt(`${this.filterType}0`) + 200,
      };
      const data = await this.props.resourceAction.getAdviserList(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  renderItem = ({ item, index }) => {
    return (
      <View key={index.toString()} style={resourcesStyle.itemContainer}>
        <TouchableOpacity onPress={() => this.onViewDetail(item)}>
          <Text style={resourcesStyle.titleText}>{item?.title}</Text>
          <View style={resourcesStyle.coinContainer}>
            <Text style={resourcesStyle.timeText}>{item.sendAt}</Text>
          </View>
          <View style={resourcesStyle.infoContainer}>
            <Text numberOfLines={3} style={resourcesStyle.contentText}>
              {item.parseContent}
            </Text>
            <Image
              style={resourcesStyle.infoImage}
              source={
                item.images && item.images?.length > 1
                  ? {
                      uri: item.images[1],
                      cache: 'force-cache',
                    }
                  : res.mission
              }
            />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <>
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
