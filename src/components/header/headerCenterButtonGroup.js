import React from 'react';
import { Text, View } from 'react-native';
import { ButtonGroup } from 'react-native-elements';
import styles from '../../themes';
import { getLanguage } from '../../i18n';

function getStyle(theme) {
  return {
    headerButtonGroup: {
      height: 29,
      width: 180,
      borderRadius: 5,
      borderColor: '#fff',
      backgroundColor: theme.primaryColor,
    },
    headerButtonGroupKM: {
      height: 32,
      width: 220,
    },
    headerSelectedButton: {
      backgroundColor: '#fff',
    },
    headerBtnText: {
      fontSize: 14,
      color: '#fff',
    },
    headerBtnTextActive: {
      color: theme.primaryColor,
    },
    badge: {
      position: 'absolute',
      top: 2,
      right: -3,
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.unreadPoint,
    },
    badgeZH: {
      top: -3,
    },
    badgeKM: {
      top: 0,
    },
  };
}

function Button({ item, lang, selected, style }) {
  const content = (
    <Text style={[style.headerBtnText, selected && style.headerBtnTextActive]}>{item.text}</Text>
  );
  if (item.badgeNum && (!item.showBadgeOnlyUnselected || selected)) {
    return (
      <View>
        {content}
        <View style={[style.badge, style[`badge${lang}`]]} />
      </View>
    );
  }
  return content;
}

/**
 * header中间的按钮组
 * author: Rays
 */
export default class HeaderCenterButtonGroup extends React.Component {
  style = getStyle(styles.get('theme'));

  render() {
    const { items, selectedIndex, ...rest } = this.props;
    if (!items?.length) return null;
    const lang = getLanguage().toUpperCase();
    const { style } = this;
    const buttons = items.map((item, index) => {
      return {
        element: () => (
          <Button item={item} lang={lang} selected={selectedIndex === index} style={style} />
        ),
      };
    });
    return (
      <ButtonGroup
        selectedIndex={selectedIndex}
        buttons={buttons}
        containerStyle={[style.headerButtonGroup, style[`headerButtonGroup${lang}`]]}
        selectedButtonStyle={style.headerSelectedButton}
        {...rest}
      />
    );
  }
}
