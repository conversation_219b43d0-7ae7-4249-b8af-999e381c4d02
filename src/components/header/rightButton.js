import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Icon from '../icons/icon';
import Touchable from '../touchable';
import themes from '../../themes/enterprise';

/**
 * 标题栏右边按钮组件
 * 属性：
 * title 标题
 * onPress 点击事件，需有title或icon才生效
 * permission 权限，可选
 * icon 右边icon按钮
 * iconType 右边icon类型，参考https://react-native-training.github.io/react-native-elements/docs/icon.html
 *
 * author: Rays
 */
export default class RightButton extends React.Component {
  render() {
    const { headerStyle } = themes.get(['header']);
    const {
      onPress,
      title,
      icon,
      iconColor,
      iconType = 'antdesign',
      titleColor,
      rightBtnStyle = {},
    } = this.props;
    if (title) {
      return (
        <Touchable onPress={onPress} style={styles.container}>
          <Text
            style={[headerStyle.rightBtn, titleColor ? { color: titleColor } : {}, rightBtnStyle]}
          >
            {title}
          </Text>
        </Touchable>
      );
    }
    if (icon) {
      return (
        <Touchable onPress={onPress} style={styles.container}>
          <View style={headerStyle.iconContainer}>
            <Icon
              type={iconType}
              name={icon}
              size={headerStyle.iconSize}
              color={iconColor || headerStyle.iconColor}
            />
          </View>
        </Touchable>
      );
    }
    return null;
  }
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
    justifyContent: 'center',
    marginRight: -18,
    paddingRight: 18,
  },
});
