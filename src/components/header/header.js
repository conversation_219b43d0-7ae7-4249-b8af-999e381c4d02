import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Header } from 'react-native-elements';
import styles from '../../themes/enterprise';
import { headerHeight, statusBarHeight } from '../../common';
import Goback from './goback';
import RightButton from './rightButton';
import resIcon from '../../res';
/**
 * 标题栏组件，属性参考https://react-native-training.github.io/react-native-elements/docs/header.html
 * 属性：
 * title 标题
 * hideBack 是否隐藏返回按钮
 * backCallback 自定义返回按钮事件
 * rightTitle 右边文字按钮
 * rightPress 右边点击事件，需有rightTitle或rightIcon才生效
 * rightPermission 右边按钮的权限，可选
 * rightIcon 右边icon按钮
 * rightIconType 右边icon类型，参考https://react-native-training.github.io/react-native-elements/docs/icon.html
 * demo:
 * <Header title="teee" leftIconColor="#fff" containerStyle={{ backgroundColor: 'transparent' }} />
 * author: Rays
 */
export default class HeaderComponent extends React.Component {
  rightComponent = () => {
    const {
      rightPress,
      rightTitle,
      rightPermission,
      rightIcon,
      rightIconType,
      rightIconColor,
      rightTitleColor,
    } = this.props;
    return (
      <RightButton
        onPress={rightPress}
        title={rightTitle}
        titleColor={rightTitleColor}
        permission={rightPermission}
        icon={rightIcon}
        iconType={rightIconType}
        iconColor={rightIconColor}
      />
    );
  };

  getHeader = (barStyle, containerStyle, titleStyle, leftIconColor) => {
    const { headerStyle } = styles.get(['header']);
    const { title, hideBack, backCallback, backgroundImage, ...attributes } = this.props;
    delete attributes.theme;
    const backgroundImageProps = backgroundImage ? { backgroundImage: resIcon.commonBgHeader } : {};
    return (
      <Header
        statusBarProps={{
          barStyle,
          translucent: true,
          backgroundColor: 'transparent',
        }}
        outerContainerStyles={{ borderBottomWidth: 0 }}
        centerComponent={{
          text: title || '',
          style: StyleSheet.flatten([headerStyle.center, titleStyle]),
        }}
        leftComponent={
          hideBack ? null : <Goback callback={backCallback} leftIconColor={leftIconColor} />
        }
        rightComponent={this.rightComponent()}
        {...attributes}
        containerStyle={containerStyle}
        {...backgroundImageProps}
      />
    );
  };

  mergeThemeStyle = (headerStyle, containerStyle = {}) => {
    const { theme, containerStyle: propsContainerStyle, showBottomLine } = this.props;
    let { leftIconColor, titleStyle, barStyle } = this.props;
    if (theme === 'light' || theme === 'blue') {
      barStyle = barStyle || 'light-content';
      containerStyle = StyleSheet.flatten([containerStyle, headerStyle.lightContainerStyle]);
      titleStyle = StyleSheet.flatten([headerStyle.lightTitleStyle, titleStyle]);
      leftIconColor = leftIconColor || headerStyle.lightIconColor;
      if (theme === 'blue') {
        containerStyle = StyleSheet.flatten([containerStyle, headerStyle.blueContainerStyle]);
      }
    }
    containerStyle = StyleSheet.flatten([
      headerStyle.containerStyle,
      containerStyle,
      showBottomLine && headerStyle.bottomLine,
      propsContainerStyle,
    ]);
    barStyle = barStyle || 'dark-content';
    return {
      barStyle,
      containerStyle,
      titleStyle,
      leftIconColor,
    };
  };

  render() {
    const { headerStyle, themeStyle } = styles.get(['header', 'theme']);
    // Android 23以下不能更改状态栏文字颜色，默认是白色
    if (IS_ANDROID && OS_VERSION < 23) {
      const headerContainerStyle = {
        paddingTop: 0,
        height: headerHeight,
      };
      // Android 19以下不能对状态栏做任何改变
      // if (OS_VERSION < 19) {
      //   return this.getHeader(null, containerStyle);
      // }
      const { containerStyle, titleStyle, leftIconColor } = this.mergeThemeStyle(
        headerStyle,
        headerContainerStyle
      );
      let { barBackgroundColor } = this.props;
      const { containerStyle: cs, theme } = this.props;
      if (!barBackgroundColor) {
        barBackgroundColor =
          cs || theme ? containerStyle.backgroundColor : themeStyle.statusBarColor;
      }
      return (
        <View>
          <View
            style={{
              height: statusBarHeight,
              backgroundColor: barBackgroundColor,
            }}
          />
          {this.getHeader('light-content', containerStyle, titleStyle, leftIconColor)}
        </View>
      );
    }
    const { barStyle, containerStyle, titleStyle, leftIconColor } =
      this.mergeThemeStyle(headerStyle);
    return this.getHeader(barStyle, containerStyle, titleStyle, leftIconColor);
  }
}
