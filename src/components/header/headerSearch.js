import React from 'react';
import { View, Text } from 'react-native';
import { Header, Input } from 'react-native-elements';
import styles from '../../themes/enterprise';
import { statusBarHeight, headerHeight } from '../../common';
import { Touchable } from '../index';
import I18n from '../../i18n';

function getComponentStyle(theme) {
  return {
    leftContainer: {
      flex: 3,
    },
    centerContainer: {
      flex: 0,
      display: 'none',
    },
    rightContainer: {
      flex: 0,
      paddingLeft: 20,
    },
    inputContainer: {
      backgroundColor: '#F3F3F9',
      width: '100%',
      paddingLeft: 0,
      paddingRight: 0,
      borderWidth: 1,
      borderColor: '#efefef',
      borderRadius: 18,
    },
    inputText: {
      left: -12,
      fontSize: theme.fontSizeM,
    },
    inputTextContainer: {
      width: '100%',
      height: 36,
      borderWidth: 0,
      borderBottomWidth: 0,
    },
    inputLeftIcon: {
      left: -12,
      paddingRight: 8,
      paddingLeft: 0,
    },
  };
}

/**
 * 标题栏组件，属性参考https://react-native-training.github.io/react-native-elements/docs/header.html
 * 属性：
 * author: Moke
 */
export default class HeaderSearchComponent extends React.Component {
  onChangeText = (text) => {
    if (this.props.onSearch) {
      this.props.onSearch(text);
    }
  };

  onCancel = () => {
    if (this.props.onCancel) {
      this.props.onCancel();
    }
  };

  getHeader = (barStyle, containerStyle) => {
    const { themeStyle } = styles.get(['theme']);
    const componentStyle = getComponentStyle(themeStyle);
    const { placeholder } = this.props;
    return (
      <Header
        statusBarProps={
          barStyle
            ? {
                barStyle,
                translucent: true,
                backgroundColor: 'transparent',
              }
            : null
        }
        containerStyle={containerStyle}
        rightContainerStyle={componentStyle.rightContainer}
        leftContainerStyle={componentStyle.leftContainer}
        centerContainerStyle={componentStyle.centerContainer}
        leftComponent={
          <Input
            placeholder={placeholder}
            containerStyle={componentStyle.inputContainer}
            inputStyle={componentStyle.inputText}
            inputContainerStyle={componentStyle.inputTextContainer}
            leftIcon={{
              type: 'antdesign',
              name: 'search1',
              color: '#999999',
              size: 20,
              containerStyle: componentStyle.inputLeftIcon,
            }}
            clearButtonMode="while-editing"
            underlineColorAndroid="transparent"
            onChangeText={this.onChangeText}
          />
        }
        rightComponent={
          <Touchable onPress={this.onCancel}>
            <Text style={{ color: themeStyle.primaryFontColor }}>
              {I18n.t('page_headerSearch_cancel')}
            </Text>
          </Touchable>
        }
        {...this.props}
      />
    );
  };

  render() {
    const { themeStyle } = styles.get(['theme']);
    // Android 23以下不能更改状态栏文字颜色，默认是白色
    if (IS_ANDROID && OS_VERSION < 23) {
      const headerStyle = {
        backgroundColor: themeStyle.primaryBgColor,
        paddingTop: 0,
        paddingHorizontal: 18,
        height: headerHeight,
      };
      // Android 19以下不能对状态栏做任何改变
      if (OS_VERSION < 19) {
        return this.getHeader(null, headerStyle);
      }
      return (
        <View>
          <View style={{ height: statusBarHeight, backgroundColor: themeStyle.primaryColor }} />
          {this.getHeader('light-content', headerStyle)}
        </View>
      );
    }
    return this.getHeader('dark-content', {
      backgroundColor: themeStyle.primaryBgColor,
      paddingTop: statusBarHeight,
      paddingHorizontal: 18,
      height: headerHeight + statusBarHeight,
    });
  }
}
