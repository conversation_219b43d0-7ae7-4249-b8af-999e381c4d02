import React from 'react';
import { StyleSheet } from 'react-native';
import Icon from '../icons/icon';
import NavigationService from '../../navigationService';
import Touchable from '../touchable';
import resIcon from '../../res';
import Image from '../images/image';

const btnImg = {
  '#fff': resIcon.iconBtnBackWhite,
  '#ffffff': resIcon.iconBtnBackWhite,
  '#333': resIcon.iconBtnBackBlack,
  '#333333': resIcon.iconBtnBackBlack,
  default: resIcon.iconBtnBackBlack,
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    justifyContent: 'center',
    marginLeft: -18,
    paddingHorizontal: 18,
  },
});

export default class GoBack extends React.Component {
  goBack = () => {
    if (this.props.callback) {
      this.props.callback();
    } else {
      NavigationService.goBack();
    }
  };

  render() {
    const { leftIconColor, style } = this.props;
    let img = btnImg[leftIconColor] || btnImg.default;
    return (
      <Touchable onPress={this.goBack} style={style ? [styles.container, style] : styles.container}>
        {img ? (
          <Image source={img} />
        ) : (
          <Icon
            name="left"
            size={24}
            type="antdesign"
            color={this.props.leftIconColor ? this.props.leftIconColor : '#333'}
          />
        )}
      </Touchable>
    );
  }
}
