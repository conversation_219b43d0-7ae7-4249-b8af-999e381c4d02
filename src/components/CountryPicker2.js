import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SectionList,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { Icon } from './index';
import PropTypes from 'prop-types';
import { inject, observer } from 'mobx-react';
import { baseBlueColor, titleColor } from '../themes';
import res from '../res';
import util from '../util';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },

  container2: {
    flex: 11,
    flexDirection: 'row',
    // paddingRight: 15,
  },
  sessionList: {
    flex: 1,
  },
  rightBar: {
    position: 'absolute',
    width: 20,
    right: 5,
    top: 50,
  },
  rightBarText: {
    color: baseBlueColor,
    textAlign: 'center',
    // lineHeight: 20,
    height: 20,
    width: 20,
  },
  sessionListItemContainer: {
    flex: 1,
    flexDirection: 'row',
    padding: 8,
    paddingLeft: 0,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 14,
  },
  sectionHeaderContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#eee',
    width: '100%',
    paddingRight: 14,
  },
  sessionListItem1: {
    paddingHorizontal: 15,
    flex: 1,
    paddingVertical: 6,
    color: titleColor,
  },

  sessionHeader: {
    backgroundColor: '#eee',
    paddingVertical: 10,
    paddingLeft: 10,
    color: titleColor,
  },
  itemSeparator: {
    flex: 1,
    height: 1,
    backgroundColor: '#eee',
  },
});
@inject('jobStore')
@observer
export default class extends React.Component {
  static propTypes = {
    onPick: PropTypes.func,
    animationType: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.state = {
      fullList: true,
      selectItemPlaceId: 0,
      selectHeaderKey: '',
    };
  }

  handleRightBarPress(item) {
    const city = this.props.jobStore.pinyinIndexArray2;
    this.sectionlist.scrollToLocation({
      animated: true,
      itemIndex: -1,
      sectionIndex: city.indexOf(item) == 0 ? 1 : city.indexOf(item),
    });
  }

  phoneCodeSelected(item, index, sec) {
    this.setState({
      selectItemPlaceId: item.placeId,
      selectHeaderKey: '',
    });
    item.parentName = sec;
    this.props.onPick(item);
  }

  selectHeaderAction(item) {
    // if (this.props.disableHeader) return;
    this.setState({
      selectHeaderKey: item,
      selectItemPlaceId: '',
    });
    this.props.onPick(item);
  }

  phoneCodeSelected2(placeId) {
    this.setState({
      selectItemPlaceId: placeId,
      selectHeaderKey: '',
    });
  }

  selectHeaderAction2(item) {
    this.setState({
      selectHeaderKey: item,
      selectItemPlaceId: '',
    });
  }

  /* 手指滑动，触发事件 */
  scrollSectionList(event, length) {
    const touch = event.nativeEvent.touches[0];
    if (touch.locationY >= 0 && touch.locationY <= 20 * length) {
      const index = touch.locationY / 20;
      if (index <= length) {
        this.sectionlist.scrollToLocation({
          animated: true,
          itemIndex: -1,
          sectionIndex: parseInt(index, 10),
        });
      }
    }
  }

  /* 右侧索引 */
  sectionItemView() {
    const city = this.props.jobStore.pinyinIndexArray3;
    return (
      <View style={styles.rightBar}>
        <FlatList
          ref={(w) => {
            this.sectionIndexlist = w;
          }}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          renderItem={({ item }) => (
            <TouchableOpacity onPress={() => this.handleRightBarPress(item)}>
              <Text style={styles.rightBarText}>{item}</Text>
            </TouchableOpacity>
          )}
          data={city}
          keyExtractor={(item, index) => index + item}
        />
      </View>
    );
  }

  render() {
    let city = this.props.pickerData;

    if (city && Object.keys(city).length === 0) {
      city = [];
    }
    const { selectHeaderKey, selectItemPlaceId } = this.state;
    const { iconSelected = false } = this.props;
    return (
      <View style={styles.container}>
        <View style={[styles.container2]}>
          <SectionList
            onScrollToIndexFailed={() => {}}
            ref={(w) => {
              this.sectionlist = w;
            }}
            style={[styles.sessionList]}
            renderItem={({ item, index, section }) => (
              <TouchableOpacity onPress={() => this.phoneCodeSelected(item, index, section)}>
                <View
                  style={[
                    styles.sessionListItemContainer,
                    iconSelected && selectItemPlaceId == item.placeId
                      ? { backgroundColor: '#FAECEC' }
                      : {},
                  ]}
                >
                  <Text style={[styles.sessionListItem1]}>
                    {item ? util.getCityNameWithCityData(item) : ''}
                  </Text>
                  <View>
                    {selectItemPlaceId == item.placeId &&
                      (!iconSelected ? (
                        <Image source={res.citySelected} style={{ width: 20, height: 20 }} />
                      ) : (
                        <Icon
                          type="ionicon"
                          name="ios-checkmark-circle"
                          size={24}
                          color="#EF3D48"
                        />
                      ))}
                  </View>
                </View>
              </TouchableOpacity>
            )}
            renderSectionHeader={({ section }) => (
              <TouchableOpacity onPress={() => this.selectHeaderAction(section.key)}>
                <View style={[styles.sectionHeaderContainer]}>
                  <Text style={[styles.sessionHeader]}>{section.key}</Text>
                  <View>
                    {selectHeaderKey == section.key && (
                      <Image source={res.citySelected} style={{ width: 20, height: 20 }} />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            )}
            sections={city}
            keyExtractor={(item, index) => index + item}
            ItemSeparatorComponent={() =>
              this.state.fullList ? <View style={[styles.itemSeparator]} /> : <View />
            }
            ListFooterComponent={() => <View style={{ height: 34 }} />}
          />
        </View>
        {/* {this.sectionItemView()} */}
      </View>
    );
  }
}
