import React, { Component } from 'react';
import { View, Platform } from 'react-native';
import Toast from 'react-native-easy-toast';
import I18n from '../i18n';
import OpenFile from 'react-native-doc-viewer';
import RNFS from 'react-native-fs';
import ProgressModal from './progressModal';

// 保存路径可以跳进去看，具体自己选择
const SavePath = Platform.OS === 'ios' ? RNFS.LibraryDirectoryPath : RNFS.ExternalDirectoryPath;

export default class AnnexPreview extends Component {
  constructor(props) {
    super(props);
    this.params = null;
    this.state = {
      progress: '',
      showProgress: false,
    };
  }

  openFile = (params) => {
    this.params = params;
    const {
      params: { url, fileName, resumeId },
    } = this;

    const typeIndex = fileName.lastIndexOf('.');
    const fileType = fileName.substring(typeIndex + 1, fileName.length);
    RNFS.exists(`${SavePath}/${resumeId}.${fileType}`)
      .then(
        // 判断文件是否已存在，返回result为true说明存在，result为false说明不存在
        (result) => {
          if (!result) {
            this.setState({ showProgress: true });
            const DownloadFileOptions = {
              fromUrl: url, // 下载文件的URL
              toFile: `${SavePath}/${resumeId}.${fileType}`, // 将文件保存到的本地文件系统路径
              // 进度条
              begin: () => {},
              progress: (res) => {
                const pro = res.bytesWritten / res.contentLength;
                this.setState({ progress: `${Math.floor(pro * 1000) / 10}%`, showProgress: true });
              },
            };

            const ret = RNFS.downloadFile(DownloadFileOptions);
            ret.promise
              .then(() => {
                this.setState({ progress: '', showProgress: false });
                this.handlePressLocal();
              })
              .catch(() => {
                this.setState({ progress: '', showProgress: false });
              });
          } else {
            this.handlePressLocal();
          }
        }
      )
      .catch();
  };

  // 打开本地文件

  handlePressLocal = () => {
    const {
      params: { name, fileName, resumeId },
    } = this;

    const typeIndex = fileName.lastIndexOf('.');
    const fileType = fileName.substring(typeIndex + 1, fileName.length);

    if (global.IS_IOS) {
      OpenFile.openDoc(
        [
          {
            url: `${SavePath}/${resumeId}.${fileType}`,
            fileNameOptional: name,
          },
        ],
        () => {}
      );
    } else {
      OpenFile.openDoc(
        [
          {
            url: `file://${SavePath}/${resumeId}.${fileType}`, // 打开本地文件必须加上file://
            fileName: name,
            cache: false,
            fileType,
          },
        ],
        (error) => {
          if (error) {
            this.toast.show(I18n.t('page_resume_annex_preview_software_error'));
            // this.setState({ showLoading: false });
          } else {
            // this.toast.show(I18n.t('page_resume_annex_preview_error'));
            // this.setState({ showLoading: false });
          }
        }
      );
    }
  };

  render() {
    const { showProgress, progress } = this.state;
    return (
      <View>
        <ProgressModal
          ref={(ref) => {
            this.progressModal = ref;
          }}
          isOpen={showProgress}
          progress={progress}
          loadingTips
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
