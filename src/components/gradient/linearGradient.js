import React from 'react';
import LinearGradient from 'react-native-linear-gradient';

/**
 * 渐变组件
 * <AUTHOR>
 */
export default class LinearGradientComponent extends React.Component {
  render() {
    const { children, isHorizontal, ...rest } = this.props;
    return (
      <LinearGradient
        start={isHorizontal ? { x: 0, y: 0.5 } : undefined}
        end={isHorizontal ? { x: 1, y: 0.5 } : undefined}
        {...rest}
      >
        {children}
      </LinearGradient>
    );
  }
}
