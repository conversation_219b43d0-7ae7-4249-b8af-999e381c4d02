import React from 'react';
import { FlatList, Text, View, Touchable } from '../index';
import I18n from '../../i18n';
import styles from '../../themes/enterprise';
import NoData from '../empty/noData';
import promiseUtil from '../../util/promiseUtil';

/**
 * 分页列表
 *
 * 支持外部store数据
 * 如：
 * <PageFlatList
 *    data={store.data}
 *    handleLoadResponse={(res, refreshing) => ({ totalCount: 100,
 *    currentTotalCount: 10 })}
 *    ...
 *    />
 *
 * 属性：
 * loadData 必传，异步方法类型，(page) => {}
 * itemId 可选，字符串类型，item的唯一id属性名称
 * handleLoadResponse 可选，当支持外部store数据时必选
 *
 * <AUTHOR>
 */
export default class PageFlatList extends React.Component {
  constructor(props) {
    super(props);
    this.page = 1;
    this.state = {
      data: [],
      totalCount: 0,
      currentTotalCount: 0,
      refreshing: false,
    };
  }

  componentDidMount() {
    if (!(this.props && this.props.isNoInitData)) {
      this.onRefresh(true, true);
    }
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  onRefresh = (showRefresh = true, isFirst) => {
    this.isFirst = isFirst;
    this.onLoadData(1, showRefresh);
  };

  onLoadMore = () => {
    const { currentTotalCount, totalCount } = this.state;
    if (currentTotalCount >= totalCount) {
      return;
    }
    this.onLoadData(this.page + 1);
  };

  handleLoadResponse = (res, refreshing) => {
    const result = res.result || [];
    const data = refreshing ? result : this.state.data.concat(result);
    return {
      data,
      totalCount: +res.totalCount,
      currentTotalCount: data.length,
    };
  };

  onLoadData = async (page, showRefresh = true) => {
    if (!this.props.loadData) {
      return;
    }
    const refreshing = page === 1;
    const showRefreshing = refreshing && showRefresh && !this.props.noShowRefresh;
    if (showRefreshing) {
      this.setState({ refreshing: true });
    }
    try {
      const res = await promiseUtil.requestMin(
        this.props.loadData(page),
        !this.isFirst && showRefresh ? 1000 : undefined
      );
      if (this.isUnmount) {
        console.log('PageFlatList onLoadData isUnmount');
        return;
      }
      if (res?.isStale) {
        console.log('PageFlatList onLoadData isStale');
        if (showRefreshing) {
          this.setState({ refreshing: false });
        }
        return;
      }
      this.page = page;
      if (this.props.handleLoadResponse) {
        const { totalCount, currentTotalCount } = this.props.handleLoadResponse(res, refreshing);
        this.setState({
          totalCount: +totalCount,
          currentTotalCount,
        });
      } else {
        const state = this.handleLoadResponse(res, refreshing);
        this.setState({ ...state });
      }
    } catch (e) {
      if (this.isUnmount) {
        console.log('PageFlatList onLoadData isUnmount');
        return;
      }
      if (e && e.message) {
        toast.show(e.message);
      } else {
        logger.warn(e);
      }
    }
    if (showRefreshing) {
      this.setState({ refreshing: false });
    }
  };

  /**
   * item状态变化时调用
   */
  changeItem = (item, index) => {
    const { data } = this.state;
    data.splice(index, 1, item);
    this.setState({ data: data.slice() });
  };

  // itemSeparatorComponent = () => {
  //   const { globalStyle } = styles.get(['global']);
  //   return <View style={globalStyle.listItemSeparator} />;
  // };

  onViewPasswd = (isShow, currentItem) => {
    if (this.props.onViewPasswd) {
      this.props.onViewPasswd(isShow, currentItem);
    }
  };

  scrollToOffset = (params) => {
    this.flatList?.scrollToOffset(params);
  };

  listFooterComponent = () => {
    const { globalStyle } = styles.get(['global']);
    const {
      state: { refreshing, currentTotalCount, totalCount, data },
      props: { tipsColor, footerComponent, noShowEmpty },
    } = this;
    if (!refreshing && totalCount > 0 && data && data.length) {
      return (
        <View>
          {footerComponent}
          {noShowEmpty && currentTotalCount >= totalCount ? null : (
            <Text style={[globalStyle.loadingTips, tipsColor ? { color: tipsColor } : {}]}>
              {I18n.t(currentTotalCount < totalCount ? 'list_loading' : 'list_empty_data')}
            </Text>
          )}
        </View>
      );
    }
    return null;
  };

  renderListEmptyComponent = () => {
    const { refreshing } = this.state;
    const { tipsColor, noDataExtraChildren } = this.props;
    return (
      <>
        <NoData
          style={this.props.noDataStyle}
          text={this.props.noDataText}
          loading={refreshing}
          textColor={tipsColor}
        />
        {!refreshing ? noDataExtraChildren : null}
      </>
    );
  };

  keyExtractor = (item, index) => {
    const { itemId } = this.props;
    return `${itemId ? item[itemId] : index}`;
  };

  render() {
    const { data, refreshing } = this.state;
    const { parentDatas, extraData } = this.props;
    return (
      <FlatList
        ref={(ref) => (this.flatList = ref)}
        data={parentDatas || data}
        keyExtractor={this.keyExtractor}
        refreshing={refreshing}
        onRefresh={this.onRefresh}
        onEndReachedThreshold={0.1}
        onEndReached={this.onLoadMore}
        // ItemSeparatorComponent={this.itemSeparatorComponent}
        ListFooterComponent={this.listFooterComponent}
        ListEmptyComponent={this.renderListEmptyComponent}
        {...this.props}
        extraData={{ state: this.state, extraData }}
      />
    );
  }
}
