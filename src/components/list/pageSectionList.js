import React from 'react';
import { SectionList, Text, View } from '../index';
import I18n from '../../i18n';
import styles from '../../themes/enterprise';
import NoData from '../empty/noData';

/**
 * 分组列表
 *
 * 支持外部store数据
 * 如：
 * <PageSectionList
 *    sections={store.data}
 *    handleLoadResponse={(res, refreshing) => ({ totalCount: 100,
 *    currentTotalCount: 10 })}
 *    ...
 *    />
 *
 * 属性：
 * loadData 必传，异步方法类型，(page) => {}
 * itemId 可选，字符串类型，item的唯一id属性名称
 * mergeSameItem 可选，方法类型，(oldLastItem, newFirstItem) => {}，当分组头除了key之外还有其它信息，则需要自行合并，参考tradeList页面
 * handleLoadResponse 可选，当支持外部store数据时必选
 *
 * <AUTHOR>
 */
export default class PageSectionList extends React.Component {
  constructor(props) {
    super(props);
    this.page = 1;
    this.loading = false;
    this.state = {
      data: [],
      totalCount: 0,
      currentTotalCount: 0,
      refreshing: false,
    };
  }

  componentDidMount() {
    this.onRefresh();
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  onRefresh = () => {
    this.onLoadData(1);
  };

  onLoadMore = () => {
    const { totalCount, currentTotalCount } = this.state;
    if (currentTotalCount >= totalCount) {
      return;
    }
    this.onLoadData(this.page + 1);
  };

  handleLoadResponse = (res, refreshing) => {
    // 合并数据
    let data = this.state.data.slice();
    if (refreshing || data.length === 0) {
      data = res.result;
    } else if (res.result.length) {
      const oldLastItem = data[data.length - 1];
      const newFirstItem = res.result[0];
      if (oldLastItem.key === newFirstItem.key) {
        if (this.props.mergeSameItem) {
          this.props.mergeSameItem(oldLastItem, newFirstItem);
        } else {
          oldLastItem.data = oldLastItem.data.concat(newFirstItem.data);
        }
        if (res.result.length > 1) {
          data = data.concat(res.result.slice(1));
        }
      } else {
        data = data.concat(res.result);
      }
    }
    console.log(data);

    // 统计当前记录总数
    // const currentTotalCount = data.reduce(
    //   (total, item) => total + item.data.length,
    //   0
    // );
    return {
      data,
      totalCount: +res.totalCount,
      currentTotalCount: 10,
    };
  };

  onLoadData = async (page) => {
    if (this.loading) {
      return;
    }
    this.loading = true;
    const refreshing = page === 1;
    if (refreshing) {
      this.setState({ refreshing });
    }
    try {
      const res = await this.props.loadData(page);

      if (this.isUnmount) {
        console.log('PageSectionList onLoadData isUnmount');
        return;
      }
      this.page = page;

      if (this.props.handleLoadResponse) {
        const { totalCount, currentTotalCount } = this.props.handleLoadResponse(res, refreshing);
        this.setState({
          totalCount: +totalCount,
          currentTotalCount,
          refreshing: false,
        });
      } else {
        const state = this.handleLoadResponse(res, refreshing);
        console.log('PageSectionList onLoadData', state);
        this.setState({
          ...state,
          refreshing: false,
        });
      }
    } catch (e) {
      console.log('1111111');

      console.log(e);
      if (this.isUnmount) {
        console.log('PageSectionList onLoadData isUnmount');
        return;
      }
      if (e && e.message) {
        toast.show(e.message);
      }
      this.setState({
        refreshing: false,
      });
    }
    this.loading = false;
  };

  itemSeparatorComponent = () => {
    console.log('1111111');

    return <View style={{ height: 1, backgroundColor: 'red' }} />;
  };

  listFooterComponent = () => {
    const { globalStyle } = styles.get(['global']);
    const { refreshing, totalCount, currentTotalCount } = this.state;
    // console.log('PageSectionList listFooterComponent', currentTotalCount, totalCount);
    if (!refreshing && totalCount > 0) {
      return (
        <Text style={globalStyle.loadingTips}>
          {I18n.t(currentTotalCount < totalCount ? 'list_loading' : 'list_empty_data')}
        </Text>
      );
    }
    return null;
  };

  keyExtractor = (item, index) => {
    const { itemId } = this.props;
    return `${itemId ? item[itemId] : index}`;
  };

  render() {
    const { data, refreshing } = this.state;
    return (
      <SectionList
        sections={data}
        // keyExtractor={this.keyExtractor}
        refreshing={refreshing}
        // onRefresh={this.onRefresh}
        // onEndReachedThreshold={0.1}
        // onEndReached={this.onLoadMore}
        // ItemSeparatorComponent={this.itemSeparatorComponent}
        // ListFooterComponent={this.listFooterComponent}
        // ListEmptyComponent={() => <NoData loading={this.state.refreshing} />}
        {...this.props}
        // extraData={{ state: this.state, extraData: this.props.extraData }}
      />
    );
  }
}
