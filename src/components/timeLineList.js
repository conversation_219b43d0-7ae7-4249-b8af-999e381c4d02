import React, { PureComponent } from 'react';
import { StyleSheet, FlatList, View } from 'react-native';
import { Surface, Shape, Path, Group } from '@react-native-community/art';

const defaultCircleSize = 16;
const defaultCircleColor = '#007AFF';
const defaultLineWidth = 10;
const defaultLineColor = '#007AFF';
const defaultTimeTextColor = 'black';
const defaultDotColor = 'white';
const defaultInnerCircle = 'none';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listview: {
    flex: 1,
  },
  sectionHeader: {
    marginBottom: 15,
    backgroundColor: '#007AFF',
    height: 30,
    justifyContent: 'center',
  },
  sectionHeaderText: {
    color: '#FFF',
    fontSize: 18,
    alignSelf: 'center',
  },
  rowContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  timeContainer: {
    minWidth: 45,
  },
  time: {
    textAlign: 'right',
    color: defaultTimeTextColor,
  },
  circle: {
    width: 16,
    height: 16,
    borderRadius: 10,
    position: 'absolute',
    left: -8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: defaultDotColor,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  details: {
    width: defaultLineWidth,
    flex: 1,
  },
  detail: { paddingTop: 10, paddingBottom: 10 },
  description: {
    marginTop: 10,
  },
  separator: {
    height: 1,
    backgroundColor: '#aaa',
    marginTop: 10,
    marginBottom: 10,
  },
});

export default class TimelineList extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      data: this.props.data,
      x: 0,
      width: 0,
    };
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      data: nextProps.data,
    });
  }

  line = (path, pathCircle) => (
    <Surface width={10} style={{ flex: 1 }}>
      <Group>
        {this.props.hiddenLine ? (
          <Shape />
        ) : (
          <Shape
            d={path}
            stroke="rgba(212,226,242,1)"
            strokeWidth={1}
            strokeDash={global.IS_ANDROID ? [3, 6] : [2, 2]}
          />
        )}
        <Shape d={pathCircle} stroke="rgba(212,226,242,1)" fill="#2089DC" strokeWidth={2} />
      </Group>
    </Surface>
  );

  renderDetail() {
    return <View />;
  }

  renderLine = (item, index) => {
    const path = new Path();
    path.moveTo(4, index === 0 ? 15 : 0); // 将起始点移动到(1,1) 默认(0,0)
    path.lineTo(4, 300); // 连线到目标点(300,1)
    const pathCircle = new Path().moveTo(4, 15).arc(0, 6, 3).arc(0, -6, 3).close();
    return (
      <View
        style={{ backgroundColor: 'white', width: 10, marginLeft: 17 }}
        onLayout={(evt) => {
          if (!this.state.x && !this.state.width) {
            const { x, width } = evt.nativeEvent.layout;
            this.setState({ x, width });
          }
        }}
      >
        {this.line(path, pathCircle)}
      </View>
    );
  };

  renderRow = ({ item, index }) => (
    <View>
      <View style={[styles.rowContainer]}>
        {this.renderLine(item, index)}
        {this.props.renderDetail ? this.props.renderDetail(item) : this.renderDetail(item)}
      </View>
    </View>
  );

  render() {
    return (
      <View style={[styles.container, this.props.style]}>
        <FlatList
          style={[styles.listview, this.props.conStyle]}
          data={this.state.data}
          renderItem={this.renderRow}
          keyExtractor={(item) => item[this.props.itemKey]}
        />
      </View>
    );
  }
}

TimelineList.defaultProps = {
  circleSize: defaultCircleSize,
  circleColor: defaultCircleColor,
  lineWidth: defaultLineWidth,
  lineColor: defaultLineColor,
  innerCircle: defaultInnerCircle,
  columnFormat: 'single-column-left',
  separator: false,
  showTime: true,
};
