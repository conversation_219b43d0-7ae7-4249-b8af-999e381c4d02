import React from 'react';
import styles from '../themes/enterprise';
import { View } from './index';

/**
 * 未读红点
 * <AUTHOR>
 */
export default class UnreadPoint extends React.Component {
  backgroundColor = styles.get('theme').unreadPoint;

  render() {
    const { size = 10, hide, style, rest } = this.props;
    if (hide) {
      return null;
    }
    return (
      <View
        style={[
          {
            backgroundColor: this.backgroundColor,
            width: size,
            height: size,
            borderRadius: size / 2,
          },
          style,
        ]}
        {...rest}
      />
    );
  }
}
