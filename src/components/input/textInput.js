import React, { Component } from 'react';
import { TextInput as RnTextInput, StyleSheet, View, TouchableOpacity, Image } from 'react-native';
import { Input } from 'react-native-elements';
import InputUtil from '../../util/inputUtil';
import resIcon from '../../res';

function getComponentStyle() {
  return {
    rightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
  };
}
export const maxLengthObj = {
  amount: 13,
  phone: 15,
  phoneCode: 5,
  bankCard: 30,
  passportNumber: 30,
  cryptoAmount: 26,
  cryptoOrderId: 200,
  cryptoAddress: 200,
  email: 50,
};

const keyboardTypeObj = {
  amount: 'decimal-pad',
  cryptoAmount: 'decimal-pad',
  phone: 'numeric',
  bankCard: 'numeric',
  passportNumber: 'default',
  phoneCode: 'numeric',
  cryptoOrderId: 'default',
  email: 'email-address',
  cryptoAddress: 'default',
};

/**
 * 文本输入框
 * <AUTHOR>
 */
export default class TextInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFocus: false,
    };
  }

  clearstyle = getComponentStyle();

  setNativeProps = (nativeProps) => {
    this.textInput.setNativeProps(nativeProps);
  };

  focus = () => this.textInput?.focus();

  initTextInput = (ref) => (this.textInput = ref);

  onChangeText = (text) => {
    const { inputType, onChangeText, phoneRegion, maxLength, precision, handleOptions } =
      this.props;
    if (inputType === 'amount') {
      text = InputUtil.handleAmount(text, {
        input: this.textInput,
        maxLength: maxLength || maxLengthObj.amount,
        precision,
        ...(handleOptions || {}),
      });
    } else if (inputType === 'phone') {
      text = InputUtil.handlePhone(text, this.textInput, phoneRegion);
    } else if (inputType === 'bankCard') {
      text = InputUtil.handleBankCard(text, this.textInput);
    } else if (inputType === 'passportNumber') {
      text = InputUtil.handlePassportNumber(text, this.textInput);
    } else if (inputType === 'cryptoAmount') {
      text = InputUtil.handleAmount(text, {
        input: this.textInput,
        maxLength: maxLengthObj.cryptoAmount,
        precision,
      });
    } else if (inputType === 'phoneCode') {
      text = InputUtil.handlePhoneCode(text, {
        input: this.textInput,
        maxLength: this.getMaxLength(),
      });
    } else if (inputType === 'age') {
      text = InputUtil.handleAge(text, {
        input: this.textInput,
        maxLength: 2,
      });
    }
    if (onChangeText) {
      onChangeText(text);
    }
  };

  getMaxLength = () => {
    const { inputType, maxLength } = this.props;
    return maxLength || (inputType && maxLengthObj[inputType]) || undefined;
  };

  getKeyboardType = () => {
    const { inputType, keyboardType } = this.props;
    return keyboardType || (inputType && keyboardTypeObj[inputType]) || undefined;
  };

  clear = () => {
    this.onChangeText('');
    this.props.onClear?.();
    return this.textInput?.clear();
  };

  _isNull(str) {
    let result = true;
    if (str === '' || str === undefined) {
      result = true;
    }

    if (str?.length > 0) {
      result = false;
    }
    return result;
  }

  getInputStyle = () => {
    const { isInput, style, inputStyle, inputType } = this.props;
    if (inputType !== 'amount' && inputType !== 'cryptoAmount') {
      return isInput ? inputStyle : StyleSheet.flatten([{ padding: 0 }, style]);
    }
    if (isInput) {
      return StyleSheet.flatten([{ fontFamily: 'Roboto' }, inputStyle]);
    }
    return StyleSheet.flatten([{ padding: 0, fontFamily: 'Roboto' }, style]);
  };

  onFocus = () => {
    if (this.props.onFocus) {
      this.props.onFocus();
    }
    this.setState({ isFocus: true });
  };

  onBlur = () => {
    if (this.props.onBlur) {
      this.props.onBlur();
    }
    this.setState({ isFocus: false });
  };

  render() {
    const {
      isInput,
      style,
      closeIconStyle,
      inputStyle,
      clear = true,
      rightIcon,
      disabled,
      ...rest
    } = this.props;
    const { isFocus } = this.state;
    const { clearstyle } = this;
    if (isInput) {
      return (
        <Input
          ref={this.initTextInput}
          errorStyle={{
            height: 0,
            margin: 0,
            padding: 0,
          }}
          selectionColor="#FFB731"
          underlineColorAndroid="transparent"
          style={style}
          {...rest}
          disabled={!!disabled}
          rightIcon={() => (
            <View style={clearstyle.rightContainer}>
              {clear && !this._isNull(this.props.defaultValue || this.props.value) && isFocus ? (
                <TouchableOpacity onPress={this.clear}>
                  <Image
                    style={[{ width: 32 }, { ...closeIconStyle }]}
                    source={resIcon.clearEnterprise}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              ) : null}
              {rightIcon && rightIcon()}
            </View>
          )}
          inputStyle={this.getInputStyle()}
          maxLength={this.getMaxLength()}
          keyboardType={this.getKeyboardType()}
          onChangeText={this.onChangeText}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
        />
      );
    }
    return (
      <RnTextInput
        ref={this.initTextInput}
        underlineColorAndroid="transparent"
        {...rest}
        disabled={!!disabled}
        style={this.getInputStyle()}
        onChangeText={this.onChangeText}
        maxLength={this.getMaxLength()}
        keyboardType={this.getKeyboardType()}
      />
    );
  }
}
