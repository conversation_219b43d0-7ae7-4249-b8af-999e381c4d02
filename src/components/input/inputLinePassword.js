/**
 *功能：密码输入
 *描述：
 *作者：sxw
 * @export
 * @class ModifyPassword
 * @extends {Component}
 */

import React, { Component } from 'react';
import { Input, Text, View, Image, Touchable } from '../index';
import styles from '../../themes/enterprise';
import resIcon from '../../res';

function getComponentStyle(theme) {
  return {
    container: {},
    inputStyle: {
      fontSize: theme.fontSizeS,
      height: 40,
      paddingLeft: 10,
      paddingRight: 10,
      color: theme.titleFontColor,
    },
    inputContainerStyle: {
      borderBottomWidth: 1,
      borderRadius: 4,
      borderColor: '#eee',
    },
    inputLabelStyle: {
      paddingLeft: 7,
      color: theme.minorFontColor,
      fontSize: theme.fontSizeM,
    },
    imageStyle: {
      paddingHorizontal: 10,
    },
    errorBorder: {
      borderBottomWidth: 1,
      borderColor: theme.errorTextColor,
      borderRadius: 4,
    },
    error: {
      color: theme.errorTextColor,
      marginTop: 5,
      paddingLeft: 17,
      paddingRight: 17,
    },
  };
}

export default class InputLinePassword extends Component {
  constructor(props) {
    super(props);
    this.state = {
      secureText: true,
      invalid: false,
    };
  }

  onChangeText = (text) => {
    if (this.props.onChangeText) {
      this.props.onChangeText(text);
    }
    if (!this.isValidForm(text)) {
      this.setState({ invalid: true });
      return;
    }
    this.setState({ invalid: false });
  };

  onFocus = () => {
    if (this.props.onFocus) {
      this.props.onFocus();
    }
  };

  toggleShowPassword = () => {
    const { secureText } = this.state;
    this.setState({ secureText: !secureText });
  };

  isValidForm = (text) => {
    const { required, reg, validate } = this.props;
    const value = text.trim();
    if (required && !value) {
      return false;
    }
    if (validate) {
      return validate(value);
    }
    if (reg) {
      return reg.test(value);
    }
    return true;
  };

  render() {
    const { themeStyle } = styles.get(['theme']);
    const inputPasswordStyle = getComponentStyle(themeStyle);
    const { placeholder, maxLength } = this.props;
    const { secureText, invalid } = this.state;
    return (
      <View style={inputPasswordStyle.container}>
        <Input
          autoFocus={false}
          secureTextEntry={secureText}
          placeholder={placeholder}
          inputStyle={[
            inputPasswordStyle.inputStyle,
            { fontSize: this.props.fontSizeM ? themeStyle.fontSizeM : themeStyle.fontSizeS },
          ]}
          inputContainerStyle={[
            inputPasswordStyle.inputContainerStyle,
            invalid ? inputPasswordStyle.errorBorder : {},
          ]}
          placeholderTextColor={themeStyle.mediumFontColor}
          rightIcon={() => (
            <Touchable onPress={this.toggleShowPassword}>
              <Image
                style={{ width: 32 }}
                source={!secureText ? resIcon.loginEyeOpen : resIcon.loginEyeClose}
                resizeMode="contain"
              />
            </Touchable>
          )}
          maxLength={maxLength || 16}
          onChangeText={this.onChangeText}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          {...this.props}
        />
        {invalid ? <Text style={inputPasswordStyle.error}>{placeholder}</Text> : <View />}
      </View>
    );
  }
}
