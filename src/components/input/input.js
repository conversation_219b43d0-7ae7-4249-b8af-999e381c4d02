import React, { Component } from 'react';
import TextInput from './textInput';

/**
 * 文本输入框
 * <AUTHOR>
 */
export default class Input extends Component {
  setNativeProps = (nativeProps) => {
    this.textInput.setNativeProps(nativeProps);
  };

  clear = () => {
    return this.textInput?.clear();
  };

  focus = () => {
    return this.textInput?.focus();
  };

  initTextInput = (ref) => (this.textInput = ref);

  render() {
    return <TextInput ref={this.initTextInput} isInput {...this.props} />;
  }
}
