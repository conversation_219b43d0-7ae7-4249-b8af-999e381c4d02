import React, { Component } from 'react';
import { Icon, Input, Text, Touchable, View } from '../index';
import styles from '../../themes';
import I18n from '../../i18n';

function getComponentStyle(theme) {
  return {
    containerStyle: {
      width: 'auto',
      backgroundColor: '#F7F7F7',
      borderRadius: 10,
      paddingHorizontal: 0,
      borderWidth: 1,
      borderColor: '#EFF1F3',
      borderStyle: 'solid',
    },
    inputContainerStyle: {
      borderBottomWidth: 0,
      height: 40,
    },
    inputStyle: {
      paddingLeft: 10,
      paddingVertical: 0,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
      height: 40,
    },
    placeholderTextColor: '#99A3BA',
    rightIconContainerStyle: {
      paddingRight: 0,
      marginVertical: 0,
      height: 40,
    },
    clearIconStyle: {
      paddingHorizontal: 10,
      height: '100%',
      justifyContent: 'center',
    },
    searchIcon: {
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 10,
    },
    rightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    searchTextBox: {
      paddingRight: 20,
      paddingLeft: 10,
      // height: '100%',
      justifyContent: 'center',
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
    },
  };
}

export default class SearchBox extends Component {
  constructor(props) {
    super(props);
    this.searchBoxStyle = getComponentStyle(styles.get('theme'));
    this.state = {
      // text: null,
    };
  }

  setNativeProps = (nativeProps) => {
    this.textInput.setNativeProps(nativeProps);
  };

  clear = () => {
    return this.textInput?.clear();
  };

  initTextInput = (ref) => (this.textInput = ref);

  onChangeText = (text) => {
    if (this.props.onChangeText) {
      this.props.onChangeText(text);
    }
    // this.setState({ text });
  };

  onSubmitEditing = () => {
    if (this.props.onSubmitEditing) {
      this.props.onSubmitEditing();
    }
  };

  renderRightIcon = () => {
    const { searchBoxStyle } = this;
    return (
      <View style={searchBoxStyle.rightContainer}>
        {/* {this.state.text ? (
          <Touchable onPress={this.clear}>
            <Icon
              type="ionicon"
              name="close-circle-outline"
              size={20}
              color="#666"
              style={searchBoxStyle.clearIconStyle}
            />
          </Touchable>
        ) : null} */}
        <Touchable onPress={this.props.onSubmitEditing} style={searchBoxStyle.searchTextBox}>
          <Text style={searchBoxStyle.searchBtn}>{I18n.t('op_search_title')}</Text>
        </Touchable>
      </View>
    );
  };

  renderLeftIcon = () => {
    const { searchBoxStyle } = this;
    return (
      <Icon type="ionicon" name="search" color="#999" size={18} style={searchBoxStyle.searchIcon} />
    );
  };

  render() {
    const { searchBoxStyle } = this;
    const {
      style,
      showRight,
      editable,
      placeholder = I18n.t('op_search_title'),
      onPress,
      ...rest
    } = this.props;
    if (!editable && onPress) {
      return (
        <Touchable onPress={onPress}>
          <View
            style={[
              searchBoxStyle.containerStyle,
              { flexDirection: 'row', alignItems: 'center' },
              style,
            ]}
          >
            {this.renderLeftIcon()}
            <Text
              style={[
                searchBoxStyle.inputStyle,
                {
                  lineHeight: searchBoxStyle.inputStyle.height,
                  color: searchBoxStyle.placeholderTextColor,
                  paddingLeft: 15,
                },
              ]}
            >
              {placeholder}
            </Text>
          </View>
        </Touchable>
      );
    }
    return (
      <Input
        containerStyle={[searchBoxStyle.containerStyle, style]}
        inputContainerStyle={searchBoxStyle.inputContainerStyle}
        inputStyle={searchBoxStyle.inputStyle}
        rightIconContainerStyle={searchBoxStyle.rightIconContainerStyle}
        placeholderTextColor={searchBoxStyle.placeholderTextColor}
        placeholder={placeholder}
        editable={editable}
        returnKeyType="search"
        ref={this.initTextInput}
        rightIcon={showRight ? this.renderRightIcon : undefined}
        leftIcon={this.renderLeftIcon}
        onSubmitEditing={this.onSubmitEditing}
        {...rest}
        onChangeText={this.onChangeText}
      />
    );
  }
}
