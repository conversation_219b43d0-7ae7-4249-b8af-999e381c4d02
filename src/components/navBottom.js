import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { baseBlueColor } from '../themes';
import { RVW, RFT } from '../common';
import I18n from '../i18n';
import res from '../res';
import Image from './image';

const localStyle = {
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  tab: {
    flex: 1,
    maxHeight: 24 * RVW,
    alignItems: 'center',
    paddingBottom: 2,
    paddingTop: 2,
  },
  label: {
    fontSize: 3.8 * RFT,
    textAlign: 'center',
    marginTop: 4,
  },
  icon: {
    width: 35,
    height: 35,
  },
};

export default (props) => {
  const { navigation } = props;
  const { routeName } = navigation.state;
  return (
    <View style={localStyle.wrapper}>
      <TouchableOpacity
        style={[localStyle.tab]}
        onPress={() => {
          navigation.navigate('contact');
        }}
      >
        <Image
          source={routeName === 'contact' ? res.iconWorkActive : res.iconWork}
          style={localStyle.icon}
        />
        <Text
          style={[localStyle.label, { color: routeName === 'contact' ? baseBlueColor : '#333' }]}
        >
          {I18n.t('menu_nav_bottom_job')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[localStyle.tab]}
        onPress={() => {
          navigation.navigate('session');
        }}
      >
        <Image
          source={routeName === 'session' ? res.iconChatActive : res.iconChat}
          style={localStyle.icon}
        />
        <Text
          style={[localStyle.label, { color: routeName === 'session' ? baseBlueColor : '#333' }]}
        >
          {I18n.t('menu_nav_bottom_chat')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[localStyle.tab]}
        onPress={() => {
          navigation.navigate('contact');
        }}
      >
        <Image
          source={routeName === 'contact' ? res.iconForumActive : res.iconForum}
          style={localStyle.icon}
        />
        <Text
          style={([localStyle.label], { color: routeName === 'contact' ? baseBlueColor : '#333' })}
        >
          {I18n.t('menu_nav_bottom_dynamic')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[localStyle.tab]}
        onPress={() => {
          navigation.navigate('contact');
        }}
      >
        <Image
          source={routeName === 'contact' ? res.iconResumeActive : res.iconResume}
          style={localStyle.icon}
        />
        <Text
          style={[localStyle.label, { color: routeName === 'contact' ? baseBlueColor : '#333' }]}
        >
          {I18n.t('menu_nav_bottom_resume')}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[localStyle.tab]}
        onPress={() => {
          navigation.navigate('general');
        }}
      >
        <Image
          source={routeName === 'general' ? res.iconMeActive : res.iconMe}
          style={localStyle.icon}
        />
        <Text
          style={[localStyle.label, { color: routeName === 'general' ? baseBlueColor : '#333' }]}
        >
          {I18n.t('menu_nav_bottom_mine')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
