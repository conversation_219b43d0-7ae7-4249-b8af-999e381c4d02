import { NativeModules, Platform } from 'react-native';

/**
 * 系统将调整布局以适配输入法，带有输入法的布局需使用ScrollView包装，缺点：底部使用绝对布局的会被输入法顶上来
 */
export function setAdjustResize() {
  if (Platform.OS.toLowerCase() === 'android') {
    const { SoftInputModeAndroid } = NativeModules;
    SoftInputModeAndroid.setMode(
      SoftInputModeAndroid.stateAlwaysHidden | SoftInputModeAndroid.adjustResize
    );
  }
}

/**
 * 系统会将页面整体上移，以适配输入法，缺点：会导致标题栏看不到或遮挡一部分
 */
export function setAdjustPan() {
  if (Platform.OS.toLowerCase() === 'android') {
    const { SoftInputModeAndroid } = NativeModules;
    SoftInputModeAndroid.setMode(
      SoftInputModeAndroid.stateAlwaysHidden | SoftInputModeAndroid.adjustPan
    );
  }
}

/**
 * 重置为默认设置
 */
export function reset() {
  setAdjustPan();
}
