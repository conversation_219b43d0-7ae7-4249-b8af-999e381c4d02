import React, { Component } from 'react';
import { Image, Text, View } from '../index';
import I18n from '../../i18n';
import { Icon } from 'react-native-elements';
import resIcon from '../../res';
import styles from '../../themes/enterprise';

export default class ResumeItem extends Component {
  constructor(props) {
    super(props);
    this.style = props.style || styles.get('resumeList');
  }

  render() {
    const {
      item,
      intervieweeAvatar = item.intervieweeAvatar,
      intervieweeName = item.intervieweeName,
      careerExperience = item.careerExperience,
      qualificationLabel = item.qualificationId?.label,
      schoolName = item.schoolName,
      renderPopover,
    } = this.props;
    const { style } = this;
    return (
      <View style={style.itemTopContainer}>
        <View style={style.avatarContainer}>
          <Image
            source={intervieweeAvatar ? { uri: intervieweeAvatar } : resIcon.defaultAvatar}
            defaultSource={resIcon.defaultAvatar}
            style={style.itemAvatar}
            resizeMode="cover"
          />
        </View>
        <View style={style.itemTopRightContainer}>
          <View style={style.itemNameContainer}>
            <View style={[style.flexRow, { flexShrink: 1 }]}>
              <Text style={style.titleText}>{intervieweeName}</Text>
              {item.fav ? <Icon name="star" size={16} color="#F4B022" type="antdesign" /> : null}
            </View>
            {renderPopover?.(item)}
          </View>
          <Text style={style.sencondText}>
            {I18n.t('page_resume_text_work_experience')}：
            {I18n.t('page_resume_text_years', { count: careerExperience || 0 })}
          </Text>
          <Text style={style.sencondText}>
            {I18n.t('page_resume_label_career_edu')}：{qualificationLabel || '-'}
          </Text>
          <Text style={style.sencondText}>
            {I18n.t('page_resume_text_school')}：{schoolName || '-'}
          </Text>
        </View>
      </View>
    );
  }
}
