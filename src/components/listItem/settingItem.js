import React, { Component } from 'react';
import { Switch, Text, Touchable, View } from '../index';
import styles from '../../themes/enterprise';
import RightArrow from '../rightArrow';
import UnreadPoint from '../unreadPoint';

export default class SettingItem extends Component {
  securityStyle = styles.get(['security']).securityStyle;

  render() {
    const { securityStyle } = this;
    const {
      label,
      value,
      valueComponent,
      maxSpacing,
      maxBottom,
      onPress,
      switched,
      onSwitch,
      showRedPoint,
      hideRightArrow,
      valueTextStyle,
      hideTopLine,
    } = this.props;
    const content = (
      <View
        style={[
          securityStyle.itemContainer,
          { marginTop: maxSpacing ? 10 : 1, marginBottom: maxBottom ? 9 : 0 },
          hideTopLine ? { marginTop: 0 } : {},
        ]}
      >
        <Text style={securityStyle.itemLabelText} numberOfLines={1}>
          {label}
        </Text>
        {onSwitch ? (
          <Switch onValueChange={onSwitch} value={switched} />
        ) : (
          <View style={securityStyle.itemRightContainer}>
            <UnreadPoint hide={!showRedPoint} style={{ marginRight: 10 }} />
            {value ? (
              <Text style={[securityStyle.itemValueText, valueTextStyle]} numberOfLines={1}>
                {value}
              </Text>
            ) : null}
            {valueComponent}
            {!hideRightArrow && <RightArrow color="#999" />}
          </View>
        )}
      </View>
    );
    if (onPress) {
      return <Touchable onPress={onPress}>{content}</Touchable>;
    }
    return content;
  }
}
