import React from 'react';
import ImageViewer from 'react-native-image-zoom-viewer';

export default class MyImageViewer extends React.Component {
  render() {
    const { props } = this;
    if (!__DEV__ && props.imageUrls && props.imageUrls.length > 0) {
      props.imageUrls = props.imageUrls.map((item) => {
        if (item.url && item.url.indexOf('http:') === 0) {
          item.url = item.url.replace('http', 'https');
        }
        return item;
      });
    }
    return <ImageViewer {...props} />;
  }
}
