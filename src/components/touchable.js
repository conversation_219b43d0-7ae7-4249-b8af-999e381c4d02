import React from 'react';
import { Keyboard, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import DebounceUtil from '../util/debounceUtil';

/**
 * withoutFeedback 无触摸反馈
 * 本组件用于封装视图，使其可以正确响应触摸操作
 * 在Android设备上，这个组件利用原生状态来渲染触摸的反馈，目前它只支持一个单独的View实例作为子节点
 * 在iOS设备上，当按下的时候，封装的视图的不透明度会降低
 * author: Rays
 */
class Touchable extends React.Component {
  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
  }

  _keyboardDidShow = () => {
    this.keyBoardIsShow = true;
  };

  _keyboardDidHide = () => {
    this.keyBoardIsShow = false;
  };

  _onPress = (pressEvent) => {
    if (this.keyBoardIsShow && !this.props.notHideKeyboard) {
      Keyboard.dismiss();
    }
    const { onPress } = this.props;
    if (typeof onPress === 'function') {
      onPress(pressEvent);
    }
  };

  onPress = (pressEvent) => {
    // nativeEvent: { timestamp: ********,
    //       pageY: 113.**************,
    //       locationX: 21.**************,
    //       locationY: 21.***************,
    //       identifier: 0,
    //       target: 1535,
    //       pageX: 366.*************,
    //       changedTouches: [ [Circular] ],
    //       touches: [] }
    // touchHistory: { touchBank:
    //        [ { touchActive: false,
    //            startPageX: 366.*************,
    //            startPageY: 113.**************,
    //            startTimeStamp: ********,
    //            currentPageX: 366.*************,
    //            currentPageY: 113.**************,
    //            currentTimeStamp: ********,
    //            previousPageX: 366.*************,
    //            previousPageY: 113.**************,
    //            previousTimeStamp: ******** } ],
    //       numberActiveTouches: 0,
    //       indexOfSingleActiveTouch: 0,
    //       mostRecentTimeStamp: ******** }
    const { disableDebounce } = this.props;
    if (disableDebounce) {
      this._onPress(pressEvent);
    } else {
      DebounceUtil.exec(this, this._onPress, { param: pressEvent });
    }
  };

  render() {
    const { children, withoutFeedback, forwardRef, disabled, ...rest } = this.props;
    // console.log('Touchable type', children.type.displayName);
    // if (IS_ANDROID && children.type.displayName === 'View') {
    //   return (
    //     <TouchableNativeFeedback
    //       background={TouchableNativeFeedback.SelectableBackground()}
    //       {...props}
    //     >
    //       {children}
    //     </TouchableNativeFeedback>
    //   );
    // }
    return (
      <>
        {withoutFeedback ? (
          <TouchableWithoutFeedback
            ref={forwardRef}
            {...rest}
            disabled={!!disabled}
            onPress={this.onPress}
          >
            {children}
          </TouchableWithoutFeedback>
        ) : (
          <TouchableOpacity ref={forwardRef} {...rest} disabled={!!disabled} onPress={this.onPress}>
            {children}
          </TouchableOpacity>
        )}
      </>
    );
  }
}

export default React.forwardRef((props, ref) => <Touchable forwardRef={ref} {...props} />);
