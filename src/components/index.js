import {
  FlatList,
  TouchableOpacity,
  View,
  RefreshControl,
  Keyboard,
  Dimensions,
  SectionList,
  ToastAndroid,
  TouchableWithoutFeedback,
  TouchableHighlight,
  ScrollView,
  Linking,
  Platform,
  BackHandler,
  AppState,
  AppRegistry,
  PixelRatio,
  Modal,
  StyleSheet,
  ActivityIndicator,
  PermissionsAndroid,
  ImageBackground,
  Animated,
  Easing,
  PanResponder,
  LayoutAnimation,
  InteractionManager,
  KeyboardAvoidingView,
  Share,
  SafeAreaView,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { ListItem, Badge, Slider } from 'react-native-elements';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import WebView from 'react-native-webview';
import LinearGradient from './gradient/linearGradient';

import Touchable from './touchable';
import Text from './text/text';
import Icon from './icons/icon';
import Button from './button/button';
import Header from './header/header';
import StatusBar from './header/statusBar';
import Swiper from './swiper/swiper';
import Alert from './alert/alert';
import AlertPro from './alert/alertPro';
import Input from './input/input';
import TextInput from './input/textInput';
import Switch from './switch/switch';
import { Picker } from '@react-native-picker/picker';
import Image from './images/image';
import BaseComponent from './baseComponent';
import Popover from './popover/Popover';

export {
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
  Picker,
  RefreshControl,
  Keyboard,
  Dimensions,
  SectionList,
  TextInput,
  ToastAndroid,
  TouchableWithoutFeedback,
  TouchableHighlight,
  ScrollView,
  Linking,
  Platform,
  Alert,
  Touchable,
  BackHandler,
  NetInfo,
  AppState,
  AppRegistry,
  AsyncStorage,
  PixelRatio,
  StatusBar,
  Modal,
  Button,
  StyleSheet,
  ActivityIndicator,
  CameraRoll,
  PermissionsAndroid,
  Icon,
  KeyboardAwareScrollView,
  Input,
  ListItem,
  Header,
  ImageBackground,
  Swiper,
  Switch,
  Animated,
  Easing,
  PanResponder,
  WebView,
  LayoutAnimation,
  AlertPro,
  InteractionManager,
  KeyboardAvoidingView,
  Badge,
  Share,
  SafeAreaView,
  Slider,
  BaseComponent,
  LinearGradient,
  Popover,
  Clipboard,
};
