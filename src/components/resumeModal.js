import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import EvilIcon from 'react-native-vector-icons/EvilIcons';
import { ScrollView, View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Icon, Button } from 'react-native-elements';
import Modal from 'react-native-modalbox';
import { titleColor, desColor, baseBlueColor } from '../themes';
import I18n from '../i18n';
import { deviceHeight, footerHeight } from '../common';
import LoadingModal from './loadingModal';
import Session from '../api/session';
import constant from '../store/constant';
import resIcon from '../res';

const styles = StyleSheet.create({
  bottomResumeAreaModel: {
    height: deviceHeight / 2,
  },
  bottomModelContainer: {
    flex: 1,
  },
  bottomModelTitleWrap: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 5,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  bottomModelTitle: {
    fontSize: 16,
    color: titleColor,
    lineHeight: 40,
  },
  bottomModelClose: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  bottomModelBody: {
    height: deviceHeight / 2,
    position: 'relative',
  },
  buttonStyle: {
    backgroundColor: baseBlueColor,
    width: '72%',
    height: 40,
    borderWidth: 0,
    borderRadius: 5,
    marginLeft: '14%',
    borderColor: baseBlueColor,
    elevation: 0,
  },
  buttonStyleContainer: {
    paddingVertical: 10,
    position: 'absolute',
    bottom: footerHeight + 60,
    width: '100%',
  },
  nullText: {
    fontSize: 14,
    color: desColor,
    textAlign: 'center',
    paddingVertical: 44,
  },
});

@inject('resumeAction', 'jobAction', 'resumeStore')
@observer
export default class ResumeModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      resumeList: [],
      showLoading: false,
      isOpen: false,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.showResumeModal, this.onShow);
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.resumeAction.getAllResumes().then(
          (res) => {
            this.setState({ resumeList: res || [] });
          },
          () => {}
        );
        this.props.resumeAction.getAnnexResumes();
      }
    });
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.showResumeModal, this.onShow);
  }

  onShow = (param) => {
    if (!param || this.props.page !== param.page) return;
    this.param = param;
    this.setState({ isOpen: param.isOpen });
  };

  onSelectResume = (item) => {
    const { resumeList } = this.state;
    resumeList.forEach((l) => {
      l.isSelected = false;
    });
    item.isSelected = !item.isSelected;
    this.setState({
      resumeList: resumeList.slice(),
    });
  };

  onClosed = () => {
    this.setState({ isOpen: false });
    return true;
  };

  sendResume = async () => {
    const { resumeList } = this.state;
    const currentResume = resumeList.find((item) => item.isSelected);
    if (currentResume) {
      if (this.props.getResumeId) {
        this.props.sendResume(currentResume.resumeId, currentResume, this.param);
      } else {
        this.setState({ showLoading: true });
        const res = await this.props.jobAction
          .sendResume(this.props.jobId, {
            cvId: currentResume.resumeId,
          })
          .catch((e) => e);
        console.debug('sendResume res', res);
        if (res?.successful) {
          this.props.sendResume(currentResume.resumeId, currentResume, this.param);
        } else {
          toast.show(res?.message);
        }
        this.setState({ showLoading: false });
      }
    } else {
      toast.show(I18n.t('page_job_toast_select_resume'));
    }
  };

  preview(resume) {
    this.setState({ isOpen: false });
    if (resume.type.value === constant.resumeType.annex) {
      const {
        resumeStore: { annexResumeList },
      } = this.props;
      const currentAnnexResume = annexResumeList.find((item) => item.cvId === resume.resumeId);

      this.props.nav.navigate('annexResumePreview', {
        name: currentAnnexResume.name,
        url: currentAnnexResume.downloadHttp,
        fileName: currentAnnexResume.fileName,
        resumeId: currentAnnexResume.cvId,
      });
    } else {
      this.props.nav.navigate('resumeWebview', {
        resumeId: resume.resumeId,
        title: I18n.t('page_resume_text_previews_title'),
        des: resume.name,
      });
    }
  }

  renderOnlineType = () => {
    return (
      <Text style={{ marginLeft: 15, color: '#CCCCCC', fontSize: 12 }}>
        {I18n.t('page_resume_annex_online')}
      </Text>
    );
  };

  renderAnnexType = () => {
    return (
      <Image style={{ marginLeft: 15, width: 12, height: 13 }} source={resIcon.resumeAnnexGray} />
    );
  };

  renderItem = (item, index) => {
    let isOnline = true;
    if (item.type.value === constant.resumeType.annex) {
      isOnline = false;
    }

    return (
      <TouchableOpacity
        key={index}
        onPress={() => this.onSelectResume(item)}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          paddingVertical: 15,
          paddingHorizontal: 15,
          borderBottomColor: '#EEEEEE',
          borderBottomWidth: 1,
          alignItems: 'center',
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          {item.isSelected ? (
            <Icon type="ionicon" name="ios-checkmark-circle" size={24} color={baseBlueColor} />
          ) : (
            <Icon type="material" name="radio-button-unchecked" size={24} color={desColor} />
          )}
          <Text style={{ marginLeft: 15, color: '#333333', fontSize: 14, maxWidth: '40%' }}>
            {item.name}
          </Text>
          {isOnline ? this.renderOnlineType() : this.renderAnnexType()}
        </View>
        <View style={{ flexDirection: 'row', marginLeft: 15, alignItems: 'center' }}>
          <Button
            title={I18n.t('page_job_text_preview')}
            titleStyle={{ color: baseBlueColor, fontSize: 12 }}
            buttonStyle={{ backgroundColor: 'white', elevation: 0 }}
            onPress={() => this.preview(item)}
          />
          <Icon name="chevron-right" size={24} color={desColor} />
        </View>
      </TouchableOpacity>
    );
  };

  renderResumeList = () => {
    const { resumeList } = this.state;
    return <View>{resumeList.map(this.renderItem)}</View>;
  };

  render() {
    const { resumeList, isOpen, showLoading } = this.state;
    return (
      <Modal
        style={styles.bottomResumeAreaModel}
        position="bottom"
        onClosed={this.onClosed}
        isOpen={isOpen}
        onRequestClose={this.onClosed}
        swipeToClose={false}
        backButtonClose
      >
        <View style={styles.bottomModelContainer}>
          <View style={styles.bottomModelTitleWrap}>
            <Text style={styles.bottomModelTitle}>{I18n.t('page_job_title_choose_resume')}</Text>
            <TouchableOpacity style={styles.bottomModelClose} onPress={this.onClosed}>
              <EvilIcon name="close" size={32} color={desColor} />
            </TouchableOpacity>
          </View>
          <View style={styles.bottomModelBody}>
            <ScrollView style={{ marginBottom: footerHeight + 60 + 60 }}>
              {resumeList.length > 0 ? (
                this.renderResumeList()
              ) : (
                <Text style={styles.nullText}>{I18n.t('page_job_ph_text_no_resume')}</Text>
              )}
            </ScrollView>
            <View style={styles.buttonStyleContainer}>
              <Button
                title={
                  this.props.getResumeId ? I18n.t('page_job_btn_coonfirm') : I18n.t('page_job_send')
                }
                buttonStyle={styles.buttonStyle}
                titleStyle={{ fontSize: 14 }}
                onPress={this.sendResume}
              />
            </View>
          </View>
        </View>

        <LoadingModal isOpen={showLoading} loadingTips={false} />
      </Modal>
    );
  }
}
