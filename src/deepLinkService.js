import { Linking } from './components';
import Util from './util';
import NavigationService from './navigationService';
import Store from './store';
import constant from './store/constant';

const deepLinkType = {
  inviteColleagues: '1',
  inviteHouses: '2',
  longPayPaymentCallback: '3', // 龙支付支付回调
  recommendedJobs: '4', // 推荐工作
};

function emitterActions(param) {
  // todo: 需要根据类型发送事件
  global.emitter.emit(constant.event.rechargeSuccess, param);
}

function handleDeepLink(param) {
  console.log('DeepLink handleDeepLink', param);
  if (!param || !param.deepLinkType) {
    return;
  }
  switch (param.deepLinkType) {
    case deepLinkType.longPayPaymentCallback:
      // {"status":"ok","message":"payment success","data":{"expiredAt":"1660899442","amount":"5.00","longPayOrderId":"1327811523300691969","paymentCaptcha":"8d34ce3487b04866b0a51ca196a7d948","orderId":"1327811522755432448","sign":"8fe15b267fabadf75a5093cce6638442","secretId":"2","currency":"USD","createAt":"1660897642","status":"3","statusCode":"INIT","callbackUrl":"qxhwyxt://dev.wyst.qxh.com?deepLinkType=3"},"res":{"amount":"5.00","createAt":"1660897656","currency":"USD","currencyId":"0","downstreamMerchantLogo":null,"downstreamMerchantName":"pms物业公司","downstreamOrderId":"1327811522755432448","expiredAt":"1660899442","merchantId":"93","orderId":"1327811523300691969","paymentCaptcha":"8d34ce3487b04866b0a51ca196a7d948","remarks":null,"status":"0","userId":"1261"}}
      param.data = JSON.parse(param.data);
      console.log('DeepLink handleDeepLink longPayPaymentCallback', param);
      emitterActions(param);
      break;
    case deepLinkType.recommendedJobs:
      if (param?.jobId) {
        NavigationService.navigate('jobDetail', {
          isPush: true,
          jobId: param.jobId,
          detail: { id: param.jobId },
        });
        break;
      }
    default:
      break;
  }
}

function _handleOpenURL({ url }) {
  console.log('DeepLink _handleOpenURL', url);
  handleDeepLink(Util.getUrlParams(url));
}

let _urlLinkingListener;
function addListener() {
  _urlLinkingListener = Linking.addEventListener('url', _handleOpenURL);
}

function removeListener() {
  _urlLinkingListener?.remove();
}

function handleOpenURL(url) {
  _handleOpenURL({ url });
}

function appendDeepLinkType(url, type) {
  return `${url}&deepLinkType=${type}`;
}

/**
 * 深度链接
 * <AUTHOR>
 */
export default {
  addListener,
  removeListener,
  appendDeepLinkType,
  deepLinkType,
  handleOpenURL,
  handleDeepLink,
};
