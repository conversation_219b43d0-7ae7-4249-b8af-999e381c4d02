import configs from '../configs';

const emojiArray = [
  { emoji: '😀', code: '1f600' },
  { emoji: '😃', code: '1f603' },
  { emoji: '😄', code: '1f604' },
  { emoji: '😁', code: '1f601' },
  { emoji: '😆', code: '1f606' },
  { emoji: '😅', code: '1f605' },
  { emoji: '😂', code: '1f602' },
  { emoji: '🤣', code: '1f923' },
  { emoji: '😭', code: '1f62d' },
  { emoji: '😉', code: '1f609' },
  { emoji: '😗', code: '1f617' },
  { emoji: '😙', code: '1f619' },
  { emoji: '😚', code: '1f61a' },
  { emoji: '😘', code: '1f618' },
  { emoji: '🥰', code: '1f970' },
  { emoji: '😍', code: '1f60d' },
  { emoji: '🤩', code: '1f929' },
  { emoji: '🥳', code: '1f973' },
  { emoji: '🙃', code: '1f643' },
  { emoji: '🙂', code: '1f642' },
  { emoji: '🥲', code: '1f972' },
  { emoji: '😊', code: '1f60a' },
  { emoji: '☺️', code: '263a_fe0f' },
  { emoji: '😌', code: '1f60c' },
  { emoji: '😏', code: '1f60f' },
  { emoji: '😴', code: '1f634' },
  { emoji: '😪', code: '1f62a' },
  { emoji: '🤤', code: '1f924' },
  { emoji: '😋', code: '1f60b' },
  { emoji: '😛', code: '1f61b' },
  { emoji: '😝', code: '1f61d' },
  { emoji: '😜', code: '1f61c' },
  { emoji: '🤪', code: '1f92a' },
  { emoji: '🥴', code: '1f974' },
  { emoji: '😔', code: '1f614' },
  { emoji: '🥺', code: '1f97a' },
  { emoji: '😬', code: '1f62c' },
  { emoji: '😑', code: '1f611' },
  { emoji: '😐', code: '1f610' },
  { emoji: '😶', code: '1f636' },
  { emoji: '🤐', code: '1f910' },
  { emoji: '🤔', code: '1f914' },
  { emoji: '🤫', code: '1f92b' },
  { emoji: '🤭', code: '1f92d' },
  { emoji: '🥱', code: '1f971' },
  { emoji: '🤗', code: '1f917' },
  { emoji: '😱', code: '1f631' },
  { emoji: '🤨', code: '1f928' },
  { emoji: '🧐', code: '1f9d0' },
  { emoji: '😒', code: '1f612' },
  { emoji: '🙄', code: '1f644' },
  { emoji: '😤', code: '1f624' },
  { emoji: '😠', code: '1f620' },
  { emoji: '😡', code: '1f621' },
  { emoji: '🤬', code: '1f92c' },
  { emoji: '😞', code: '1f61e' },
  { emoji: '😓', code: '1f613' },
  { emoji: '😟', code: '1f61f' },
  { emoji: '😥', code: '1f625' },
  { emoji: '😢', code: '1f622' },
  { emoji: '☹️', code: '2639_fe0f' },
  { emoji: '🙁', code: '1f641' },
  { emoji: '😕', code: '1f615' },
  { emoji: '😰', code: '1f630' },
  { emoji: '😨', code: '1f628' },
  { emoji: '😧', code: '1f627' },
  { emoji: '😦', code: '1f626' },
  { emoji: '😮', code: '1f62e' },
  { emoji: '😯', code: '1f62f' },
  { emoji: '😲', code: '1f632' },
  { emoji: '😳', code: '1f633' },
  { emoji: '🤯', code: '1f92f' },
  { emoji: '😖', code: '1f616' },
  { emoji: '😣', code: '1f623' },
  { emoji: '😩', code: '1f629' },
  { emoji: '😫', code: '1f62b' },
  { emoji: '😵', code: '1f635' },
  { emoji: '🥶', code: '1f976' },
  { emoji: '🥵', code: '1f975' },
  { emoji: '🤢', code: '1f922' },
  { emoji: '🤮', code: '1f92e' },
  { emoji: '🤧', code: '1f927' },
  { emoji: '🤒', code: '1f912' },
  { emoji: '🤕', code: '1f915' },
  { emoji: '😷', code: '1f637' },
  { emoji: '🤥', code: '1f925' },
  { emoji: '😇', code: '1f607' },
  { emoji: '🤠', code: '1f920' },
  { emoji: '🤑', code: '1f911' },
  { emoji: '🤓', code: '1f913' },
  { emoji: '😎', code: '1f60e' },
  { emoji: '🥸', code: '1f978' },
  { emoji: '🤡', code: '1f921' },
  { emoji: '😈', code: '1f608' },
  { emoji: '👿', code: '1f47f' },
  { emoji: '👻', code: '1f47b' },
  { emoji: '🎃', code: '1f383' },
  { emoji: '💩', code: '1f4a9' },
  { emoji: '🤖', code: '1f916' },
  { emoji: '👽', code: '1f47d' },
  { emoji: '🌛', code: '1f31b' },
  { emoji: '🌜', code: '1f31c' },
  { emoji: '🌞', code: '1f31e' },
  { emoji: '🔥', code: '1f525' },
  { emoji: '💯', code: '1f4af' },
  { emoji: '🌟', code: '1f31f' },
  { emoji: '✨', code: '2728' },
  { emoji: '💥', code: '1f4a5' },
  { emoji: '🎉', code: '1f389' },
  { emoji: '🙈', code: '1f648' },
  { emoji: '🙉', code: '1f649' },
  { emoji: '🙊', code: '1f64a' },
  { emoji: '😺', code: '1f63a' },
  { emoji: '😸', code: '1f638' },
  { emoji: '😹', code: '1f639' },
  { emoji: '😻', code: '1f63b' },
  { emoji: '😼', code: '1f63c' },
  { emoji: '😽', code: '1f63d' },
  { emoji: '🙀', code: '1f640' },
  { emoji: '😿', code: '1f63f' },
  { emoji: '😾', code: '1f63e' },
  { emoji: '❤️', code: '2764_fe0f' },
  { emoji: '🧡', code: '1f9e1' },
  { emoji: '💛', code: '1f49b' },
  { emoji: '💚', code: '1f49a' },
  { emoji: '💙', code: '1f499' },
  { emoji: '💜', code: '1f49c' },
  { emoji: '🤎', code: '1f90e' },
  { emoji: '🖤', code: '1f5a4' },
  { emoji: '🤍', code: '1f90d' },
  { emoji: '💘', code: '1f498' },
  { emoji: '💝', code: '1f49d' },
  { emoji: '💖', code: '1f496' },
  { emoji: '💗', code: '1f497' },
  { emoji: '💓', code: '1f493' },
  { emoji: '💞', code: '1f49e' },
  { emoji: '💕', code: '1f495' },
  { emoji: '💌', code: '1f48c' },
  { emoji: '❣️', code: '2763_fe0f' },
  { emoji: '💔', code: '1f494' },
  { emoji: '💋', code: '1f48b' },
  { emoji: '👣', code: '1f463' },
  { emoji: '🫀', code: '1fac0' },
  { emoji: '🩸', code: '1fa78' },
  { emoji: '🦠', code: '1f9a0' },
  { emoji: '💀', code: '1f480' },
  { emoji: '👀', code: '1f440' },
  { emoji: '👁️', code: '1f441_fe0f' },
  { emoji: '🦿', code: '1f9bf' },
  { emoji: '🦾', code: '1f9be' },
  { emoji: '💪', code: '1f4aa' },
  { emoji: '💪🏻', code: '1f4aa_1f3fb' },
  { emoji: '💪🏼', code: '1f4aa_1f3fc' },
  { emoji: '💪🏽', code: '1f4aa_1f3fd' },
  { emoji: '💪🏾', code: '1f4aa_1f3fe' },
  { emoji: '💪🏿', code: '1f4aa_1f3ff' },
  { emoji: '👏', code: '1f44f' },
  { emoji: '👏🏻', code: '1f44f_1f3fb' },
  { emoji: '👏🏼', code: '1f44f_1f3fc' },
  { emoji: '👏🏽', code: '1f44f_1f3fd' },
  { emoji: '👏🏾', code: '1f44f_1f3fe' },
  { emoji: '👏🏿', code: '1f44f_1f3ff' },
  { emoji: '👍', code: '1f44d' },
  { emoji: '👍🏻', code: '1f44d_1f3fb' },
  { emoji: '👍🏼', code: '1f44d_1f3fc' },
  { emoji: '👍🏽', code: '1f44d_1f3fd' },
  { emoji: '👍🏾', code: '1f44d_1f3fe' },
  { emoji: '👍🏿', code: '1f44d_1f3ff' },
  { emoji: '👎', code: '1f44e' },
  { emoji: '👎🏻', code: '1f44e_1f3fb' },
  { emoji: '👎🏼', code: '1f44e_1f3fc' },
  { emoji: '👎🏽', code: '1f44e_1f3fd' },
  { emoji: '👎🏾', code: '1f44e_1f3fe' },
  { emoji: '👎🏿', code: '1f44e_1f3ff' },
  { emoji: '🙌', code: '1f64c' },
  { emoji: '🙌🏻', code: '1f64c_1f3fb' },
  { emoji: '🙌🏼', code: '1f64c_1f3fc' },
  { emoji: '🙌🏽', code: '1f64c_1f3fd' },
  { emoji: '🙌🏾', code: '1f64c_1f3fe' },
  { emoji: '🙌🏿', code: '1f64c_1f3ff' },
  { emoji: '👋', code: '1f44b' },
  { emoji: '👋🏻', code: '1f44b_1f3fb' },
  { emoji: '👋🏼', code: '1f44b_1f3fc' },
  { emoji: '👋🏽', code: '1f44b_1f3fd' },
  { emoji: '👋🏾', code: '1f44b_1f3fe' },
  { emoji: '👋🏿', code: '1f44b_1f3ff' },
  { emoji: '✌️', code: '270c_fe0f' },
  { emoji: '✌🏻', code: '270c_1f3fb' },
  { emoji: '✌🏼', code: '270c_1f3fc' },
  { emoji: '✌🏽', code: '270c_1f3fd' },
  { emoji: '✌🏾', code: '270c_1f3fe' },
  { emoji: '✌🏿', code: '270c_1f3ff' },
  { emoji: '🤞', code: '1f91e' },
  { emoji: '🤞🏻', code: '1f91e_1f3fb' },
  { emoji: '🤞🏼', code: '1f91e_1f3fc' },
  { emoji: '🤞🏽', code: '1f91e_1f3fd' },
  { emoji: '🤞🏾', code: '1f91e_1f3fe' },
  { emoji: '🤞🏿', code: '1f91e_1f3ff' },
  { emoji: '☝️', code: '261d_fe0f' },
  { emoji: '☝🏻', code: '261d_1f3fb' },
  { emoji: '☝🏼', code: '261d_1f3fc' },
  { emoji: '☝🏽', code: '261d_1f3fd' },
  { emoji: '☝🏾', code: '261d_1f3fe' },
  { emoji: '☝🏿', code: '261d_1f3ff' },
  { emoji: '🙏', code: '1f64f' },
  { emoji: '🙏🏻', code: '1f64f_1f3fb' },
  { emoji: '🙏🏼', code: '1f64f_1f3fc' },
  { emoji: '🙏🏽', code: '1f64f_1f3fd' },
  { emoji: '🙏🏾', code: '1f64f_1f3fe' },
  { emoji: '🙏🏿', code: '1f64f_1f3ff' },
  { emoji: '💃', code: '1f483' },
  { emoji: '💃🏻', code: '1f483_1f3fb' },
  { emoji: '💃🏼', code: '1f483_1f3fc' },
  { emoji: '💃🏽', code: '1f483_1f3fd' },
  { emoji: '💃🏾', code: '1f483_1f3fe' },
  { emoji: '💃🏿', code: '1f483_1f3ff' },
  { emoji: '🌹', code: '1f339' },
  { emoji: '🥀', code: '1f940' },
  { emoji: '🍂', code: '1f342' },
  { emoji: '🌱', code: '1f331' },
  { emoji: '🍀', code: '1f340' },
  { emoji: '❄️', code: '2744_fe0f' },
  { emoji: '🌋', code: '1f30b' },
  { emoji: '🌅', code: '1f305' },
  { emoji: '🌄', code: '1f304' },
  { emoji: '🌈', code: '1f308' },
  { emoji: '🌬️', code: '1f32c_fe0f' },
  { emoji: '⚡', code: '26a1' },
  { emoji: '💫', code: '1f4ab' },
  { emoji: '☄️', code: '2604_fe0f' },
  { emoji: '🌍', code: '1f30d' },
  { emoji: '🦄', code: '1f984' },
  { emoji: '🦎', code: '1f98e' },
  { emoji: '🐉', code: '1f409' },
  { emoji: '🦖', code: '1f996' },
  { emoji: '🐢', code: '1f422' },
  { emoji: '🐍', code: '1f40d' },
  { emoji: '🐸', code: '1f438' },
  { emoji: '🐇', code: '1f407' },
  { emoji: '🐀', code: '1f400' },
  { emoji: '🐕', code: '1f415' },
  { emoji: '🐖', code: '1f416' },
  { emoji: '🐎', code: '1f40e' },
  { emoji: '🐂', code: '1f402' },
  { emoji: '🐐', code: '1f410' },
  { emoji: '🦘', code: '1f998' },
  { emoji: '🐅', code: '1f405' },
  { emoji: '🐒', code: '1f412' },
  { emoji: '🐿️', code: '1f43f_fe0f' },
  { emoji: '🦦', code: '1f9a6' },
  { emoji: '🦇', code: '1f987' },
  { emoji: '🐓', code: '1f413' },
  { emoji: '🐣', code: '1f423' },
  { emoji: '🐤', code: '1f424' },
  { emoji: '🐥', code: '1f425' },
  { emoji: '🦅', code: '1f985' },
  { emoji: '🕊️', code: '1f54a_fe0f' },
  { emoji: '🦚', code: '1f99a' },
  { emoji: '🦭', code: '1f9ad' },
  { emoji: '🐬', code: '1f42c' },
  { emoji: '🐳', code: '1f433' },
  { emoji: '🐡', code: '1f421' },
  { emoji: '🦀', code: '1f980' },
  { emoji: '🐙', code: '1f419' },
  { emoji: '🐌', code: '1f40c' },
  { emoji: '🐜', code: '1f41c' },
  { emoji: '🦟', code: '1f99f' },
  { emoji: '🐝', code: '1f41d' },
  { emoji: '🦋', code: '1f98b' },
  { emoji: '🐾', code: '1f43e' },
  { emoji: '🍅', code: '1f345' },
  { emoji: '🍿', code: '1f37f' },
  { emoji: '☕', code: '2615' },
  { emoji: '🍻', code: '1f37b' },
  { emoji: '🥂', code: '1f942' },
  { emoji: '🍾', code: '1f37e' },
  { emoji: '🍷', code: '1f377' },
  { emoji: '🍹', code: '1f379' },
  { emoji: '🚨', code: '1f6a8' },
  { emoji: '🛸', code: '1f6f8' },
  { emoji: '🚀', code: '1f680' },
  { emoji: '🛫', code: '1f6eb' },
  { emoji: '🛬', code: '1f6ec' },
  { emoji: '🎢', code: '1f3a2' },
  { emoji: '🎊', code: '1f38a' },
  { emoji: '🎈', code: '1f388' },
  { emoji: '🎂', code: '1f382' },
  { emoji: '🎆', code: '1f386' },
  { emoji: '⚽', code: '26bd' },
  { emoji: '🎯', code: '1f3af' },
  { emoji: '🎻', code: '1f3bb' },
  { emoji: '🥁', code: '1f941' },
  { emoji: '🔋', code: '1f50b' },
  { emoji: '💸', code: '1f4b8' },
  { emoji: '💡', code: '1f4a1' },
  { emoji: '🎓', code: '1f393' },
  { emoji: '☂️', code: '2602_fe0f' },
  { emoji: '💎', code: '1f48e' },
  { emoji: '⏰', code: '23f0' },
  { emoji: '🛎️', code: '1f6ce_fe0f' },
  { emoji: '🔔', code: '1f514' },
  { emoji: '♈', code: '2648' },
  { emoji: '♉', code: '2649' },
  { emoji: '♊', code: '264a' },
  { emoji: '♋', code: '264b' },
  { emoji: '♌', code: '264c' },
  { emoji: '♍', code: '264d' },
  { emoji: '♎', code: '264e' },
  { emoji: '♏', code: '264f' },
  { emoji: '♐', code: '2650' },
  { emoji: '♑', code: '2651' },
  { emoji: '♒', code: '2652' },
  { emoji: '♓', code: '2653' },
  { emoji: '⛎', code: '26ce' },
  { emoji: '‼️', code: '203c_fe0f' },
  { emoji: '❌', code: '274c' },
  { emoji: '🎶', code: '1f3b6' },
  { emoji: '✅', code: '2705' },
  { emoji: '🆒', code: '1f192' },
  { emoji: '➕', code: '2795' },
  { emoji: '🏁', code: '1f3c1' },
];

let maxLength = 2;
emojiArray.forEach((item) => {
  if (item.emoji.length > maxLength) {
    maxLength = item.emoji.length;
  }
});

function getEmojiArray() {
  return emojiArray;
}

function isEmoji(text) {
  return !text || text.length > maxLength || text.match?.(/[\w\s+=,.，。-]/);
}

function getBigEmoji(text) {
  if (isEmoji(text)) return null;
  const item = emojiArray.find((it) => it.emoji === text);
  if (item) {
    return configs.im.bigEmojiUrl.replace('{{code}}', item.code);
  }
  return null;
}

export default {
  getEmojiArray,
  getBigEmoji,
};
