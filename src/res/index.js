import _ from 'lodash';

const res = {
  loadingMsg: require('./loading-msg.gif'),
  loadingImg: require('./loading-img.gif'),
  page404: require('./404.png'),
  iconChat: require('./icon/bt_chat.png'),
  iconChatActive: require('./icon/bt_chat_active.png'),
  iconForum: require('./icon/bt_forum.png'),
  iconForumActive: require('./icon/bt_forum_active.png'),
  iconMe: require('./icon/bt_me.png'),
  iconMeActive: require('./icon/bt_me_active.png'),
  iconWork: require('./icon/bt_work.png'),
  iconWorkActive: require('./icon/bt_work_active.png'),
  iconResume: require('./icon/bt_resume.png'),
  iconResumeActive: require('./icon/bt_resume_active.png'),
  bgLogin: require('./bg/bg_login.png'),
  iconSuccess: require('./icon/icon_successful.png'),
  iconBack: require('./icon/icon_back.png'),
  iconDown: require('./icon/icon_down.png'),
  mapImage: require('./map/map.png'),
  logo: require('./logo.png'),
  first: require('./first.png'),
  launchimage: require('./launchimage.jpg'),
  launchimage2: require('./launchimage2.jpg'),
  iconPreview: require('./icon/icon_preview.png'),
  iconRefresh: require('./icon/icon_refresh.png'),
  iconSettings: require('./icon/icon_setting.png'),
  iconEdu: require('./icon/icon_package.png'),
  iconEduTime: require('./icon/icon_edu.png'),
  iconBirth: require('./icon/icon_birth.png'),
  iconEdit: require('./icon/edit.png'),
  defaultAvatar: require('./default/default_avatar.png'),
  defaultAdvancedAvatar: require('./default/default_advanced_avatar.png'),
  defaultGroupAvatar: require('./default/default_group_avatar.png'),
  myPhone: require('./default/my_phone.png'),
  notice: require('./default/notice.png'),
  defaultEmployerAvatar: require('./default/default_employer_avatar.png'),
  cnFlag: require('./flag/cn.jpg'),
  khFlag: require('./flag/kh.jpg'),
  hkFlag: require('./flag/hk.jpg'),
  moFlag: require('./flag/mo.jpg'),
  twFlag: require('./flag/tw.jpg'),
  thFlag: require('./flag/th.jpg'),
  myFlag: require('./flag/my.jpg'),
  sgFlag: require('./flag/sg.jpg'),
  etFlag: require('./flag/et.jpg'),
  laFlag: require('./flag/la.jpg'),
  vnFlag: require('./flag/vn.jpg'),
  krFlag: require('./flag/kr.jpg'),
  jpFlag: require('./flag/jp.jpg'),
  mcFlag: require('./flag/mc.jpg'),
  phFlag: require('./flag/ph.jpg'),
  usFlag: require('./flag/us.png'),
  noData: require('./default/nothing.png'),
  fabu: require('./default/fabu.png'),
  guideline: require('./default/guideline.png'),
  resumeIcon: require('./default/resume_icon.png'),
  phoneIcon: require('./default/phone_icon.png'),
  verify: require('./default/V2.png'),
  trash: require('./default/trash.png'),
  versionBg: require('./Group3.png'),
  companyBg: require('./company_bg.png'),
  search: require('./icon/search.png'),
  scan: require('./icon/scan.png'),
  down: require('./icon/down.png'),
  citySelected: require('./icon/city_selected.png'),

  // 简历
  resumeAnnex: require('./resume/resume_annex.png'),
  resumeAnnexGray: require('./resume/resume_annex_gray.png'),
  resumeScan: require('./resume/resume_scan.png'),
  resumePhone: require('./resume/resume_phone.png'),
  resumeDoc: require('./resume/resume_doc.png'),
  resumeDocx: require('./resume/resume_docx.png'),
  resumePdf: require('./resume/resume_pdf.png'),
  resumePng: require('./resume/resume_png.png'),
  resumeJpg: require('./resume/resume_jpg.png'),
  resumeComputer: require('./resume/resume_computer.png'),
  resumeDefault: require('./resume/resume_default.png'),

  jobPhone: require('./job/phone.png'),
  jobEmail: require('./job/email.png'),

  appleLogin: require('./apple_login.png'),
  mission: require('./mission.png'),

  // 企业端icon
  tabbarHome: require('./enterprise/tabbar/tabbar_home.png'),
  tabbarHomeActive: require('./enterprise/tabbar/tabbar_home_active.png'),
  tabbarResume: require('./enterprise/tabbar/tabbar_resume.png'),
  tabbarResumeActive: require('./enterprise/tabbar/tabbar_resume_active.png'),
  tabbarChat: require('./enterprise/tabbar/tabbar_chat.png'),
  tabbarChatActive: require('./enterprise/tabbar/tabbar_chat_active.png'),
  tabbarWork: require('./enterprise/tabbar/tabbar_work.png'),
  tabbarWorkActive: require('./enterprise/tabbar/tabbar_work_active.png'),
  tabbarForum: require('./enterprise/tabbar/tabbar_forum.png'),
  tabbarForumActive: require('./enterprise/tabbar/tabbar_forum_active.png'),

  // 首页
  homeSetting: require('./enterprise/home/<USER>'),
  homeUserChange: require('./enterprise/home/<USER>'),
  homeLine: require('./enterprise/home/<USER>'),
  homeSearch: require('./enterprise/home/<USER>'),
  homeAdd: require('./enterprise/home/<USER>'),
  homeArrow: require('./enterprise/home/<USER>'),
  homeBaBg: require('./enterprise/home/<USER>'),

  iconDownEnterprise: require('./enterprise/icon/icon_down.png'),
  iconCloseEnterprise: require('./enterprise/icon/close.png'),
  iconDefaultAvatarEnterprise: require('./enterprise/icon/default_avatar.png'),
  iconGoEnterprise: require('./enterprise/icon/go.png'),
  iconErrorData: require('./enterprise/icon/icon_err.png'),
  iconNoData: require('./enterprise/icon/icon_nodata.png'),
  iconScanLineEnterprise: require('./enterprise/icon/scan.png'),
  iconUpdateBgEnterprise: require('./enterprise/icon/update_bg.png'),

  iconBtnBackWhite: require('./enterprise/icon/btn_back_white.png'),
  iconBtnBackBlack: require('./enterprise/icon/btn_back_black.png'),

  loginEyeClose: require('./enterprise/login/eye-close.png'),
  loginEyeOpen: require('./enterprise/login/eye-open.png'),
  loginChange: require('./enterprise/login/change.png'),
  loginLogo: require('./enterprise/login/logo.png'),

  iconRecordAllgEnterprise: require('./enterprise/icon/record_all.png'),
  iconRecordWaitEnterprise: require('./enterprise/icon/record_wait.png'),
  iconRecordPaidEnterprise: require('./enterprise/icon/record_paid.png'),
  iconRechargeEnterprise: require('./enterprise/icon/recharge.png'),
  iconAmountCheckEnterprise: require('./enterprise/icon/amount-check.png'),
  iconMethodCheckedkEnterprise: require('./enterprise/icon/method-checked.png'),
  iconAlipayEnterprise: require('./enterprise/icon/alipay.png'),
  iconWechatPayEnterprise: require('./enterprise/icon/wechat-pay.png'),

  resumeFemaleEnterprise: require('./enterprise/resume/female.png'),
  resumeMaleEnterprise: require('./enterprise/resume/male.png'),
  resumeStatus0Enterprise: require('./enterprise/resume/status0.png'),
  resumeStatus1Enterprise: require('./enterprise/resume/status1.png'),
  resumeStatus2Enterprise: require('./enterprise/resume/status2.png'),

  resumeInfo1Enterprise: require('./enterprise/resume/info1.png'),
  resumeInfo2Enterprise: require('./enterprise/resume/info2.png'),
  resumeInfo3Enterprise: require('./enterprise/resume/info3.png'),
  resumeInfo4Enterprise: require('./enterprise/resume/info4.png'),
  resumeInfo5Enterprise: require('./enterprise/resume/info5.png'),
  resumeInfo6Enterprise: require('./enterprise/resume/info6.png'),

  jobDelEnterprise: require('./enterprise/job/job_delete.png'),
  jobViewEnterprise: require('./enterprise/job/job_view.png'),
  jobEditEnterprise: require('./enterprise/job/job_edit.png'),
  jobSaveEnterprise: require('./enterprise/job/job_save.png'),
  jobOfflineEnterprise: require('./enterprise/job/job_offline.png'),
  jobCopyEnterprise: require('./enterprise/job/job_copy.png'),
  jobPublishEnterprise: require('./enterprise/job/job_publish.png'),
  jobRightArrowEnterprise: require('./enterprise/job/right_arrow.png'),
  jobModalCloseEnterprise: require('./enterprise/job/modal_close.png'),
  jobAlertEnterprise: require('./enterprise/job/job_alert.png'),
  iconRecordEnterprise: require('./enterprise/icon/record.png'),
  iconLogoEnterprise: require('./enterprise/icon/logo.png'),
  iconFlowerEnterprise: require('./enterprise/icon/flower.png'),
  resumeFilterEnterprise: require('./enterprise/resume/filter.png'),
  resumeUnfavEnterprise: require('./enterprise/resume/unfav.png'),
  resumeSearchEnterprise: require('./enterprise/resume/search.png'),
  resumeFavEnterprise: require('./enterprise/resume/fav.png'),
  resumeCheckedEnterprise: require('./enterprise/resume/checked.png'),
  clearEnterprise: require('./enterprise/login/clear.png'),
  msgEnterprise: require('./enterprise/icon/msg.png'),

  addImage: require('./enterprise/company/add_image.png'),
  editCompany: require('./enterprise/company/edit_company.png'),
  editLogo: require('./enterprise/company/edit_logo.png'),
  iconVipEnterprise: require('./enterprise/home/<USER>'),
  companyEditBgEnterprise: require('./enterprise/home/<USER>'),
  iconPaywayEnterprise: require('./enterprise/icon/pay_aba.png'),
  iconPayKHQREnterprise: require('./enterprise/icon/pay_khqr.png'),
  iconPayPIPAYEnterprise: require('./enterprise/icon/pay_pipay.png'),
  iconLongpayEnterprise: require('./enterprise/icon/longpay.png'),
  iconSubmitSuccessEnterprise: require('./enterprise/icon/submit_success.png'),
  iconWalletEnterprise: require('./enterprise/icon/wallet.png'),
  loginGuide: require('./enterprise/login/login_guide.png'),

  //聊天页
  chatBook: require('./chat/book.png'),
  chatBookActive: require('./chat/chat-book-active.png'),
  chatSearch: require('./chat/search.png'),
  chatAddBookFir: require('./chat/addFir.png'),
  chatNewFir: require('./chat/newFir.png'),
  chatGroupList: require('./chat/group_list.png'),
  chatMenuAdd: require('./chat/chat-menu-add.png'),
  chatMenuGroup: require('./chat/chat-menu-group.png'),
  chatMenuScan: require('./chat/chat-menu-scan.png'),
  chatMenuMedia: require('./chat/audio_video.png'),
  chatCallBook: require('./chat/call_book.png'),
  chatMenuSingle: require('./chat/chat-menu-single.png'),
  chatAccountRedpacket: require('./chat/account-redpacket.png'),
  chatRedBg: require('./chat/red_bg.png'),
  chatRedClose: require('./chat/red_close.png'),
  // chatRedPacketDetailBg: require('./bg/red_packet_bg.png'),
  chatLittleRedPacket: require('./chat/little_red_packet.png'),
  chatTradeRedBag: require('./chat/trade_red_bag.png'),
  groupCreate: require('./chat/addGroup.png'),
  groupUpload: require('./chat/upload.png'),
  receiveType: require('./chat/receiveType.png'),
  newChat: require('./chat/newchat.png'),

  addressAdd: require('./chat/address_add.png'),

  messageVoice: require('./chat/message/voice_input.png'),
  messageKeyboard: require('./chat/message/keyboard.png'),
  messageEmoji: require('./chat/message/emoji.png'),
  messageMoreFeat: require('./chat/message/more_feat.png'),
  messageSend: require('./chat/message/send.png'),
  messageSendStatusFail: require('./chat/message/send_status_fail.png'),
  messageFeatCamera: require('./chat/message/feat_camera.png'),
  messageFeatDocument: require('./chat/message/feat_document.png'),
  messageFeatPhoto: require('./chat/message/feat_photo.png'),
  messageFeatReceive: require('./chat/message/feat_receive.png'),
  messageFeatRedPocket: require('./chat/message/feat_red_pocket.png'),
  messageFeatTransfer: require('./chat/message/feat_transfer.png'),
  messageFeatVideo: require('./chat/message/feat_video.png'),
  messageFeatAudio: require('./chat/message/feat_audio.png'),
  featChangeJob: require('./chat/message/feat_change_job.png'),
  featCompanyBusiness: require('./chat/message/feat_company_business.png'),

  messageTransferAmount: require('./chat/message/transfer_amount.png'),
  messageTransferComplete: require('./chat/message/transfer_complete.png'),
  messageTransferArrowTop: require('./chat/message/transfer_arrow_top.png'),
  messageReceiveArrowWhite: require('./chat/message/receive_arrow_white.png'),
  messageIconDisturb: require('./chat/message/icon_disturb.png'),
  messageCancelQuote: require('./chat/message/cancel_quote.png'),
  icTopChat: require('./chat/ic_top_chat.png'),
  findPhone: require('./chat/find_phone.png'),

  fileTypeExcel: require('./chat/message/file_type_excel.png'),
  fileTypePDF: require('./chat/message/file_type_pdf.png'),
  fileTypeZip: require('./chat/message/file_type_zip.png'),
  fileTypeWord: require('./chat/message/file_type_word.png'),
  fileTypePPT: require('./chat/message/file_type_ppt.png'),
  fileTypeTxt: require('./chat/message/file_type_txt.png'),
  fileTypeUnknown: require('./chat/message/file_type_unknown.png'),

  audioCancelRecord: require('./chat/message/ic_cancel_record.png'),
  audioRecordInvalid: require('./chat/message/record_invalid.png'),
  audioPlayLeft1: require('./chat/message/play_audio_l1.png'),
  audioPlayLeft2: require('./chat/message/play_audio_l2.png'),
  audioPlayLeft3: require('./chat/message/play_audio_l3.png'),
  audioPlayRight1: require('./chat/message/play_audio_r1.png'),
  audioPlayRight2: require('./chat/message/play_audio_r2.png'),
  audioPlayRight3: require('./chat/message/play_audio_r3.png'),
  audioRecordAnim1: require('./chat/message/record_anim_1.png'),
  audioRecordAnim2: require('./chat/message/record_anim_2.png'),
  audioRecordAnim3: require('./chat/message/record_anim_3.png'),
  scanPic: require('./chat/pic.png'),

  chatMenu: require('./chat/message/menu.png'),
  chatDownload: require('./chat/message/download.png'),

  tradeWait: require('./chat/waiting.png'),
  tradeDone: require('./chat/done.png'),
  tradeBack: require('./chat/back.png'),
  addGroupMember: require('./chat/add_group_member.png'),
  removeGroupMember: require('./chat/remove_group_member.png'),

  // bindphone: require('./login/bindphone.png'),
  // bindemail: require('./login/bindemail.png'),
  // bindproblem: require('./login/bindproblem.png'),
  messageRedPacket: require('./chat/message/messageRedpacket.png'),
  messageReceived: require('./chat/message/messageReceived.png'),

  chatbookAddFir: require('./chat/chatbook_add.png'),
  chatTag: require('./chat/chat_tag.png'),
  chatAddFir: require('./chat/chat_add.png'),
  chatAudio: require('./chat/chat_audio.png'),
  chatVideo: require('./chat/chat_video.png'),
  chatCall: require('./chat/chat_call.png'),
  chatReceive: require('./chat/chat_receive.png'),
  chatSetting: require('./chat/chat_setting.png'),
  filterFavActive: require('./chat/filter_fav_active.png'),
  filterFav: require('./chat/filter_fav.png'),
  filterGroupActive: require('./chat/filter_group_active.png'),
  filterGroup: require('./chat/filter_group.png'),
  filterPcActive: require('./chat/filter_pc_active.png'),
  filterPc: require('./chat/filter_pc.png'),
  phoneChatbook: require('./chat/phone_chatbook.png'),
  tabChatActive: require('./chat/tab_chat_active.png'),
  tabChat: require('./chat/tab_chat.png'),
  tabPhoneActive: require('./chat/tab_phone_active.png'),
  tabPhone: require('./chat/tab_phone.png'),

  chatTooltipsAdd: require('./chat/tooltips/tooltips_add.png'),
  chatTooltipsBroadcast: require('./chat/tooltips/tooltips_broadcast.png'),
  chatTooltipsChat: require('./chat/tooltips/tooltips_chat.png'),
  chatTooltipsMedia: require('./chat/tooltips/tooltips_media.png'),
  chatTooltipsScan: require('./chat/tooltips/tooltips_scan.png'),
  chatTooltipsStatus: require('./chat/tooltips/tooltips_status.png'),
  chatTooltipsUser: require('./chat/tooltips/tooltips_user.png'),

  chatAddChatbook: require('./chat/new/chat_add_chatbook.png'),
  chatAddQrcode: require('./chat/new/chat_add_qrcode.png'),
  chatAddScan: require('./chat/new/chat_add_scan.png'),
  chatAddShare: require('./chat/new/chat_add_share.png'),
  chatDetailEdit: require('./chat/new/detail_edit.png'),
  chatDetailChat: require('./chat/new/detail_chat.png'),
  chatDetailAudio: require('./chat/new/detail_audio.png'),
  chatDetailAudioDisabled: require('./chat/new/detail_audio_disabled.png'),
  chatDetailTransfer: require('./chat/new/detail_transfer.png'),
  chatDetailTransferDisabled: require('./chat/new/detail_transfer_disabled.png'),
  chatDetailVideo: require('./chat/new/detail_video.png'),
  chatDetailVideoDisabled: require('./chat/new/detail_video_disabled.png'),
  qrcodeScan: require('./chat/new/qrcode_scan.png'),
  qrcodeShare: require('./chat/new/qrcode_share.png'),
  qrcodeSave: require('./chat/new/qrcode_save.png'),
  chatDefaultAvatar: require('./chat/new/default_avatar.png'),

  chatMessageFeatCard: require('./chat/message/feat_card.png'),
  chatMessageFeatLocation: require('./chat/message/feat_location.png'),
  chatBtnBackBlack: require('./chat/message/btn_back_black.png'),
  chatHeaderMore: require('./chat/message/header_more.png'),
  chatMessageCamera: require('./chat/message/message_camera.png'),
  chatMessagePic: require('./chat/message/message_pic.png'),
  chatGroupMedia: require('./chat/message/chat_group_media.png'),

  // loginTextLogo: require('./login/longchat.png'),
  // loginLongPayLogo: require('./login/longpay_logo.png'),
  messageRobotAvatar: require('./chat/robotavatar.png'),

  chatMsgRead0: require('./chat/message/msg_read_0.png'),
  chatMsgRead1: require('./chat/message/msg_read_1.png'),
  chatMsgRead2: require('./chat/message/msg_read_2.png'),
  chatBubbleLeft: require('./chat/bubble_left.png'),
  chatBubbleRight: require('./chat/bubble_right.png'),
  chatGroupNotice: require('./chat/new/group_notice.png'),
  audioPlay: require('./chat/new/audio_play.png'),
  audioPlayGray: require('./chat/new/audio_play_gray.png'),
  audioTimeline: require('./chat/new/audio_timeline.png'),
  reply: require('./chat/reply.png'),
  audioPlaying1: require('./chat/message/audio_playing_1.png'),
  audioPlaying2: require('./chat/message/audio_playing_2.png'),
  audioPlaying3: require('./chat/message/audio_playing_3.png'),

  chatBookArrow: require('./chat/chat_book_arrow.png'),
  chatResume: require('./chat/chat_resume.png'),
  chatResumeReq: require('./chat/chat_resume_req.png'),
  chatResumeSend: require('./chat/chat_resume_send.png'),
  chatInterviewReq: require('./chat/chat_interview_req.png'),
  chatInterviewReqRed: require('./chat/chat_interview_req_red.png'),
  chatHeaderInterviewReq: require('./chat/chat_header_interview_req.png'),

  audioCall: require('./call/audio_call.png'),
  videoCall: require('./call/video_call.png'),
  messageAudio: require('./call/call_audio.png'),
  messageVideo: require('./call/call_video.png'),
  cameraSwitch: require('./call/camera_enable.png'),
  switchVoice: require('./call/switch_voice.png'),
  hungUp: require('./call/hung_up.png'),
  micOpen: require('./call/mic_open.png'),
  micClose: require('./call/mic_close.png'),
  speakClose: require('./call/speak_close.png'),
  speakOpen: require('./call/speak_open.png'),
  zoom: require('./call/zoom.png'),
  rejectCall: require('./call/reject_call.png'),
  videoAccept: require('./call/video_accept.png'),
  audioAccept: require('./call/audio_accept.png'),
  zoomAudio: require('./call/zoom_audio.png'),
  meetingAddUser: require('./call/add_user.png'),
  meetingCameraDisable: require('./call/camera_disable.png'),
  meetingCameraEnable: require('./call/camera_enable.png'),
  meetingVideoClose: require('./call/video_close.png'),
  meetingVideoOpen: require('./call/video_open.png'),
  meetingVoiceIcon: require('./call/voice_icon.png'),

  iconBackDark: require('./icon/backd.png'),
  iconVerify: require('./job/verify.png'),
};

export default res;

/**
 * 获取默认头像
 * @param {*} avatar
 */
export function getAvatarSource(avatar) {
  return avatar && _.isString(avatar) && avatar.indexOf('http') === 0
    ? { uri: avatar }
    : res.defaultAvatar;
}

/**
 * 获取默认公司图片
 * @param {*} avatar
 */
export function getEmployerAvatarSource(avatar) {
  return avatar && _.isString(avatar) && avatar.indexOf('http') === 0
    ? { uri: avatar }
    : res.defaultEmployerAvatar;
}
