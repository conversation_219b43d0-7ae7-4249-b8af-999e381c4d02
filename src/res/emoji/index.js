import { zh_emoji } from './zh';
import { en_emoji } from './en';
import { km_emoji } from './km';

const pinupList = {
  ajmd: {
    ajmd001: { img: require('./ajmd/ajmd001.png') },
    ajmd041: { img: require('./ajmd/ajmd041.png') },
    ajmd040: { img: require('./ajmd/ajmd040.png') },
    ajmd029: { img: require('./ajmd/ajmd029.png') },
    ajmd028: { img: require('./ajmd/ajmd028.png') },
    ajmd045: { img: require('./ajmd/ajmd045.png') },
    ajmd044: { img: require('./ajmd/ajmd044.png') },
    ajmd047: { img: require('./ajmd/ajmd047.png') },
    ajmd046: { img: require('./ajmd/ajmd046.png') },
    ajmd023: { img: require('./ajmd/ajmd023.png') },
    ajmd022: { img: require('./ajmd/ajmd022.png') },
    ajmd021: { img: require('./ajmd/ajmd021.png') },
    ajmd020: { img: require('./ajmd/ajmd020.png') },
    ajmd027: { img: require('./ajmd/ajmd027.png') },
    ajmd026: { img: require('./ajmd/ajmd026.png') },
    ajmd025: { img: require('./ajmd/ajmd025.png') },
    ajmd024: { img: require('./ajmd/ajmd024.png') },
    ajmd042: { img: require('./ajmd/ajmd042.png') },
    ajmd012: { img: require('./ajmd/ajmd012.png') },
    ajmd013: { img: require('./ajmd/ajmd013.png') },
    ajmd010: { img: require('./ajmd/ajmd010.png') },
    ajmd011: { img: require('./ajmd/ajmd011.png') },
    ajmd016: { img: require('./ajmd/ajmd016.png') },
    ajmd017: { img: require('./ajmd/ajmd017.png') },
    ajmd014: { img: require('./ajmd/ajmd014.png') },
    ajmd015: { img: require('./ajmd/ajmd015.png') },
    ajmd018: { img: require('./ajmd/ajmd018.png') },
    ajmd019: { img: require('./ajmd/ajmd019.png') },
    ajmd038: { img: require('./ajmd/ajmd038.png') },
    ajmd039: { img: require('./ajmd/ajmd039.png') },
    ajmd030: { img: require('./ajmd/ajmd030.png') },
    ajmd031: { img: require('./ajmd/ajmd031.png') },
    ajmd032: { img: require('./ajmd/ajmd032.png') },
    ajmd033: { img: require('./ajmd/ajmd033.png') },
    ajmd034: { img: require('./ajmd/ajmd034.png') },
    ajmd035: { img: require('./ajmd/ajmd035.png') },
    ajmd036: { img: require('./ajmd/ajmd036.png') },
    ajmd037: { img: require('./ajmd/ajmd037.png') },
    ajmd048: { img: require('./ajmd/ajmd048.png') },
    ajmd005: { img: require('./ajmd/ajmd005.png') },
    ajmd004: { img: require('./ajmd/ajmd004.png') },
    ajmd007: { img: require('./ajmd/ajmd007.png') },
    ajmd006: { img: require('./ajmd/ajmd006.png') },
    ajmd003: { img: require('./ajmd/ajmd003.png') },
    ajmd002: { img: require('./ajmd/ajmd002.png') },
    ajmd009: { img: require('./ajmd/ajmd009.png') },
    ajmd008: { img: require('./ajmd/ajmd008.png') },
    ajmd043: { img: require('./ajmd/ajmd043.png') },
  },
  xxy: {
    xxy001: { img: require('./xxy/xxy001.png') },
    xxy023: { img: require('./xxy/xxy023.png') },
    xxy036: { img: require('./xxy/xxy036.png') },
    xxy022: { img: require('./xxy/xxy022.png') },
    xxy005: { img: require('./xxy/xxy005.png') },
    xxy004: { img: require('./xxy/xxy004.png') },
    xxy007: { img: require('./xxy/xxy007.png') },
    xxy006: { img: require('./xxy/xxy006.png') },
    xxy003: { img: require('./xxy/xxy003.png') },
    xxy002: { img: require('./xxy/xxy002.png') },
    xxy027: { img: require('./xxy/xxy027.png') },
    xxy026: { img: require('./xxy/xxy026.png') },
    xxy025: { img: require('./xxy/xxy025.png') },
    xxy024: { img: require('./xxy/xxy024.png') },
    xxy009: { img: require('./xxy/xxy009.png') },
    xxy008: { img: require('./xxy/xxy008.png') },
    xxy021: { img: require('./xxy/xxy021.png') },
    xxy020: { img: require('./xxy/xxy020.png') },
    xxy030: { img: require('./xxy/xxy030.png') },
    xxy031: { img: require('./xxy/xxy031.png') },
    xxy033: { img: require('./xxy/xxy033.png') },
    xxy032: { img: require('./xxy/xxy032.png') },
    xxy040: { img: require('./xxy/xxy040.png') },
    xxy029: { img: require('./xxy/xxy029.png') },
    xxy034: { img: require('./xxy/xxy034.png') },
    xxy035: { img: require('./xxy/xxy035.png') },
    xxy016: { img: require('./xxy/xxy016.png') },
    xxy017: { img: require('./xxy/xxy017.png') },
    xxy014: { img: require('./xxy/xxy014.png') },
    xxy015: { img: require('./xxy/xxy015.png') },
    xxy012: { img: require('./xxy/xxy012.png') },
    xxy013: { img: require('./xxy/xxy013.png') },
    xxy010: { img: require('./xxy/xxy010.png') },
    xxy011: { img: require('./xxy/xxy011.png') },
    xxy038: { img: require('./xxy/xxy038.png') },
    xxy037: { img: require('./xxy/xxy037.png') },
    xxy028: { img: require('./xxy/xxy028.png') },
    xxy018: { img: require('./xxy/xxy018.png') },
    xxy019: { img: require('./xxy/xxy019.png') },
    xxy039: { img: require('./xxy/xxy039.png') },
  },
  lt: {
    lt001: { img: require('./lt/lt001.png') },
    lt005: { img: require('./lt/lt005.png') },
    lt004: { img: require('./lt/lt004.png') },
    lt007: { img: require('./lt/lt007.png') },
    lt006: { img: require('./lt/lt006.png') },
    lt010: { img: require('./lt/lt010.png') },
    lt003: { img: require('./lt/lt003.png') },
    lt002: { img: require('./lt/lt002.png') },
    lt011: { img: require('./lt/lt011.png') },
    lt009: { img: require('./lt/lt009.png') },
    lt008: { img: require('./lt/lt008.png') },
    lt014: { img: require('./lt/lt014.png') },
    lt019: { img: require('./lt/lt019.png') },
    lt017: { img: require('./lt/lt017.png') },
    lt016: { img: require('./lt/lt016.png') },
    lt015: { img: require('./lt/lt015.png') },
    lt018: { img: require('./lt/lt018.png') },
    lt012: { img: require('./lt/lt012.png') },
    lt020: { img: require('./lt/lt020.png') },
    lt013: { img: require('./lt/lt013.png') },
  },
  emoji: {
    emoji_136: { img: require('./emoji/emoji_136.png') },
    emoji_137: { img: require('./emoji/emoji_137.png') },
    emoji_134: { img: require('./emoji/emoji_134.png') },
    emoji_135: { img: require('./emoji/emoji_135.png') },
    emoji_132: { img: require('./emoji/emoji_132.png') },
    emoji_133: { img: require('./emoji/emoji_133.png') },
    emoji_130: { img: require('./emoji/emoji_130.png') },
    emoji_131: { img: require('./emoji/emoji_131.png') },
    emoji_45: { img: require('./emoji/emoji_45.png') },
    emoji_44: { img: require('./emoji/emoji_44.png') },
    emoji_47: { img: require('./emoji/emoji_47.png') },
    emoji_46: { img: require('./emoji/emoji_46.png') },
    emoji_41: { img: require('./emoji/emoji_41.png') },
    emoji_40: { img: require('./emoji/emoji_40.png') },
    emoji_138: { img: require('./emoji/emoji_138.png') },
    emoji_139: { img: require('./emoji/emoji_139.png') },
    emoji_109: { img: require('./emoji/emoji_109.png') },
    emoji_108: { img: require('./emoji/emoji_108.png') },
    emoji_103: { img: require('./emoji/emoji_103.png') },
    emoji_102: { img: require('./emoji/emoji_102.png') },
    emoji_101: { img: require('./emoji/emoji_101.png') },
    emoji_100: { img: require('./emoji/emoji_100.png') },
    emoji_107: { img: require('./emoji/emoji_107.png') },
    emoji_106: { img: require('./emoji/emoji_106.png') },
    emoji_105: { img: require('./emoji/emoji_105.png') },
    emoji_104: { img: require('./emoji/emoji_104.png') },
    emoji_38: { img: require('./emoji/emoji_38.png') },
    emoji_39: { img: require('./emoji/emoji_39.png') },
    emoji_30: { img: require('./emoji/emoji_30.png') },
    emoji_31: { img: require('./emoji/emoji_31.png') },
    emoji_32: { img: require('./emoji/emoji_32.png') },
    emoji_33: { img: require('./emoji/emoji_33.png') },
    emoji_34: { img: require('./emoji/emoji_34.png') },
    emoji_35: { img: require('./emoji/emoji_35.png') },
    emoji_36: { img: require('./emoji/emoji_36.png') },
    emoji_37: { img: require('./emoji/emoji_37.png') },
    emoji_118: { img: require('./emoji/emoji_118.png') },
    emoji_119: { img: require('./emoji/emoji_119.png') },
    emoji_114: { img: require('./emoji/emoji_114.png') },
    emoji_115: { img: require('./emoji/emoji_115.png') },
    emoji_116: { img: require('./emoji/emoji_116.png') },
    emoji_117: { img: require('./emoji/emoji_117.png') },
    emoji_110: { img: require('./emoji/emoji_110.png') },
    emoji_111: { img: require('./emoji/emoji_111.png') },
    emoji_112: { img: require('./emoji/emoji_112.png') },
    emoji_113: { img: require('./emoji/emoji_113.png') },
    emoji_82: { img: require('./emoji/emoji_82.png') },
    emoji_29: { img: require('./emoji/emoji_29.png') },
    emoji_28: { img: require('./emoji/emoji_28.png') },
    emoji_23: { img: require('./emoji/emoji_23.png') },
    emoji_22: { img: require('./emoji/emoji_22.png') },
    emoji_21: { img: require('./emoji/emoji_21.png') },
    emoji_20: { img: require('./emoji/emoji_20.png') },
    emoji_27: { img: require('./emoji/emoji_27.png') },
    emoji_26: { img: require('./emoji/emoji_26.png') },
    emoji_25: { img: require('./emoji/emoji_25.png') },
    emoji_24: { img: require('./emoji/emoji_24.png') },
    emoji_49: { img: require('./emoji/emoji_49.png') },
    emoji_48: { img: require('./emoji/emoji_48.png') },
    emoji_200: { img: require('./emoji/emoji_200.png') },
    emoji_97: { img: require('./emoji/emoji_97.png') },
    emoji_94: { img: require('./emoji/emoji_94.png') },
    emoji_95: { img: require('./emoji/emoji_95.png') },
    emoji_92: { img: require('./emoji/emoji_92.png') },
    emoji_93: { img: require('./emoji/emoji_93.png') },
    emoji_90: { img: require('./emoji/emoji_90.png') },
    emoji_91: { img: require('./emoji/emoji_91.png') },
    emoji_161: { img: require('./emoji/emoji_161.png') },
    emoji_160: { img: require('./emoji/emoji_160.png') },
    emoji_163: { img: require('./emoji/emoji_163.png') },
    emoji_162: { img: require('./emoji/emoji_162.png') },
    emoji_165: { img: require('./emoji/emoji_165.png') },
    emoji_164: { img: require('./emoji/emoji_164.png') },
    emoji_98: { img: require('./emoji/emoji_98.png') },
    emoji_166: { img: require('./emoji/emoji_166.png') },
    emoji_18: { img: require('./emoji/emoji_18.png') },
    emoji_19: { img: require('./emoji/emoji_19.png') },
    emoji_16: { img: require('./emoji/emoji_16.png') },
    emoji_17: { img: require('./emoji/emoji_17.png') },
    emoji_14: { img: require('./emoji/emoji_14.png') },
    emoji_15: { img: require('./emoji/emoji_15.png') },
    emoji_12: { img: require('./emoji/emoji_12.png') },
    emoji_13: { img: require('./emoji/emoji_13.png') },
    emoji_10: { img: require('./emoji/emoji_10.png') },
    emoji_11: { img: require('./emoji/emoji_11.png') },
    emoji_89: { img: require('./emoji/emoji_89.png') },
    emoji_88: { img: require('./emoji/emoji_88.png') },
    emoji_43: { img: require('./emoji/emoji_43.png') },
    emoji_81: { img: require('./emoji/emoji_81.png') },
    emoji_80: { img: require('./emoji/emoji_80.png') },
    emoji_83: { img: require('./emoji/emoji_83.png') },
    emoji_42: { img: require('./emoji/emoji_42.png') },
    emoji_85: { img: require('./emoji/emoji_85.png') },
    emoji_84: { img: require('./emoji/emoji_84.png') },
    emoji_87: { img: require('./emoji/emoji_87.png') },
    emoji_86: { img: require('./emoji/emoji_86.png') },
    emoji_01: { img: require('./emoji/emoji_01.png') },
    emoji_00: { img: require('./emoji/emoji_00.png') },
    emoji_03: { img: require('./emoji/emoji_03.png') },
    emoji_02: { img: require('./emoji/emoji_02.png') },
    emoji_05: { img: require('./emoji/emoji_05.png') },
    emoji_04: { img: require('./emoji/emoji_04.png') },
    emoji_07: { img: require('./emoji/emoji_07.png') },
    emoji_06: { img: require('./emoji/emoji_06.png') },
    emoji_09: { img: require('./emoji/emoji_09.png') },
    emoji_08: { img: require('./emoji/emoji_08.png') },
    emoji_96: { img: require('./emoji/emoji_96.png') },
    emoji_58: { img: require('./emoji/emoji_58.png') },
    emoji_59: { img: require('./emoji/emoji_59.png') },
    emoji_149: { img: require('./emoji/emoji_149.png') },
    emoji_148: { img: require('./emoji/emoji_148.png') },
    emoji_147: { img: require('./emoji/emoji_147.png') },
    emoji_146: { img: require('./emoji/emoji_146.png') },
    emoji_145: { img: require('./emoji/emoji_145.png') },
    emoji_144: { img: require('./emoji/emoji_144.png') },
    emoji_143: { img: require('./emoji/emoji_143.png') },
    emoji_142: { img: require('./emoji/emoji_142.png') },
    emoji_141: { img: require('./emoji/emoji_141.png') },
    emoji_140: { img: require('./emoji/emoji_140.png') },
    emoji_74: { img: require('./emoji/emoji_74.png') },
    emoji_75: { img: require('./emoji/emoji_75.png') },
    emoji_76: { img: require('./emoji/emoji_76.png') },
    emoji_77: { img: require('./emoji/emoji_77.png') },
    emoji_70: { img: require('./emoji/emoji_70.png') },
    emoji_71: { img: require('./emoji/emoji_71.png') },
    emoji_72: { img: require('./emoji/emoji_72.png') },
    emoji_73: { img: require('./emoji/emoji_73.png') },
    emoji_78: { img: require('./emoji/emoji_78.png') },
    emoji_79: { img: require('./emoji/emoji_79.png') },
    emoji_56: { img: require('./emoji/emoji_56.png') },
    emoji_57: { img: require('./emoji/emoji_57.png') },
    emoji_99: { img: require('./emoji/emoji_99.png') },
    emoji_150: { img: require('./emoji/emoji_150.png') },
    emoji_151: { img: require('./emoji/emoji_151.png') },
    emoji_152: { img: require('./emoji/emoji_152.png') },
    emoji_67: { img: require('./emoji/emoji_67.png') },
    emoji_66: { img: require('./emoji/emoji_66.png') },
    emoji_65: { img: require('./emoji/emoji_65.png') },
    emoji_64: { img: require('./emoji/emoji_64.png') },
    emoji_63: { img: require('./emoji/emoji_63.png') },
    emoji_62: { img: require('./emoji/emoji_62.png') },
    emoji_61: { img: require('./emoji/emoji_61.png') },
    emoji_60: { img: require('./emoji/emoji_60.png') },
    emoji_69: { img: require('./emoji/emoji_69.png') },
    emoji_68: { img: require('./emoji/emoji_68.png') },
    emoji_del: { img: require('./emoji/emoji_del.png') },
    emoji_125: { img: require('./emoji/emoji_125.png') },
    emoji_124: { img: require('./emoji/emoji_124.png') },
    emoji_127: { img: require('./emoji/emoji_127.png') },
    emoji_126: { img: require('./emoji/emoji_126.png') },
    emoji_121: { img: require('./emoji/emoji_121.png') },
    emoji_120: { img: require('./emoji/emoji_120.png') },
    emoji_123: { img: require('./emoji/emoji_123.png') },
    emoji_122: { img: require('./emoji/emoji_122.png') },
    emoji_52: { img: require('./emoji/emoji_52.png') },
    emoji_53: { img: require('./emoji/emoji_53.png') },
    emoji_50: { img: require('./emoji/emoji_50.png') },
    emoji_51: { img: require('./emoji/emoji_51.png') },
    emoji_129: { img: require('./emoji/emoji_129.png') },
    emoji_128: { img: require('./emoji/emoji_128.png') },
    emoji_54: { img: require('./emoji/emoji_54.png') },
    emoji_55: { img: require('./emoji/emoji_55.png') },
  },
};

// 所有语言的表情合并
const emojiList = {
  emoji: Object.assign({}, zh_emoji, en_emoji, km_emoji),
};

Object.keys(emojiList).forEach((emojiAlbum) => {
  const emojiItem = emojiList[emojiAlbum];
  Object.keys(emojiItem).forEach((emojiKey) => {
    const item = emojiItem[emojiKey];
    const keyName = item.file.replace('.png', '');
    emojiList[emojiAlbum][emojiKey].img = pinupList.emoji[keyName].img;
  });
});

// 中文表情
const zh_emojiList = {
  emoji: zh_emoji,
};

Object.keys(zh_emojiList).forEach((emojiAlbum) => {
  const zh_emojiItem = zh_emojiList[emojiAlbum];
  Object.keys(zh_emojiItem).forEach((emojiKey) => {
    const item = zh_emojiItem[emojiKey];
    const keyName = item.file.replace('.png', '');
    zh_emojiList[emojiAlbum][emojiKey].img = pinupList.emoji[keyName].img;
  });
});

// 英文表情
const en_emojiList = {
  emoji: en_emoji,
};

Object.keys(en_emojiList).forEach((emojiAlbum) => {
  const en_emojiItem = en_emojiList[emojiAlbum];
  Object.keys(en_emojiItem).forEach((emojiKey) => {
    const item = en_emojiItem[emojiKey];
    const keyName = item.file.replace('.png', '');
    en_emojiList[emojiAlbum][emojiKey].img = pinupList.emoji[keyName].img;
  });
});

// 高棉文表情

const km_emojiList = {
  emoji: km_emoji,
};

Object.keys(km_emojiList).forEach((emojiAlbum) => {
  const km_emojiItem = km_emojiList[emojiAlbum];
  Object.keys(km_emojiItem).forEach((emojiKey) => {
    const item = km_emojiItem[emojiKey];
    const keyName = item.file.replace('.png', '');
    km_emojiList[emojiAlbum][emojiKey].img = pinupList.emoji[keyName].img;
  });
});

delete pinupList.emoji;

export default {
  emojiList,
  km_emojiList,
  en_emojiList,
  zh_emojiList,
  pinupList,
};
