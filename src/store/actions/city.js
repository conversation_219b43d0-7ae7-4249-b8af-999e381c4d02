/**
 * 城市actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import cityStore from '../stores/city';
import cityService from '../../api/cityService';
import util from '../../util';

class Actions {
  @observable cityStore;

  @action
  queryHotLocations = async () => {
    const data = await cityService.queryHotLocations();
    cityStore.hotCityList = data;
    return data;
  };

  @action
  getLocalCity = async (lng, lat) => {
    const data = await cityService.getLocalCity(lng, lat);
    data.cityName = util.getCityNameWithCityData(data);
    cityStore.locationCity = data || {};
    return data || {};
  };

  @action
  getAllPlaceTree = async () => {
    const data = await cityService.getAllPlaceTree();
    // cityStore.allPlaceTree = data || [];
    return data;
  };
}

export default new Actions();
