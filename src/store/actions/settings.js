import { observable, action } from 'mobx';
import settingsStore from '../stores/settings';
import { setLanguage } from '../../i18n';
import Storage from '../../common/storage';
import I18nUtil from '../../util/I18nUtil';
import constant from '../constant';

class Actions {
  @observable settingsStore;

  /**
   * 切换主题
   */
  @action
  switchTheme = async (name) => this.setTheme(name);

  /**
   * 设置主题
   */
  @action
  setTheme = async (name) => {
    settingsStore.theme = name;
    await Storage.setTheme(name);
    return true;
  };

  /**
   * 设置语言
   */
  @action
  setLanguage = async (language) => {
    settingsStore.language = language;
    return true;
  };

  @action
  switchLanguage = async (lang) => {
    setLanguage(lang);
    settingsStore.language = lang;
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(lang, () => {});
    }
    global.emitter.emit('languageChange', true);
    return true;
  };

  @action
  selectedTab = async (name) => {
    settingsStore.selectedTab = name;
    global.emitter.emit(constant.event.tabbarChanged, { name });
    return true;
  };
}

export default new Actions();
