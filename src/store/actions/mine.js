import { action } from 'mobx';
import userStore from '../stores/user';
import mineStore from '../stores/mine';
import userAction from './user';
import RefreshManager from '../../util/refreshManager';
/**
 * 我的页面actions
 * action 统一返回一个promise
 * author: Rays
 */
class MineAction {
  static refreshManager = new RefreshManager();

  @action
  stopRefresh = () => {
    mineStore.refreshing = false;
  };

  /**
   * 获取我的页面数据
   */
  @action
  getData = async (isShowLoading = false) => {
    if (MineAction.refreshManager.refreshing) {
      console.log('我的页面数据正在加载中');
      return;
    }
    mineStore.refreshing = !!isShowLoading;
    const tasks = [userAction.getUserInfo()];
    MineAction.refreshManager.startRefresh(tasks, this.stopRefresh);
  };

  @action
  getUserInfo = async () => {
    if (userStore.isLogin) {
      try {
        await userAction.refreshUserInfo();
      } catch (e) {
        logger.warn('refreshUserInfo', e);
      }
    }
  };
}

export default new MineAction();
