/**
 * 登录actions
 * 1. action 统一返回一个promise，不要用callback
 * 2. 如果数据结构需要处理，要在这边先处理完
 */

import { action } from 'mobx';
import { Base64 } from 'js-base64';
import { GoogleSignin } from 'react-native-google-signin';
import { LoginManager } from 'react-native-fbsdk-next';
import Session from '../../api/session';
import LoginService from '../../api/loginService';
import UserService from '../../api/userService';
import { resetStore } from '../stores';
import settingsAction from './settings';
import NavigationService from '../../navigationService';
import companyAction from './company';
import globalStore from '../stores/global';
import AgoraSdk from '../../api/agoraSdk';
import { callSound } from '../../util/soundPlayer';
import { Vibration } from 'react-native';
import promiseUtil from '../../util/promiseUtil';
import globalAction from './global';

class Actions {
  @action
  loginByPhone = async (phone, code, regionCode) => {
    const res = await LoginService.loginByPhone({ phone, code, regionCode });
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  @action
  sendLoginCode = (data) => LoginService.sendLoginCode(data);

  @action
  loginByAccount = async (name, password, captcha, signature) => {
    const res = await LoginService.loginByAccount({
      name,
      password,
      captcha,
      signature,
    });
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  stopAudioAndVideo = () => {
    AgoraSdk.leaveChannel();
    callSound.stop();
    Vibration.cancel();
  };

  @action
  logout = async () => {
    try {
      await this.kicked();
      this.stopAudioAndVideo();
    } catch (e) {
      console.warn('loginAction logout', e);
    }
  };

  @action
  kicked = async () => {
    try {
      NavigationService.reset('login');
      // 不用等待上报结束，延迟500毫秒保证当前token可以供该接口使用
      UserService.uploadDeviceInfo(3);
      this.googleSignOut();
      this.facebookSignOut();
      await promiseUtil.sleep(500);
      Session.clear();
      // 延迟1秒保证跳到登录页面后再清理store，否则会触发请求相关接口数据导致UI渲染警告
      await promiseUtil.sleep(1000);
      resetStore();
      globalAction.setEnterprise(false);
      settingsAction.selectedTab('Job');
    } catch (e) {
      console.log(e);
    }
  };

  @action
  logoutForEp = async ({ isEnterprise = true } = {}) => {
    try {
      if (isEnterprise) {
        NavigationService.reset('epLogin');
      }
      // 不用等待上报结束，延迟500毫秒保证当前token可以供该接口使用
      UserService.uploadDeviceInfo(3);
      await promiseUtil.sleep(500);
      await Session.clear();
      // 延迟1秒保证跳到登录页面后再清理store，否则会触发请求相关接口数据导致UI渲染警告
      await promiseUtil.sleep(1000);
      resetStore();
      globalAction.setEnterprise(isEnterprise === true);
      this.stopAudioAndVideo();
    } catch (e) {
      console.log(e);
    }
  };

  @action
  loginFail = async () => {
    try {
      Session.clear();
      resetStore();
      this.googleSignOut();
      this.facebookSignOut();
    } catch (e) {
      console.log(e);
    }
  };

  @action
  autoLoginIM = async () => this.connectIM();

  @action
  connectIM = async () => {};

  @action
  loginIM = (account, token) => {};

  @action
  registerByPhone = async ({ code, phone, password, regionCode }) => {
    const res = await LoginService.registerByPhone({
      code,
      phone,
      password,
      regionCode,
    });
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  @action
  registerByEmail = async (username, password) => {
    const res = await LoginService.registerByEmail(username, password);
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  @action
  forgotPasswordByPhone = async ({ code, phone, password, regionCode }) => {
    const res = await LoginService.forgotPasswordByPhone({
      code,
      phone,
      password,
      regionCode,
    });
    if (res && res.successful) {
      return true;
    }
    return Promise.reject(res);
  };

  @action
  setPasswordByPhone = async ({ code, phone, password, regionCode }) => {
    const res = await LoginService.forgotPasswordByPhone({
      code,
      phone,
      password,
      regionCode,
    });
    if (res && res.successful) {
      return res;
    }
    return Promise.reject(res);
  };

  @action
  forgotPasswordByEmail = async ({ code, email, password }) => {
    const res = await LoginService.forgotPasswordByEmail({
      code,
      email,
      password,
    });
    if (res && res.successful) {
      return true;
    }
    return Promise.reject(res);
  };

  @action
  sendForgotCodeByPhone = (data) => LoginService.sendForgotCodeByPhone(data);

  @action
  sendForgotCodeByEmail = (email) => LoginService.sendForgotCodeByEmail(email);

  @action
  sendRegisterCode = (phone, regionCode) => LoginService.sendRegisterCode(phone, regionCode);

  @action
  isUsernameExist = async (name) => LoginService.isUsernameExist(name);

  @action
  thirdParyLogin = async (provider, accessToken) => {
    const res = await LoginService.thirdParyLogin(provider, accessToken);
    if (
      res &&
      res.responseEntity &&
      res.responseEntity.body &&
      res.responseEntity.body.access_token
    ) {
      const user = await this.saveSession(
        res.responseEntity.body.access_token,
        res.responseEntity.body.expires_in,
        res.responseEntity.body.refresh_token
      );
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  @action
  thirdParyLoginWithCode = async (provider, code) => {
    const res = await LoginService.thirdParyLoginWithCode(provider, code);
    if (
      res &&
      res.responseEntity &&
      res.responseEntity.body &&
      res.responseEntity.body.access_token
    ) {
      const user = await this.saveSession(
        res.responseEntity.body.access_token,
        res.responseEntity.body.expires_in,
        res.responseEntity.body.refresh_token
      );
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve(user);
    }
    return Promise.reject(res);
  };

  /**
   * 更新用户的首选语言，只要本地缓存的跟用户的不一致就更新
   * 注：本地的永远不会为空
   */
  setUserLanguage = async (userLanguage) => {
    const LOCAL_LANGUAGE_MAP = {
      en: 'en',
      zh: 'zh',
      kh: 'km',
    };
    // 客户端存储的是客户端的语言定义格式，跟服务端不一样
    const localLanguage = await Session.getLanguage();
    if (localLanguage !== LOCAL_LANGUAGE_MAP[userLanguage]) {
      // 更新用户首选语言
      if (globalStore.isEnterprise) {
        return UserService.setEmployersLanguage(LOCAL_LANGUAGE_MAP[localLanguage]);
      }
      return UserService.setLanguage(LOCAL_LANGUAGE_MAP[localLanguage]);
    }
    return true;
  };

  /**
   * 保存session
   */
  saveSession = async (access_token, expires_in, refresh_token) => {
    const user = this.getUserFromAccessToken(access_token);
    await Promise.all([
      Session.saveAccessToken(access_token, expires_in),
      Session.saveRefreshToken(refresh_token),
      Session.setUser(user),
      Session.setIsLogin(true),
    ]);
    return user;
  };

  /**
   * 解析access token，获取用户信息
   * @param {*} accessToken
   */
  getUserFromAccessToken = (accessToken) => {
    if (!accessToken) {
      return null;
    }
    return JSON.parse(Base64.decode(accessToken.split('.')[1]));
  };

  googleSignOut = async () => {
    try {
      await GoogleSignin.revokeAccess();
      await GoogleSignin.signOut();
    } catch (error) {
      console.log(error);
    }
  };

  facebookSignOut = async () => {
    try {
      await LoginManager.logOut();
    } catch (error) {
      console.log(error);
    }
  };

  @action
  epLoginByAccount = async (name, password, captcha, signature) => {
    globalStore.isEnterprise = true;
    const res = await LoginService.epLogin({
      name,
      password,
      captcha,
      signature,
    });
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      // this.loginIM(user.user.accid, user.user.imToken);
      return Promise.resolve({ ...user, successful: true });
    }
    return Promise.resolve(res);
  };

  @action
  authorizeMobile = async (data) => {
    const res = await companyAction.authorizeMobile(data);
    if (res && res.access_token) {
      const user = await this.saveSession(res.access_token, res.expires_in, res.refresh_token);
      await this.setUserLanguage(user.language);
      return Promise.resolve({ ...user, successful: true });
    }
    return Promise.resolve(res);
  };

  // 企业端注销
  @action
  unregisterForEnterPrise = async (data) => {
    return await LoginService.unregisterForEnterPrise(data);
  };

  // 用户端注销
  @action
  unregisterForPersonal = async (data) => {
    return await LoginService.unregisterForPersonal(data);
  };
}

export default new Actions();
