import { action } from 'mobx';
import homeStore from '../stores/home';
import userAction from './user';
import settingsAction from './settings';
import companyAction from './company';
import RefreshManager from '../../util/refreshManager';

/**
 * 首页actions
 * action 统一返回一个promise
 * <AUTHOR>
 */
class HomeAction {
  static refreshManager = new RefreshManager();

  @action
  stopRefresh = () => {
    homeStore.refreshing = false;
  };

  /**
   * 获取首页数据
   */
  @action
  getData = async (isShowLoading = false) => {
    if (HomeAction.refreshManager.refreshing) {
      console.log('首页数据正在加载中');
      return;
    }
    homeStore.refreshing = !!isShowLoading;
    // const tasks = [userAction.refreshUserInfo, settingsAction.checkNewVersion];
    const tasks = [
      companyAction.getEmployer(),
      companyAction.getBalance(),
      companyAction.getEmployersJobStatistics(),
      companyAction.getHomeResumeStatistics(),
      companyAction.getApplicationsStatistics(),
      companyAction.queryFollowResumes({ page: 1, size: 1 }),
      companyAction.queryWillInterviews(),
      companyAction.queryPastInterviews(),
      companyAction.queryNotInterviews(),
      companyAction.queryMessagesStatistics,
    ];
    HomeAction.refreshManager.startRefresh(tasks, this.stopRefresh);
  };
}

export default new HomeAction();
