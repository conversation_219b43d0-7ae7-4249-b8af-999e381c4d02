/**
 * 职位actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import jobStore from '../stores/job';
import jobService from '../../api/jobService';
import cityService from '../../api/cityService';
import pinyin from '../../util/pinyin';
import application from './application';
import util from '../../util';
import Session from '../../api/session';

const SEARCH_HISTORY = 'searchHistory';
const CURRENT_CITY = 'currentCity';
const LOCAL_ADDRESS_LIST = 'localAddressList';

class Actions {
  @observable jobStore;

  getConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    return Object.values(obj).map((item) => {
      item.isSelected = false;
      item.tempSelected = false;
      return item;
    });
  }

  getCategoryConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    const arr1 = [];
    Object.keys(obj).forEach((item) => {
      Object.values(obj[item]).map((l) => {
        l.isSelected = false;
        l.tempSelected = false;
        arr1.push(l);
        return l;
      });
    });
    return arr1;
  }

  @action
  getLocationConstants2 = async () => {
    const obj = await cityService.getAllPlaceTree();
    jobStore.allPlaceTree = obj;
    const locationObject = {};
    const pyArr = [];
    if (!obj) {
      return [];
    }

    obj.forEach((item) => {
      // 截取字符串首个字符
      const sing = util.getCityNameWithCityData(item).substring(0, 1);
      const firstPinyin = pinyin(sing).toLocaleUpperCase().substring(0, 1);
      const cityList = locationObject[firstPinyin] ? locationObject[firstPinyin] : [];
      cityList.push(item);
      pyArr.push(firstPinyin);
      locationObject[firstPinyin] = cityList;
    });
    jobStore.pinyinIndexArray2 = this.uniqueArray(pyArr.sort());
    jobStore.locationList2 = Object.keys(locationObject)
      .sort()
      .map((item) => ({ key: item, data: locationObject[item] }));
  };

  @action
  formatPickerData = async (obj) => {
    // const obj = await cityService.getAllPlaceTree();
    // jobStore.allPlaceTree = obj
    const locationObject = {};
    const pyArr = [];
    if (!obj) {
      return [];
    }

    obj.forEach((item) => {
      // 截取字符串首个字符
      const sing = util.getCityNameWithCityData(item).substring(0, 1);
      const firstPinyin = pinyin(sing).toLocaleUpperCase().substring(0, 1);
      const cityList = locationObject[firstPinyin] ? locationObject[firstPinyin].children : [];
      cityList.push(item);
      pyArr.push(firstPinyin);
      const name = util.getCityNameWithCityData(item);
      locationObject[name] = item.children;
    });
    jobStore.pinyinIndexArray3 = this.uniqueArray(pyArr.sort());
    return Object.keys(locationObject)
      .sort()
      .map((i) => ({ key: i, data: locationObject[i] }));
  };

  uniqueArray(arr) {
    const hash = [];
    for (let i = 0; i < arr.length; i += 1) {
      if (hash.indexOf(arr[i]) === -1) {
        hash.push(arr[i]);
      }
    }
    return hash;
  }

  @action
  queryJobs = async (param) => {
    const paramData = param;
    if (paramData.location) {
      const locationArray = paramData.location.split(',');
      paramData.latitude = locationArray[0];
      paramData.longitude = locationArray[1];
    }
    delete paramData.children;
    if (paramData.twoLevelCityData) {
      delete paramData.twoLevelCityData;
    }
    if (paramData.threeLevelCityData) {
      delete paramData.threeLevelCityData;
    }
    const data = await jobService.queryJobs(paramData);
    jobStore.total = data.totalCount;
    jobStore.tempJobList =
      param.page === 1 ? data.result : jobStore.tempJobList.concat(data.result);
    jobStore.jobList = [{ key: 'jobList', data: jobStore.tempJobList.slice() }];
    return data;
  };

  @action
  queryRecommends = async (param) => {
    const data = await jobService.queryRecommends(param);
    jobStore.recommendList = data.result;
    jobStore.recommendTotalPage = data.totalPage;
    return data;
  };

  @action
  queryCompanyJobs = async (param) => {
    const paramData = param;
    if (paramData.location) {
      const locationArray = paramData.location.split(',');
      paramData.latitude = locationArray[0];
      paramData.longitude = locationArray[1];
    }
    const data = await jobService.queryJobs(paramData);
    jobStore.companyDetailCount = data.totalCount;
    jobStore.companyDetailPage = data.totalPage;
    jobStore.companyJobList =
      param.page === 1 ? data.result : jobStore.companyJobList.concat(data.result);
    return data;
  };

  @action
  queryEmployers = async (param) => {
    const data = await jobService.queryEmployers(param);
    jobStore.companyTotalCount = data.totalCount;
    jobStore.companyTotalPage = data.totalPage;
    if (data.page === 1) {
      jobStore.companySearchList = data.result;
    } else {
      jobStore.companySearchList = jobStore.companySearchList.slice().concat(data.result);
    }
  };

  @action
  querySingleCompany = async (employerId) => {
    const data = await jobService.querySingleCompany(employerId);
    jobStore.companyDetail = data;
    return data;
  };

  @action
  queryCommunicatedJobs = async (param) => {
    const data = await jobService.queryCommunicatedJobs(param);
    jobStore.communicatedTotal = data.totalCount;
    jobStore.communicatedList =
      param.page === 1 ? data.result : jobStore.communicatedList.concat(data.result);
    return data;
  };

  @action
  queryDeliveredJobs = async (param) => {
    const data = await jobService.queryDeliveredJobs(param);
    jobStore.deliveredTotal = data.totalCount;
    data.result = data.result.map((item) => {
      item.expdate = item.expdate ? util.dateToLocalString1(item.expdate) : '';
      item.actionTime = item.actionTime ? util.dateToLocalString1(item.actionTime) : '';
      return item;
    });
    jobStore.deliveredList =
      param.page === 1 ? data.result : jobStore.deliveredList.concat(data.result);
    return data;
  };

  @action
  querySingleJobs = async (jobApplyId) => {
    const data = await jobService.querySingleJobs(jobApplyId);
    jobStore.jobDetail = data;
    return data;
  };

  @action
  queryJob = async (jobId) => {
    const data = await jobService.queryJob(jobId);
    return data;
  };

  @action
  getJobStatistics = async () => {
    const data = await jobService.getJobStatistics();
    jobStore.hasCommunicated = data.hasCommunicated;
    jobStore.totalJobApply = data.totalJobApply;
    jobStore.userApply = data.userApply;
    return data;
  };

  @action
  flagCommunicated = async (jobId) => {
    const data = await jobService.flagCommunicated(jobId);
    return data;
  };

  @action
  checkAndFlagCommunicated = async (jobId, localExtra) => {
    if (!jobId || !localExtra || localExtra.isFlagCommunicated) return;
    await this.flagCommunicated(jobId);
    localExtra.isFlagCommunicated = true;
    this.getJobStatistics();
  };

  sendResume = async (jobId, jobApply) => {
    return jobService.sendResume(jobId, jobApply);
  };

  @action
  updateIntentions = async (intention) => {
    const data = await jobService.updateIntentions(intention);
    return data;
  };

  @action
  updateWorkStatus = async (status) => {
    const data = await jobService.updateWorkStatus(status);
    return data;
  };

  @action
  getIntensions = async () => {
    let data = await jobService.getIntensions();
    data = data.map((item) => {
      item.isSelected = false;
      return item;
    });
    jobStore.intensionList = data && data.length > 3 ? data.slice(0, 3) : data;
    return data && data.length > 3 ? data.slice(0, 3) : data;
  };

  @action
  queryConstants = async () => {
    const constants = await application.getConstants();
    const categoryConstants = await application.getConstantsFlatByName('CATEGORY');
    jobStore.teamScaleList = this.getConstantsValues(constants.EMPLOYER_SCALE);
    jobStore.indutrialList = this.getConstantsValues(constants.INDUSTRIAL);
    jobStore.qualificationList = this.getConstantsValues(constants.QUALIFICATION);
    jobStore.jobStatusList = this.getConstantsValues(constants.WORK_STATUS);
    jobStore.jobTypeList = this.getConstantsValues(constants.JOBTERM);
    jobStore.jobCategoryList = this.getCategoryConstantsValues(categoryConstants);
    jobStore.jobSalaryList = this.getConstantsValues(constants.SALARY);
    jobStore.jobCityList = this.getConstantsValues(constants.LOCATION);
    jobStore.languages = this.getConstantsValues(constants.JOB_LANGUAGE);
    jobStore.positionList = this.getConstantsValues(constants.POSITION);
    jobStore.departmentList = this.getConstantsValues(constants.DEPARTMENT);
    await this.getLocationConstants2();
  };

  @action
  jobSearsh = async (param) => {
    const paramData = param;
    if (paramData.location) {
      const locationArray = paramData.location.split(',');
      paramData.latitude = locationArray[0];
      paramData.longitude = locationArray[1];
    }
    const data = await jobService.queryJobs(paramData);
    jobStore.searchTotalCount = data.totalCount;
    jobStore.searchTotalPage = data.totalPage;
    if (param.page === 1) {
      jobStore.jobSearchList = data.result;
    } else {
      jobStore.jobSearchList = jobStore.jobSearchList.slice().concat(data.result);
    }
  };

  @action
  getSearchHistory = async () =>
    await global.storage.load({ key: SEARCH_HISTORY }).catch((err) => Promise.resolve(null));

  @action
  addSearchHistory = async (param) => {
    let history = await this.getSearchHistory();
    if (!history) {
      history = [];
    }
    history.push(param);
    await global.storage.save({ key: SEARCH_HISTORY, data: history });
  };

  @action
  clearSearchHistory = async () => {
    try {
      global.storage.remove({ key: SEARCH_HISTORY });
      return await Promise.resolve(true);
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  getCurrentCity = async () => {
    const data = await global.storage
      .load({
        key: CURRENT_CITY,
      })
      .catch(() => Promise.resolve(null));
    return data;
  };

  @action
  saveCurrentCity = async (param) => {
    await this.clearCurrentCity();
    const data = await global.storage.save({ key: CURRENT_CITY, data: param });
    return data;
  };

  @action
  clearCurrentCity = async () => {
    try {
      global.storage.remove({ key: CURRENT_CITY });
      return await Promise.resolve(true);
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  getLocalAddressList = async () => {
    const userData = await Session.getUser();
    const data = await global.storage
      .load({
        key: LOCAL_ADDRESS_LIST,
        id: userData?.user?.userId?.toString(),
      })
      .catch(() => Promise.resolve(null));
    return data;
  };

  @action
  setLocalAddressList = async (ele) => {
    const list = await this.getLocalAddressList();
    const userData = await Session.getUser();
    let saveParams = {
      key: LOCAL_ADDRESS_LIST,
      id: userData?.user?.userId?.toString(),
      data: null,
    };
    if (!list) {
      saveParams.data = [ele];
    } else {
      const index = list.findIndex((item) => item.timestamp == ele.timestamp);
      if (index === -1) {
        list.push(ele);
        saveParams.data = list;
      } else {
        list[index] = ele;
        saveParams.data = list;
      }
    }
    await global.storage.save(saveParams);
  };

  @action
  deleteLocalAddress = async (ele) => {
    const list = await this.getLocalAddressList();
    const userData = await Session.getUser();
    if (list) {
      const index = list.findIndex((item) => item.timestamp == ele.timestamp);
      if (index !== -1) {
        list.splice(index, 1);
        await global.storage.save({
          key: LOCAL_ADDRESS_LIST,
          id: userData?.user?.userId?.toString(),
          data: list,
        });
      }
    }
  };
}

export default new Actions();
