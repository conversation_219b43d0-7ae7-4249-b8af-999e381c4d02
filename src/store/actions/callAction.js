import { action } from 'mobx';
import callStore, { LockCall } from '../stores/callStore';
import userStore from '../stores/user';
import constant from '../constant';
import sendMessageUtil from '../../database/sendMessageUtil';
import imAction from './imAction';
import chatAction from './chatAction';
import AgoraSdk from '../../api/agoraSdk';
import Vibration from '../../util/vibrationUtil';
import { callSound } from '../../util/soundPlayer';
import I18n from '../../i18n';
import JPush from 'jpush-react-native';
import { AppState } from '../../components';
import AppModule, { RingerMode } from '../../modules/AppModule';
import AppUtil from '../../util/appUtil';
import chatSessionDao from '../../database/dao/chatSessionDao';

function append0(number) {
  return number < 10 ? `0${number}` : number.toString();
}

class CallAction {
  /**
   * 开始呼叫邀请
   */
  @LockCall({ showCall: true, checkConnect: true })
  async startCall(callType, userId) {
    const { disableSend, infoData: friend } = await imAction.isDisableSend({
      sessionId: userId,
      isGroup: false,
      isSend: true,
    });
    if (disableSend || !friend) {
      console.warn('callAction startCall', disableSend, friend);
      toast.show(I18n.t('op_send_error'));
      return;
    }

    const channel = global.now().toString();
    callStore.channel = channel;
    callStore.callType = callType;

    console.debug(
      `callAction startCall 开始呼叫 channel:${channel} callType:${callType} enableSpeakerphone:${callStore.enableSpeakerphone}`
    );

    await AgoraSdk.joinChannel(channel, userStore.imId);
    if (callType === constant.callType.singleVideoCall) {
      //单聊视频时，发起时根据配置处理视频
      await AgoraSdk.engine.muteLocalVideoStream(!callStore.openCamera);
    }

    this.playCallNotification({ sessionId: userId });

    this.sendControlMessage(userId, callType, constant.callState.invite);
    callStore.peerId = userId;
    callStore.callState = constant.callState.invite;
    callStore.showCall = true;
    callStore.isHost = true;
    callStore.timeoutCount = 0;
    callStore.timeCount = 0;
    callStore.openMicrophone = true;
    callStore.user = {
      avatar: friend.avatar,
      name: friend.memo || friend.nickname,
    };
    this.startTimer();
  }

  startTimer = () => {
    this.stopTimer();
    this.timer = setInterval(() => {
      let { callState, timeoutCount } = callStore;
      if (callState === constant.callState.invite) {
        timeoutCount++;
        callStore.timeoutCount = timeoutCount;
        if (timeoutCount >= 40) {
          this.onReceiveNoResponse();
        }
      } else if (callState === constant.callState.accept) {
        callStore.timeCount += 1;
      } else if (callState === constant.callState.none) {
        this.stopTimer();
      }
    }, 1000);
  };

  stopTimer = () => {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  };

  /**
   * 取消呼叫
   */
  @LockCall({ showCall: false })
  async cancelCall() {
    const { peerId, callType } = callStore;

    this.sendControlMessage(peerId, callType, constant.callState.cancel);

    await this.addLocalMessage(peerId, userStore.imId, callType, constant.callState.cancel);

    toast.show(I18n.t('page_call_tips_canceled'));
    this.callWillEnd();
  }

  /**
   * 拒绝呼叫
   */
  @LockCall({ showCall: false })
  async refuseCall() {
    const { peerId, callType } = callStore;
    logger.info(
      'VideoCall  拒绝呼叫 channel:{@channel} {@callType} {@callStore}',
      callStore.channel,
      callType,
      callStore
    );

    this.sendControlMessage(peerId, callType, constant.callState.reject);

    this.addLocalMessage(peerId, peerId, callType, constant.callState.reject);

    this.endCallNotification();

    callStore.peerId = 0;
    callStore.callState = constant.callState.none;
    callStore.showCall = false;
    callStore.zoomIn = false;
  }

  /**
   * 接受呼叫
   */
  @LockCall({})
  async acceptCall() {
    console.debug(
      `callAction acceptCall 接受呼叫 channel:${callStore.channel} callState:${callStore.callState}`
    );
    await AgoraSdk.joinChannel(callStore.channel, userStore.imId);
    await AgoraSdk.engine.setEnableSpeakerphone(callStore.enableSpeakerphone);
    callStore.callState = constant.callState.accept;

    this.isEndCallNotification = true;
    this.endCallNotification();

    callStore.timeoutCount = 0;
    callStore.timeCount = 0;
    callStore.openCamera = callStore.callType == constant.callType.singleVideoCall;
    this.startTimer();
  }

  /**
   * 结束呼叫
   */
  @LockCall({ showCall: false })
  async endCall() {
    const { peerId, callType, isHost } = callStore;

    await this.addLocalMessage(
      peerId,
      isHost ? userStore.imId : peerId,
      callType,
      constant.callState.end
    );

    toast.show(I18n.t('page_call_tips_ended'));
    this.callWillEnd();

    // await sendMessageUtil.sendP2PAudioCallMessage(peerId, callType, constant.callState.endCall);
  }

  // 选择音频播放设备为扬声器或耳机。
  enableSpeakerphone = async () => {
    const enableSpeakerphone = !callStore.enableSpeakerphone;
    console.debug(`callAction setEnableSpeakerphone:${enableSpeakerphone}`);
    if (callSound.isPlaying()) {
      callSound.setSpeakerphoneOn(enableSpeakerphone);
      callStore.enableSpeakerphone = enableSpeakerphone;
      return;
    }
    AgoraSdk.engine
      ?.setEnableSpeakerphone(enableSpeakerphone)
      .then(() => {
        callStore.enableSpeakerphone = enableSpeakerphone;
      })
      .catch((err) => {
        console.warn('setEnableSpeakerphone', err);
      });
  };

  // 打开关闭麦克风
  enableMicrophone = async () => {
    AgoraSdk.engine
      ?.muteLocalAudioStream(callStore.openMicrophone)
      .then(() => {
        callStore.openMicrophone = !callStore.openMicrophone;
      })
      .catch((err) => {
        console.warn('muteLocalAudioStream', err);
      });
  };

  // 打开关闭视频
  enableVideo = async () => {
    AgoraSdk.engine
      ?.muteLocalVideoStream(callStore.openCamera)
      .then(() => {
        callStore.openCamera = !callStore.openCamera;
      })
      .catch((err) => {
        console.warn('muteLocalVideoStream', err);
      });
  };

  /**
   * 切换到语音
   */
  @action
  switchToAudio = async (type) => {
    callStore.callType = type;

    AgoraSdk.engine.enableLocalVideo(false);
    AgoraSdk.stopPreview();
    const { peerId } = callStore;

    this.sendControlMessage(peerId, callStore.callType, constant.callState.switch);
  };

  sendControlMessage = async (userId, callType, state) => {
    if (callType === constant.callType.singleAudioCall) {
      await sendMessageUtil.sendP2PCallMessage(
        userId,
        constant.messageType.audioCall,
        state,
        callStore.channel
      );
    } else if (callType === constant.callType.singleVideoCall) {
      await sendMessageUtil.sendP2PCallMessage(
        userId,
        constant.messageType.videoCall,
        state,
        callStore.channel
      );
    }
  };

  addLocalMessage = async (sessionId, senderId, callType, callState, data) => {
    const { friend } = await chatAction.getFriend(sessionId, true);
    if (!friend) {
      return;
    }
    const channel = data?.msg?.channel || callStore.channel;
    const message = await imAction.addMessage({
      isGroup: 0,
      sessionId,
      senderId,
      msgId: data?.msg_id || global.now(),
      msgTime: data?.msg_time || global.now(),
      isRead: 0,
      extra: JSON.stringify({ callType, callState, timeCount: callStore.timeCount, channel }),
      type: constant.messageType.call,
    });
    global.emitter.emit(constant.event.receiveMessage, { message });
  };

  //{"msg": {"im_id": 10300, "req_id": "A6CE0B24-6E25-4AF1-8885-7CF967D72AF9103001645067667915", "rim_id": 10298, "state": 1, "timestamp": 1645067667915, "type": "notice"}, "msg_id": 1645067670296610, "msg_time": 1645067670296, "receiver_id": 10298, "send_self": 0, "sender_id": 10300}
  onReceiveSingleCall = async (data) => {
    logger.info('VideoCall 收到自定义消息 {@message}', data);
    // 自己发送的过滤掉
    if (data.sender_id === userStore.imId) return;
    const callType =
      data.msg.type === 'audioCall'
        ? constant.callType.singleAudioCall
        : constant.callType.singleVideoCall;
    if (data.msg.state === constant.callState.invite) {
      // 收到对方邀请的消息
      this.onReceiveInvite(data.sender_id, callType, data.msg.channel);
    } else if (data.msg.state === constant.callState.cancel) {
      // 收到对方取消呼叫的消息
      this.onReceiveCancel(data.sender_id, callType, data);
    } else if (data.msg.state === constant.callState.reject) {
      // 收到对方拒绝的消息
      this.onReceiveReject(data.sender_id, callType);
    } else if (data.msg.state === constant.callState.busy) {
      // 收到对方正忙的消息
      this.onReceiveBusy(data.sender_id, callType);
    } else if (data.msg.state === constant.callState.switch) {
      // 收到对方正忙切换语音
      this.onReceiveSwitch(data.sender_id, callType);
    }
  };

  onReceiveVoipInvite = async (message) => {
    logger.info('VideoCall 收到来自voip的消息 {@message} ', message);
    let type = constant.callType.singleAudioCall;
    if (message?.callType === 'videoCall') {
      type = constant.callType.singleVideoCall;
    }
    if (message?.state === constant.callState.invite) {
      // TODO 去掉推送的音频频点击处理，靠离线消息来
      // this.onReceiveInvite(message?.sessionId, type, message?.channel);
    } else if (message?.state === constant.callState.cancel) {
    }
  };

  /**
   * 收到对方邀请的消息
   */
  @action
  onReceiveInvite = async (userId, type, channel) => {
    logger.info('VideoCall 收到邀请 channel:{channel} {userId} {type}', channel, userId, type);

    if (channel === callStore.channel) {
      return;
    }
    if (callStore.showCall) {
      // 忙线中
      this.sendControlMessage(userId, type, constant.callState.busy);
      this.addLocalMessage(userId, userId, type, constant.callState.busy);

      return;
    }
    await this.onReceiveInvite2(userId, type, channel);
  };

  /**
   * 收到对方邀请的消息
   */
  @LockCall({ showCall: true })
  async onReceiveInvite2(userId, type, channel) {
    callStore.callType = type;

    const { friend } = await chatAction.getFriend(userId, true);
    if (friend) {
      callStore.user = {
        avatar: friend.avatar,
        name: friend.memo || friend.nickname,
      };
    }
    this.playCallNotification({ sessionId: userId, isReceive: true });
    callStore.channel = channel;
    callStore.peerId = userId;
    callStore.callState = constant.callState.receiveInvite;
    callStore.showCall = true;
    callStore.openMicrophone = true;

    //有推送权限时，iOS原生通过voip推送已经显示
    //无推送权限时，iOS不会显示本地通知
    if (AppState.currentState === 'background' && IS_ANDROID) {
      let content;
      let callType;
      if (type === constant.callType.singleVideoCall) {
        content = I18n.t('page_call_notice_invite_video');
        callType = 'videoCall';
      } else {
        content = I18n.t('page_call_notice_invite_audio');
        callType = 'audioCall';
      }
      JPush.addLocalNotification({
        messageID: '999',
        title: '',
        content: `${friend.memo || friend.nickname}${content}`,
        extras: { type: 'voip', channel, state: 1, callType, sessionId: userId },
      });
    }
  }

  /**
   * 收到对方取消呼叫的消息
   */
  @LockCall({ showCall: false })
  async onReceiveCancel(userId, callType, data) {
    const { peerId, channel } = callStore;
    await this.addLocalMessage(userId, userId, callType, constant.callState.cancel, data);
    if (peerId === userId) {
      toast.show(I18n.t('page_call_tips_peer_cancel'));
      this.callWillEnd();
    }
    //有推送权限时，iOS原生通过voip推送已经显示
    //无推送权限时，iOS不会显示本地通知
    if (AppState.currentState === 'background' && IS_ANDROID) {
      const params = {
        messageID: '999',
        title: '',
        content: I18n.t('page_call_notice_end'),
        extras: {
          type: 'voip',
          channel,
          state: constant.callState.cancel,
          callType: data.msg.type,
          sessionId: userId,
        },
      };
      JPush.addLocalNotification(params);
    }
  }

  /**
   * 收到对方拒绝的消息
   */
  @LockCall({ showCall: false })
  async onReceiveReject(userId, type) {
    const { peerId, callType } = callStore;
    if (peerId !== userId) {
      return;
    }
    await this.addLocalMessage(peerId, userStore.imId, callType, constant.callState.reject);

    toast.show(I18n.t('page_call_tips_peer_reject'));
    this.callWillEnd();
  }

  /**
   * 收到对方正忙的消息
   */
  @LockCall({ showCall: false })
  async onReceiveBusy(userId, type) {
    const { peerId, callType } = callStore;
    if (peerId !== userId) {
      return;
    }
    await this.addLocalMessage(peerId, userStore.imId, callType, constant.callState.busy);

    toast.show(I18n.t('page_call_tips_busy'));
    this.callWillEnd();
  }

  @action
  onReceiveAccept = async (userId) => {
    callStore.callState = constant.callState.accept;
    this.endCallNotification();
    AgoraSdk.engine.setEnableSpeakerphone(callStore.enableSpeakerphone);
  };

  @LockCall({ showCall: false })
  async onReceiveNoResponse(userId, type) {
    const { peerId, callType } = callStore;

    this.sendControlMessage(peerId, callType, constant.callState.cancel);

    await this.addLocalMessage(peerId, userStore.imId, callType, constant.callState.noResponse);

    toast.show(I18n.t('page_call_tips_no_response'));
    this.callWillEnd();
  }

  @LockCall({ showCall: false })
  async onReceiveEndCall(userId) {
    const { peerId, callType, isHost } = callStore;
    if (peerId !== userId) {
      return;
    }
    await this.addLocalMessage(
      peerId,
      isHost ? userStore.imId : peerId,
      callType,
      constant.callState.end
    );
    toast.show(I18n.t('page_call_tips_call_end'));

    this.callWillEnd();
  }

  /**
   * 收到对方正忙切换语音
   */
  @LockCall({ showCall: false })
  async onReceiveSwitch(userId, type) {
    const { peerId } = callStore;
    if (peerId !== userId) {
      return;
    }
    toast.show(I18n.t('page_call_tips_switch_audio'));

    callStore.callType = type;
  }

  handleOfflineMessage = (message) => {
    if (!message || message.sender_id === userStore.imId) return false;
    logger.debug('VideoCall 收到离线的消息 {@message}', message);
    if (callStore.showCall) {
      return true;
    }
    const callType =
      message.msg.type === 'audioCall'
        ? constant.callType.singleAudioCall
        : constant.callType.singleVideoCall;
    if (
      message.msg.state === constant.callState.invite &&
      global.now() < parseInt(message.msg.channel, 10) + 38000
    ) {
      this.onReceiveInvite(message.sender_id, callType, message.msg.channel);
      return true;
    }
    return false;
  };

  @action
  formatHHMMSS = (second) => {
    if (!second || second <= 0) {
      return '00:00';
    }
    const s = second % 60;
    const m = Math.floor(second / 60);
    const h = Math.floor(m / 60);
    if (h === 0) {
      return `${append0(m)}:${append0(s)}`;
    } else {
      return `${append0(h)}:${append0(m)}:${append0(s)}`;
    }
  };

  callWillEnd = async () => {
    this.stopTimer();
    this.endCallNotification();
    AgoraSdk.leaveChannel();
    this.showCallEndModal();
    callStore.zoomIn = false;
    callStore.peerId = 0;
    callStore.callState = constant.callState.none;
    callStore.showCall = false;
    callStore.isHost = false;
    callStore.timeCount = 0;
    this.isEndCallNotification = false;
  };

  playCallNotification = async ({ sessionId, isGroup, isReceive }) => {
    const ringerMode = await AppModule.getRingerMode();
    let { enableSpeakerphone } = callStore;
    if (callStore.ringerMode !== ringerMode) {
      callStore.ringerMode = ringerMode;
      enableSpeakerphone = ringerMode === RingerMode.NORMAL;
      callStore.enableSpeakerphone = enableSpeakerphone;
    }
    callSound.setSpeakerphoneOn(enableSpeakerphone);

    // 接收到设为免打扰的聊天，不播放声音/震动
    let isDisturb = false;
    if (isReceive) {
      isDisturb = await chatSessionDao.isDisturb({ sessionId, isGroup });
      if (AppState.currentState === 'background') {
        AppUtil.startLauncherActivity();
      }
    }
    if (isDisturb || this.isEndCallNotification) return;
    callSound.play();
    if (isReceive) {
      Vibration.chatVibrate();
    }
    setTimeout(() => {
      if (
        this.isEndCallNotification ||
        !callStore.showCall ||
        !(
          callStore.callState === constant.callState.invite ||
          callStore.callState === constant.callState.receiveInvite
        )
      ) {
        this.endCallNotification();
      }
    }, 2000);
  };

  endCallNotification = () => {
    callSound.stop();
    Vibration.cancel();
  };

  @action
  checkInCall = () => {
    const { showCall } = callStore;

    if (showCall) {
      toast.show(I18n.t('page_call_tips_incall'));
      return true;
    }
    return false;
  };

  @action
  onReceiveMuteVideo = async (userId, mute) => {
    callStore.remoteVideoMute = mute;
  };

  showCallEndModal = () => {
    const { channel, callType, timeCount } = callStore;
    if (timeCount > 0) {
      global.emitter.emit(constant.event.showCallEndModal, { channel, callType });
    }
  };
}

export default new CallAction();
