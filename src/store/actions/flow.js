/**
 * 向导及检查流程控制
 */

import applicationAction from './application';
import jobAction from './job';

const statusMap = {
  INIT: 'INIT',
  RUNNING: 'RUNNING',
  CONTINUE: 'CONTINUE',
  STOP: 'STOP',
};

export const EVENT_NO_INTENSION = 'EVENT_NO_INTENSION';
export const EVENT_NO_RESUME = 'EVENT_NO_RESUME';
export const EVENT_NO_BIND_MOBILE = 'EVENT_NO_BIND_MOBILE';
export const EVENT_LOCATION_DEFAULT = 'EVENT_LOCATION_DEFAULT';
export const EVENT_LOCATION_CHANGE = 'EVENT_LOCATION_CHANGE';
export const EVENT_LOCATION_FAIL = 'EVENT_LOCATION_FAIL';

class Flow {
  init = async () => {
    this.nodes = [];
    this.nodes.push(this.checkIntensionNode);
    this.nodes.push(this.checkResumeNode);
    this.nodes.push(this.checkPhoneNode);
    this.nodes.push(this.checkLocationNode);

    this.status = statusMap.INIT;
    this.currNode = null;
    this.currStep = 0;
    this.event = {};
  };

  start = async () => {
    if (!this.nodes || this.nodes.length === 0) {
      return;
    }
    for (let i = 0; i < this.nodes.length; i++) {
      this.currNode = this.nodes[i];
      this.currStep = i + 1;
      const next = await this.runNode(this.currNode);
      if (!next) {
        this.status = statusMap.STOP;
        return;
      }
      if (!this.hasNext()) {
        this.stop();
      }
    }
  };

  continue = async (step) => {
    if (this.status === statusMap.CONTINUE) {
      return;
    }
    if (!this.nodes || this.nodes.length === 0) {
      return;
    }
    if (this.currStep >= this.nodes.length) {
      return;
    }
    this.status = statusMap.CONTINUE;
    let startStep = this.currStep;
    if (step) {
      startStep = step - 1;
    }
    for (let i = startStep; i < this.nodes.length; i++) {
      this.currNode = this.nodes[i];
      this.currStep = i + 1;
      const next = await this.runNode(this.currNode);
      if (!next) {
        this.status = statusMap.STOP;
        return;
      }
      if (!this.hasNext()) {
        this.stop();
      }
    }
  };

  stop = async () => {
    this.status = statusMap.STOP;
  };

  runNode = async (node) => {
    if (node) {
      return node();
    } else {
      return true;
    }
  };

  hasNext() {
    return this.currStep === this.nodes.length;
  }

  on = (type, cb) => {
    this.event[type] = cb;
  };

  emit = (type, data) => {
    const cb = this.event[type];
    if (cb) {
      cb(data);
    }
  };

  checkIntensionNode = async () => {
    const hasIntension = await applicationAction.checkIntension();
    let next = true;
    if (!hasIntension) {
      this.emit(EVENT_NO_INTENSION);
      next = false;
    }
    return next;
  };

  checkResumeNode = async () => {
    const noNeedResume = await applicationAction.checkResume();
    let next = true;
    if (!noNeedResume) {
      this.emit(EVENT_NO_RESUME);
      next = false;
    }
    return next;
  };

  checkPhoneNode = async () => {
    const hasMobile = await applicationAction.checkPhone();
    let next = true;
    if (!hasMobile) {
      this.emit(EVENT_NO_BIND_MOBILE);
      next = false;
    }
    return next;
  };

  checkLocationNode = async () => {
    try {
      let next = true;
      const location = await applicationAction.getLocation();
      const currLocation = await jobAction.getCurrentCity();
      if (currLocation) {
        if (location && location.placeId !== currLocation.placeId) {
          // 本地与定位不一致，提醒是否切换
          next = false;
          this.emit(EVENT_LOCATION_CHANGE, location);
        } else {
          // 忽略
          next = true;
        }
      } else {
        if (location) {
          // 设置默认定位位置
          next = true;
          this.emit(EVENT_LOCATION_DEFAULT, location);
        } else {
          // 定位失败，设置去选择城市
          next = false;
          this.emit(EVENT_LOCATION_FAIL);
        }
      }
      return next;
    } catch (e) {
      // 定位失败，设置去选择城市
      this.emit(EVENT_LOCATION_FAIL);
      return false;
    }
  };
}

export default new Flow();
