/**
 * Application actions
 * 主要是处理系统常量
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import constObj from '../constant';
import ApplicationService from '../../api/applicationService';
import cityAction from './city';
import ResumeService from '../../api/resumeService';
import Session from '../../api/session';
import I18n from '../../i18n';
import Geolocation from '@react-native-community/geolocation';
import globalStore from '../stores/global';
import applicationStore from '../stores/applicationStore';
import userStore from '../stores/user';

class Actions {
  @action
  initConstants = async () => {
    this.getPositionConstants();
    const language = constObj.languageMap[I18n.locale] || constObj.languageMap.zh;
    if (language === constObj.languageMap.zh) {
      if (constObj.constantEnumZh) {
        return constObj.constantEnumZh;
      }
    }

    if (language === constObj.languageMap.en) {
      if (constObj.constantEnumEn) {
        return constObj.constantEnumEn;
      }
    }

    if (language === constObj.languageMap.km) {
      if (constObj.constantEnumKm) {
        return constObj.constantEnumKm;
      }
    }
    const constants = await ApplicationService.getConstants();
    // 缓存到常量对象
    // 先存内存里面，后期再考虑是否存到local storage
    switch (language) {
      case constObj.languageMap.zh:
        constObj.constantEnumZh = constants;
        break;
      case constObj.languageMap.en:
        constObj.constantEnumEn = constants;
        break;
      case constObj.languageMap.km:
        constObj.constantEnumKm = constants;
        break;
      default:
        break;
    }

    return constants;
  };

  getPositionById = (positionId) => {
    let { POSITION } = applicationStore.constants;
    if (!POSITION) {
      // POSITION = await this.getPositionConstants();
      this.getPositionConstants();
      return null;
    }
    return Object.values(POSITION).find((it) => it.value === positionId);
  };

  @action
  getPositionConstants = () => this.getConstantsByName('POSITION');

  @action
  getConstantsByName = async (name) => {
    const res = await ApplicationService.getConstantsByName(name);
    console.debug('getConstantsByName', name, res);
    applicationStore.constants[name] = res;
    return res;
  };

  @action
  getConstantsFlatByName = (name) => ApplicationService.getConstantsFlatByName(name);

  @action
  getConstants = async () => this.initConstants();

  @action
  getLastestAPPVersion = async (appType) => ApplicationService.getLastestAPPVersion(appType);

  @action
  getAllAPPVersions = async (param) => ApplicationService.getAllAPPVersions(param);

  @action
  addAppVersion = async (appVersion) => ApplicationService.addAppVersion(appVersion);

  @action
  deleteResume = async (id) => ApplicationService.deleteResume(id);

  // /**
  //  * 初始化缓存数据
  //  */
  // @action
  // initCacheData = async () => {
  //   try {
  //     const isLogin = await Session.isLogin();
  //     if (isLogin) {
  //       this.getLocation();
  //       this.getAppVersion();
  //       this.checkIntension();
  //       this.checkResume();
  //       this.checkPhone();
  //     }
  //   } catch (e) {
  //     console.debug('缓存失败，忽略', e);
  //   }
  // };

  @action
  getLocation = async () => {
    try {
      const position = await this.getCurrentPosition();
      // 获取定位城市，并且存储到city store
      const city = await cityAction.getLocalCity(position.longitude, position.latitude);
      return city;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  getAppVersion = async () => {
    try {
      const version = await this.getLastestAPPVersion(global.IS_ANDROID ? 0 : 1);
      return version;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  checkIntension = async () => {
    try {
      const isLogin = await Session.isLogin();
      if (!isLogin) {
        return false;
      }
      let hasIntension = false;
      const resumes = await this.getResumeList();
      if (!resumes || resumes.length === 0) {
        return false;
      }
      resumes.forEach((resume) => {
        if (
          resume.intention &&
          resume.intention.industryIds &&
          resume.intention.industryIds.length > 0
        ) {
          hasIntension = true;
        }
      });
      return hasIntension;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  checkResume = async () => {
    try {
      let noNeedResume = false;
      const resumes = await this.getResumeList();
      if (!resumes || resumes.length === 0) {
        return false;
      }
      resumes.forEach((resume) => {
        if (resume.profile && resume.profile.firstName) {
          noNeedResume = true;
        }
      });
      return noNeedResume;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  @action
  checkPhone = async () => {
    try {
      let hasMobile = false;
      const resumes = await this.getResumeList();
      if (!resumes || resumes.length === 0) {
        return false;
      }
      resumes.forEach((resume) => {
        if (resume.profile && resume.profile.mobile) {
          hasMobile = true;
        }
      });
      return hasMobile;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  getCurrentPosition = () =>
    new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          resolve(position.coords);
        },
        (err) => {
          reject(err);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 1000,
        }
      );
    });

  getResumeList = async () => {
    if (globalStore.isEnterprise || !userStore.isLogin) {
      return [];
    }
    return await ResumeService.getResumes();
  };
}

export default new Actions();
