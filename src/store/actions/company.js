/**
 * 公司actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import companyStore from '../stores/company';
import CompanyService from '../../api/companyService';
import application from './application';
import job from './job';
import UserService from '../../api/userService';
import Md5 from 'crypto-js/md5';
import DeviceInfo from '../../util/deviceInfo';
import configs from '../../configs';

class Actions {
  @observable companyStore;

  @action
  hasIMAccount = async (employerId) => {
    return CompanyService.hasIMAccount(employerId);
  };

  @action
  queryTrades = async (param) => {
    return CompanyService.queryTrades(param);
  };

  @action
  queryAmount = async () => {
    return CompanyService.queryAmount();
  };

  @action
  queryJobs = async (param) => {
    return CompanyService.queryJobs(param);
  };

  @action
  addJobs = async (job) => {
    return CompanyService.addJobs(job);
  };

  @action
  queryJobsByJobId = async (jobId) => {
    return CompanyService.queryJobsByJobId(jobId);
  };

  @action
  editJobs = async (jobId, job) => {
    return CompanyService.editJobs(jobId, job);
  };

  @action
  deleteJobs = async (jobId) => {
    return CompanyService.deleteJobs(jobId);
  };

  @action
  closeJobs = async (jobId) => {
    return CompanyService.closeJobs(jobId);
  };

  @action
  publishJobs = async (jobId, data) => {
    return CompanyService.publishJobs(jobId, data);
  };

  @action
  refreshJobs = async (jobId) => {
    return CompanyService.refreshJobs(jobId);
  };

  @action
  autoRenewJobs = async (autoRenewMap, jobId) => {
    return CompanyService.autoRenewJobs(autoRenewMap, jobId);
  };

  @action
  communicationJobs = async (jobId, seekerId) => {
    return CompanyService.communicationJobs(jobId, seekerId);
  };

  @action
  topJobs = async (jobId) => {
    return CompanyService.topJobs(jobId);
  };

  @action
  hasServicePackage = async (jobId) => {
    return CompanyService.hasServicePackage(jobId);
  };

  @action
  queryProducts = async (param) => {
    const res = await CompanyService.queryProducts(param);
    companyStore.totalPackage = res?.totalCount || 0;
    return res;
  };

  @action
  queryProductServices = async (param) => {
    return CompanyService.queryProductServices(param);
  };

  @action
  queryProductItems = async (param) => {
    return CompanyService.queryProductItems(param);
  };

  @action
  getEmployer = async () => {
    const { employerId } = companyStore;
    const res = await CompanyService.getEmployer();
    companyStore.companyInfo = res;
    if (!employerId) {
      UserService.uploadDeviceInfo(2);
    }
    return res;
  };

  @action
  queryContacts = async (param) => {
    const res = await CompanyService.queryContacts(param);
    companyStore.totalContacts = res?.totalCount || 0;
    return res;
  };

  @action
  addContact = async (contact) => {
    return CompanyService.addContact(contact);
  };

  @action
  editContact = async (contact) => {
    return CompanyService.editContact(contact);
  };

  @action
  deleteContact = async (id) => {
    return CompanyService.deleteContact(id);
  };

  @action
  getJobStatistics = async () => {
    return CompanyService.getJobStatistics();
  };

  @action
  getBalance = async () => {
    const res = await CompanyService.getBalance();
    if (Object.prototype.toString.call(res) === '[object Object]') {
      companyStore.balance = res?.data || 0;
    } else {
      companyStore.balance = res;
    }
  };

  @action
  getEmployersJobStatistics = async () => {
    const res = await CompanyService.getEmployersJobStatistics();
    companyStore.statistics = res;
    return res;
  };

  @action
  getHomeResumeStatistics = async (param) => {
    const res = await CompanyService.getApplicationsStatistics(param);
    companyStore.homeResumeStatistics = res;
    return res;
  };

  @action
  getApplicationsStatistics = async (param) => {
    const res = await CompanyService.getApplicationsStatistics(param);
    companyStore.applicationsStatistics = res;
    return res;
  };

  @action
  getResumeStatistics = async (param) => {
    const res = await CompanyService.getApplicationsStatistics(param);
    if (param.jobApplyType == 0) {
      companyStore.deliveryStatistics = res;
    } else if (param.jobApplyType == 1) {
      companyStore.buyStatistics = res;
    }
    return res;
  };

  @action
  queryRecharges = async (param) => {
    return CompanyService.queryRecharges(param);
  };

  @action
  changePassword = async ({ oldPassword, newPassword }) => {
    return CompanyService.changePassword({ oldPassword, newPassword });
  };

  @action
  searchResumes = async (param) => {
    return CompanyService.searchResumes(param);
  };

  @action
  queryApplications = async (param) => {
    return CompanyService.queryApplications(param);
  };

  queryApplicationByJobId = async (param) => {
    param.offset = 0;
    param.limit = 1;
    const res = await this.queryApplication(param);
    if (!res && param.jobId !== '0') {
      param.jobId = '0';
      return this.queryApplication(param);
    }
    return res;
  };

  @action
  queryApplication = async (param) => {
    param.offset = 0;
    param.limit = 1;
    const res = await CompanyService.queryApplications(param);
    return res.result?.length ? res.result[0] : null;
  };

  @action
  getResumeDetail = async (resumeId) => {
    return CompanyService.getResumeDetail(resumeId);
  };

  @action
  updateJobApplyStatusById = async (jobApplyId, status) => {
    return CompanyService.updateJobApplyStatusById(jobApplyId, status);
  };

  @action
  getResumeTagsStatistics = async (type) => {
    const res = await CompanyService.getResumeTagsStatistics(type);
    companyStore.downloadStatistics = res;
    return res;
  };

  @action
  queryFollowResumes = async (param) => {
    const res = await CompanyService.queryFollowResumes(param);
    companyStore.totalCollection = res?.totalCount || 0;
    return res;
  };

  @action
  followResume = async (resumeId) => {
    return CompanyService.followResume(resumeId);
  };

  @action
  deleteFollowResume = async ({ resumeId, followCvId }) => {
    return CompanyService.deleteFollowResume(resumeId, followCvId);
  };

  @action
  queryInterviews = async (param) => {
    return CompanyService.queryInterviews(param);
  };

  @action
  queryWillInterviews = async () => {
    const res = await CompanyService.queryInterviews({ page: 1, size: 1, interviewStatus: -1 });
    companyStore.totalWaitCount = res?.totalCount || 0;
  };

  @action
  queryPastInterviews = async () => {
    const res = await CompanyService.queryInterviews({ page: 1, size: 1, interviewStatus: 1 });
    companyStore.totalPastCount = res?.totalCount || 0;
  };

  @action
  queryNotInterviews = async () => {
    const res = await CompanyService.queryInterviews({ page: 1, size: 1, interviewStatus: 0 });
    companyStore.totalNotCount = res?.totalCount || 0;
  };

  @action
  queryConstants = async () => {
    const constants = await application.getConstants();
    companyStore.commentOptions = job.getConstantsValues(constants?.COMMENT);
    companyStore.locationOptions = this.getConstantsValues(constants?.LOCATION);
    companyStore.employerScaleOptions = this.getConstantsValues(constants?.EMPLOYER_SCALE);
    companyStore.etypeOptions = this.getConstantsValues(constants?.ETYPE);
    companyStore.industrialOptions = this.getConstantsValues(constants?.INDUSTRIAL);
  };

  @action
  addCommentsForJobApply = async (jobApplyId, comments) => {
    return CompanyService.addCommentsForJobApply(jobApplyId, comments);
  };

  @action
  updateInterviewStatus = async (jobApplyId, interviewStatus) => {
    return CompanyService.updateInterviewStatus(jobApplyId, interviewStatus);
  };

  @action
  updateResumeTags = async (data) => {
    return CompanyService.updateResumeTags(data);
  };

  @action
  queryOrderConstants = async () => {
    const constants = await application.getConstants();
    companyStore.orderStatusList = job.getConstantsValues(constants.ORDER_STATUS);
  };

  @action
  queryTradeTypeConstants = async () => {
    const constants = await application.getConstants();
    companyStore.tradeTypeList = job.getConstantsValues(constants.TRADE_TYPE);
  };

  @action
  buyProduct = async (data) => {
    return CompanyService.buyProduct(data);
  };

  @action
  buyResume = async (data) => {
    return CompanyService.buyResume(data);
  };

  @action
  inviteInterview = async (data) => {
    return CompanyService.inviteInterview(data);
  };

  certification = async (data) => {
    return CompanyService.updateEmployerCertificate(data);
  };

  uploadCompanyImage = async (employerId, path) => {
    return CompanyService.uploadCompanyImage(employerId, path);
  };

  uploadCompanyLogo = async (employerId, path) => {
    return CompanyService.uploadCompanyLogo(employerId, path);
  };

  editEmployer = async (employer) => {
    return CompanyService.editEmployer(employer);
  };

  getConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    return Object.values(obj);
  }

  @action
  queryRechargeBags = async () => {
    return CompanyService.queryRechargeBags();
  };

  @action
  queryPaymentGateway = async () => {
    return CompanyService.queryPaymentGateway();
  };

  @action
  queryInvoiceChargeRate = async () => {
    return CompanyService.queryInvoiceChargeRate();
  };

  @action
  createRecharge = async (data) => {
    return CompanyService.createRecharge(data);
  };

  @action
  payRecharge = async (orderId, data) => {
    return CompanyService.payRecharge(orderId, data);
  };

  @action
  cancelRecharge = async (rechargeId) => {
    return CompanyService.cancelRecharge(rechargeId);
  };

  @action
  paywayPrepay = async (orderId, data) => {
    return CompanyService.paywayPrepay(orderId, data);
  };

  @action
  queryRechargeStatus = async (orderId) => {
    return CompanyService.queryRechargeStatus(orderId);
  };

  @action
  sendVerifyCode = async (data) => {
    const nonce = DeviceInfo.getUniqueID();
    data = {
      ...data,
      nonce,
      sign: Md5(data?.regionCode + data?.mobile + nonce + configs.smsCodeSalt).toString(), // md5(regionCode+phone+nonce+固定盐)
      imageCaptcha: data?.imageCaptcha || '',
    };
    return CompanyService.sendVerifyCode(data);
  };

  @action
  authorizeMobile = async (data) => {
    return CompanyService.authorizeMobile(data);
  };

  @action
  queryInboxMessages = async (param) => {
    return CompanyService.queryInboxMessages(param);
  };

  @action
  queryMessagesStatistics = async () => {
    const res = await CompanyService.queryMessagesStatistics();
    companyStore.messageTotal = res?.length ? res[0].unread : 0;
    return res;
  };

  @action
  readMessage = async (messageId) => {
    return CompanyService.readMessage(messageId);
  };

  @action
  refreshJobs = async (jobId) => {
    return CompanyService.refreshJobs(jobId);
  };
}

export default new Actions();
