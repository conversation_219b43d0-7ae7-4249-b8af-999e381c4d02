/**
 * 城市actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import { cos } from 'react-native-reanimated';
import ResourcesService from '../../api/resources';
import regExp from '../../util/regExp';
import moment from 'moment';

class Actions {
  @action
  getAdviserList = async (params) => {
    let res = await ResourcesService.getAdviserList(params);
    const regex = new RegExp('<img', 'gi');
    const result = res ? res.result : [];
    res.result = result.map((item) => {
      const images = item.content && item.content.match(regExp.imgMatch);
      item.sendAt = ''; // moment(item.cdate_unixtime * 1000).format('YYYY-MM-DD');
      item.content = item.content
        ? item.content.replace(regex, `<img style="max-width: 620px; height: auto"`)
        : '';
      item.parseContent =
        item.content &&
        JSON.parse(
          JSON.stringify(item.content)
            .replace(/<\/?.+?\/?>/g, '')
            .replace(/&nbsp;/gi, '')
        );
      item.images = images || [];
      return item;
    });
    return res;
  };
  @action
  getAdviserDetail = async (id) => {
    let res = await ResourcesService.getAdviserDetail(id);
    // const regex = new RegExp('<img', 'gi');
    // const images = res.content.match(regExp.imgMatch);
    // res.sendAt = ''; //moment(res.cdate_unixtime * 1000).format('YYYY-MM-DD');
    // res.nextDate = ''; //res.nextArticle ? moment(res.nextArticle.cdate).format('YYYY-MM-DD') : '';
    // res.nextTitle = res.nextArticle ? res.nextArticle.title : '';
    // res.nextId = res.nextArticle ? res.nextArticle.id : '';
    // res.content = res.content.replace(regex, `<img style="max-width:300px; height: auto"`);
    // res.images = images || [];
    return res;
  };
}

export default new Actions();
