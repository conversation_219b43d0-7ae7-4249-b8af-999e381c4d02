/**
 * 用户actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import DynamicService from '../../api/dynamicService';
import dynamicStore from '../stores/dynamic';
import Session from '../../api/session';

class Actions {
  @observable dynamicStore

  @action
  initLastDynamic = async (lastDynamic, preLastDynamic) => {
    dynamicStore.lastDynamic = lastDynamic || 0;
    dynamicStore.preLastDynamic = preLastDynamic || 0;
  }

  @action
  storeLastDynamic = async (value) => {
    await Session.setPreLastDynamice(dynamicStore.lastDynamic);
    dynamicStore.preLastDynamic = dynamicStore.lastDynamic;
    await Session.setLastDynamice(value);
    dynamicStore.lastDynamic = value;
  }

  @action
  hasNewDynamics = async () => {
    const data = await DynamicService.queryDynamics({ page: 1, size: 10 });
    const lastDynamic = data && data.result && data.result.length > 0 ? data.result[0].id : 0;
    dynamicStore.lastDynamic = lastDynamic;
  }

  @action
  queryDynamics = async (param) => {
    const data = await DynamicService.queryDynamics(param);
    // console.log('动态列表数据', data, param);
    dynamicStore.dynamicList = param.page === 1
      ? data.result : dynamicStore.dynamicList.concat(data.result);
    dynamicStore.dynamicTotalCount = data.totalCount;
    // 每次加载第一页就缓存起来
    if (param.page === 1) {
      const lastDynamic = data && data.result ? data.result[0].id : 0;
      this.storeLastDynamic(lastDynamic);
    }
    return data;
  }

  @action
  publishDynamic = async (twitter) => {
    const data = await DynamicService.publishDynamic(twitter);
    return data;
  }

  @action
  modifyDynamic = async (twitterId, twitter) => {
    const data = await DynamicService.modifyDynamic(twitterId, twitter);
    return data;
  }

  @action
  deleteDynamic = async (twitterId) => {
    const data = await DynamicService.deleteDynamic(twitterId);
    return data;
  }

  @action
  publishComment = async (twitterId, twitterComment) => {
    const data = await DynamicService.publishComment(twitterId, twitterComment);
    return data;
  }

  @action
  queryComments = async (twitterId, param) => {
    const data = await DynamicService.queryComments(twitterId, param);
    // console.log('评论列表数据', data, param, twitterId);
    dynamicStore.commentList = param.page === 1
      ? data.result : dynamicStore.commentList.concat(data.result);
    dynamicStore.commentTotalCount = data.totalCount;
    return data;
  }

  @action
  modifyComment = async (twitterId, twitterCommentId, twitterComment) => {
    const data = await DynamicService.modifyComment(twitterId, twitterCommentId, twitterComment);
    return data;
  }

  @action
  deleteComment = async (twitterId, twitterCommentId) => {
    const data = await DynamicService.deleteComment(twitterId, twitterCommentId);
    return data;
  }

  @action
  likes = async (twitterId) => {
    const data = await DynamicService.likes(twitterId);
    return data;
  }

  @action
  dislikes = async (twitterId) => {
    const data = await DynamicService.dislikes(twitterId);
    return data;
  }

  @action
  uploadDynamicImage = async (file) => {
    const data = await DynamicService.uploadDynamicImage(file);
    return data;
  }

  @action
  blockedUser = async (blockedId) => {
    const data = await DynamicService.blockedUser(blockedId);
    return data;
  }

  @action
  unblockedUser = async (blockedId) => {
    const data = await DynamicService.unblockedUser(blockedId);
    return data;
  }

  @action
  reportedUser = async (reportedId, reportParam) => {
    const data = await DynamicService.reportedUser(reportedId, reportParam);
    return data;
  }

  @action
  resetComments = () => {
    dynamicStore.commentList = [];
    dynamicStore.commentTotalCount = 0;
  }
}

export default new Actions();
