/**
 * 用户actions
 * action 统一返回一个promise
 */
import { action } from 'mobx';
import UserService from '../../api/userService';
import personStore from '../stores/person';
import userStore from '../stores/user';
import application from './application';
import messageStore from '../stores/message';
import util from '../../util';
import { cacheAdvImage, getCacheAdvImage } from '../../common/adv';
import chatMessageUtil from '../../database/chatMessageUtil';
import session from '../../api/session';

class Actions {
  getConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    return Object.values(obj).map((item) => {
      item.isSelected = false;
      item.tempSelected = false;
      return item;
    });
  }

  @action
  getCurrUserInfo = async () => {
    const { isLogin } = personStore;
    const user = await UserService.getCurrUserInfo();
    personStore.me = user;
    personStore.isLogin = !!user?.seekerId;
    session.setPersonInfo(user);
    // 登录注册或进入APP时调用
    if (!isLogin) {
      UserService.uploadDeviceInfo(2);
    }
    return user;
  };

  @action
  getUserBySeekerId = async (seekerId) => UserService.getUserBySeekerId(seekerId);

  @action
  generalModifyPassword = async (newPassword, oldPassword) => {
    const result = await UserService.generalModifyPassword(newPassword, oldPassword);
    return result;
  };

  @action
  generalModifyPhone = async (data) => {
    const result = await UserService.generalModifyPhone(data);
    return result;
  };

  @action
  followCompany = async (employerId) => {
    const result = await UserService.followCompany(employerId);
    return result;
  };

  @action
  queryFollowedCompanys = async (param) => {
    const data = await UserService.queryFollowedCompanys(param);
    // console.log('收藏的公司列表', data.result, data.totalCount);
    personStore.companyTotalCount = data.totalCount;
    personStore.followedCompanyList =
      param.page === 1
        ? data.result.map((item) => {
            item.showDel = false;
            return item;
          })
        : personStore.followedCompanyList.concat(
            data.result.map((item) => {
              item.showDel = false;
              return item;
            })
          );
    return data;
  };

  @action
  deleteFollowedCompany = async (employerIds) => {
    const result = await UserService.deleteFollowedCompany(employerIds);
    return result;
  };

  @action
  followJob = async (jobId) => {
    const result = await UserService.followJob(jobId);
    return result;
  };

  @action
  queryFollowedJobs = async (param) => {
    const data = await UserService.queryFollowedJobs(param);
    // console.log('收藏的职位列表', data.result, data.totalCount);
    personStore.jobTotalCount = data.totalCount;
    personStore.followedJobList =
      param.page === 1
        ? data.result.map((item) => {
            item.showDel = false;
            return item;
          })
        : personStore.followedJobList.concat(
            data.result.map((item) => {
              item.showDel = false;
              return item;
            })
          );
    return data;
  };

  @action
  deleteFollowedJob = async (jobIds) => {
    const result = await UserService.deleteFollowedJob(jobIds);
    return result;
  };

  @action
  uploadImage = async (file) => UserService.uploadImage(file);

  @action
  postInterviewConfirm = async (jobApplyId, data) => {
    const result = await UserService.postInterviewConfirm(jobApplyId, data);
    return result;
  };

  @action
  getInterviewDetail = async (jobApplyId) => {
    const result = await UserService.getInterviewDetail(jobApplyId);
    return result;
  };

  @action
  userFeedback = async (feedback) => UserService.userFeedback(feedback);

  @action
  getBanners = async () => {
    const data = await UserService.getBanners();
    personStore.bannerList = data && data.result ? data.result : [];
    return data;
  };

  /**
   * 缓存启动页面广告图片
   */
  syncCacheAdvImage = async () => {
    // 请求接口获取图片
    const data = await UserService.getLaunchAdvImages();
    const result = data && data.result && data.result.length > 0 ? data.result[0] : {};
    cacheAdvImage(result.slideshowImages);
  };

  /**
   * 获取启动页广告图片
   */
  getAdvImage = async () => {
    return getCacheAdvImage();
  };

  @action
  queryConstants = async () => {
    const constants = await application.getConstants();
    personStore.reporterList = this.getConstantsValues(constants.REPORT_TYPE);
  };

  @action
  setDefaultLanguage = async (language) => {
    const data = await UserService.setDefaultLanguage(language);
    return data;
  };

  /** ********************  通知消息处理接口  ********************** */
  @action
  queryMessageInbox = async (param) => {
    const data = await UserService.queryMessageInbox(param);
    messageStore.totalCount = data.totalCount;
    data.result = data.result.map((item) => {
      item.createAt = item.sendAt ? util.timestampToLongString1(item.sendAt) : '';
      item.sendAt = item.sendAt ? util.getDiffBetweenHM(item.sendAt) : '';
      return item;
    });
    messageStore.list = param.page === 1 ? data.result : messageStore.list.concat(data.result);
    return data;
  };

  @action
  markMessageRead = async (messageId) => UserService.markMessageRead(messageId);

  @action
  statsMessage = async () => {
    if (!userStore.isLogin) return;
    const result = await UserService.statsMessage();
    const temp = result && result.find((item) => item.topic === 'notification');
    messageStore.unread = (temp && temp.unread) || 0;
    messageStore.newPushMessageCount = result.find((item) => item.topic === 'newJob')?.unread || 0;
    messageStore.totalUnreadCount = result.find((item) => item.topic === 'total')?.unread || 0;
    return result;
  };

  @action
  getMessageDetail = async (messageId) => {
    const data = await UserService.getMessageDetail(messageId);
    return data;
  };

  @action
  getPublicMessageDetail = async (messageId) => {
    const data = await UserService.getPublicMessageDetail(messageId);
    return data;
  };

  @action
  getUnreadMessages = async () => {
    const data = await UserService.getUnreadMessages();
    return data;
  };

  /** ****************  动态相关新需求  *********************** */
  @action
  getUnreadTwitterMessages = async () => {
    const data = await UserService.getUnreadTwitterMessages();
    personStore.hasNewTwitterMsg = !!data;
    personStore.newTwitterMsgData = data;
    if (data && data.unreadTwitterNum !== 0) {
      global.emitter.emit('msgModalShow', true);
    }
    return data;
  };

  @action
  markTwitterMessagesRead = async () => {
    const data = await UserService.markTwitterMessagesRead();
    return data;
  };

  @action
  deleteAllTwitterMessages = async () => {
    const data = await UserService.deleteAllTwitterMessages();
    return data;
  };

  @action
  deleteTwitterMessage = async (messageId) => {
    const data = await UserService.deleteTwitterMessage(messageId);
    return data;
  };

  @action
  getTwitterLikedUsers = async (twitterId) => {
    const data = await UserService.getTwitterLikedUsers(twitterId);
    personStore.likedList = data;
    return data;
  };

  @action
  resetTwitterLikedUsers = async () => {
    personStore.likedList = [];
  };

  @action
  resetTwitterDetail = async () => {
    personStore.twitterDetail = null;
  };

  @action
  resetTwitterMsgData = async () => {
    personStore.newTwitterMsgData = null;
  };

  @action
  resetTwitterMsgList = async () => {
    personStore.twitterMsgList = [];
  };

  @action
  getTwitterDetail = async (twitterId) => {
    const data = await UserService.getTwitterDetail(twitterId);
    personStore.twitterDetail = data;
    return data;
  };

  @action
  queryTwitterMessage = async (param) => {
    const data = await UserService.queryTwitterMessage(param);
    personStore.totalCount = data.totalCount;
    data.result = data.result.map((item) => {
      item.sendAt = item.sendAt ? util.getDiffBetweenHM(item.sendAt) : '';
      return item;
    });
    personStore.twitterMsgList =
      param.page === 1 ? data.result : personStore.twitterMsgList.concat(data.result);
    return data;
  };

  @action
  scanLogin = async (queryString) => {
    const data = await UserService.scanLogin(queryString);
    return data;
  };

  getImInfo = async (param) => {
    if (!userStore.hasConnectImSocket) return null;
    const res = await UserService.getImInfo(param);
    return res?.contacts?.length && chatMessageUtil.handleImContact(res.contacts[0]);
  };

  @action
  queryPushMessageCount = async () => {
    const res = await UserService.queryNewJobMessageBadge();
    messageStore.newPushMessageCount = (typeof res == 'number' ? res : res?.data) || 0;
    return res;
  };
}

export default new Actions();
