/**
 * 简历actions
 * action 统一返回一个promise
 */

import { observable, action } from 'mobx';
import resumeStore from '../stores/resume';
import globalStore from '../stores/global';
import ResumeService from '../../api/resumeService';
import application from './application';
import Util from '../../util';
import Session from '../../api/session';
import I18n from '../../i18n';

class Actions {
  @observable resumeStore;

  getConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    return Object.values(obj);
  }

  getCategoryConstantsValues(obj) {
    if (!obj) {
      return [];
    }
    const arr1 = [];
    Object.keys(obj).forEach((item) => {
      Object.values(obj[item]).map((l) => {
        arr1.push(l);
        return l;
      });
    });
    return arr1;
  }

  @action
  initLastSeen = async (lastSeen, preLastSeen) => {
    resumeStore.lastSeen = lastSeen || 0;
    resumeStore.preLastSeen = preLastSeen || 0;
  };

  @action
  storeLastSeen = async (value) => {
    await Session.setPreLastSeen(resumeStore.lastSeen);
    resumeStore.preLastSeen = resumeStore.lastSeen;
    await Session.setLastSeen(value);
    resumeStore.lastSeen = value;
  };

  @action
  hasNewSeen = async () => {
    const data = await ResumeService.getResumesViewers({ page: 1, size: 8 });
    const lastSeen = data && data.result && data.result.length > 0 ? data.result[0].employerId : 0;
    resumeStore.lastSeen = lastSeen;
  };

  @action
  getResumes = async () => {
    const isLogin = await Session.isLogin();
    if (globalStore.isEnterprise || !isLogin) return;
    const resumes = await ResumeService.getResumes();
    const resultResumeArr = [];
    if (resumes && resumes.length > 0) {
      const defaultCV = resumes.find((item) => item.defaultCv === true);
      if (defaultCV) {
        resumeStore.hasDefaultResmue = true;
        resumeStore.defaultResmueId = defaultCV.resumeId;
      } else if (resumeStore.annexResumeList && resumeStore.annexResumeList.length > 0) {
        const defaultOtherCV = resumeStore.annexResumeList.find((item) => item.defaultCv === true);
        if (defaultOtherCV) {
          resumeStore.hasDefaultResmue = true;
          resumeStore.defaultResmueId = defaultOtherCV.cvId;
        } else {
          resumeStore.hasDefaultResmue = false;
          resumeStore.defaultResmueId = '';
        }
      } else {
        resumeStore.hasDefaultResmue = false;
        resumeStore.defaultResmueId = '';
      }

      for (let i = 0; i < resumes.length; i += 1) {
        const element = resumes[i];
        element.updateTime = Util.dateToLocalString1(element.updateTime);
        element.isSelected = false;
        resultResumeArr.push(element);
      }
      resumeStore.resumeList = Object.values(resultResumeArr)
        .sort((item1, item2) => item1.resumeId - item2.resumeId)
        .slice(0, 3);
      this.changeResumeFromRefresh();
      return resumes && resumes.length > 0
        ? resumes.map((item) => {
            item.isSelected = false;
            return item;
          })
        : [];
    }
    return this.addResume({});
  };

  @action
  changeResumeFromRefresh = () => {
    if (resumeStore.resumeList && resumeStore.resumeList.length > 0) {
      if (resumeStore.currentIndex > resumeStore.resumeList.length - 1) {
        this.changeCurrentResume(resumeStore.resumeList.length - 1);
      } else {
        this.changeCurrentResume(resumeStore.currentIndex);
      }
    }
  };

  @action
  changeCurrentResume = (index) => {
    resumeStore.currentIndex = index;
    resumeStore.currentResume = Object(resumeStore.resumeList[index]);
    resumeStore.currentWorkList = resumeStore.currentResume.experiences
      .map((item) => {
        item.showDel = false;
        return item;
      })
      .sort((item1, item2) => (item1.fromDate < item2.fromDate ? 1 : -1));
    resumeStore.currentEducationLsit = resumeStore.currentResume.educations
      .map((item) => {
        item.showDel = false;
        return item;
      })
      .sort((item1, item2) => (item1.fromDate < item2.fromDate ? 1 : -1));
    resumeStore.currentLanguageList = resumeStore.currentResume.languageLevels.map((item) => {
      item.showDel = false;
      return item;
    });
    resumeStore.currentSkillList = resumeStore.currentResume.skills.map((item) => {
      item.showDel = false;
      return item;
    });
    resumeStore.currentQualificationList = resumeStore.currentResume.qualifications.map((item) => {
      item.showDel = false;
      return item;
    });
    this.updateCompleteness();
  };

  @action
  queryConstants = async () => {
    const constants = await application.getConstants();
    const categoryConstants = await application.getConstantsFlatByName('CATEGORY');
    resumeStore.sexList = this.getConstantsValues(constants.SEX);
    resumeStore.qualificationList = this.getConstantsValues(constants.QUALIFICATION);
    resumeStore.majorList = this.getConstantsValues(constants.MAJOR);
    resumeStore.languageList = this.getConstantsValues(constants.LANGUAGE);
    resumeStore.languageLevelList = this.getConstantsValues(constants.LANGUAGE_LEVEL);
    resumeStore.indutrialList = this.getConstantsValues(constants.INDUSTRIAL);
    resumeStore.categoryList = this.getCategoryConstantsValues(categoryConstants);
    resumeStore.jobLevelList = this.getConstantsValues(constants.JOB_LEVEL);
    const arr = [{ label: I18n.t('page_job_text_unlimited'), value: 0 }];
    for (let i = 1; i < 10; i += 1) {
      arr.push({ label: `${i}`, value: i });
    }
    arr.push({ label: '15', value: 15 });
    arr.push({ label: '20', value: 20 });
    arr.push({ label: '30', value: 30 });
    resumeStore.workYearList = arr;
    resumeStore.maritalStatusList = this.getConstantsValues(constants.MARITAL) || [];
    resumeStore.maritalStatusList.unshift({ label: I18n.t('page_job_text_unlimited'), value: 0 });
    resumeStore.filterWorkYears = [
      {
        label: I18n.t('page_resume_text_no_experience'),
        min: 0,
        max: 0,
        value: 0,
        selected: false,
      },
      {
        label: I18n.t('page_resume_text_one_year'),
        min: 0,
        max: 1,
        value: 1,
        selected: false,
      },
      {
        label: '1-3',
        min: 1,
        max: 3,
        value: 2,
        selected: false,
      },
      {
        label: '3-5',
        min: 3,
        max: 5,
        value: 3,
        selected: false,
      },
      {
        label: '5-10',
        min: 5,
        max: 10,
        value: 4,
        selected: false,
      },
      {
        label: I18n.t('page_resume_text_ten_years'),
        min: 10,
        max: '',
        value: 5,
        selected: false,
      },
    ];
  };

  @action
  addResume = async (resume) => await ResumeService.addResume(resume);

  @action
  deleteResume = async (resumeId) => await ResumeService.deleteResume(resumeId);

  @action
  updateResumeProfile = async (resumeId, resumeProfile) =>
    await ResumeService.updateResumeProfile(resumeId, resumeProfile);

  @action
  updateResumeAttributes = async (resumeId, attributes) =>
    await ResumeService.updateResumeAttributes(resumeId, attributes);

  @action
  addResumeWork = async (resumeId, experience) =>
    await ResumeService.addExperience(resumeId, experience);

  @action
  updateResumeWork = async (experienceId, resumeId, experience) =>
    await ResumeService.updateExperiences(experienceId, resumeId, experience);

  @action
  deleteResumeWork = async (experienceId, resumeId) =>
    await ResumeService.deleteExperiences(experienceId, resumeId);

  @action
  addEducation = async (resumeId, education) =>
    await ResumeService.addEducation(resumeId, education);

  @action
  updateEducation = async (eduId, resumeId, education) =>
    await ResumeService.updateEducation(eduId, resumeId, education);

  @action
  deleteEducation = async (eduId, resumeId) => await ResumeService.deleteEducation(eduId, resumeId);

  @action
  addLanguage = async (resumeId, language) => await ResumeService.addLanguage(resumeId, language);

  @action
  updateLanguage = async (languageId, resumeId, language) =>
    await ResumeService.updateLanguage(languageId, resumeId, language);

  @action
  deleteLanguage = async (languageId, resumeId) =>
    await ResumeService.deleteLanguage(languageId, resumeId);

  @action
  updateCareerProfile = async (resumeId, careerProfile) =>
    await ResumeService.updateCareerProfile(resumeId, careerProfile);

  @action
  addSkill = async (resumeId, skill) => await ResumeService.addSkill(resumeId, skill);

  @action
  updateSkill = async (skillId, resumeId, skill) =>
    await ResumeService.updateSkill(skillId, resumeId, skill);

  @action
  deleteSkill = async (skillId, resumeId) => await ResumeService.deleteSkill(skillId, resumeId);

  @action
  hideResume = async (resumeId, level) => await ResumeService.hideResume(resumeId, level);

  @action
  refreshResume = async (resumeId) => await ResumeService.refreshResume(resumeId);

  @action
  copyResume = async (resume) => await ResumeService.copyResume(resume);

  @action
  addQualification = async (resumeId, qualification) =>
    await ResumeService.addQualification(resumeId, qualification);

  @action
  updateQualification = async (qualificationId, resumeId, qualification) =>
    await ResumeService.updateQualification(qualificationId, resumeId, qualification);

  @action
  deleteQualification = async (qualificationId, resumeId) =>
    await ResumeService.deleteQualification(qualificationId, resumeId);

  @action
  uploadImage = async (resumeId, file) => await ResumeService.uploadImage(resumeId, file);

  @action
  shareResume = async (resumeId) => {
    const result = await ResumeService.shareResume(resumeId);
    resumeStore.shareToken = result.data;
    return result.data;
  };

  updateCompleteness = async () => {
    const current = resumeStore.currentResume;
    let count = 0;
    if (resumeStore.currentWorkList.length > 0) {
      count += 1;
    }
    if (resumeStore.currentEducationLsit.length > 0) {
      count += 1;
    }
    if (resumeStore.currentLanguageList.length > 0) {
      count += 1;
    }
    if (resumeStore.currentSkillList.length > 0) {
      count += 1;
    }
    if (resumeStore.currentQualificationList.length > 0) {
      count += 1;
    }
    if (current.profile.firstName) {
      count += 1;
    }
    if (current.profile.avatar) {
      count += 1;
    }
    if (current.intention.reqJobTitle) {
      count += 1;
    }
    if (current.careerProfile.careerPosition) {
      count += 1;
    }
    if (current.description) {
      count += 1;
    }
    if (current.hobby) {
      count += 1;
    }
    if (current.training) {
      count += 1;
    }

    const comp = (100 * count) / 12;
    if (parseInt(comp, 10) !== current.completeness) {
      const postData = {};
      postData.name = current.name;
      postData.hobby = current.hobby ? current.hobby : '';
      postData.training = current.training ? current.training : '';
      postData.completeness = parseInt(comp, 10);
      postData.description = current.description ? current.description : '';
      this.updateResumeAttributes(current.resumeId, postData).then((res) => {
        if (res && res.successful) {
          this.getResumes();
        }
      });
    }
  };

  @action
  getResumesViewers = async (params) => {
    const res = await ResumeService.getResumesViewers(params);
    resumeStore.resumeViewersTotalCount = res.totalCount;
    resumeStore.resumeViewers =
      params.page === 1 ? res.result : resumeStore.resumeViewers.concat(res.result);
    if (params.page === 1) {
      const lastSeen = res && res.result && res.result.length > 0 ? res.result[0].employerId : 0;
      this.storeLastSeen(lastSeen);
    }
    return res;
  };

  @action
  setDefaultResume = async (resumeId, defaultCv) =>
    await ResumeService.setDefaultResume(resumeId, defaultCv);

  @action
  addAnnexResume = async (params) => {
    const res = await ResumeService.addAnnexResume(params);
    return res;
  };

  @action
  getAnnexResumes = async () => {
    const res = await ResumeService.getAnnexResumes();
    if (res && res.length > 0) {
      const defaultCV = res.find((item) => item.defaultCv === true);
      if (defaultCV) {
        resumeStore.hasDefaultResmue = true;
        resumeStore.defaultResmueId = defaultCV.cvId;
      } else if (resumeStore.resumeList && resumeStore.resumeList.length > 0) {
        const defaultOtherCV = resumeStore.resumeList.find((item) => item.defaultCv === true);
        if (defaultOtherCV) {
          resumeStore.hasDefaultResmue = true;
          resumeStore.defaultResmueId = defaultOtherCV.resumeId;
        } else {
          resumeStore.hasDefaultResmue = false;
          resumeStore.defaultResmueId = '';
        }
      } else {
        resumeStore.hasDefaultResmue = false;
        resumeStore.defaultResmueId = '';
      }
    }
    resumeStore.annexResumeList = res || [];
    return res;
  };

  @action
  getAllResumes = async () => {
    const res = await ResumeService.getAllResumes();
    resumeStore.allResumeList = res || [];
    return res;
  };

  @action
  changeCurrentAnnexResume = (item) => {
    resumeStore.currentAnnexResume = item;
  };
}

export default new Actions();
