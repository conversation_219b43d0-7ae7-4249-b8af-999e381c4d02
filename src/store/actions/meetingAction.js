import { action } from 'mobx';
import callStore from '../stores/callStore';
import userStore from '../stores/user';
import constant from '../constant';
import sendMessageUtil from '../../database/sendMessageUtil';
import AgoraSdk from '../../api/agoraSdk';
import { Vibration } from 'react-native';
import { callSound } from '../../util/soundPlayer';
import I18n from '../../i18n';
import JPush from 'jpush-react-native';
import { AppState } from '../../components';
import PermissionUtilExtra from '../../util/permissionUtilExtra';
import _ from 'lodash';
import AppModule, { RingerMode } from '../../modules/AppModule';
import AppUtil from '../../util/appUtil';

function append0(number) {
  return number < 10 ? `0${number}` : number.toString();
}

class Meeting {
  /**
   * 开始呼叫邀请
   * users:[{imId:100100,avatar:'',name:'1'},{imId:100101,avatar:'',name:'2'}];
   */
  @action
  startCall = async (users) => {
    const channel = new Date().getTime().toString();
    logger.debug('MeetingCall 开始呼叫 users:{@users} channel:{@channel}', users, channel);

    callStore.channel = channel;
    callStore.callType = constant.callType.meetingCall;

    await AgoraSdk.joinChannel(channel, userStore.imId);
    const ringerMode = await AppModule.getRingerMode();
    callStore.enableSpeakerphone = ringerMode === RingerMode.NORMAL;
    await AgoraSdk.engine.muteLocalVideoStream(true);

    this.playCallNotification(false);

    callStore.peersMap = new Map(
      users.map((x) => [
        x.imId || x.sessionId,
        {
          imId: x.imId || x.sessionId,
          avatar: x.avatar,
          name: x.nickname || x.friendNickname || x.title,
          callState: constant.callState.invite,
          timeoutCount: new Date().getTime() + 40000,
        },
      ])
    );

    const { imId, avatar, imNickname } = userStore;
    callStore.peersMap.set(imId, {
      imId,
      avatar,
      name: imNickname,
      callState: constant.callState.accept,
      openMicrophone: true,
      isHost: true,
    });

    callStore.showCall = true; // 显示呼叫页面
    callStore.timeCount = 0;

    this.sendControlMessage(constant.callState.invite);
    this.startTimeoutTimer();
  };

  startTimeoutTimer = () => {
    if (!this.timer1) {
      this.timer1 = setInterval(() => {
        let deleteKeys = [];

        Array.from(callStore.peersMap.values())
          .map((y) => {
            return { ...y };
          })
          .filter(
            (x) =>
              x.callState === constant.callState.invite ||
              x.callState === constant.callState.receiveInvite
          )
          .forEach((item) => {
            const currentTime = new Date().getTime();
            // console.log('timeout 2222', currentTime, item.timeoutCount);
            if (currentTime > item.timeoutCount) {
              item.callState = constant.callState.noResponse;
              deleteKeys.push(item);
            }
          });
        if (deleteKeys.length > 0) {
          console.log('timeout 2222', deleteKeys);
          deleteKeys.forEach((item) => callStore.peersMap.delete(item.imId));
          this.checkCallWillEnd();
        }
      }, 1000);
    }
  };

  startTimeCountTimer = () => {
    console.log('startTimeCountTimer 11111111');
    if (!this.timer2) {
      console.log('startTimeCountTimer 22222');

      this.timer2 = setInterval(() => {
        if (
          Array.from(callStore.peersMap.values())
            .map((y) => {
              return { ...y };
            })
            .filter((x) => x.callState === constant.callState.accept).length >= 2 ||
          callStore.timeCount > 0
        ) {
          callStore.timeCount += 1;
        }
      }, 1000);
    }
  };

  stopTimer = () => {
    if (this.timer1) {
      clearInterval(this.timer1);
      this.timer1 = null;
    }
    if (this.timer2) {
      clearInterval(this.timer2);
      this.timer2 = null;
    }
  };

  /**
   * 邀请其他人
   */
  @action
  inviteMembers = async (users) => {
    logger.debug(
      'MeetingCall 邀请其他人 users:{@users} channel:{@channel}',
      users,
      callStore.channel
    );

    const newMembers = new Map(
      users.map((x) => [
        x.imId || x.sessionId,
        {
          imId: x.imId || x.sessionId,
          avatar: x.avatar,
          name: x.nickname || x.friendNickname || x.title,
          callState: constant.callState.invite,
          timeoutCount: new Date().getTime() + 40000,
        },
      ])
    );

    callStore.peersMap.merge(newMembers);
    console.log('MeetingCall333', callStore.peersMap);
    this.sendControlMessage(constant.callState.invite);
    this.startTimeoutTimer();
  };

  /**
   * 单聊-邀请其他人
   */
  @action
  singleInviteMembers = async (users) => {
    logger.debug(
      'MeetingCall 单聊-邀请其他人 users:{@users} channel:{@channel}',
      users,
      callStore.channel
    );
    callStore.callType = 3; // 变为多人会议

    const { imId, avatar, imNickname } = userStore;
    callStore.peersMap = new Map();
    callStore.peersMap.set(imId, {
      imId,
      avatar,
      name: imNickname,
      callState: constant.callState.accept,
      openMicrophone: true,
      openCamera: false,
      isHost: true,
    });

    const { peerId, user } = callStore;
    callStore.peersMap.set(peerId, {
      imId: peerId,
      avatar: user?.avatar,
      name: user?.name,
      callState: constant.callState.accept,
    });

    const newMembers = new Map(
      users.map((x) => [
        x.imId || x.sessionId,
        {
          imId: x.imId || x.sessionId,
          avatar: x.avatar,
          name: x.nickname || x.friendNickname || x.title,
          callState: constant.callState.invite,
          timeoutCount: new Date().getTime() + 40000,
        },
      ])
    );

    callStore.peersMap.merge(newMembers);

    console.log('MeetingCall444', callStore.peersMap);

    this.sendControlMessage(constant.callState.invite);

    // 启用视频模块。
    await AgoraSdk.engine.enableVideo();
    // 开启本地视频预览。
    await AgoraSdk.startPreview();
    // 开启通话音量检测
    await AgoraSdk.engine.enableAudioVolumeIndication(1000, 3, false);
    await AgoraSdk.engine.muteLocalVideoStream(true);
    await AgoraSdk.engine.muteLocalAudioStream(false);
    this.startTimeoutTimer();
  };

  /**
   * 拒绝呼叫
   */
  @action
  refuseCall = () => {
    const { imId } = userStore;

    let myInfo = callStore.peersMap.get(imId);
    console.log('MeetingCall 123', myInfo);

    myInfo.callState = constant.callState.reject;
    callStore.peersMap.set(imId, myInfo);

    this.sendControlMessage(constant.callState.reject);

    this.endCallNotification();

    callStore.peersMap = new Map();
    callStore.showCall = false;
    callStore.channel = '';
    callStore.zoomIn = false;
  };

  /**
   * 接受呼叫
   */
  @action
  acceptCall = async () => {
    const { imId } = userStore;
    await AgoraSdk.joinChannel(callStore.channel, imId);
    await AgoraSdk.engine.setEnableSpeakerphone(callStore.enableSpeakerphone);
    await AgoraSdk.engine.muteLocalVideoStream(true);

    let myInfo = callStore.peersMap.get(imId);
    myInfo.callState = constant.callState.accept;
    callStore.peersMap.set(imId, myInfo);

    this.sendControlMessage(constant.callState.accept);

    this.endCallNotification();

    callStore.timeCount = 0;
    this.startTimeCountTimer();
  };

  /**
   * 结束呼叫
   */
  @action
  endCall = async () => {
    const { imId } = userStore;

    let myInfo = callStore.peersMap.get(imId);
    if (myInfo) {
      myInfo.callState = constant.callState.end;
    }
    callStore.peersMap.set(imId, myInfo);

    this.endCallNotification();

    this.sendControlMessage(constant.callState.end);

    setTimeout(() => {
      AgoraSdk.leaveChannel();

      callStore.peersMap = new Map();
      callStore.zoomIn = false;
      callStore.showCall = false;
      callStore.timeCount = 0;
      callStore.channel = '';
      this.stopTimer();
    }, 1000);
  };

  @action
  openMicrophone = async () => {
    const { imId } = userStore;
    let myInfo = callStore.peersMap.get(imId);
    const openMicrophone = !!myInfo?.openMicrophone;
    await AgoraSdk.engine?.muteLocalAudioStream(openMicrophone);

    myInfo.openMicrophone = !openMicrophone;
    callStore.peersMap.set(imId, myInfo);
  };

  @action
  openCamera = async () => {
    const { imId } = userStore;
    let myInfo = callStore.peersMap.get(imId);
    const openCamera = !!myInfo?.openCamera;
    await AgoraSdk.engine?.muteLocalVideoStream(openCamera);

    myInfo.openCamera = !openCamera;
    callStore.peersMap.set(imId, myInfo);
  };

  // 查看用户大屏
  @action
  showBigScreen = async (user) => {
    let myInfo = callStore.peersMap.get(user.imId);
    const isUserActive = !!myInfo?.isUserActive;
    myInfo.isUserActive = !isUserActive;
    callStore.peersMap.set(imId, myInfo);
  };

  // 发送指令消息
  sendControlMessage = async (state, members, userId, channel) => {
    const { imId } = userStore;
    const { peersMap } = callStore;

    if (state === constant.callState.busy) {
      sendMessageUtil.sendMeetingCallMessage(userId, state, channel, null);
    } else {
      peersMap.forEach((user) => {
        if (user.imId !== imId) {
          sendMessageUtil.sendMeetingCallMessage(user.imId, state, callStore.channel, peersMap);
        }
      });
    }
  };

  //{"msg": {"im_id": 10300, "req_id": "A6CE0B24-6E25-4AF1-8885-7CF967D72AF9103001645067667915", "rim_id": 10298, "state": 1, "timestamp": 1645067667915, "type": "notice"}, "msg_id": 1645067670296610, "msg_time": 1645067670296, "receiver_id": 10298, "send_self": 0, "sender_id": 10300}
  onReceiveMeetingCall = async (data) => {
    logger.debug('MeetingCall 收到自定义消息 {@message}', data);
    if (data.msg.state === constant.callState.invite) {
      // 收到邀请的消息
      this.onReceiveInvite(data.sender_id, data.msg.channel, data.msg.meetingInfo);
    } else if (data.msg.state === constant.callState.accept) {
      // 收到接受呼叫的消息
      this.onReceiveAccept(data.sender_id, data.msg.channel, data.msg.meetingInfo);
    } else if (data.msg.state === constant.callState.reject) {
      // 收到拒绝的消息
      this.onReceiveReject(data.sender_id);
    } else if (data.msg.state === constant.callState.end) {
      // 收到结束的消息
      this.onReceiveEndCall(data.sender_id);
    } else if (data.msg.state === constant.callState.busy) {
      // 收到正忙的消息
      this.onReceiveBusy(data.sender_id);
    } else if (data.msg.state === constant.callState.noResponse) {
      // 收到无响应的消息
      this.onNoResponse(data.sender_id, data.msg.channel, data.msg.meetingInfo);
    }
  };

  onReceiveVoipInvite = async (message) => {
    logger.debug('MeetingCall 收到来自voip的消息 {@message} ', message);
    if (message?.state === constant.callState.invite) {
      // TODO 去掉推送的音频频点击处理，靠离线消息来
      //this.onReceiveInvite(message?.sessionId, message?.channel, message?.meetingInfo);
    } else if (message?.state === constant.callState.cancel) {
    }
  };

  @action
  onReceiveInvite = async (userId, channel, meetingInfo) => {
    logger.debug(
      'MeetingCall 收到邀请 channel:{@channel} {@userId} {@meetingInfo}',
      channel,
      userId,
      meetingInfo
    );

    if (channel === callStore.channel) {
      logger.debug('MeetingCall 收到邀请3 ');

      // 单聊转多聊
      if (
        meetingInfo &&
        (callStore.callType === constant.callType.singleAudioCall ||
          callStore.callType === constant.callType.singleVideoCall)
      ) {
        callStore.callType = constant.callType.meetingCall;

        callStore.peersMap = new Map();
        Object.values(meetingInfo).forEach((user) => {
          callStore.peersMap.set(user.imId, user);
        });
        let myInfo = callStore.peersMap.get(userStore.imId);
        myInfo.openMicrophone = true;
        myInfo.openCamera = false;
        callStore.peersMap.set(myInfo.imId, myInfo);
        logger.debug('MeetingCall 收到邀请 单聊转多聊 ');

        await AgoraSdk.setupCall();
        await AgoraSdk.engine.setEnableSpeakerphone(true);
        await AgoraSdk.engine.muteLocalVideoStream(true);
        await AgoraSdk.engine.muteLocalAudioStream(false);
        this.startTimeoutTimer();
      } else {
        // 邀请了其他人
        Object.values(meetingInfo).forEach((user) => {
          if (!callStore.peersMap.has(user.imId)) {
            callStore.peersMap.set(user.imId, user);
          }
        });
      }
      return;
    }
    if (callStore.showCall) {
      // 忙线中
      this.sendControlMessage(constant.callState.busy, null, userId, channel);
      return;
    }
    callStore.callType = constant.callType.meetingCall;
    callStore.enableSpeakerphone = true;
    this.playCallNotification(true);
    callStore.peersMap = new Map();
    Object.values(meetingInfo).forEach((user) => {
      callStore.peersMap.set(user.imId, user);
    });
    let myInfo = callStore.peersMap.get(userStore.imId);
    // 判断超时
    if (new Date().getTime() > myInfo.timeoutCount) {
      return;
    }

    myInfo.openMicrophone = true;
    myInfo.openCamera = false;
    myInfo.callState = constant.callState.receiveInvite;
    callStore.peersMap.set(myInfo.imId, myInfo);

    if (callStore.peersMap.has(userId)) {
      callStore.hostInfo = callStore.peersMap.get(userId);
    }
    callStore.showCall = true;
    callStore.channel = channel;

    this.startTimeoutTimer();

    logger.debug('MeetingCall 收到邀请 callStore:{@callStore}', callStore);
    if (AppState.currentState === 'background') {
      const params = {
        messageID: '999',
        title: '',
        content: callStore.hostInfo.name + I18n.t('page_call_notice_invite_metting'),
        extras: {
          type: 'voip',
          channel,
          state: 1,
          callType: 'meetingCall',
          sessionId: userId,
          meetingInfo,
        },
      };
      JPush.addLocalNotification(params);
    }
    await PermissionUtilExtra.requestMultiple();
  };

  @action
  onReceiveAccept = async (userId, channel, meetingInfo) => {
    this.endCallNotification();

    if (callStore.peersMap.has(userId)) {
      let userInfo = callStore.peersMap.get(userId);
      userInfo.callState = constant.callState.accept;
      callStore.peersMap.set(userInfo.imId, userInfo);
    } else {
      if (meetingInfo) {
        Object.values(meetingInfo).forEach((user) => {
          if (user.imId === userId) {
            callStore.peersMap.set(user.imId, user);
          }
        });
      }
    }
    this.startTimeCountTimer();
  };

  onReceiveReject = async (userId) => {
    console.log('onReceiveReject111', userId, callStore.peersMap);
    if (!callStore.peersMap.has(userId)) {
      return;
    }
    let userInfo = callStore.peersMap.get(userId);
    userInfo.callState = constant.callState.reject;
    callStore.peersMap.set(userInfo.imId, userInfo);
    setTimeout(() => {
      callStore.peersMap.delete(userId);
      this.checkCallWillEnd();
    }, 0);
  };

  onReceiveBusy = async (userId) => {
    let userInfo = callStore.peersMap.get(userId);
    userInfo.callState = constant.callState.busy;
    callStore.peersMap.set(userInfo.imId, userInfo);
    setTimeout(() => {
      callStore.peersMap.delete(userId);
      this.checkCallWillEnd();
    }, 0);
  };

  onNoResponse = async (userId, channel, meetingInfo) => {
    Object.values(meetingInfo).forEach((user) => {
      if (user.callState === constant.callState.noResponse) {
        callStore.peersMap.delete(user.imId);
      }
    });
    console.log('onNoResponse 222', callStore.peersMap);
    this.checkCallWillEnd();
  };

  @action
  onReceiveEndCall = async (userId) => {
    console.log('onReceiveEndCall 000');
    if (!callStore.peersMap.has(userId)) {
      return;
    }
    let userInfo = callStore.peersMap.get(userId);
    if (userInfo.callState === constant.callState.end) {
      return;
    }
    userInfo.callState = constant.callState.end;
    callStore.peersMap.set(userInfo.imId, userInfo);
    setTimeout(() => {
      callStore.peersMap.delete(userId);
      this.checkCallWillEnd();
    }, 0);
  };

  @action
  onReceiveMuteVideo = async (userId, mute) => {
    if (!callStore.peersMap.has(userId)) {
      return;
    }
    let userInfo = callStore.peersMap.get(userId);
    userInfo.openCamera = !mute;
    callStore.peersMap.set(userInfo.imId, userInfo);
  };

  @action
  onReceiveMuteAudio = async (userId, mute) => {
    if (!callStore.peersMap.has(userId)) {
      return;
    }
    let userInfo = callStore.peersMap.get(userId);
    userInfo.openMicrophone = !mute;
    callStore.peersMap.set(userInfo.imId, userInfo);
  };

  @action
  onReceiveIsSpeaking = (users) => {
    callStore.peersMap.forEach((x) => {
      x.isSpeaking = users.map((k) => k.uid).includes(x.imId);
    });
  };

  @action
  handleOfflineMessage = (message) => {
    if (!message) return;
    logger.debug('MeetingCall 收到离线的消息 {@message}', message);
    if (callStore.showCall) {
      return;
    }
    let meetingInfo = _.cloneDeep(message.msg.meetingInfo);
    //去掉已经结束 或者已经拒绝 或者已经超时的成员
    Object.values(meetingInfo).forEach((user) => {
      if (
        user.callState === constant.callState.reject ||
        user.callState === constant.callState.end
      ) {
        delete meetingInfo[user.imId];
      }
      if (
        user.callState === constant.callState.invite &&
        new Date().getTime() > user.timeoutCount
      ) {
        delete meetingInfo[user.imId];
      }
    });
    callStore.hostInfo =
      Object.values(meetingInfo).find((x) => x.isHost) || Object.values(meetingInfo)[0];
    if (Object.values(meetingInfo).length <= 1) {
      return;
    }

    let myInfo = meetingInfo[userStore.imId];
    // 自己是被邀请状态并且还没有超时
    if (
      myInfo &&
      myInfo.callState === constant.callState.invite &&
      new Date().getTime() < myInfo.timeoutCount - 2000
    ) {
      this.onReceiveInvite(callStore.hostInfo.imId, message.msg.channel, meetingInfo);
    }
  };

  @action
  formatHHMMSS = (second) => {
    if (!second || second <= 0) {
      return '00:00';
    }
    const s = second % 60;
    const m = Math.floor(second / 60);
    const h = Math.floor(m / 60);
    if (h === 0) {
      return `${append0(m)}:${append0(s)}`;
    } else {
      return `${append0(h)}:${append0(m)}:${append0(s)}`;
    }
  };

  checkCallWillEnd = async () => {
    let acceptArray = Array.from(callStore.peersMap.values()).filter(
      (x) => x.callState === constant.callState.accept
    );
    // 剩下一个人 || 成员里已经不包含自己了 || 成员中没有接受的
    if (
      callStore.peersMap.size === 1 ||
      !callStore.peersMap.has(userStore.imId) ||
      acceptArray.length === 0
    ) {
      this.endCallNotification();
      console.log('22221');

      AgoraSdk.leaveChannel();

      callStore.zoomIn = false;
      callStore.showCall = false;
      callStore.timeCount = 0;
      callStore.peersMap = new Map();
      callStore.channel = '';
      callStore.callState = constant.callState.none;
      JPush.removeLocalNotification({ messageID: '999' });
      this.stopTimer();
    }
  };

  playCallNotification = (vibrate) => {
    console.log('ssssdf1111', callSound);
    callSound.setSpeakerphoneOn(callStore.enableSpeakerphone);
    callSound.play();
    if (vibrate) {
      Vibration.vibrate([0, 1000, 1000, 1000], true);
      if (AppState.currentState === 'background') {
        AppUtil.startLauncherActivity();
      }
    }
  };

  endCallNotification = async () => {
    callSound.stop();
    Vibration.cancel();
  };

  @action
  checkInCall = () => {
    const { showCall } = callStore;

    if (showCall) {
      toast.show(I18n.t('page_call_tips_incall'));
      return true;
    }
    return false;
  };

  @action
  hideCall = () => {
    callStore.showCall = false;
  };

  @action
  setCallType = (type) => {
    callStore.callType = type;
  };
}

export default new Meeting();
