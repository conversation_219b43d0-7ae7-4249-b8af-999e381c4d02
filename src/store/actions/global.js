import { observable, action } from 'mobx';
import globalStore from '../stores/global';
import storage from '../../common/storage';

class Actions {
  @observable globalStore;

  @action
  setEnterprise = async (isEnterprise) => {
    await storage.setEnterprise(isEnterprise);
    globalStore.isEnterprise = isEnterprise;
  };

  @action
  setRemindCompleteIntension = async (remindCompleteIntension = false) => {
    globalStore.remindCompleteIntension = remindCompleteIntension;
  };
}

export default new Actions();
