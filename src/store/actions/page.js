/**
 * 设置actions
 * action 统一返回一个promise
 * author: moke
 */

import { action } from 'mobx';
import pageStore from '../stores/page';
import constant from '../constant';

class Actions {
  /**
   * 设置当前tab
   */
  @action
  selectedTab = (name) => {
    pageStore.selectedTab = name;
    global.emitter.emit(constant.event.tabbarChanged, { name });
    return true;
  };

  /**
   * 是否打开支付
   */
  @action
  openPay = (openPay) => {
    pageStore.openPay = openPay;
  };
}

export default new Actions();
