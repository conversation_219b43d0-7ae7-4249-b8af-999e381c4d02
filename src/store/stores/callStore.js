import { observable, action, computed } from 'mobx';
import constant from '../constant';
import userStore from './user';
import chatStore from './chatStore';
import I18n from '../../i18n';

class Stores {
  // 公共音视频属性

  @observable channel = '';

  @observable showCall = false; // 显示音视频弹窗

  operating = false; // 是否准备显示音视频弹窗，避免并发

  @observable callType = 0;

  @observable zoomIn = false;

  // 私聊音视频属性
  @observable callState = constant.callState.none;

  @observable peerId = 0;

  @observable isHost = false;

  @observable timeoutCount = 0;

  @observable timeCount = 0;

  @observable user = null;

  @observable openMicrophone = true;

  @observable enableSpeakerphone = true;

  ringerMode = null; // 当前手机的铃声模式

  @observable openCamera = true;

  @observable remoteVideoMute = false;

  // meeting音视频属性

  @observable peersMap = new Map();
  //[10299: {callState: constant.callState.none,name: '111',imId: 10299, avatar: '',timeoutCount: 0,}]

  @observable hostInfo = {}; //发起人信息

  @computed get localInfo() {
    return this.peersMap.get(userStore.imId);
  }

  @computed get otherUsers() {
    return Array.from(this.peersMap.values()).filter((x) => x.imId !== this.hostInfo.imId);
  }

  callGroupId = ''; //音视频群聊id

  tempCallGroupId = ''; //临时音视频群聊id，收到邀请时保存

  @action
  reset = () => {
    this.channel = '';
    this.callType = 0;
    this.showCall = false;
    this.operating = false;
    this.openMicrophone = true;
    this.enableSpeakerphone = true;
    this.zoomIn = false;

    this.callState = constant.callState.none;
    this.peerId = 0;
    this.isHost = false;
    this.timeoutCount = 0;
    this.timeCount = 0;
    this.user = null;

    this.peersMap = new Map();
    this.hostInfo = {};

    this.openCamera = true;

    this.remoteVideoMute = false;
  };
}

const st = new Stores();
st.reset();

/**
 * 发起音视频时防止并发等限制
 * showCall：如果与callStore里的值一样，则操作频繁
 * checkConnect：是否检查socket已连接
 */
export function LockCall({ showCall, checkConnect, actionName = 'callAction' } = {}) {
  return function (target, name, descriptor) {
    // console.debug('Decorator LockCall', target, name, descriptor);
    const original = descriptor.value; // typeof original === 'function'
    const logPrefix = `Decorator LockCall ${actionName}.${name}`;
    descriptor.value = function (...args) {
      console.debug(`${logPrefix} args`, args, showCall);
      try {
        // 正在操作中
        if (st.operating || showCall === st.showCall) {
          console.warn(
            `${logPrefix} 操作频繁 operating:true showCall:${showCall} st.showCall:${st.showCall}`
          );
          return;
        }
        if (checkConnect && !chatStore.isConnected) {
          console.warn(`${logPrefix} socket未连接`);
          toast.show(I18n.t('msg_network_connect_fail'));
          return;
        }
        st.operating = true;

        let res = original.apply(this, args);
        if (res && typeof res.then === 'function') {
          console.debug(`${logPrefix} is promise`);
          return res.then(
            (r) => {
              st.operating = false;
              console.debug(`${logPrefix} then res`, r);
              return r;
            },
            (e) => {
              st.operating = false;
              console.warn(`${logPrefix} then error`, e);
              return Promise.reject(e);
            }
          );
        }
        console.debug(`${logPrefix} res`, res);
        st.operating = false;
        return res;
      } catch (e) {
        st.operating = false;
        console.warn(`${logPrefix} error`, e);
        throw e;
      }
    };
    return descriptor;
  };
}

export default st;
