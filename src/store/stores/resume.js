import { observable, action } from 'mobx';

/**
 * 简历
 */
class Stores {
  // 简历列表
  @observable resumeList = [];
  // 当前简历
  @observable currentIndex = 0;
  @observable currentResume = null;
  @observable currentWorkList = null;
  @observable currentSkillList = null;
  @observable currentLanguageList = null;
  @observable currentQualificationList = null;
  @observable shareToken = null;

  @observable sexList = null;
  @observable languageList = null;
  @observable languageLevelList = null;
  @observable indutrialList = null;
  @observable categoryList = null;
  @observable jobLevelList = null;

  @observable currentEducationLsit = null;
  @observable majorList = null;
  @observable qualificationList = null;
  // 谁看过我
  @observable resumeViewers = [];
  @observable resumeViewersOffset = 0;
  @observable resumeViewersLimit = 8;
  @observable resumeViewersTotalCount = 0;
  @observable hasDefaultResmue = false;
  @observable defaultResmueId = '';

  @observable lastSeen = 0;
  @observable preLastSeen = 0;

  @observable annexResumeList = []; //附件简历列表
  @observable currentAnnexResume = null; // 当前附件简历
  @observable allResumeList = []; // 所有简历列表
  @observable workYearList = []; // 工作年限列表
  @observable maritalStatusList = []; // 婚姻状况列表

  @action
  reset = () => {
    this.resumeList = [];
    this.currentResume = {};
    this.sexList = [];
    this.shareToken = '';
    this.currentWorkList = [];
    this.currentSkillList = [];
    this.currentLanguageList = [];
    this.currentEducationLsit = [];
    this.currentQualificationList = [];
    this.majorList = [];
    this.languageList = [];
    this.languageLevelList = [];
    this.indutrialList = [];
    this.categoryList = [];
    this.jobLevelList = [];
    this.qualificationList = [];
    this.hasDefaultResmue = false;
    this.defaultResmueId = '';

    this.lastSeen = 0;
    this.preLastSeen = 0;

    this.annexResumeList = [];
    this.currentAnnexResume = null;
    this.allResumeList = [];
    this.workYearList = [];
    this.maritalStatusList = [];
  };
}

export default new Stores();
