import { observable, action } from 'mobx';

/**
 * 职位
 */
class Stores {
  @observable.shallow jobList = null;

  @observable page = null;

  @observable size = null;

  @observable total = null;

  @observable.shallow tempJobList = [];

  @observable.shallow companyJobList = [];

  @observable companyDetailPage = null;

  @observable companyDetailCount = null;

  @observable.shallow urgentJobList = [];

  @observable.shallow jobSearchList = null;

  @observable searchTotalPage = null;

  @observable searchTotalCount = null;

  @observable.shallow companySearchList = null;

  @observable companyTotalPage = null;

  @observable companyTotalCount = null;

  @observable locationList2 = null;

  @observable teamScaleList = [];

  @observable indutrialList = [];

  @observable jobLevelList = [];

  @observable qualificationList = [];

  @observable jobStatusList = null;

  @observable jobTypeList = null;

  @observable jobCategoryList = null;

  @observable jobSalaryList = null;

  @observable jobCityList = null;

  @observable intensionList = null;

  @observable.shallow communicatedList = null;

  @observable communicatedTotal = null;

  @observable.shallow deliveredList = null;

  @observable deliveredTotal = null;

  @observable jobDetail = null;

  @observable companyDetail = null;

  @observable hasCommunicated = null;

  @observable totalJobApply = null;

  @observable userApply = null;

  @observable recommendList = [];

  @observable recommendTotalPage = null;

  @observable pinyinIndexArray = [];

  @observable allPlaceTree = [];

  @observable languages = [];

  @observable departmentList = [];

  @observable positionList = [];

  @action
  init = () => {
    this.jobList = [];
    this.page = 1;
    this.size = 20;
    this.total = 0;
    this.tempJobList = [];
    this.companyJobList = [];
    this.companyDetailPage = 1;
    this.companyDetailCount = 0;
    this.urgentJobList = [];
    this.jobSearchList = [];
    this.searchTotalPage = 1;
    this.searchTotalCount = 0;
    this.companySearchList = [];
    this.companyTotalPage = 1;
    this.companyTotalCount = 0;

    this.locationList2 = [];
    this.teamScaleList = [];
    this.indutrialList = [];
    this.jobLevelList = [];
    this.qualificationList = [];
    this.jobStatusList = [];
    this.jobTypeList = [];
    this.jobCategoryList = [];
    this.jobSalaryList = [];
    this.jobCityList = [];
    this.intensionList = [];
    this.communicatedList = [];
    this.communicatedTotal = 0;
    this.deliveredList = [];
    this.deliveredTotal = 0;
    this.jobDetail = {};
    this.companyDetail = {};

    this.hasCommunicated = 0;
    this.totalJobApply = 0;
    this.userApply = 0;
    this.recommendList = [];
    this.recommendTotalPage = 1;
    this.pinyinIndexArray = [];
    // this.allPlaceTree = [];
    this.languages = [];
    this.departmentList = [];
    this.positionList = [];
  };

  @action
  reset = () => {};
}
const st = new Stores();
st.init();
st.reset();

export default st;
