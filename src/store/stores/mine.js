/**
 * 用户
 * author: Rays
 */
import { observable, action } from 'mobx';

class Stores {
  @observable collectNum = 0; // 收藏数量

  @observable commentNum = 0; // 评价数量

  @observable houseNum = 0; // 房源数量

  @observable refreshing = false;

  @action
  reset = () => {
    this.collectNum = 0;
    this.commentNum = 0;
    this.houseNum = 0;
    this.refreshing = false;
  };
}

const st = new Stores();
st.reset();

export default st;
