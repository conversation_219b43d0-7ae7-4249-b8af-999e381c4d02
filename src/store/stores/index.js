import chatroomStore from './chatroom';
import globalStatus from './status';
import dynamicStore from './dynamic';
import jobStore from './job';
import resumeStore from './resume';
import personStore from './person';
import settingsStore from './settings';
import cityStore from './city';
import companyStore from './company';
import messageStore from './message';
import globalStore from './global';
import homeStore from './home';
import mineStore from './mine';
import pageStore from './page';
import userStore from './user';
import callStore from './callStore';
import chatStore from './chatStore';
import applicationStore from './applicationStore';

const stores = {
  globalStatus,
  dynamicStore,
  jobStore,
  resumeStore,
  personStore,
  settingsStore,
  cityStore,
  companyStore,
  messageStore,
  globalStore,
  homeStore,
  mineStore,
  pageStore,
  userStore,
  callStore,
  chatStore,
  chatroomStore,
  applicationStore,
};

export function resetStore() {
  Object.values(stores).forEach((item) => {
    item.reset?.();
  });
}

export default stores;
