import { action, computed, observable } from 'mobx';
import constant from '../constant';

/**
 * 设置
 */
class Stores {
  @observable theme = constant.theme.night;
  @observable hasNewVersion = false;

  @observable language = constant.language['en-US'].code;
  @observable selectedTab = 'Job';

  @computed get imSocketConfig() {
    return null;
    // return this.newVersionInfo?.imSocketConfig;
  }

  @action
  reset() {
    this.theme = constant.theme.night;
    this.hasNewVersion = false;

    this.selectedTab = 'Job';
  }
}
const st = new Stores();
st.reset();

export default st;
