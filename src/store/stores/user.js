import { action, computed, observable } from 'mobx';
import constant from '../constant';
import personStore from './person';
import companyStore from './company';
import util from '../../util';
import enterpriseStyles from '../../themes/enterprise';
import styles from '../../themes';

class Stores {
  @observable.shallow imLoginInfo = {}; // im登录信息

  @observable isLatestToken = false;

  /**
   * 是否企业登录用户
   */
  @computed get isCompany() {
    return !!companyStore.companyInfo;
  }

  /**
   * camhr的用户Id
   */
  @computed get userId() {
    return this.isCompany ? companyStore.companyInfo?.employerId : personStore.me?.seekerId;
  }

  /**
   * 是否已登录
   */
  @computed get isLogin() {
    const b = this.isCompany || personStore.isLogin;
    console.debug('是否已登录', b, personStore.isLogin, companyStore.companyInfo);
    return b;
  }

  /**
   * IM系统的用户Id
   */
  @computed get imId() {
    return this.imLoginInfo?.im_id || 0;
  }

  @computed get isManage() {
    return this.userType === constant.userType.manage;
  }

  @computed get userType() {
    return this.imLoginInfo?.user_type;
  }

  @computed get avatar() {
    const { companyInfo } = companyStore;
    if (companyInfo) {
      return companyInfo.avatar || '';
    }
    return personStore.me?.avatar || '';
  }

  @computed get mobile() {
    const { regionCode, mobile } = companyStore.companyInfo || personStore.me || {};
    return mobile && regionCode && regionCode !== 'nophone' ? '+' + regionCode + '-' + mobile : '';
  }

  // 显示的名称
  @computed get userName() {
    const { companyInfo } = companyStore;
    if (companyInfo) {
      console.log('companyInfo.company', companyInfo.company);
      return companyInfo.contact?.name || companyInfo.company || '';
    }
    return util.getUserDisplayName(personStore.me);
  }

  // IM显示的名称
  @computed get imNickname() {
    return this.userName;
  }

  // 是否可以连接聊天socket了和获取离线聊天消息
  @computed get hasConnectImSocket() {
    return this.isLogin && this.isLatestToken;
  }

  @computed get themeStyle() {
    return this.isCompany ? enterpriseStyles.get('theme') : styles.get('theme');
  }

  @computed get receiveNotify() {
    // const { [this.userId]: receiveNotify = true } = settingsStore.receiveNotifyObj;
    // return !!receiveNotify;
    return true;
  }

  @action
  reset = () => {
    this.imLoginInfo = null;
    this.isLatestToken = false;
  };
}

const st = new Stores();
st.reset();

export default st;
