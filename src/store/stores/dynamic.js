import { observable, action } from 'mobx';

/**
 * 动态
 */
class Stores {
  @observable.shallow dynamicList = []
  @observable dynamicTotalCount = 0
  @observable dynamicTotalPage = 1

  @observable.shallow commentList = []
  @observable commentTotalCount = 0
  @observable commentTotalPage = 1
  @observable blockedUser = null

  @observable lastDynamic = 0
  @observable preLastDynamic = 0

  @action
  reset = () => {
    this.dynamicList = [];
    this.dynamicTotalCount = 0;
    this.dynamicTotalPage = 1;

    this.commentList = [];
    this.commentTotalCount = 0;
    this.commentTotalPage = 1;

    this.blockedUser = {};

    this.lastDynamic = 0;
    this.preLastDynamic = 0;
  }
}
const st = new Stores();
st.reset();

export default st;
