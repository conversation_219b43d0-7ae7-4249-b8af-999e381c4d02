import { observable, action, computed } from 'mobx';

class Stores {
  @observable showLoading = false;
  @observable isEnterprise = false;
  @observable remindCompleteIntension = false;

  @action
  showLoadingModal(show) {
    this.showLoading = show;
  }

  @action
  reset = () => {
    this.showLoading = false;
    this.isEnterprise = false;
    this.remindCompleteIntension = false;
  };
}

const st = new Stores();
st.reset();

export default st;
