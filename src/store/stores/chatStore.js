import { action, observable, computed } from 'mobx';
import constant from '../constant';
import userStore from './user';
import settingsStore from './settings';
import I18n from '../../i18n';

class ChatStore {
  @observable imSocketState = constant.imSocketState.init; // 公告未读数量

  @observable.shallow bookList = [];

  uploadAuth = null;

  currentSessionInfo = null; // 当前聊天会话信息

  get currentSession() {
    return this.currentSessionInfo?.session;
  }

  @computed get isConnecting() {
    const { imSocketState } = this;
    return (
      imSocketState !== constant.imSocketState.connected &&
      imSocketState !== constant.imSocketState.close &&
      imSocketState !== constant.imSocketState.error
    );
  }

  @computed get connectStateLabel() {
    // 下面日志不要注释去掉，不然切换语言，不会生效
    console.debug('connectStateLabel language', settingsStore.language);
    let state;
    switch (this.imSocketState) {
      case constant.imSocketState.connected:
      case constant.imSocketState.opened:
        state = '';
        break;
      case constant.imSocketState.close:
      case constant.imSocketState.error:
      case constant.imSocketState.init:
        state = I18n.t('page_chat_status_offline');
        break;
      default:
        state = I18n.t('page_chat_status_connecting');
        break;
    }
    return state;
  }

  /**
   * socket连接成功
   * @returns {boolean}
   */
  @computed get isConnected() {
    return this.imSocketState === constant.imSocketState.connected;
  }

  /**
   * 聊天列表中当前用户信息
   * @return {{name: string, _id: number, avatar: string}}
   */
  @computed get user() {
    const { imId, userName, avatar } = userStore;
    return {
      _id: imId,
      name: userName,
      avatar,
    };
  }

  @action
  changeImSocketState = (state) => {
    this.imSocketState = state;
  };

  @action
  reset = () => {
    this.bookList = [];
    this.currentSessionInfo = null;
  };
}

const st = new ChatStore();
st.reset();

export default st;
