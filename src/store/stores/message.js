import { observable, action } from 'mobx';

/**
 * 消息
 */
class Stores {
  @observable.shallow list = [];
  @observable offset = 0;
  @observable limit = 8;
  @observable totalCount = 0;
  @observable unread = 0;
  @observable newPushMessageCount = 0;
  @observable totalUnreadCount = 0;

  @action
  reset = () => {
    this.unread = 0;
    this.list = [];
    this.offset = 0;
    this.limit = 8;
    this.totalCount = 0;
    this.newPushMessageCount = 0;
    this.totalUnreadCount = 0;
  };
}

const st = new Stores();
st.reset();

export default st;
