import { observable, action } from 'mobx';

/**
 * 个人
 */
class Stores {
  @observable me = null;

  @observable.shallow followedCompanyList = null;

  @observable companyTotalCount = null;

  @observable.shallow followedJobList = null;

  @observable jobTotalCount = null;

  @observable bannerList = [];

  @observable reporterList = [];

  @observable isLogin = false;

  @observable hasNewTwitterMsg = false;

  @observable newTwitterMsgData = null;

  @observable likedList = [];

  @observable twitterMsgList = [];

  @observable totalCount = 0;

  @observable twitterDetail = null;

  @action
  reset = () => {
    this.me = {};
    this.followedCompanyList = [];
    this.followedJobList = [];
    this.companyTotalCount = 0;
    this.jobTotalCount = 0;
    this.bannerList = [];
    this.reporterList = [];
    this.isLogin = false;
    this.hasNewTwitterMsg = false;
    this.newTwitterMsgData = null;
    this.likedList = [];
    this.twitterMsgList = [];
    this.totalCount = 0;
    this.twitterDetail = null;
  };
}

const st = new Stores();
st.reset();

export default st;
