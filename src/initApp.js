import AsyncStorage from '@react-native-async-storage/async-storage';
import Storage from 'react-native-storage';
import mitt from 'mitt';
import stores from './store';
import Session from './api/session';
import { initLanguage } from './i18n';
import { Settings } from 'react-native-fbsdk-next';
import StorageService from './common/storage';
import AppUtil from './util/appUtil';
import AgoraSdk from './api/agoraSdk';

global.showLoading = (show) => {
  stores.globalStore.showLoadingModal(show);
};

export function initStorage() {
  // 配置本地存储
  const storage = new Storage({
    // 最大容量，默认值1000条数据循环存储
    size: 1000,
    // 存储引擎：对于RN使用AsyncStorage，对于web使用window.localStorage
    // 如果不指定则数据只会保存在内存中，重启后即丢失
    storageBackend: AsyncStorage,
    // 数据过期时间，默认一整天（1000 * 3600 * 24 毫秒），设为null则永不过期
    defaultExpires: null,
    // 读写时在内存中缓存数据。默认启用。
    enableCache: true,
    // 如果storage中没有相应数据，或数据已过期，
    // 则会调用相应的sync方法，无缝返回最新数据。
    // sync方法的具体说明会在后文提到
    // 你可以在构造函数这里就写好sync的方法
    // 或是在任何时候，直接对storage.sync进行赋值修改
    // 或是写到另一个文件里，这里require引入
    // sync: require('你可以另外写一个文件专门处理sync')
  });
  // 在全局范围内创建一个（且只有一个）storage实例，方便直接调用
  global.storage = storage;
}

export function initNim() {
  // try {
  //   Session.isLogin().then((isLogin) => {
  //     if (isLogin) {
  //       if (stores && stores.loginAction) {
  //         stores.loginAction.autoLoginIM();
  //       }
  //     }
  //   });
  // } catch (e) {
  //   console.log('ignore init nim err', e);
  // }
}

export function initGlobalData() {
  global.emitter = mitt();
  Settings.initializeSDK();
}

export async function initConstantsEnum() {
  await stores.applicationAction.initConstants();
  stores.resumeAction.queryConstants();
  stores.jobAction.queryConstants();
  stores.userAction.queryConstants();
}

export function initDefaultLanguage() {
  initLanguage().then((res) => {
    // 初始化应用常量
    initConstantsEnum();
    global.emitter.emit('reloadData', true);
  });
}

export function initLastDynamic() {
  Promise.all([Session.getLastDynamic(), Session.getPreLastDynamic()]).then((res) => {
    if (stores.dynamicAction) {
      stores.dynamicAction.initLastDynamic(res[0], res[1]);
    }
  });
}

export function initLastSeen() {
  Session.isLogin().then((isLogin) => {
    if (isLogin) {
      Promise.all([Session.getLastSeen(), Session.getPreLastSeen()]).then((res) => {
        if (stores.resumeAction) {
          stores.resumeAction.initLastSeen(res[0], res[1]);
        }
      });
    }
  });
}

/**
 * 初始化计划任务、定时器
 */
export async function initSchedule() {
  try {
    setInterval(() => {
      Session.isLogin().then((isLogin) => {
        if (isLogin && !stores.globalStore.isEnterprise) {
          if (stores && stores.userAction) {
            stores.userAction.statsMessage();
            stores.userAction.getUnreadTwitterMessages();
          }
          if (stores && stores.resumeAction) {
            stores.resumeAction.hasNewSeen();
          }
        }
      });
    }, 30 * 1000);
  } catch (e) {
    console.error('定时器运行错误：', e);
  }
}

/**
 * 初始化主题
 * 注意：必须先初始化本地存储
 */
export function initTheme() {
  StorageService.getTheme().then((theme) => {
    if (theme) {
      stores.settingsAction.setTheme(theme);
    }
  });
}

export async function initIsEnterprise() {
  const isEnterprise = await StorageService.getEnterprise();
  await stores.globalAction.setEnterprise(isEnterprise);
}

export async function initLoginStatus() {
  return Promise.all([Session.getPersonInfo()]).then(([personInfo]) => {
    console.log('personInfo', personInfo);
    runInAction(() => {
      stores.personStore.me = personInfo;
    });
  });
}

export async function initApplication() {
  AppUtil.setPrintLog();
  initStorage();
  initGlobalData();
  initTheme();
  await initIsEnterprise();
  initLoginStatus();
  initDefaultLanguage();
  initLastDynamic();
  initLastSeen();
  initSchedule();
  await AgoraSdk.init().catch((e) => console.warn('Agora sdk init error', e));
}
