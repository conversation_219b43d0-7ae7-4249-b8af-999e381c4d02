import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Swiper from 'react-native-swiper';
import { View, Text, TouchableOpacity } from 'react-native';
import { Icon, Badge } from 'react-native-elements';
import { titleColor, desColor, baseBlueColor, jobStyle } from '../themes';
import I18n from '../i18n';
import Image from '../components/image';

@inject('resumeAction', 'userAction', 'personStore', 'jobAction', 'resumeStore')
@observer
export default class MySwiper extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    global.emitter.on('languageChange', this.onUpdate);
    this.props.userAction.getBanners();
  }

  componentWillUnmount() {
    global.emitter.off('languageChange', this.onUpdate);
  }

  onUpdate = () => {
    this.forceUpdate();
  };

  onIntensionBadgeClick = (item) => {
    this.props.onIntensionBadgeClick(item);
  };

  onSelectIntension = () => {
    this.props.onSelectIntension();
  };

  gotoDetail = async (item) => {
    if (item.redirectObjectIdType === null) {
      if (item.redirectUrl) {
        await this.props.navigation.navigate('bannerWebview', {
          url: item.redirectUrl,
          title: item.title,
        });
      }
    }
    if (item.redirectObjectIdType && item.redirectObjectIdType.value === 0) {
      // 企业主页
      if (item.redirectObjectId) {
        this.props.navigation.navigate('companyDetail', { employerId: item.redirectObjectId });
      }
    } else if (item.redirectObjectIdType && item.redirectObjectIdType.value === 1) {
      // 站内信消息
      if (item.redirectObjectId) {
        const data = await this.props.userAction.getPublicMessageDetail(
          parseFloat(item.redirectObjectId)
        );
        this.props.navigation.navigate('messageDetails', { details: data });
      }
    }
  };

  renderSwiperItem = () =>
    this.props.personStore.bannerList.map((item, i) => (
      <TouchableOpacity
        key={`${i + 1}`}
        onPress={() => {
          this.gotoDetail(item);
        }}
      >
        <View style={jobStyle.swiperimage}>
          <Image style={jobStyle.swiperimage} source={{ uri: item.slodeshowUrl }} />
        </View>
      </TouchableOpacity>
    ));

  render() {
    const { hideSwiper } = this.props;
    const hasIntension =
      this.props.resumeStore.resumeList?.filter((item) => item?.intention?.reqJobTitle)?.length > 0;
    const intensionList = this.props.resumeStore.resumeList || [];
    const swiperItems = this.props.personStore.bannerList.map((item, i) => (
      <TouchableOpacity
        key={`${i + 1}`}
        onPress={() => {
          this.gotoDetail(item);
        }}
      >
        <View style={jobStyle.swiperimage}>
          <Image style={jobStyle.swiperimage} source={{ uri: item.slodeshowUrl }} />
        </View>
      </TouchableOpacity>
    ));
    return (
      <View>
        {!hideSwiper ? (
          <View style={jobStyle.swiperimage}>
            <Swiper
              key={this.props.personStore.bannerList.length}
              autoplay
              autoplayTimeout={2}
              horizontal
              showsPagination
              paginationStyle={{ position: 'absolute', bottom: 2 }}
              index={0}
              dotStyle={{ backgroundColor: '#fff', width: 8, height: 8 }}
              activeDotStyle={{ backgroundColor: baseBlueColor, width: 8, height: 8 }}
            >
              {swiperItems}
            </Swiper>
          </View>
        ) : null}
        <View style={jobStyle.jobHunting}>
          {hasIntension ? (
            <View style={jobStyle.intensionContainer}>
              {intensionList &&
                intensionList.length > 0 &&
                intensionList.map((item, i) =>
                  item.reqJobTitle ? (
                    <Badge
                      key={`${i + 1}`}
                      value={(item.intention && item.intention.reqJobTitle) || ''}
                      containerStyle={jobStyle.intensionBadgeContainerStyle}
                      badgeStyle={jobStyle.intensionBadgeStyle}
                      onPress={() => this.onIntensionBadgeClick(item)}
                      textStyle={{
                        fontSize: item.isSelected ? 14 : 12,
                        color: item.isSelected ? titleColor : desColor,
                        fontWeight: item.isSelected ? 'bold' : 'normal',
                      }}
                    />
                  ) : (
                    <Text key={`${i + 1}`} style={{ display: 'none' }} />
                  )
                )}
            </View>
          ) : (
            <Text
              style={{
                fontSize: 14,
                color: titleColor,
                flexGrow: 20,
                flexShrink: 200,
              }}
            >
              {I18n.t('page_job_link_intension_setting')}
            </Text>
          )}
          <View style={{ flexGrow: 1 }}>
            <Icon
              onPress={() => {
                this.onSelectIntension();
              }}
              type="simple-line-icon"
              name="plus"
              size={22}
              color={baseBlueColor}
            />
          </View>
        </View>
      </View>
    );
  }
}
