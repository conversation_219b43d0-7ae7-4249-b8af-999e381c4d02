import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, TouchableOpacity, FlatList, InteractionManager } from 'react-native';
import { Header } from 'react-native-elements';
import { headerStyle, communicatedListStyle, desColor } from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';
import res, { getEmployerAvatarSource } from '../res';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('jobStore', 'jobAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.state = {
      showLoading: true,
      page: 1,
      hasMore: false,
      refreshing: false,
    };
  }

  async componentDidMount() {
    await InteractionManager.runAfterInteractions(() => {
      this.getJobs();
    });
  }

  onToJobDetail = (item) => {
    this.props.navigation.navigate('jobDetail', { detail: item });
  };

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.state.page = 1;
    await this.getJobs();
    this.setState({ refreshing: false });
  };

  onLoadMore = async () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.state.page += 1;
      this.isLoadingMore = true;
      await this.getJobs();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 0);
    }
  };

  getJobs = async () => {
    await this.props.jobAction
      .queryCommunicatedJobs({
        page: this.state.page,
        size: 10,
        hasCommunicated: true,
      })
      .catch(() => {
        this.setState({ showLoading: false, refreshing: false });
      });
    const { communicatedList, communicatedTotal } = this.props.jobStore;
    this.setState({ hasMore: communicatedList.length < parseFloat(communicatedTotal) });
    this.setState({ showLoading: false });
  };

  renderJobList = ({ item }) => {
    const isShow =
      item.employer &&
      ((item.employer.industrialId && item.employer.industrialId.label) ||
        (item.employer.scaleId && item.employer.scaleId.label));
    return (
      <TouchableOpacity key={item.id} onPress={() => this.onToJobDetail(item)}>
        <View style={communicatedListStyle.listContainer}>
          <View style={communicatedListStyle.companyPannel}>
            <View
              style={[communicatedListStyle.companyNameCont, { flexShrink: 100, paddingRight: 10 }]}
            >
              <Text numberOfLines={1} style={[communicatedListStyle.jobName, { flexShrink: 100 }]}>
                {item.title}
              </Text>
              {item.workyears || (item.qualificationId && item.qualificationId.label) ? (
                <Text numberOfLines={1} style={{ marginLeft: 10, color: desColor, flexShrink: 1 }}>
                  {item.workyears ? `${item.workyears}${I18n.t('page_job_text_year')}` : ''}
                  {item.workyears && item.qualificationId && item.qualificationId.label
                    ? ' | '
                    : ''}
                  {item.qualificationId ? item.qualificationId.label : ''}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
            <Text style={[communicatedListStyle.jobWages, { flexShrink: 1 }]}>
              {item.salaryId ? item.salaryId.label : ''}
            </Text>
          </View>
          {item.locations && item.locations.length > 0 ? (
            <View style={communicatedListStyle.locationsStyle}>
              {item.locations.map((l) => l.locationId.label).length > 0 ? (
                <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                  {item.locations.map((l) => l.locationId.label).join(' | ')}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )}
          <View style={[communicatedListStyle.companyPannel, { marginTop: 8 }]}>
            <ImageBackground
              imageStyle={{ borderRadius: 21 }}
              style={communicatedListStyle.companyLogo}
              source={getEmployerAvatarSource(item.employer ? item.employer.logo : '')}
              resizeMode="contain"
            >
              {item &&
              item.employer &&
              item.employer.employerQualificationType &&
              item.employer.employerQualificationType.value === 1 ? (
                <Image style={communicatedListStyle.v2} source={res.verify} />
              ) : (
                <View />
              )}
            </ImageBackground>
            <View style={communicatedListStyle.companyDetail}>
              <Text
                numberOfLines={1}
                style={communicatedListStyle.companyName}
                ellipsizeMode="middle"
              >
                {item.employer ? item.employer.company : ''}{' '}
                {item &&
                item.employer &&
                item.employer.qualificationStatus &&
                item.employer.qualificationStatus.value === 1 ? (
                  <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
                ) : null}
              </Text>
              {isShow ? (
                <Text style={communicatedListStyle.companySubcribe}>
                  {item.employer.industrialId ? item.employer.industrialId.label : ''}
                  {item.employer.scaleId &&
                  item.employer.industrialId &&
                  item.employer.industrialId.label &&
                  item.employer.scaleId.label
                    ? ' | '
                    : ''}
                  {item.employer && item.employer.scaleId
                    ? `${item.employer.scaleId.label}${I18n.t('page_job_text_person')}`
                    : ''}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  render() {
    const { navigation } = this.props;
    const { communicatedList } = this.props.jobStore;
    return (
      <View style={communicatedListStyle.communicatedContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_mine_nav_communicated'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <FlatList
          data={communicatedList && communicatedList.length > 0 ? communicatedList.slice() : []}
          ListEmptyComponent={this.renderEmptyComponent}
          renderItem={this.renderJobList}
          keyExtractor={(item, index) => index + item}
          showsVerticalScrollIndicator={false}
          horizontal={false}
          ListFooterComponent={this.renderFooter}
          refreshing={this.state.refreshing}
          onRefresh={this.onRefresh}
          onEndReachedThreshold={0.1}
          onEndReached={() => this.onLoadMore()}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
