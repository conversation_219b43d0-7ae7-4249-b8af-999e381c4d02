import React, { Component } from 'react';
import { View, ScrollView, Keyboard } from 'react-native';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import ActionSheet from 'react-native-actionsheet';
import { Header, Icon, ListItem, Button, Input } from 'react-native-elements';
import { headerStyle, desColor, jobIntensionStyle, titleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('jobStore', 'jobAction', 'resumeAction')
@observer
export default class JobIntensionAdd extends Component {
  constructor(props) {
    super(props);
    const intension = this.props.navigation.state.params.data;
    this.keyboardDidShowListener = null;
    this.keyboardDidHideListener = null;
    this.state = {
      jobType: intension ? intension.jobTerm : null, // 工作性质
      salary: intension ? intension.salary : null, // 薪资
      categoryIds: intension ? intension.categoryIds : [], // 职业
      industryIds: intension ? intension.industryIds : [], // 行业
      locationIds: intension ? intension.locationIds : [], // 位置
      reqJobTitle: intension ? intension.reqJobTitle : '', // 期待职位
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this.keyboardDidShowHandler.bind(this)
    );
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      this.keyboardDidHideHandler.bind(this)
    );
  }

  componentWillUnmount() {
    if (this.keyboardDidShowListener != null) {
      this.keyboardDidShowListener.remove();
    }
    if (this.keyboardDidHideListener != null) {
      this.keyboardDidHideListener.remove();
    }
  }

  onSelectIndustry = () => {
    const { navigation } = this.props;
    navigation.navigate('jobIndustry', {
      industrys: this.state.industryIds,
      onSelect: (item) => {
        this.setState({
          industryIds: item,
        });
      },
    });
  };

  onSelectProfession = () => {
    const { navigation } = this.props;
    navigation.navigate('jobExpected', {
      expects: this.state.categoryIds,
      onSelect: (item) => {
        this.setState({
          categoryIds: item,
        });
      },
    });
  };

  onSelectCity = () => {
    const { navigation } = this.props;
    navigation.navigate('jobCityList', {
      citys: this.state.locationIds,
      onSelect: (item) => {
        this.setState({
          locationIds: item,
        });
      },
    });
  };

  onSelectSalary = () => {
    const { navigation } = this.props;
    navigation.navigate('jobSalary', {
      onSelect: (item) => {
        this.setState({
          salary: item,
        });
      },
    });
  };

  onChangeText = (text) => {
    this.setState({ reqJobTitle: text });
  };

  keyboardDidShowHandler() {}

  keyboardDidHideHandler() {}

  dissmissKeyboard() {
    Keyboard.dismiss();
  }

  typeSheetSelect = (index) => {
    const {
      jobStore: { jobTypeList },
    } = this.props;
    if (index !== jobTypeList.length) {
      const param = jobTypeList[index];
      this.setState({ jobType: param });
    }
  };

  showTypeSheet = () => {
    this.typeActionSheet.show();
  };

  formatData(array) {
    const list = array.map((item) => item.label);
    list.push(I18n.t('page_job_actionsheet_cancel'));
    return list;
  }

  saveIntension = async () => {
    const { navigation } = this.props;
    const { salary, categoryIds, industryIds, locationIds, reqJobTitle, jobType } = this.state;
    const intension = this.props.navigation.state.params.data;
    if (!jobType) {
      this.toast.show(I18n.t('page_job_toast_select_work_status'));
      return;
    }
    if (industryIds.length === 0) {
      this.toast.show(I18n.t('page_job_toast_select_expected_industry'));
      return;
    }
    if (categoryIds.length === 0) {
      this.toast.show(I18n.t('page_job_toast_select_expected_profession'));
      return;
    }
    if (!reqJobTitle) {
      this.toast.show(I18n.t('page_job_toast_select_expected_job'));
      return;
    }
    if (locationIds.length === 0) {
      this.toast.show(I18n.t('page_job_toast_select_work_city'));
      return;
    }
    if (!salary) {
      this.toast.show(I18n.t('page_job_toast_select_salary'));
      return;
    }
    const data = {
      jobTerm: jobType ? jobType.value : 0,
      categoryIds: categoryIds.length > 0 ? categoryIds.map((item) => item.value) : [],
      industryIds: industryIds.length > 0 ? industryIds.map((item) => item.value) : [],
      locationIds: locationIds.length > 0 ? locationIds.map((item) => item.value) : [],
      reqJobTitle,
      salary: salary ? salary.value : 0,
      workStatus:
        intension && intension.workStatus && intension.workStatus.value
          ? intension.workStatus.value
          : 1,
    };
    if (intension && intension.resumeId !== 0) {
      data.resumeId = intension.resumeId;
    }
    const res = await this.props.jobAction.updateIntentions(data);
    if (res && res.successful) {
      this.toast.show(
        intension && intension.resumeId !== 0
          ? res.message
          : I18n.t('page_job_toast_add_intension_success')
      );
      setTimeout(() => {
        this.props.resumeAction.getResumes();
      }, 1000);
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
    }
  };

  renderPropsStatus = () => {
    const { salary, categoryIds, industryIds, locationIds, jobType } = this.state;
    const intension = this.props.navigation.state.params.data;
    return (
      <View style={{ backgroundColor: '#fff' }}>
        <ListItem
          onPress={this.showTypeSheet}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_job_term_text')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={jobType ? jobType.label : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
        />
        <ListItem
          onPress={this.onSelectIndustry}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_expected_industy')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={industryIds.length > 0 ? industryIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          onPress={this.onSelectProfession}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_expected_profession')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={categoryIds.length > 0 ? categoryIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={[
            jobIntensionStyle.rightContainerStyle,
            { flexWrap: 'wrap', overflow: 'hidden' },
          ]}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          containerStyle={[jobIntensionStyle.listContainerStyle, { paddingRight: 3 }]}
          title={I18n.t('page_job_expected_job')}
          titleStyle={{
            color: titleColor,
            fontSize: 14,
            width: 120,
          }}
          // input={{
          //   maxLength: 30,
          //   onChangeText: (text) => { this.onChangeText(text); },
          //   placeholder: I18n.t('page_job_toast_select_expected_job'),
          //   placeholderTextColor: desColor,
          //   returnKeyType: 'done',
          //   defaultValue: intension ? intension.reqJobTitle : '',
          //   containerStyle: {
          //     marginRight: -12,
          //   },
          //   inputStyle: {
          //     fontSize: 14,
          //     color: titleColor,
          //     height: 40,
          //     textAlign: 'right',
          //     paddingRight: 0,
          //   },
          // }}
          rightElement={
            <Input
              maxLength={30}
              underlineColorAndroid="transparent"
              inputContainerStyle={{
                backgroundColor: '#fff',
                borderBottomWidth: 0,
              }}
              onChangeText={(text) => {
                this.onChangeText(text);
              }}
              placeholder={I18n.t('page_job_toast_select_expected_job')}
              placeholderTextColor={desColor}
              returnKeyType="done"
              defaultValue={intension ? intension.reqJobTitle : ''}
              containerStyle={{
                width: 240,
                marginRight: 0,
                paddingHorizontal: 0,
                height: 0,
              }}
              inputStyle={{
                fontSize: 14,
                color: titleColor,
                height: 40,
                textAlign: 'right',
                paddingRight: 5,
              }}
              rightIcon={
                <Icon size={15} color={desColor} name="chevron-thin-right" type="entypo" />
              }
            />
          }
          // rightIcon={<Icon size={15} color={desColor} name="chevron-thin-right" type="entypo" iconStyle={{ marginRight: -4 }} />}
        />
        <ListItem
          onPress={this.onSelectCity}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_work_city')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={locationIds.length > 0 ? locationIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          onPress={this.onSelectSalary}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_expected_Salary')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={salary ? salary.label : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
        />
      </View>
    );
  };

  render() {
    const {
      navigation,
      jobStore: { jobTypeList },
    } = this.props;
    return (
      <View style={jobIntensionStyle.con}>
        <Header
          statusBarProps={{
            barStyle: 'light-content',
            backgroundColor: '#2089DC',
          }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_job_nav_top_add_intension'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>{this.renderPropsStatus()}</ScrollView>
        <View style={jobIntensionStyle.submitButton}>
          <Button
            title={I18n.t('page_job_complete')}
            buttonStyle={jobIntensionStyle.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={() => {
              this.saveIntension();
            }}
          />
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <ActionSheet
          ref={(ref) => {
            this.typeActionSheet = ref;
          }}
          title={I18n.t('page_job_job_term_text')}
          options={this.formatData(jobTypeList)}
          cancelButtonIndex={jobTypeList ? jobTypeList.length : 0}
          onPress={this.typeSheetSelect}
        />
      </View>
    );
  }
}
