import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { View, Alert, Text } from 'react-native';
import DeviceInfo from '../util/deviceInfo';
import { inject, observer } from 'mobx-react';
import { Header, Icon, ListItem, Button } from 'react-native-elements';
import { BaseComponent } from '../components';

import { headerStyle, desColor, settingStyle, titleColor, subTitleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import LoadingModal from '../components/loadingModal';
import InputPwdModal from './enterprise/setting/components/inputPwdModal';

@inject(
  'loginAction',
  'personStore',
  'resumeStore',
  'messageStore',
  'jobStore',
  'settingsAction',
  'dynamicStore'
)
@observer
export default class Page extends BaseComponent {
  constructor(props) {
    super(props);
    this.state = {
      showLoading: false,
    };
  }

  onExitLogin = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_setting_sure_to_logout'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.loginAction.logout();
        },
      },
    ]);
  };

  onToAbout = () => {
    this.props.navigation.navigate('about');
  };

  onSetPassword = () => {
    const {
      personStore: { me },
    } = this.props;
    if (me && me.mobile) {
      this.props.navigation.navigate('setPassword');
    } else {
      this.toast.show(I18n.t('page_setting_text_bind_phone'));
      setTimeout(() => {
        this.props.navigation.navigate('modifyPhone');
      }, 2000);
    }
  };

  onUnregister = async () => {
    Alert.alert(
      I18n.t('page_settings_unregister_alt_title'),
      I18n.t('page_settings_unregister_alt_content'),
      [
        {
          text: I18n.t('page_setting_cancel_text'),
          onPress: () => {},
        },
        {
          text: I18n.t('page_setting_confirm_text'),
          onPress: () => {
            this.pwdInputModal.show();
          },
        },
      ]
    );
  };

  onConfirm = async (pwd) => {
    try {
      this.showGlobalLoading();
      const { loginAction } = this.props;
      const res = await loginAction.unregisterForPersonal({
        captcha: '000000',
        password: pwd,
      });
      if (res?.data?.success) {
        this.showRequestResult(I18n.t('page_settings_unregister_op_success'));
        this.props.loginAction.logout();
      } else {
        this.showRequestResult(res?.message);
      }
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  renderExitLogin = () => (
    <View style={settingStyle.exitLoingButtonContainer}>
      <Button
        title={I18n.t('page_setting_exit_login')}
        titleColor={titleColor}
        buttonStyle={settingStyle.buttonStyle}
        titleStyle={{ fontSize: 16, color: subTitleColor }}
        onPress={() => {
          this.onExitLogin();
        }}
      />
    </View>
  );

  render() {
    const {
      navigation,
      personStore: { me },
    } = this.props;
    const nowVersion = DeviceInfo.getVersion();
    return (
      <View style={settingStyle.settingContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_mine_switch_setting'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ListItem
          onPress={() => {
            navigation.navigate('modifyPhone');
          }}
          containerStyle={{ borderBottomColor: '#eee', borderBottomWidth: 0.5, paddingRight: 5 }}
          title={
            me && me.mobile
              ? I18n.t('page_setting_text_modify_phone')
              : I18n.t('page_guide_bind_phone')
          }
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={me && me.mobile ? `${me.mobile.substr(0, 3)}****${me.mobile.substr(7)}` : ''}
          rightTitleStyle={{ fontSize: 14, color: subTitleColor, marginRight: -10 }}
          rightIcon={
            <Icon
              name="chevron-thin-right"
              type="entypo"
              size={16}
              iconStyle={{ marginRight: 6 }}
              color={desColor}
            />
          }
        />
        <ListItem
          onPress={() => {
            navigation.navigate('modifyPassword');
          }}
          containerStyle={{ borderBottomColor: '#eee', borderBottomWidth: 0.5, paddingRight: 5 }}
          title={I18n.t('page_setting_text_modify_password')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightIcon={
            <Icon
              name="chevron-thin-right"
              type="entypo"
              size={16}
              iconStyle={{ marginRight: 6 }}
              color={desColor}
            />
          }
        />
        <ListItem
          onPress={this.onSetPassword}
          containerStyle={{ borderBottomColor: '#eee', borderBottomWidth: 0.5, paddingRight: 5 }}
          title={I18n.t('page_setting_text_set_password')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightIcon={
            <Icon
              name="chevron-thin-right"
              type="entypo"
              size={16}
              iconStyle={{ marginRight: 6 }}
              color={desColor}
            />
          }
        />
        {me?.name == 'lyta2017' ? (
          <ListItem
            onPress={this.onUnregister}
            containerStyle={{ borderBottomColor: '#eee', borderBottomWidth: 0.5, paddingRight: 5 }}
            title={I18n.t('page_settings_unregister')}
            titleStyle={{ color: titleColor, fontSize: 14 }}
            rightIcon={
              <Icon
                name="chevron-thin-right"
                type="entypo"
                size={16}
                iconStyle={{ marginRight: 6 }}
                color={desColor}
              />
            }
          />
        ) : null}
        <View style={{ marginTop: 10 }}>
          <ListItem
            onPress={() => this.onToAbout()}
            containerStyle={{ borderBottomColor: '#eee', borderBottomWidth: 0.5, paddingRight: 5 }}
            contentContainerStyle={{}}
            title={I18n.t('page_setting_text_about')}
            titleStyle={{ color: titleColor, fontSize: 14 }}
            rightIcon={
              <Icon
                name="chevron-thin-right"
                type="entypo"
                size={16}
                iconStyle={{ marginRight: 6 }}
                color={desColor}
              />
            }
          />
        </View>
        {this.renderExitLogin()}
        <Text
          style={{
            position: 'absolute',
            bottom: 24,
            textAlign: 'center',
            fontSize: 14,
            color: titleColor,
            width: '100%',
          }}
        >
          {`${'Camhr v'}${nowVersion}`}
        </Text>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
        <InputPwdModal
          ref={(ref) => (this.pwdInputModal = ref)}
          onConfirm={this.onConfirm}
          btnType="personalUnregister"
        />
      </View>
    );
  }
}
