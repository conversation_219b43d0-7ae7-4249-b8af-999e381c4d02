import React, { Component } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import { inject, observer } from 'mobx-react';
import { Header } from 'react-native-elements';
import { headerStyle, messageStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import config from '../configs';

@inject('userAction')
@observer
export default class MessageDetails extends Component {
  componentDidMount() {
    this.props.userAction.statsMessage();
    this.readMessage();
  }

  readMessage = () => {
    const {
      params: { details },
    } = this.props.navigation.state;
    if (
      details &&
      details.receiver &&
      details.receiver.receiveStatus &&
      details.receiver.receiveStatus.value === 1
    ) {
      return;
    }
    this.props.userAction.markMessageRead(details.messageId);
  };

  render() {
    const { navigation } = this.props;
    const {
      params: { details },
    } = this.props.navigation.state;
    const url = `${config.cmsNotifyURl.replace(
      '${id}',
      details.messageId
    )}?title=${encodeURIComponent(details.subject)}&time=${encodeURIComponent(
      details.createAt
    )}&content=${encodeURIComponent(details.content)}`;
    return (
      <View style={messageStyle.detailsContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_messageDetails_text_header_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <WebView source={{ uri: url }} startInLoadingState domStorageEnabled javaScriptEnabled />
      </View>
    );
  }
}
