import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { <PERSON><PERSON>, <PERSON><PERSON>, Icon } from 'react-native-elements';
import Swipeout from 'react-native-swipeout';
import Toast from 'react-native-easy-toast';
import {
  globalStyle,
  headerStyle,
  jobIntensionStyle,
  dynamicStyle,
  resumeEditStyle,
  desColor,
  bgColor,
} from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import Image from '../components/image';

@inject('resumeStore', 'resumeAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      edit: false,
    };
  }

  editPress = () => {
    const { currentQualificationList } = this.props.resumeStore;
    if (currentQualificationList && currentQualificationList.length === 0 && !this.state.edit) {
      return;
    }
    if (this.state.edit) {
      this.props.resumeStore.currentQualificationList = this.props.resumeStore.currentQualificationList.map(
        (item1, index) => {
          item1.showDel = false;
          return item1;
        }
      );
    }
    this.setState({
      edit: !this.state.edit,
    });
  };

  iconDelPress(item) {
    this.props.resumeStore.currentQualificationList = this.props.resumeStore.currentQualificationList.map(
      (item1, index) => {
        item1.showDel = item1.id === item.id;
        return item1;
      }
    );
  }

  deletePress(item) {
    const { currentResume } = this.props.resumeStore;
    this.props.resumeAction.deleteQualification(item.id, currentResume.resumeId).then((res) => {
      if (res && res.successful) {
        this.toast.show(res.message);
        this.props.resumeAction.getResumes();
      } else {
        this.toast.show(res.message);
      }
    });
  }

  renderItem = ({ item, i }) => {
    console.log('item', item);
    const swipeoutBtns = [
      {
        text: I18n.t('page_resume_btn_del'),
        type: 'delete',
        onPress: () => {
          this.deletePress(item);
        },
      },
    ];
    return (
      <Swipeout
        right={swipeoutBtns}
        openRight={item.showDel}
        close={!item.showDel}
        autoClose
        key={i}
      >
        <TouchableOpacity
          onPress={() =>
            this.props.navigation.navigate('resumeCertificateAdd', { data: item, edit: true })
          }
        >
          <View style={resumeEditStyle.container}>
            <View>
              {this.state.edit ? (
                <Icon
                  name="circle-with-minus"
                  type="entypo"
                  size={25}
                  iconStyle={resumeEditStyle.icon}
                  onPress={() => {
                    this.iconDelPress(item);
                  }}
                />
              ) : null}
            </View>
            <View
              style={[
                resumeEditStyle.container,
                { justifyContent: 'space-between', flex: 1, marginRight: 5 },
              ]}
            >
              <Image
                source={{ uri: item.photo }}
                style={{
                  marginTop: 15,
                  marginBottom: 15,
                  marginLeft: 15,
                  width: 70,
                  height: 70,
                  backgroundColor: bgColor,
                }}
              />
              <View style={{ flexGrow: 20, flexShrink: 200 }}>
                <Text style={resumeEditStyle.timeText} numberOfLines={1}>
                  {item.obtained ? item.obtained.toString() : ''}
                </Text>
                <Text style={resumeEditStyle.titleText} numberOfLines={1}>
                  {item.name ? item.name : ''}
                </Text>
                <Text style={resumeEditStyle.desText} numberOfLines={1}>
                  {item.issued ? item.issued : ''}
                </Text>
              </View>
              <View style={{ flexGrow: 1, flexShrink: 1 }}>
                <Icon
                  name="chevron-thin-right"
                  type="entypo"
                  size={16}
                  color={desColor}
                  iconStyle={{ color: desColor, marginRight: 6 }}
                />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Swipeout>
    );
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_qualification'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.editPress}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {this.state.edit ? I18n.t('page_resume_btn_done') : I18n.t('page_resume_btn_edit')}
              </Text>
            </TouchableOpacity>
          }
        />
        <FlatList
          data={this.props.resumeStore.currentQualificationList}
          renderItem={this.renderItem}
          keyExtractor={(item) => item.id}
          ItemSeparatorComponent={() => <View style={dynamicStyle.separatorLine} />}
        />
        <View style={jobIntensionStyle.submitButton}>
          <Button
            title={I18n.t('page_resume_btn_qualification_add')}
            buttonStyle={jobIntensionStyle.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={() => {
              navigation.navigate('resumeCertificateAdd', { data: {}, edit: false });
            }}
          />
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
