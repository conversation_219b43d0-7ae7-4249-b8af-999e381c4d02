import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import Swipeout from 'react-native-swipeout';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { Text, View, TouchableOpacity, InteractionManager, FlatList } from 'react-native';
import { Header, Badge } from 'react-native-elements';
import {
  headerStyle,
  favoriteStyle,
  desColor,
  baseBlueColor,
  jobSearchStyle,
  subTitleColor,
} from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';
import resource, { getEmployerAvatarSource, getAvatarSource } from '../res';
import LoadingModal from '../components/loadingModal';
import CommonCustomTabBar from '../components/commonCustomTabBar';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('personStore', 'userAction', 'jobAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.state = {
      showLoading: true,
      page: 1,
      hasMoreJobs: false,
      hasMoreCompanys: false,
      refreshing: false,
      currentTab: 0,
    };
  }

  async componentDidMount() {
    await InteractionManager.runAfterInteractions(() => {
      this.getFollowedCompanys();
    });
  }

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.state.page = 1;
    if (this.state.currentTab === 0) {
      await this.getFollowedCompanys();
    } else {
      await this.getFollowedJobs();
    }
    this.setState({ refreshing: false });
  };

  onLoadMore = async () => {
    if (this.state.currentTab === 0) {
      if (this.state.hasMoreCompanys && !this.isLoadingMore) {
        this.state.page += 1;
        this.isLoadingMore = true;
        await this.getFollowedCompanys();
        setTimeout(() => {
          this.isLoadingMore = false;
        }, 1000);
      }
      return;
    }
    if (this.state.hasMoreJobs && !this.isLoadingMore) {
      this.state.page += 1;
      this.isLoadingMore = true;
      await this.getFollowedJobs();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  onChangeTab = async (obj) => {
    this.setState({ currentTab: obj.i });
    this.state.page = 1;
    this.isLoadingMore = false;
    if (obj.i === 0) {
      await this.getFollowedCompanys();
    } else {
      await this.getFollowedJobs();
    }
  };

  onToJobDetail = async (item) => {
    const data = await this.props.jobAction.querySingleJobs(item.jobId);
    this.props.navigation.navigate('jobDetail', {
      detail: data,
      getFollowed: () => {
        this.state.page = 1;
        this.getFollowedJobs();
      },
    });
  };

  onToCompanyDetail = (item) => {
    this.props.navigation.navigate('companyDetail', {
      employerId: item.employerId,
      getFollowed: () => {
        this.state.page = 1;
        this.getFollowedCompanys();
      },
    });
  };

  getFollowedJobs = async () => {
    await this.props.userAction.queryFollowedJobs({ page: this.state.page, size: 10 }).catch(() => {
      this.setState({ showLoading: false, refreshing: false });
    });
    const { followedJobList, jobTotalCount } = this.props.personStore;
    this.setState({ hasMoreJobs: followedJobList.length < parseFloat(jobTotalCount) });
    this.setState({ showLoading: false });
  };

  getFollowedCompanys = async () => {
    await this.props.userAction
      .queryFollowedCompanys({ page: this.state.page, size: 10 })
      .catch(() => {
        this.setState({ showLoading: false, refreshing: false });
      });
    const { followedCompanyList, companyTotalCount } = this.props.personStore;
    this.setState({ hasMoreCompanys: followedCompanyList.length < parseFloat(companyTotalCount) });
    this.setState({ showLoading: false });
  };

  deleteFollowedJob = async (item) => {
    const result = await this.props.userAction.deleteFollowedJob({ jobIds: [item.jobId] });
    this.toast.show(result && result.message);
    if (result && result.successful) {
      await this.getFollowedJobs();
    }
  };

  deleteFollowedCompany = async (item) => {
    const result = await this.props.userAction.deleteFollowedCompany({
      employerIds: [item.employerId],
    });
    this.toast.show(result && result.message);
    if (result && result.successful) {
      await this.getFollowedCompanys();
    }
  };

  renderEmptyView = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={resource.noData} />
    </View>
  );

  renderScorllableTableview = () => (
    <View style={{ height: 44 }}>
      <ScrollableTabView
        renderTabBar={() => <CommonCustomTabBar currentIndex={this.state.currentTab} />}
        style={favoriteStyle.scorllContainer}
        initialPage={0}
        tabBarBackgroundColor={baseBlueColor}
        onChangeTab={(obj) => {
          this.onChangeTab(obj);
        }}
      >
        <Text style={favoriteStyle.textStyle} tabLabel={I18n.t('page_mine_favorite_companys')} />
        <Text style={favoriteStyle.textStyle} tabLabel={I18n.t('page_mine_favorite_jobs')} />
      </ScrollableTabView>
    </View>
  );

  renderJobList = ({ item }) => {
    const swipeoutBtns = [
      {
        text: I18n.t('page_resume_btn_del'),
        type: 'delete',
        onPress: () => {
          this.deleteFollowedJob(item);
        },
      },
    ];
    return (
      <Swipeout
        right={swipeoutBtns}
        openRight={item.showDel}
        close={!item.showDel}
        autoClose
        key={item}
      >
        <TouchableOpacity onPress={() => this.onToJobDetail(item)}>
          <View style={[jobSearchStyle.listContainer, { marginTop: 0 }]}>
            <View style={jobSearchStyle.companyInfo}>
              <View style={[jobSearchStyle.companyPannel]}>
                <Text numberOfLines={1} style={jobSearchStyle.companyName}>
                  {item.jobtitle}{' '}
                  {/* <Image
                    source={resource.iconVerify}
                    style={{ width: 9, height: 9, marginTop: 2 }}
                  /> */}
                </Text>
                <Text numberOfLines={1} style={jobSearchStyle.jobWages}>
                  {item && item.salaryId && item.salaryId.label ? item.salaryId.label : ''}
                </Text>
              </View>
              <View style={[jobSearchStyle.companyDetail, { marginTop: 6 }]}>
                <Text
                  numberOfLines={1}
                  style={[jobSearchStyle.companySubcribe, { fontSize: 14, color: subTitleColor }]}
                >
                  {item && item.employername ? item.employername : ''}
                </Text>
                {item && item.locations && item.locations.length > 0 ? (
                  <View style={[jobSearchStyle.locationsStyle, { marginBottom: 10 }]}>
                    {item.locations.map((l, index) => (
                      <View key={`${index + 1}`} style={jobSearchStyle.locationsCon}>
                        <Text style={{ color: desColor, fontSize: 12 }}>{l.locationId.label}</Text>
                        {item.locations.length > 1 &&
                        l.locationId?.code !==
                          item.locations[item.locations.length - 1].locationId.code ? (
                          <Text style={jobSearchStyle.locationsTitle} />
                        ) : (
                          <Text style={{ display: 'none' }} />
                        )}
                      </View>
                    ))}
                  </View>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
              {item && item.contact ? (
                <View style={[jobSearchStyle.namePannel, jobSearchStyle.nameContainer]}>
                  <Image style={jobSearchStyle.userImage} source={getAvatarSource('')} />
                  <Text
                    style={[
                      jobSearchStyle.companySubcribe,
                      jobSearchStyle.nameSubcribe,
                      { marginLeft: 8, marginTop: 0 },
                    ]}
                  >
                    {item && item.contact ? item.contact.name : ''}
                  </Text>
                </View>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Swipeout>
    );
  };

  renderTag = (value) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        value={value}
        containerStyle={jobSearchStyle.listTagItem}
        badgeStyle={jobSearchStyle.listTagBadgeStyle}
        textStyle={jobSearchStyle.tagText}
      />
    );
  };

  renderCompanyList = ({ item }) => {
    const swipeoutBtns = [
      {
        text: I18n.t('page_resume_btn_del'),
        type: 'delete',
        onPress: () => {
          this.deleteFollowedCompany(item);
        },
      },
    ];
    return (
      <Swipeout
        right={swipeoutBtns}
        openRight={item.showDel}
        close={!item.showDel}
        autoClose
        key={item}
      >
        <TouchableOpacity onPress={() => this.onToCompanyDetail(item)}>
          <View style={[jobSearchStyle.listContainer, { marginTop: 0 }]}>
            <View style={jobSearchStyle.companyInfo}>
              <View style={jobSearchStyle.companyPannel}>
                <ImageBackground
                  imageStyle={{ borderRadius: 21 }}
                  style={jobSearchStyle.companyLogo}
                  source={getEmployerAvatarSource(
                    item && item.employer && item.employer.logo ? item.employer.logo : ''
                  )}
                  resizeMode="contain"
                >
                  {item &&
                  item.employer &&
                  item.employer.employerQualificationType &&
                  item.employer.employerQualificationType.value === 1 ? (
                    <Image style={jobSearchStyle.v2} source={resource.verify} />
                  ) : (
                    <View />
                  )}
                </ImageBackground>
                <View style={[jobSearchStyle.companyDetail, { marginLeft: 10 }]}>
                  <View style={jobSearchStyle.companyDetail}>
                    <Text numberOfLines={1} style={jobSearchStyle.companyName}>
                      {item ? item.employername : ''}{' '}
                      {item &&
                      item.employer &&
                      item.employer.qualificationStatus &&
                      item.employer.qualificationStatus.value === 1 ? (
                        <Image source={resource.iconVerify} style={{ width: 9, height: 9 }} />
                      ) : null}
                    </Text>
                    <View style={jobSearchStyle.locationsStyle}>
                      <Text style={{ color: desColor, fontSize: 12 }}>
                        {item &&
                        item.employer &&
                        item.employer.locationId &&
                        item.employer.locationId.label
                          ? item.employer.locationId.label
                          : ''}
                      </Text>
                      {item && item.employer && item.employer.address ? (
                        <Text style={jobSearchStyle.spaceLine}>/</Text>
                      ) : (
                        <Text style={{ display: 'none' }} />
                      )}
                      <Text
                        numberOfLines={1}
                        style={{ color: desColor, fontSize: 12, width: '88%' }}
                      >
                        {item && item.employer && item.employer.address
                          ? item.employer.address
                          : ''}
                      </Text>
                    </View>
                    <View style={jobSearchStyle.listTags}>
                      {this.renderTag(
                        item &&
                          item.employer &&
                          item.employer.industrialId &&
                          item.employer.industrialId.label
                      )}
                      {this.renderTag(
                        item &&
                          item.employer &&
                          item.employer.scaleId &&
                          item.employer.scaleId.label
                      )}
                      {this.renderTag(
                        item &&
                          item.employer &&
                          item.employer.companyType &&
                          item.employer.companyType.label
                      )}
                    </View>
                    {item &&
                    item.employer &&
                    item.employer.sampleOnlineCategory &&
                    item.employer.sampleOnlineCategory.label ? (
                      <View style={jobSearchStyle.requireJob}>
                        <View style={{ flexDirection: 'row', width: '80%', alignItems: 'center' }}>
                          <Text style={{ fontSize: 12, color: desColor }}>
                            {I18n.t('page_job_text_hot_recruitment')}
                          </Text>
                          <Text style={{ fontSize: 12, color: baseBlueColor }}>
                            {item.employer.sampleOnlineCategory.label}
                          </Text>
                          <Text style={{ fontSize: 12, color: desColor }}>
                            {I18n.t('page_job_text_wait')}
                            {item && item.employer && item.employer.totalOnlineJobs
                              ? item.employer.totalOnlineJobs
                              : 0}
                            {I18n.t('page_job_text_individual_position')}
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <Text style={{ display: 'none' }} />
                    )}
                  </View>
                </View>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Swipeout>
    );
  };

  renderFooter = () =>
    this.state.hasMoreCompanys ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  renderFooter2 = () =>
    this.state.hasMoreJobs ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  render() {
    const { navigation } = this.props;
    const { currentTab, refreshing } = this.state;
    const { followedCompanyList, followedJobList } = this.props.personStore;
    const followedJobData =
      followedJobList && followedJobList.length > 0 ? followedJobList.slice() : [];
    const followedCompanyData =
      followedCompanyList && followedCompanyList.length > 0 ? followedCompanyList.slice() : [];
    return (
      <View style={favoriteStyle.viewContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_mine_my_favorite'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderScorllableTableview()}
        <FlatList
          data={currentTab === 1 ? followedJobData : followedCompanyData}
          renderItem={currentTab === 1 ? this.renderJobList : this.renderCompanyList}
          keyExtractor={(item, index) => index + item}
          showsVerticalScrollIndicator={false}
          horizontal={false}
          ListFooterComponent={currentTab === 1 ? this.renderFooter2 : this.renderFooter}
          refreshing={refreshing}
          onRefresh={this.onRefresh}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={this.renderEmptyView}
          onEndReached={() => this.onLoadMore()}
          ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
