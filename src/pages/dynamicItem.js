import React, { PureComponent } from 'react';
import { Icon } from 'react-native-elements';
import ExpandableText from 'rn-expandable-text';
import { Text, View, TouchableOpacity, Alert, TouchableHighlight } from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import { dynamicStyle } from '../themes';
import { getAvatarSource } from '../res';
import util from '../util';
import I18n from '../i18n';
import Session from '../api/session';
import Image from '../components/image';

export default class DynamicItem extends PureComponent {
  onPress = () => {
    this.props.onPress(this.props.info);
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onAvatarsClick = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        if (this.props.info.mine) return;
        navigation.navigate('userProfile', {
          data: this.props.info,
          onRefreshDynamic: () => {
            this.props.getDynmics();
          },
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  previewDetail = (i) => {
    const { info, navigation } = this.props;
    const imgArr = [];
    info.images.forEach((item) => {
      imgArr.push({ url: item });
    });
    navigation.navigate('previewImage', { images: imgArr, index: i });
  };

  renderBotton = (imageUrl, iconType, onPress, num, favorited) => (
    <TouchableOpacity onPress={onPress}>
      <View style={dynamicStyle.bottomView}>
        <Icon
          name={imageUrl}
          type={iconType}
          size={iconType == 'ionicon' ? 23 : 25}
          iconStyle={favorited ? dynamicStyle.bottomImageColorRed : dynamicStyle.bottomImageColor}
        />
        <Text style={dynamicStyle.bottomTitle}>{num}</Text>
      </View>
    </TouchableOpacity>
  );

  render() {
    const { info } = this.props;
    return (
      <TouchableHighlight
        underlayColor="transparent"
        onPress={() => {
          this.onPress();
        }}
      >
        <View style={dynamicStyle.container}>
          <View style={dynamicStyle.top}>
            <View style={dynamicStyle.left}>
              <TouchableOpacity
                onPress={() => {
                  this.onAvatarsClick();
                }}
              >
                <Image source={getAvatarSource(info.avatar)} style={dynamicStyle.icon} />
              </TouchableOpacity>
              <Text style={dynamicStyle.title} numberOfLines={1}>
                {info.userName
                  ? util.handleDisplayName(info.userName)
                  : I18n.t('page_dynamic_title_ay_username')}
              </Text>
            </View>
            <Text style={dynamicStyle.time}>
              {info.createAt_unixtime ? util.getDiffBetween(info.createAt_unixtime) : ''}
            </Text>
          </View>
          <TouchableHighlight
            underlayColor="#f2f2f2"
            onPress={() => {
              this.onPress();
            }}
            onLongPress={() => {
              this.props.onLongPress();
            }}
          >
            <View
              style={{
                marginHorizontal: 15,
                marginVertical: 10,
                marginBottom: 5,
              }}
            >
              <Hyperlink
                onPress={(url, text) => {
                  if (!url.startsWith('http')) {
                    return;
                  }
                  const { navigation } = this.props;
                  navigation.navigate('previewWeb', { title: url, url });
                }}
                linkStyle={{ color: '#2980b9' }}
              >
                <ExpandableText
                  numberOfLines={8}
                  style={dynamicStyle.contentTitle}
                  unexpandView={() => null}
                  expandView={() =>
                    info.content && info.content.length > 300 ? (
                      <View style={dynamicStyle.expandContainer}>
                        <View style={dynamicStyle.lineView} />
                        <View style={dynamicStyle.moreText}>
                          <Text style={dynamicStyle.expandTitle}>
                            {I18n.t('page_resume_label_more')}
                          </Text>
                          {/* <Icon name="chevron-small-down" type="entypo" size={24} color={baseBlueColor} iconStyle={{ marginTop: 4 }} /> */}
                        </View>
                        <View style={dynamicStyle.lineView} />
                      </View>
                    ) : (
                      <Text style={{ display: 'none' }} />
                    )
                  }
                >
                  {info.content}
                </ExpandableText>
              </Hyperlink>
            </View>
          </TouchableHighlight>
          <View
            style={[
              dynamicStyle.imagesContent,
              { marginBottom: info.images && info.images.length > 0 ? 12 : 6 },
            ]}
          >
            {info.images.map((item, i) => (
              <TouchableOpacity
                key={`${i + 1}`}
                onPress={() => {
                  this.previewDetail(i);
                }}
              >
                <Image source={{ uri: item }} style={dynamicStyle.image} />
              </TouchableOpacity>
            ))}
          </View>
          <View style={dynamicStyle.bottom}>
            {this.renderBotton(
              info.liked ? 'heart' : 'heart-outlined',
              'entypo',
              this.props.onFavorite,
              info.likes,
              info.liked
            )}
            {this.renderBotton('chatbox-outline', 'ionicon', this.props.onComment, info.comments)}
          </View>
        </View>
      </TouchableHighlight>
    );
  }
}
