import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, TouchableOpacity, FlatList } from 'react-native';
import { Input, Header, Icon, Divider, Badge } from 'react-native-elements';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {
  headerStyle,
  baseBlueColor,
  titleColor,
  desColor,
  jobSearchStyle,
  subTitleColor,
} from '../themes';
import I18n from '../i18n';
import res, { getAvatarSource, getEmployerAvatarSource } from '../res';
import { deviceWidth, RVW } from '../common';
import util from '../util';
import CommonCustomTabBar from '../components/commonCustomTabBar';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('jobStore', 'jobAction')
@observer
export default class JobSearch extends Component {
  constructor(props) {
    super(props);
    this.page = 1;
    this.loadingMore = false;
    this.state = {
      recentSearch: [],
      cityData: {},
      cityName: I18n.t('page_job_jin_bian_city'),
      cityId: null,
      currentIndex: 0,
      tagText: '',
      refresh: false,
      showLoading: true,
      keyword: null,
      pageNumber: 1,
    };
  }

  componentDidMount() {
    this.getSearchHistory();
    this.getRecommends();
    this.judgeLanguage();
  }

  judgeLanguage = () => {
    this.props.jobAction.getCurrentCity().then((data) => {
      if (data) {
        this.setState({
          cityName: util.getCityNameWithCityData(data),
          cityId: data.locationId || data.value,
          cityData: data,
        });
      }
    });
  };

  onChangeTab = (obj) => {
    this.setState({
      currentIndex: obj.i,
      showLoading: true,
    });
    this.page = 1;
    this.loadingMore = false;
    this.inputSearch(this.state.keyword, this.state.cityId, obj.i);
  };

  onToJobDetail = (item) => {
    this.inputText.blur();
    this.props.navigation.navigate('jobDetail', { detail: item });
  };

  onToCompanyDetail = (item) => {
    this.inputText.blur();
    this.props.navigation.navigate('companyDetail', { employerId: item.employerId });
  };

  onRepalceAnother = () => {
    const { recommendTotalPage } = this.props.jobStore;
    if (recommendTotalPage !== this.state.pageNumber) {
      this.state.pageNumber += 1;
    } else {
      this.state.pageNumber = 1;
    }
    this.getRecommends();
  };

  onClearSearch = () => {
    this.props.jobAction.clearSearchHistory().then(() => {
      this.getSearchHistory();
    });
  };

  onSearchJob = (text) => {
    this.setState({
      keyword: text,
      // showLoading: true,
    });
    if (!text) {
      // this.setState({
      //   currentIndex: 0,
      // });
      return;
    }
    this.inputSearch(text, this.state.cityId, this.state.currentIndex);
  };

  onSubmitEditing = () => {
    if (this.state.keyword) {
      this.props.jobAction.addSearchHistory(this.state.keyword).then(() => {
        this.getSearchHistory();
      });
    }
    this.inputSearch(this.state.keyword, this.state.cityId, this.state.currentIndex);
  };

  getJobs = async (data, index) => {
    const tempData = { page: data.page, size: 20, ...data };
    if (index === 1) {
      this.props.jobAction.queryEmployers(tempData).then(() => {
        this.setState({ showLoading: false });
        this.setDataStatus();
      });
    } else {
      this.props.jobAction.jobSearsh(tempData).then(() => {
        this.setState({ showLoading: false });
        this.setDataStatus();
      });
    }
  };

  getRecommends = async () => {
    await this.props.jobAction.queryRecommends({
      page: this.state.pageNumber,
      size: 3,
    });
  };

  getSearchHistory = () => {
    this.props.jobAction.getSearchHistory().then((result) => {
      this.setState({
        recentSearch: result,
      });
    });
  };

  setDataStatus = () => {
    this.setState({
      refresh: false,
    });
    setTimeout(() => {
      this.loadingMore = false;
    }, 300);
  };

  inputSearch = (text, city, index) => {
    this.page = 1;
    this.search(text, city, index);
  };

  search = (text, city, index) => {
    const data = {
      page: this.page,
    };
    if (index === 0) {
      data.jobTitle = text;
      if (city) {
        data.locationId = city;
      }
    } else if (index === 1) {
      data.company = text;
      if (city) {
        data.locationIds = [city];
      }
    }
    this.getJobs(data, index);
  };

  refresh = () => {
    this.page = 1;
    this.setState({ refresh: true });
    this.search(this.state.keyword, this.state.cityId, this.state.currentIndex);
  };

  loadMore = () => {
    const { jobSearchList, searchTotalPage, companySearchList, companyTotalPage } =
      this.props.jobStore;
    const { currentIndex } = this.state;
    if (currentIndex === 0) {
      if (!this.loadingMore && jobSearchList.length >= 20 && this.page < searchTotalPage) {
        this.page += 1;
        this.loadingMore = true;
        this.search(this.state.keyword, this.state.cityId, currentIndex);
      }
      return;
    }
    if (!this.loadingMore && companySearchList.length >= 20 && this.page < companyTotalPage) {
      this.page += 1;
      this.loadingMore = true;
      this.search(this.state.keyword, this.state.cityId, currentIndex);
    }
  };

  searchTagPress = (item) => {
    this.setState({
      keyword: item,
      // tagText: item,
    });
    this.inputText.blur();
    this.inputSearch(item, this.state.cityId, this.state.currentIndex);
  };

  renderSearchResult = () => {
    const { jobSearchList, companySearchList } = this.props.jobStore;
    const { keyword, currentIndex, refresh } = this.state;
    const list = currentIndex === 1 ? companySearchList.slice() : jobSearchList.slice();
    return (
      <View style={keyword ? { flex: 1 } : { height: 0 }}>
        <View style={{ height: 44 }}>
          <ScrollableTabView
            renderTabBar={() => <CommonCustomTabBar currentIndex={this.state.currentIndex} />}
            initialPage={0}
            tabBarBackgroundColor={baseBlueColor}
            onChangeTab={(obj) => {
              this.onChangeTab(obj);
            }}
          >
            <Text style={jobSearchStyle.textStyle} tabLabel={I18n.t('page_job_relate_job')} />
            <Text style={jobSearchStyle.textStyle} tabLabel={I18n.t('page_job_relate_company')} />
          </ScrollableTabView>
        </View>
        {this.state.showLoading && this.state.keyword ? (
          <View style={jobSearchStyle.loadingStyle}>
            <Image style={{ width: 100, height: 100 }} source={res.loadingImg} />
          </View>
        ) : (
          <View style={{ flex: 1 }}>
            <FlatList
              data={list}
              refreshing={refresh}
              onRefresh={() => {
                this.refresh();
              }}
              ListFooterComponent={this.renderFooter()}
              onEndReachedThreshold={0.1}
              onEndReached={() => {
                this.loadMore();
              }}
              ListEmptyComponent={this.renderEmptyComponent}
              renderItem={currentIndex === 1 ? this.renderCompanyList : this.renderJobList}
              ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
            />
          </View>
        )}
      </View>
    );
  };

  renderFooter = () => {
    const { searchTotalPage, companyTotalPage } = this.props.jobStore;
    const { currentIndex } = this.state;
    let hasMore = false;
    if (currentIndex === 0) {
      hasMore = this.page !== searchTotalPage && searchTotalPage !== 0;
    } else {
      hasMore = this.page !== companyTotalPage && companyTotalPage !== 0;
    }

    return hasMore ? (
      <View style={{ height: 40, padding: 13 }}>
        <Text style={{ textAlign: 'center', color: titleColor, fontSize: 14 }}>
          {I18n.t('page_job_text_loading')}{' '}
        </Text>
      </View>
    ) : (
      <View />
    );
  };

  renderJobList = ({ item }) => (
    <TouchableOpacity onPress={() => this.onToJobDetail(item)}>
      <View style={[jobSearchStyle.listContainer, { marginTop: 0 }]}>
        <View style={jobSearchStyle.companyInfo}>
          <View style={[jobSearchStyle.companyPannel]}>
            <Text numberOfLines={1} style={jobSearchStyle.companyName}>
              {item.title}
            </Text>
            <Text numberOfLines={1} style={jobSearchStyle.jobWages}>
              {item.salaryId && item.salaryId.label ? item.salaryId.label : ''}
            </Text>
          </View>
          <View style={[jobSearchStyle.companyDetail, { marginTop: 6, width: RVW * 90 }]}>
            <Text
              numberOfLines={1}
              style={[jobSearchStyle.companySubcribe, { fontSize: 14, color: subTitleColor }]}
            >
              {item.employer ? item.employer.company : ''}{' '}
              {item.employer &&
              item.employer.qualificationStatus &&
              item.employer.qualificationStatus.value === 1 ? (
                <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
              ) : null}
            </Text>
            {item.locations && item.locations.length > 0 ? (
              <View style={[jobSearchStyle.locationsStyle, { width: '100%' }]}>
                {item.locations.map((l) => l.locationId.label).length > 0 ? (
                  <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                    {item.locations.map((l) => l.locationId.label).join(' | ')}
                  </Text>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
          </View>
          <View style={[jobSearchStyle.namePannel, jobSearchStyle.nameContainer]}>
            <Image style={jobSearchStyle.userImage} source={getAvatarSource('')} />
            <Text
              style={[
                jobSearchStyle.companySubcribe,
                jobSearchStyle.nameSubcribe,
                { marginLeft: 8, marginTop: 0 },
              ]}
            >
              {item.contact ? item.contact.name : ''}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderTag = (value) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        value={value}
        containerStyle={jobSearchStyle.listTagItem}
        badgeStyle={jobSearchStyle.listTagBadgeStyle}
        textStyle={jobSearchStyle.tagText}
      />
    );
  };

  renderCompanyList = ({ item }) => (
    <TouchableOpacity onPress={() => this.onToCompanyDetail(item)}>
      <View style={[jobSearchStyle.listContainer, { marginTop: 0 }]}>
        <View style={jobSearchStyle.companyInfo}>
          <View style={jobSearchStyle.companyPannel}>
            <ImageBackground
              imageStyle={{ borderRadius: 21 }}
              style={jobSearchStyle.companyLogo}
              source={getEmployerAvatarSource((item && item.logo) || '')}
            >
              {item &&
              item.employerQualificationType &&
              item.employerQualificationType.value === 1 ? (
                <Image style={jobSearchStyle.v2} source={res.verify} />
              ) : (
                <View />
              )}
            </ImageBackground>
            <View style={[jobSearchStyle.companyDetail, { marginLeft: 10 }]}>
              <View style={jobSearchStyle.companyDetail}>
                <Text numberOfLines={1} style={jobSearchStyle.companyName}>
                  {item ? item.company : ''}{' '}
                  {item && item.qualificationStatus && item.qualificationStatus.value === 1 ? (
                    <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
                  ) : null}
                </Text>
                <View style={jobSearchStyle.locationsStyle}>
                  <Text style={{ color: desColor, fontSize: 12 }}>
                    {item.locationId && item.locationId.label ? item.locationId.label : ''}
                  </Text>
                  {item && item.address ? (
                    <Text style={jobSearchStyle.spaceLine}>/</Text>
                  ) : (
                    <Text style={{ display: 'none' }} />
                  )}
                  <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                    {item ? item.address : ''}
                  </Text>
                </View>
                <View style={jobSearchStyle.listTags}>
                  {this.renderTag(item && item.industrialId && item.industrialId.label)}
                  {this.renderTag(item && item.scaleId && item.scaleId.label)}
                  {this.renderTag(item && item.companyType && item.companyType.label)}
                </View>
                {item && item.sampleOnlineCategory && item.sampleOnlineCategory.label ? (
                  <View style={jobSearchStyle.requireJob}>
                    <View style={{ flexDirection: 'row', width: '80%', alignItems: 'center' }}>
                      <Text style={{ fontSize: 12, color: desColor }}>
                        {I18n.t('page_job_text_hot_recruitment')}
                      </Text>
                      <Text style={{ fontSize: 12, color: baseBlueColor }}>
                        {item.sampleOnlineCategory.label}
                      </Text>
                      <Text style={{ fontSize: 12, color: desColor }}>
                        {I18n.t('page_job_text_wait')}
                        {item.totalOnlineJobs}
                        {I18n.t('page_job_text_individual_position')}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderLeftIcon = () => {
    const { navigation } = this.props;
    const { cityName } = this.state;
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('cityList', {
            needSave: true,
            onSelect: (item) => {
              console.log('item', item);
              this.setState({
                cityData: item,
                cityId: item.value || item.locationId,
              });
              this.setState({
                cityName: util.getCityNameWithCityData(item),
              });
              this.inputSearch(
                this.state.keyword,
                item.value || item.locationId,
                this.state.currentIndex
              );
            },
          });
        }}
      >
        <View style={jobSearchStyle.searchBar}>
          <Icon name="place" iconStyle={{ marginLeft: -2 }} size={16} color={baseBlueColor} />
          <Text
            numberOfLines={1}
            style={[
              jobSearchStyle.cityName,
              { marginRight: cityName.length > 3 ? 4 : 0, width: util.calLength(cityName) },
            ]}
          >
            {cityName}
          </Text>
          <Divider style={jobSearchStyle.divider} />
        </View>
      </TouchableOpacity>
    );
  };

  renderSearch = () => {
    const { navigation } = this.props;
    const { keyword, cityData } = this.state;
    return (
      <View
        style={{
          flexDirection: 'row',
          width: deviceWidth,
          alignItems: 'center',
          justifyContent: 'space-around',
        }}
      >
        <Input
          ref={(ref) => {
            this.inputText = ref;
          }}
          inputContainerStyle={jobSearchStyle.renderSearchInput}
          containerStyle={[jobSearchStyle.renderSearchInput, { marginLeft: 2 * RVW }]}
          inputStyle={{ color: titleColor, fontSize: 12 }}
          onSubmitEditing={() => {
            this.onSubmitEditing(keyword);
          }}
          clearButtonMode="while-editing"
          returnKeyType="search"
          placeholder={I18n.t('page_job_ph_search')}
          placeholderTextColor={desColor}
          leftIcon={this.renderLeftIcon()}
          onChangeText={this.onSearchJob}
          value={keyword}
        />
        <TouchableOpacity
          onPress={() => {
            if (this.props.navigation.state.params) {
              this.props.navigation.state.params.onSelect(cityData);
            }
            navigation.goBack();
          }}
        >
          <Text style={{ color: '#fff', fontSize: 14, textAlign: 'center' }}>
            {I18n.t('page_job_link_cancel')}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderTags(tags, isRecommend) {
    if (!tags) {
      return <View />;
    }
    return tags.map((item, i) => (
      <Badge
        key={isRecommend ? item.name : item}
        containerStyle={jobSearchStyle.tagItem}
        badgeStyle={jobSearchStyle.badgeStyle}
        onPress={() => this.searchTagPress(isRecommend ? item.name : item)}
        value={isRecommend ? item.name : item}
        textStyle={jobSearchStyle.tagText}
      />
    ));
  }

  renderHistoryResult = () => {
    const { recommendList } = this.props.jobStore;
    return (
      <View style={this.state.keyword ? { height: 0 } : { flex: 1 }}>
        <View style={jobSearchStyle.recommendContainer}>
          <Text style={jobSearchStyle.placeholderTitle}>{I18n.t('page_job_recommend_search')}</Text>
          <TouchableOpacity onPress={this.onRepalceAnother}>
            <Text style={jobSearchStyle.placeholderTitle}>{I18n.t('page_job_change_another')}</Text>
          </TouchableOpacity>
        </View>
        <View style={jobSearchStyle.tags}>{this.renderTags(recommendList, true)}</View>
        <View style={jobSearchStyle.hotContainer}>
          <Text style={jobSearchStyle.placeholderTitle}>{I18n.t('page_job_recent_search')}</Text>
          <TouchableOpacity onPress={this.onClearSearch}>
            <Text style={jobSearchStyle.placeholderTitle}>{I18n.t('page_job_clear')}</Text>
          </TouchableOpacity>
        </View>
        <View style={jobSearchStyle.tags}>{this.renderTags(this.state.recentSearch, false)}</View>
      </View>
    );
  };

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  render() {
    return (
      <View style={this.state.keyword ? jobSearchStyle.jobContainer2 : jobSearchStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={this.renderSearch()}
        />
        {this.renderSearchResult()}
        {this.renderHistoryResult()}
      </View>
    );
  }
}
