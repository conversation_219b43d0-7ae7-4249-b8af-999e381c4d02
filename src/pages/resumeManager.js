import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { ScrollView, View, Text, TouchableOpacity, Alert } from 'react-native';
import { Header, Icon, ListItem, Button } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import {
  headerStyle,
  baseBlueColor,
  globalStyle,
  jobIntensionStyle,
  resumeStyle,
  desColor,
  bgColor,
  titleColor,
} from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';
import constant from '../store/constant';

@inject('jobStore', 'jobAction', 'resumeAction', 'resumeStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  deleteAction() {
    const { currentResume } = this.props.resumeStore;
    this.props.resumeAction.deleteResume(currentResume.resumeId).then((res) => {
      this.showRequestResult(res, true);
    });
  }

  resumeDelete = () => {
    const { resumeList } = this.props.resumeStore;
    if (resumeList.length <= 1) {
      Alert.alert('', I18n.t('page_resume_alt_delete_msg'), [
        { text: I18n.t('page_resume_btn_sure'), onPress: () => {} },
      ]);
    } else {
      Alert.alert(I18n.t('page_resume_alt_manager_title'), I18n.t('page_resume_alt_manager_msg'), [
        { text: I18n.t('page_resume_btn_cancel'), onPress: () => {} },
        {
          text: I18n.t('page_resume_btn_sure'),
          onPress: () => {
            this.deleteAction();
          },
        },
      ]);
    }
  };

  resumeAdd = () => {
    const { navigation } = this.props;
    navigation.navigate('resumeAdd');
  };

  resumeHide = (value) => {
    const { currentResume } = this.props.resumeStore;
    this.props.resumeAction.hideResume(currentResume.resumeId, value ? 1 : 0).then((res) => {
      this.showRequestResult(res, false);
    });
  };

  setDefaultResmue = (value) => {
    const { currentResume } = this.props.resumeStore;
    this.props.resumeAction.setDefaultResume(currentResume.resumeId, value).then((res) => {
      this.showRequestResult(res, false);
    });
  };

  showRequestResult(res, goback) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);

      if (goback) {
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
      }
      global.emitter.emit(constant.topic.onlineResumeChanged);
      // this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  render() {
    const { currentResume, resumeList } = this.props.resumeStore;

    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: I18n.t('page_resume_text_manager_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={this.props.navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.resumeDelete}>
              <Icon
                name="trash-o"
                type="font-awesome"
                color="#fff"
              />
            </TouchableOpacity>
          }
        />
        <ScrollView>
          <ListItem
            title={I18n.t('page_mine_label_hide_resume')}
            switch={{
              value: currentResume.privacyLevel.value !== 0,
              trackColor: baseBlueColor,
              onValueChange: this.resumeHide,
            }}
            titleStyle={resumeStyle.listItemLeftText}
            containerStyle={[resumeStyle.listItemContent, { marginTop: 5 }]}
          />
          <ListItem
            title={I18n.t('page_resume_label_manager_name')}
            rightElement={
              <Text
                numberOfLines={1}
                style={{
                  width: 200,
                  fontSize: 14,
                  color: titleColor,
                  textAlign: 'right',
                }}
              >
                {currentResume.name ? currentResume.name : ''}
              </Text>
            }
            onPress={() => {
              this.props.navigation.navigate('resumeName', {
                resumeName: currentResume.name,
                resumeType: constant.resumeType.online,
              });
            }}
            chevron
            titleStyle={resumeStyle.listItemLeftText}
            containerStyle={[resumeStyle.listItemContent]}
          />
          <ListItem
            title={I18n.t('page_resume_set_default_resume')}
            switch={{
              value: currentResume.defaultCv,
              trackColor: baseBlueColor,
              onValueChange: this.setDefaultResmue,
            }}
            titleStyle={resumeStyle.listItemLeftText}
            containerStyle={[resumeStyle.listItemContent, { marginTop: 5 }]}
          />
          <ListItem
            title={I18n.t('page_resume_set_default_tips')}
            titleStyle={[resumeStyle.listItemLeftText, { color: desColor, fontSize: 12 }]}
            containerStyle={{ backgroundColor: bgColor, paddingVertical: 10 }}
          />
        </ScrollView>
        <Text
          style={{
            fontSize: 12,
            color: desColor,
            marginLeft: 15,
            marginBottom: 5,
          }}
        >
          {I18n.t('page_resume_tips_manager_info')}
        </Text>
        <View style={jobIntensionStyle.submitButton}>
          <Button
            disabled={resumeList.length === 3}
            title={I18n.t('page_resume_btn_manager_add')}
            buttonStyle={jobIntensionStyle.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={() => {
              this.resumeAdd();
            }}
          />
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
