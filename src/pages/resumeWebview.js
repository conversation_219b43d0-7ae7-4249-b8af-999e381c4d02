import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import { View, TouchableOpacity } from 'react-native';
import { WebView } from 'react-native-webview';
import { Header, Icon } from 'react-native-elements';
import { headerStyle, companyDetailStyle } from '../themes';
import GoBack from '../components/goback';
// import ShareUtile from "../util/ShareUtil";
import Share from 'react-native-share';
import configs from '../configs';
import I18n from '../i18n';

@inject('resumeStore', 'resumeAction')
@observer
export default class Page extends Component {
  componentDidMount() {
    const {
      params: { resumeId },
    } = this.props.navigation.state;
    this.props.resumeAction.shareResume(resumeId);
  }

  share(message, des) {
    const url = configs.resumeShareURl
      .replace('${lang}', I18n.locale)
      .replace('${token}', this.props.resumeStore.shareToken);
    const options = {
      message,
      url,
      failOnCancel: false,
    };

    Share.open(options)
      .then(() => {
        this.toast.show(I18n.t('page_share_tips_text'));
      })
      .catch(() => {
        // toast.show(I18n.t('page_component_qr_share_error'));
      });

    // ShareUtile.shareboard(
    //   "",
    //   "",
    //   configs.resumeShareURl
    //     .replace("${lang}", I18n.locale)
    //     .replace("${token}", this.props.resumeStore.shareToken),
    //   title,
    //   [2, 7, 21, 24],
    //   (code, message) => {
    //     // console.log('share', code, message, configs.resumeShareURl.replace('${lang}', I18n.locale).replace('${token}', this.props.resumeStore.shareToken));
    //     // this.setState({ result: message });
    //     if (code === 200) {
    //       this.toast.show(I18n.t("page_share_tips_text"));
    //     } else {
    //       this.toast.show(message);
    //     }
    //   }
    // );
  }

  render() {
    const { navigation } = this.props;
    const {
      params: { title, des },
    } = this.props.navigation.state;
    const token = this.props.resumeStore.shareToken;
    return (
      <View style={companyDetailStyle.jobContainer}>
        <Header
          statusBarProps={{
            barStyle: 'light-content',
            backgroundColor: '#2089DC',
          }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: title, style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              style={{ height: 30, width: 30, marginTop: 8 }}
              onPress={() => this.share(title, des)}
            >
              <Icon type="simple-line-icon" name="share-alt" size={20} color="white" />
            </TouchableOpacity>
          }
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {token ? (
          <WebView
            source={{
              uri: configs.resumeShareURl
                .replace('${lang}', I18n.locale)
                .replace('${token}', token),
            }}
            startInLoadingState
            domStorageEnabled
            javaScriptEnabled
            showsVerticalScrollIndicator={false}
          />
        ) : (
            <View />
          )}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
