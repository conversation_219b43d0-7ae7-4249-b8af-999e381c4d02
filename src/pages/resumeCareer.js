import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle, desColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import regExp from '../util/regExp';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const { currentResume } = this.props.resumeStore;
    this.state = {
      postData: Object.assign({}, currentResume.careerProfile),
      careerPosition: currentResume.careerProfile.careerPosition,
      salary: currentResume.careerProfile.salary,
    };
  }

  onChangeSalary = (text) => {
    this.updateValue('salary', text);
  };

  onChangePosition = (text) => {
    this.updateValue('careerPosition', text);
  };

  selectConstant = (onstantName, title, updateKey) => {
    const { navigation } = this.props;
    navigation.navigate('constantList', {
      constantName: onstantName,
      title,
      onSelect: (item) => {
        this.updateValue(updateKey, item);
      },
    });
  };

  updateValue(key, value) {
    const temp = this.state.postData;
    temp[key] = value;
    this.setState({
      postData: temp,
    });
  }

  publish = () => {
    const { currentResume } = this.props.resumeStore;
    const data = this.state.postData;
    const temp = {};
    if (data.careerLevel.label) {
      temp.careerLevel = data.careerLevel.value;
    } else {
      this.toast.show(
        I18n.t('page_resume_tips_select') +
          I18n.t('page_resume_label_career_recent') +
          I18n.t('page_resume_label_career_level')
      );
      return;
    }
    if (data.careerPosition) {
      temp.careerPosition = data.careerPosition;
    } else {
      this.toast.show(
        I18n.t('page_resume_tips_input') +
          I18n.t('page_resume_label_career_recent') +
          I18n.t('page_resume_label_career_pos')
      );
      return;
    }
    if (data.qualificationId.label) {
      temp.qualificationId = data.qualificationId.value;
    } else {
      this.toast.show(
        I18n.t('page_resume_tips_select') +
          I18n.t('page_resume_label_career_highest') +
          I18n.t('page_resume_label_career_edu')
      );
      return;
    }
    if (data.careerIndustry.label) {
      temp.careerIndustry = data.careerIndustry.value;
    }
    if (data.careerCategory.label) {
      temp.careerCategory = data.careerCategory.value;
    }
    if (!regExp.numberExtra.test(data.salary)) {
      this.toast.show(I18n.t('page_resume_career_profile'));
      return;
    }
    if (data.salary) {
      temp.salary = data.salary;
    }
    this.props.resumeAction.updateCareerProfile(currentResume.resumeId, temp).then((res) => {
      this.showRequestResult(res);
    });
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  render() {
    const { navigation } = this.props;
    const career = this.state.postData;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_resume_label_career'), style: headerStyle.center }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={
            I18n.t('page_resume_label_career_recent') + I18n.t('page_resume_label_career_level')
          }
          chevron
          rightTitle={career ? career.careerLevel.label : ''}
          onPress={() =>
            this.selectConstant(
              'jobLevelList',
              I18n.t('page_resume_label_career_level'),
              'careerLevel'
            )
          }
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_career_recent') + I18n.t('page_resume_label_career_pos')}
          chevron
          input={{
            placeholder:
              I18n.t('page_resume_tips_input') +
              I18n.t('page_resume_label_career_recent') +
              I18n.t('page_resume_label_career_pos'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangePosition,
            defaultValue: this.state.careerPosition,
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={
            I18n.t('page_resume_label_career_highest') + I18n.t('page_resume_label_career_edu')
          }
          chevron
          rightTitle={career ? career.qualificationId.label : ''}
          onPress={() =>
            this.selectConstant(
              'qualificationList',
              I18n.t('page_resume_label_career_edu'),
              'qualificationId'
            )
          }
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={
            <Text style={resumeStyle.listItemLeftText}>
              {I18n.t('page_resume_label_career_recent') +
                I18n.t('page_resume_label_career_category')}
              <Text style={[resumeStyle.listItemLeftText, { color: desColor }]}>
                {' '}
                {I18n.t('page_resume_tips_optional')}
              </Text>
            </Text>
          }
          chevron
          rightTitle={career ? career.careerCategory.label : ''}
          onPress={() =>
            this.selectConstant(
              'categoryList',
              I18n.t('page_resume_label_career_category'),
              'careerCategory'
            )
          }
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={
            <Text style={resumeStyle.listItemLeftText}>
              {I18n.t('page_resume_label_career_recent') +
                I18n.t('page_resume_label_career_industry')}
              <Text style={[resumeStyle.listItemLeftText, { color: desColor }]}>
                {' '}
                {I18n.t('page_resume_tips_optional')}
              </Text>
            </Text>
          }
          chevron
          rightTitle={career ? career.careerIndustry.label : ''}
          onPress={() =>
            this.selectConstant(
              'indutrialList',
              I18n.t('page_resume_label_career_industry'),
              'careerIndustry'
            )
          }
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={
            <Text style={resumeStyle.listItemLeftText}>
              {I18n.t('page_resume_label_career_recent') +
                I18n.t('page_resume_label_career_salary')}
              <Text style={[resumeStyle.listItemLeftText, { color: desColor }]}>
                {' '}
                {I18n.t('page_resume_tips_optional')}
              </Text>
            </Text>
          }
          chevron
          input={{
            placeholder:
              I18n.t('page_resume_tips_input') +
              I18n.t('page_resume_label_career_recent') +
              I18n.t('page_resume_label_career_salary'),
            keyboardType: 'numeric',
            maxLength: 6,
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeSalary,
            defaultValue: this.state.salary.toString(),
          }}
          containerStyle={resumeStyle.listItemContent}
        />

        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
