import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { Text, View, TouchableOpacity, InteractionManager, FlatList } from 'react-native';
import { Header } from 'react-native-elements';
import { headerStyle, interviewListStyle, desColor, baseBlueColor } from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';
import resource, { getEmployerAvatarSource } from '../res';
import LoadingModal from '../components/loadingModal';
import CommonCustomTabBar from '../components/commonCustomTabBar';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('jobStore', 'jobAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.state = {
      showLoading: true,
      page: 1,
      hasMore: false,
      refreshing: false,
      interviewStatus: -1,
      currentTab: 0,
      list: [],
    };
  }

  async componentDidMount() {
    await InteractionManager.runAfterInteractions(() => {
      this.getJobs({ interviewStatus: -1, isPull: true });
    });
  }

  onRefresh = () => {
    const { interviewStatus } = this.state;
    this.setState({ refreshing: true });
    this.state.page = 1;
    this.getJobs({ interviewStatus, isPull: true });
    this.setState({ refreshing: false });
  };

  onLoadMore = () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      const { interviewStatus } = this.state;
      this.state.page += 1;
      this.isLoadingMore = true;
      this.getJobs({ interviewStatus, isPull: false });
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  onChangeTab = (obj) => {
    this.state.page = 1;
    this.setState({ currentTab: obj.i, interviewStatus: obj.i === 0 ? -1 : obj.i === 1 ? 1 : 0 });
    this.getJobs({ interviewStatus: obj.i === 0 ? -1 : obj.i === 1 ? 1 : 0, isPull: true });
  };

  onToInterviewDetail = (item) => {
    this.props.navigation.navigate('interviewDetail', { id: item.id, isChat: false });
  };

  getJobs = (data) => {
    const { jobAction } = this.props;
    jobAction
      .queryDeliveredJobs(
        Object.assign(
          {
            page: this.state.page,
            size: 10,
            status: [3, 4],
          },
          data
        )
      )
      .then(
        (res) => {
          this.setState({
            list: data.isPull ? res.result : this.state.list.concat(res.result),
            hasMore: data.isPull
              ? res.result.length < res.totalCount
              : this.state.list.length < res.totalCount,
            showLoading: false,
          });
        },
        (err) => {
          console.log(err);
          this.setState({ showLoading: false });
        }
      );
  };

  renderScorllableTableview = () => (
    <View style={{ height: 44 }}>
      <ScrollableTabView
        renderTabBar={() => <CommonCustomTabBar currentIndex={this.state.currentTab} />}
        style={interviewListStyle.scorllContainer}
        initialPage={0}
        tabBarBackgroundColor={baseBlueColor}
        onChangeTab={(obj) => {
          this.onChangeTab(obj);
        }}
      >
        <Text
          style={interviewListStyle.textStyle}
          tabLabel={I18n.t('page_mine_interview_nav_title_interviewing')}
        />
        <Text
          style={interviewListStyle.textStyle}
          tabLabel={I18n.t('page_mine_interview_nav_title_interviewed')}
        />
        <Text
          style={interviewListStyle.textStyle}
          tabLabel={I18n.t('page_mine_interview_nav_title_not_interview')}
        />
      </ScrollableTabView>
    </View>
  );

  renderInterviewList = ({ item }) => (
    <TouchableOpacity key={item.id} onPress={() => this.onToInterviewDetail(item)}>
      <View style={interviewListStyle.listContainer}>
        <View style={interviewListStyle.namePannel}>
          <ImageBackground
            imageStyle={{ borderRadius: 21 }}
            style={interviewListStyle.companyLogo}
            source={getEmployerAvatarSource(item.employerLogo ? item.employerLogo : '')}
          >
            {item &&
            item.employerQualificationType &&
            item.employerQualificationType.value === 1 ? (
              <Image style={interviewListStyle.v2} source={resource.verify} />
            ) : (
              <View />
            )}
          </ImageBackground>
          <View style={interviewListStyle.listItemContainer}>
            <Text numberOfLines={1} style={interviewListStyle.companySubcribe}>
              {item ? item.company : ''}{' '}
              {item && item.qualificationStatus && item.qualificationStatus.value === 1 ? (
                <Image source={resource.iconVerify} style={{ width: 9, height: 9 }} />
              ) : null}
            </Text>
            <View style={interviewListStyle.jobInfo}>
              <Text
                numberOfLines={1}
                style={[interviewListStyle.nameSubcribe, { flexShrink: 50, paddingRight: 6 }]}
              >
                {item ? item.jobTitle : ''}
              </Text>
              <Text
                style={[
                  interviewListStyle.nameSubcribe,
                  {
                    color:
                      item.interviewStatus && item.interviewStatus.value === -1
                        ? baseBlueColor
                        : desColor,
                    textAlign: 'right',
                    flexShrink: 1,
                  },
                ]}
              >
                {item && item.interviewStatus ? item.interviewStatus.label : ''}
              </Text>
            </View>
            <View style={interviewListStyle.wagesInfo}>
              <Text numberOfLines={1} style={[interviewListStyle.jobWages, { flexShrink: 50 }]}>
                {item && item.salaryId ? item.salaryId.label : ''}
              </Text>
              <Text
                style={[
                  interviewListStyle.nameSubcribe,
                  { color: desColor, textAlign: 'right', flexShrink: 1 },
                ]}
              >
                {item.actionTime}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={resource.noData} />
    </View>
  );

  render() {
    const { navigation } = this.props;
    const { list } = this.state;
    return (
      <View style={interviewListStyle.viewContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_mine_label_Interview'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderScorllableTableview()}
        <FlatList
          data={list}
          renderItem={this.renderInterviewList}
          keyExtractor={(item, index) => index + item}
          showsVerticalScrollIndicator={false}
          horizontal={false}
          ListEmptyComponent={this.renderEmptyComponent}
          ListFooterComponent={this.renderFooter}
          refreshing={this.state.refreshing}
          onRefresh={this.onRefresh}
          onEndReachedThreshold={0.1}
          onEndReached={() => this.onLoadMore()}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
