import React, { Component } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import { Header } from 'react-native-elements';
import { headerStyle, companyDetailStyle, titleColor, baseBlueColor } from '../themes';
import GoBack from '../components/goback';

export default class PreviewWeb extends Component {
  render() {
    const { navigation } = this.props;
    const {
      params: { url, title },
    } = this.props.navigation.state;
    return (
      <View style={companyDetailStyle.jobContainer}>
        <Header
          statusBarProps={
            global.IS_ANDROID
              ? { barStyle: 'light-content', backgroundColor: baseBlueColor }
              : { barStyle: 'dark-content' }
          }
          containerStyle={[headerStyle.wrapper, { backgroundColor: '#fff' }]}
          centerComponent={{ text: title, style: [headerStyle.center, { color: titleColor }] }}
          leftComponent={<GoBack navigation={navigation} iconColor={titleColor} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <WebView source={{ uri: url }} startInLoadingState domStorageEnabled javaScriptEnabled />
      </View>
    );
  }
}
