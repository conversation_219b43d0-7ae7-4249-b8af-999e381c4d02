import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View } from 'react-native';
import QxhWebView from '../components/qxhWebview';
import articleHtml from './articleHtml';
import { Header } from 'react-native-elements';
import GoBack from '../components/goback';
import { headerStyle } from '../themes';

/**
 * 文章详情
 * <AUTHOR>
 */
@inject('resourceAction')
@observer
export default class ArticleDetail extends Component {
  constructor(props) {
    super(props);
    this.articleId = props.navigation.getParam('articleId');
    this.state = {
      showLoadError: false,
      item: null,
    };
  }

  componentDidMount() {
    this.getDetail();
  }

  getDetail = async () => {
    console.log('777777777777', this.articleId);
    if (!this.articleId) {
      return;
    }
    try {
      const data = await this.props.resourceAction.getAdviserDetail(this.articleId);
      this.setState({ item: data, showLoadError: false });
    } catch (error) {
      console.error(error);
      this.setState({ showLoadError: true });
    }
  };

  renderContent = () => {
    const { item, showLoadError } = this.state;
    const html = articleHtml.replace('articleDescription', item?.content);
    return (
      <QxhWebView
        source={{ html }}
        showsVerticalScrollIndicator={false}
        style={{
          marginHorizontal: 18,
          marginTop: 10,
          paddingBottom: 20,
          backgroundColor: '#00000000',
        }}
      />
    );
  };

  render() {
    const { item } = this.state;
    return (
      <View style={{ flex: 1, backgroundColor: '#fff' }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: item?.title,
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={this.props.navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderContent()}
      </View>
    );
  }
}
