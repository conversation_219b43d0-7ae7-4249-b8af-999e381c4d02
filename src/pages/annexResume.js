/**
 * 功能：附件简历
 * 描述：
 * Author:孙宇强
 */
import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  ImageBackground,
} from 'react-native';
import { <PERSON><PERSON>, Icon, Button } from 'react-native-elements';
import ActionSheet from 'react-native-actionsheet';
import Toast from 'react-native-easy-toast';
import { globalStyle, headerStyle, baseBlueColor, annexResumeStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import res from '../res';
import LoadingModal from '../components/loadingModal';
import constant from '../store/constant';
import util from '../util';
import AnnexPreview from '../components/annexPreview';

@inject('resumeStore', 'resumeAction')
@observer
export default class AnnexResume extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showTips: true,
      showLoading: false,
      options: [],
    };
  }

  componentDidMount() {
    this.setState({ showLoading: true });
    this.props.resumeAction.getAnnexResumes().then(
      () => {
        this.setState({ showLoading: false });
      },
      () => {
        this.setState({ showLoading: false });
      }
    );
  }

  onClose = () => {
    this.setState({ showTips: false });
  };

  resumeAdd = () => {
    this.props.navigation.navigate('uploadAnnex');
  };

  showRequestResult(result) {
    if (result && result.successful) {
      this.toast.show(result.message);
      this.props.resumeAction.getAnnexResumes();
    } else {
      this.toast.show(result.message);
    }
  }

  onActionSheet = (index) => {
    const { currentAnnexResume } = this.props.resumeStore;
    if (index === 0) {
      this.annexPreview.openFile({
        name: currentAnnexResume.name,
        url: currentAnnexResume.downloadHttp,
        fileName: currentAnnexResume.fileName,
        resumeId: currentAnnexResume.cvId,
      });
    } else if (index === 1) {
      this.props.navigation.navigate('resumeName', {
        resumeName: currentAnnexResume.name,
        resumeType: constant.resumeType.annex,
      });
    } else if (index === 2) {
      const value = !currentAnnexResume.defaultCv;
      this.props.resumeAction.setDefaultResume(currentAnnexResume.cvId, value).then((result) => {
        this.props.resumeAction.getResumes();
        this.showRequestResult(result);
      });
    } else if (index === 3) {
      Alert.alert('', I18n.t('page_resume_annex_alert_title'), [
        {
          text: I18n.t('page_resume_annex_alert_cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('page_resume_annex_alert_confirm'),
          onPress: () => {
            this.props.resumeAction.deleteResume(currentAnnexResume.cvId).then((result) => {
              this.showRequestResult(result);
            });
          },
        },
      ]);
    }
  };

  onOption = (isDefault, item) => {
    this.props.resumeAction.changeCurrentAnnexResume(item);
    if (isDefault) {
      this.setState(
        {
          options: [
            I18n.t('page_resume_annex_preview'),
            I18n.t('page_resume_annex_rename'),
            I18n.t('page_resume_annex_default_cancel'),
            I18n.t('page_resume_annex_delete'),
            I18n.t('page_resume_annex_cancel'),
          ],
        },
        () => {
          this.actionSheet.show();
        }
      );
    } else {
      this.setState(
        {
          options: [
            I18n.t('page_resume_annex_preview'),
            I18n.t('page_resume_annex_rename'),
            I18n.t('page_resume_annex_default'),
            I18n.t('page_resume_annex_delete'),
            I18n.t('page_resume_annex_cancel'),
          ],
        },
        () => {
          this.actionSheet.show();
        }
      );
    }
  };

  renderTips = () => {
    const { showTips } = this.state;
    if (showTips) {
      return (
        <View style={annexResumeStyle.tipsContainer}>
          <Text style={annexResumeStyle.tipsText}>{I18n.t('page_resume_annex_max')}</Text>
          <TouchableOpacity onPress={() => this.onClose()} style={annexResumeStyle.tipsClose}>
            <Icon type="antDesign" name="close" size={15} color="#32A5E7" />
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  renderBottom = () => {
    const {
      resumeStore: { annexResumeList },
    } = this.props;

    return (
      <View style={annexResumeStyle.bottomContainer}>
        <Text style={annexResumeStyle.remarkText}>{I18n.t('page_resume_annex_tips')}</Text>
        <Button
          disabled={annexResumeList.length === 3}
          title={I18n.t('page_resume_annex_button_upload')}
          buttonStyle={{
            backgroundColor: baseBlueColor,
            height: 40,
            elevation: 0,
          }}
          titleStyle={{ fontSize: 16 }}
          onPress={() => {
            this.resumeAdd();
          }}
        />
      </View>
    );
  };

  renderEmptyComponent = () => (
    <View style={{ marginTop: '10%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderFileType = (fileType) => {
    if (fileType === constant.resumeFileType.pdf) {
      return res.resumePdf;
    }
    if (fileType === constant.resumeFileType.doc) {
      return res.resumeDoc;
    }
    if (fileType === constant.resumeFileType.docx) {
      return res.resumeDocx;
    }
    if (fileType === constant.resumeFileType.jpg) {
      return res.resumeJpg;
    }
    return res.resumePng;
  };

  renderResumeItem = ({ item, index }) => {
    const typeIndex = item.fileName.lastIndexOf('.');
    // const fileName = item.fileName.substring(0, typeIndex);
    const fileType = item.fileName.substring(typeIndex + 1, item.fileName.length);
    const fileSizeMB = parseFloat(item.fileSize, 10) / 1024.0 / 1024.0;
    let fileSizeMessage = '';
    if (fileSizeMB > 1) {
      fileSizeMessage = `${fileSizeMB.toFixed(1)}MB`;
    } else {
      const filesizeKB = parseFloat(item.fileSize, 10) / 1024.0;
      fileSizeMessage = `${filesizeKB.toFixed(1)}KB`;
    }
    const time = util.timestampToLongString1(item.createAt);
    const messageText = `${fileSizeMessage} ${time}${I18n.t('page_resume_annex_upload')}`;
    const isDefault = item.defaultCv || false;
    return (
      <View
        style={[
          annexResumeStyle.itemContainer,
          { borderTopColor: index === 0 ? '#fff' : '#EEEEEE' },
        ]}
      >
        <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
          <Image style={{ width: 33, height: 39 }} source={this.renderFileType(fileType)} />
          <View style={annexResumeStyle.nameContainer}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={annexResumeStyle.nameText}>{item.name}</Text>
              {isDefault ? (
                <ImageBackground
                  style={{
                    marginLeft: 10,
                    paddingHorizontal: 7,
                    height: 16,
                    justifyContent: 'center',
                  }}
                  source={res.resumeDefault}
                >
                  <Text style={{ color: '#fff', fontSize: 10 }}>
                    {I18n.t('page_resume_annex_text_default')}
                  </Text>
                </ImageBackground>
              ) : null}
            </View>
            <Text style={annexResumeStyle.timeText}>{messageText}</Text>
          </View>
        </View>
        <TouchableOpacity onPress={() => this.onOption(isDefault, item)}>
          <Icon
            type="entypo"
            name="dots-three-vertical"
            size={21}
            color="#cccccc"
            iconStyle={{ marginRight: -5 }}
          />
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    const {
      navigation,
      resumeStore: { annexResumeList },
    } = this.props;
    const { showLoading, options } = this.state;

    return (
      <View style={{ ...globalStyle.container, flex: 1, backgroundColor: '#fff' }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_resume_annex_title'), style: headerStyle.center }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
        />
        {this.renderTips()}
        <FlatList
          style={annexResumeStyle.listContainer}
          data={annexResumeList}
          renderItem={this.renderResumeItem}
          keyExtractor={(item, index) => index + item}
          showsVerticalScrollIndicator={false}
          horizontal={false}
          ListEmptyComponent={this.renderEmptyComponent}
        />
        {this.renderBottom()}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <ActionSheet
          ref={(ref) => {
            this.actionSheet = ref;
          }}
          options={options}
          cancelButtonIndex={4}
          destructiveButtonIndex={3}
          onPress={this.onActionSheet}
        />
        <LoadingModal isOpen={showLoading} loadingTips={false} />
        <AnnexPreview
          ref={(ref) => {
            this.annexPreview = ref;
          }}
        />
      </View>
    );
  }
}
