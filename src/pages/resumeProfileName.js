import React, { Component } from 'react';
// import { inject } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import { globalStyle, headerStyle, resumeStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import regExp from '../util/regExp';

export default class Name extends Component {
  constructor(props) {
    super(props);
    const name1 = (this.props.navigation.state.params.firstName || '').trim();
    const name2 = (this.props.navigation.state.params.lastName || '').trim();
    this.state = {
      postData: {
        first: name1,
        last: name2,
      },
      first: name1,
      last: name2,
    };
  }

  onChangeName = (text) => {
    this.updateValue('first', text);
  };

  onChangeName1 = (text) => {
    this.updateValue('last', text);
  };

  updateValue(key, value) {
    const temp = this.state.postData;
    temp[key] = value;
    this.setState({
      postData: temp,
    });
  }

  save() {
    const data = this.state.postData;
    if (data.first.trim() === '' || !regExp.nameExtra.test(data.first.trim())) {
      this.toast.show(I18n.t('page_resume_op_first_name_required'));
      return;
    }
    if (data.last.trim() === '' || !regExp.nameExtra.test(data.last.trim())) {
      this.toast.show(I18n.t('page_resume_last_name_required'));
      return;
    }
    this.props.navigation.state.params.callBack(this.state.postData);
    this.props.navigation.goBack();
  }

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_resume_label_name'), style: headerStyle.center }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={() => this.save()}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_ph_first_name')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_ph_first_name'),
            inputStyle: [resumeStyle.inputText, { marginRight: 0 }],
            onChangeText: this.onChangeName,
            defaultValue: this.state.first ? this.state.first : '',
            maxLength: 15,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_ph_last_name')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_ph_last_name'),
            inputStyle: [resumeStyle.inputText, { marginRight: 0 }],
            onChangeText: this.onChangeName1,
            defaultValue: this.state.last ? this.state.last : '',
            maxLength: 15,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
