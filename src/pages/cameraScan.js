/**
 * 扫码上传
 * author： 孙宇强
 */
import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { RNCamera } from 'react-native-camera';
import qs from 'qs';
import { Text, View, Animated, Easing, StyleSheet } from 'react-native';
import Toast from 'react-native-easy-toast';
import { headerStyle } from '../themes';
import { Header } from 'react-native-elements';
import I18n from '../i18n';
import GoBack from '../components/goback';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  preview: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  rectangleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  rectangle: {
    height: 200,
    width: 200,
    borderWidth: 1,
    borderColor: '#00FF00',
    backgroundColor: 'transparent',
  },
  rectangleText: {
    flex: 0,
    color: '#fff',
    marginTop: 10,
  },
  border: {
    flex: 0,
    width: 200,
    height: 2,
    backgroundColor: '#00FF00',
  },
});

@inject('resumeStore', 'userAction')
@observer
export default class CameraScan extends Component {
  constructor(props) {
    super(props);
    this.state = {
      moveAnim: new Animated.Value(0),
    };
  }

  componentDidMount() {
    this.startAnimation();
  }

  startAnimation = () => {
    this.state.moveAnim.setValue(0);
    Animated.timing(this.state.moveAnim, {
      toValue: -200,
      duration: 1500,
      easing: Easing.linear,
    }).start(() => this.startAnimation());
  };

  //  识别二维码
  onBarCodeRead = ({ data }) => {
    const param = qs.parse(data);
    if (param.hasOwnProperty('token')) {
      this.scanLogin(data);
    }
  };

  scanLogin = (queryString) => {
    // 'token=E8363045082EE54F9BCAE599297D44B4.6598387429634347008&scanOpenType=Notification'
    this.props.userAction.scanLogin(queryString).then(
      (res) => {
        if (res && res.successful) {
          this.toast.show(I18n.t('page_scan_upload_login_success'));
          setTimeout(() => {
            this.props.navigation.goBack();
          }, 1000);
        } else {
          this.toast.show(res.message || I18n.t('page_scan_upload_login_error'));
        }
      },
      (err) => {
        this.toast.show(err.message || I18n.t('page_scan_upload_login_error'));
      }
    );
  };

  render() {
    const { navigation } = this.props;
    // const { showLoading } = this.state;

    return (
      <View style={styles.container}>
        <RNCamera
          ref={(ref) => {
            this.camera = ref;
          }}
          autoFocus
          style={styles.preview}
          type={RNCamera.Constants.Type.back}
          flashMode={RNCamera.Constants.FlashMode.on}
          onBarCodeRead={this.onBarCodeRead}
          androidCameraPermissionOptions={{
            title: I18n.t('page_scan_upload_camera_permission_title'),
            message: I18n.t('page_scan_upload_camera_permission_tips'),
            buttonPositive: I18n.t('page_scan_upload_camera_permission_confirm'),
            buttonNegative: I18n.t('page_scan_upload_camera_permission_cancel'),
          }}
        >
          <Header
            statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
            containerStyle={headerStyle.wrapper}
            centerComponent={{
              text: I18n.t('page_scan_upload_camera_title'),
              style: headerStyle.center,
            }}
            leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          />
          <View style={styles.rectangleContainer}>
            <View style={styles.rectangle} />
            <Animated.View
              style={[styles.border, { transform: [{ translateY: this.state.moveAnim }] }]}
            />
            <Text style={styles.rectangleText}>
              {I18n.t('page_scan_upload_camera_scan_message')}
            </Text>
          </View>
        </RNCamera>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
