import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import { <PERSON><PERSON>, <PERSON><PERSON>, Icon } from 'react-native-elements';
import Swipeout from 'react-native-swipeout';
import Toast from 'react-native-easy-toast';
import {
  globalStyle,
  headerStyle,
  jobIntensionStyle,
  resumeEditStyle,
  dynamicStyle,
  desColor,
} from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import util from '../util';

@inject('resumeStore', 'resumeAction')
@observer
export default class Work extends Component {
  constructor(props) {
    super(props);
    this.state = {
      edit: false,
    };
  }

  editPress = () => {
    const { currentEducationLsit } = this.props.resumeStore;
    if (currentEducationLsit && currentEducationLsit.length === 0 && !this.state.edit) {
      return;
    }
    if (this.state.edit) {
      this.props.resumeStore.currentEducationLsit = this.props.resumeStore.currentEducationLsit.map(
        (item1, index) => {
          item1.showDel = false;
          return item1;
        }
      );
    }
    this.setState({
      edit: !this.state.edit,
    });
  };

  iconDelPress(item) {
    this.props.resumeStore.currentEducationLsit = this.props.resumeStore.currentEducationLsit.map(
      (item1, index) => {
        item1.showDel = item1.eduId === item.eduId;
        return item1;
      }
    );
  }

  deletePress(item) {
    const { currentResume } = this.props.resumeStore;
    this.props.resumeAction.deleteEducation(item.eduId, currentResume.resumeId).then((res) => {
      if (res && res.successful) {
        this.toast.show(res.message);
        this.props.resumeAction.getResumes();
      } else {
        this.toast.show(res.message);
      }
    });
  }

  renderItem = ({ item }) => {
    const swipeoutBtns = [
      {
        text: I18n.t('page_resume_btn_del'),
        type: 'delete',
        onPress: () => {
          this.deletePress(item);
        },
      },
    ];
    return (
      <Swipeout
        right={swipeoutBtns}
        openRight={item.showDel}
        close={!item.showDel}
        autoClose
        key={item}
      >
        <TouchableOpacity
          onPress={() =>
            this.props.navigation.navigate('resumeEducationAdd', { data: item, edit: true })
          }
        >
          <View style={resumeEditStyle.container}>
            <View>
              {this.state.edit ? (
                <Icon
                  name="circle-with-minus"
                  type="entypo"
                  size={25}
                  iconStyle={resumeEditStyle.icon}
                  onPress={() => {
                    this.iconDelPress(item);
                  }}
                />
              ) : null}
            </View>
            <View style={[resumeEditStyle.majorContainer, { marginRight: 5 }]}>
              <View style={{ flexGrow: 20, flexShrink: 200 }}>
                <Text style={resumeEditStyle.timeText}>
                  {`${util.dateToYM1String(item.fromDate)} - ${util.dateToYM1String(item.toDate)}`}
                </Text>
                <Text numberOfLines={1} style={resumeEditStyle.titleText}>
                  {item.name}
                </Text>
                <View style={resumeEditStyle.majorContainer}>
                  <Text style={resumeEditStyle.desText}>
                    {item.qualificationId ? item.qualificationId.label : ''}
                  </Text>
                  <View style={resumeEditStyle.spaceLine} />
                  <Text style={[resumeEditStyle.desText, { marginLeft: 0 }]}>{item.major}</Text>
                </View>
              </View>
              <View style={{ flexGrow: 1 }}>
                <Icon
                  name="chevron-thin-right"
                  type="entypo"
                  size={16}
                  iconStyle={[resumeEditStyle.icon, { color: desColor, marginRight: 6 }]}
                />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Swipeout>
    );
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_education'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.editPress}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {this.state.edit ? I18n.t('page_resume_btn_done') : I18n.t('page_resume_btn_edit')}
              </Text>
            </TouchableOpacity>
          }
        />
        <FlatList
          data={this.props.resumeStore.currentEducationLsit}
          renderItem={this.renderItem}
          keyExtractor={(item) => item.eduId}
          ItemSeparatorComponent={() => <View style={dynamicStyle.separatorLine} />}
        />
        <View style={jobIntensionStyle.submitButton}>
          <Button
            title={I18n.t('page_resume_btn_edu_add')}
            buttonStyle={jobIntensionStyle.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={() => {
              navigation.navigate('resumeEducationAdd', { data: {}, edit: false });
            }}
          />
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
