import React, { Component } from 'react';
import { ScrollView, View, TouchableOpacity, Text } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Input, Header, Icon, ListItem } from 'react-native-elements';
import { headerStyle, titleColor, desColor, jobStyle, baseBlueColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import Toast from 'react-native-easy-toast';

@inject('jobStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      industryList: [],
    };
  }

  componentDidMount() {
    const temp = [];
    const {
      jobStore: { indutrialList },
    } = this.props;
    const { industrys } = this.props.navigation.state.params;
    for (let i = 0, len1 = indutrialList.length; i < len1; i += 1) {
      const item1 = indutrialList[i];
      for (let j = 0, len2 = industrys.length; j < len2; j += 1) {
        const item2 = industrys[j];
        if (item1.value === item2.value) {
          item1.isSelected = true;
          continue;
        }
      }
      temp.push(item1);
    }
    this.setState({ industryList: temp });
  }

  onSelectIndutrial = (item) => {
    item.isSelected = !item.isSelected;
    const { industryList } = this.state;
    const selectArr = industryList.filter((l) => l.isSelected === true);
    if (selectArr.length > 3) {
      item.isSelected = false;
      this.toast.show(I18n.t('page_job_toast_text'));
    }
  };

  onChangeText = (text) => {
    const {
      jobStore: { indutrialList },
    } = this.props;
    let temp = indutrialList;
    temp = temp.filter((item) => item.label.indexOf(text) > -1);
    if (!text) {
      this.setState({ industryList: indutrialList });
    } else {
      this.setState({ industryList: temp });
    }
  };

  saveIndustry = () => {
    const { navigation } = this.props;
    const { industryList } = this.state;
    const selectArr = industryList.filter((item) => item.isSelected === true);
    if (navigation.state.params) {
      navigation.state.params.onSelect(selectArr);
      navigation.goBack();
    }
  };

  renderIndutrialList = () => {
    const { industryList } = this.state;
    return industryList.map((item) => (
      <ListItem
        key={item.value}
        containerStyle={{
          borderBottomColor: '#eee',
          borderBottomWidth: 0.5,
          paddingHorizontal: 12,
          paddingVertical: 12,
          backgroundColor: item.isSelected ? '#E1F2FC' : '#fff',
        }}
        // eslint-disable-next-line react/jsx-no-bind
        onPress={this.onSelectIndutrial.bind(this, item)}
        rightIcon={
          item.isSelected ? (
            <Icon type="ionicon" name="ios-checkmark-circle" size={24} color={baseBlueColor} />
          ) : (
            <Icon type="ionicon" name="ios-checkmark-circle" size={24} color="#fff" />
          )
        }
        title={item.label}
        titleStyle={{
          color: titleColor,
          fontSize: 14,
        }}
      />
    ));
  };

  renderSearch = () => (
    <View style={jobStyle.jobExpectedSearch}>
      <Input
        inputContainerStyle={{
          backgroundColor: '#fff',
          marginVertical: 10,
          borderBottomWidth: 0,
          borderRadius: 6,
          marginLeft: 0,
          paddingVertical: 2,
          height: 38,
        }}
        onSubmitEditing={this.onSubmitEditing}
        onChangeText={(text) => this.onChangeText(text)}
        inputStyle={{
          color: titleColor,
          fontSize: 14,
        }}
        clearButtonMode="while-editing"
        returnKeyType="search"
        placeholder={I18n.t('page_job_ph_expected_industry')}
        placeholderTextColor={desColor}
        leftIcon={<Icon type="evilicon" name="search" size={30} color={desColor} />}
        leftIconContainerStyle={{
          marginLeft: 2,
        }}
      />
    </View>
  );

  render() {
    const { navigation } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_job_expected_industy'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
          rightComponent={
            <TouchableOpacity onPress={this.saveIndustry}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_job_btn_coonfirm')}
              </Text>
            </TouchableOpacity>
          }
        />
        {this.renderSearch()}
        <ScrollView>
          <View style={{ backgroundColor: '#fff' }}>{this.renderIndutrialList()}</View>
        </ScrollView>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
