import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { AlertPro, Text, Touchable, View, Icon } from '../../components';
import I18n from '../../i18n';
import styles from '../../themes';
import { footerHeight } from '../../common';
import NavigationService from '../../navigationService';
import { SwipeListView } from 'react-native-swipe-list-view';
import NoData from '../../components/empty/noData';
import ActionSheet from '../../components/modal/actionSheet';
import { Head<PERSON>, But<PERSON> } from 'react-native-elements';
import GoBack from '../../components/goback';
import { headerStyle, baseBlueColor } from '../../themes';
import _ from 'lodash';
import DraggableFlatList from 'react-native-draggable-flatlist';

function getComponentStyle(theme) {
  return {
    chatContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerText: {
      fontSize: 18,
      color: '#fff',
    },
    headerTextNum: {
      fontSize: 18,
      color: 'orange',
      fontWeight: theme.fontWeightBold,
    },
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: '#ffffff',
      paddingVertical: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#f2f2f2',
    },
    itemText: {
      fontSize: 14,
      color: '#333',
      flexShrink: 1,
      marginRight: 10,
    },
    subContainer: {
      flex: 1,
      paddingHorizontal: 10,
    },
    backTextWhite: {
      color: '#ffffff',
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightBold,
    },
    rowBack: {
      alignItems: 'center',
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingLeft: 15,
    },
    backRightBtn: {
      alignItems: 'center',
      bottom: 0,
      justifyContent: 'center',
      position: 'absolute',
      top: 0,
      width: 75,
      backgroundColor: '#FF4B5A',
    },
    backRightBtnRight: {
      right: 0,
    },
    btnContainer: {
      width: '100%',
      height: 44 + footerHeight,
      shadowColor: 'black',
      shadowOffset: { h: 2 },
    },
    chatBtn: {
      marginHorizontal: 15,
      backgroundColor: baseBlueColor,
      minHeight: 40,
      marginTop: 8,
    },
    dragHandle: {
      paddingHorizontal: 15,
      justifyContent: 'center',
    },
  };
}
/**
 * 聊天-快捷文本列表
 */
@inject('userStore', 'pageStore', 'settingsStore', 'imAction', 'chatStore', 'meetingAction')
@observer
export default class ChatQuickTextList extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      list: [],
      showAlert: false,
      deleteItem: '',
      isSort: false,
    };
  }

  componentDidMount() {
    this.loadData();
  }

  componentWillUnmount() {}

  loadData = async () => {
    const res = await new Promise((resolve, reject) => {
      resolve([
        {
          id: 1,
          key: '1',
          content: 'Hello! I am interested in this position, can I send you my resume?',
        },
        {
          id: 2,
          key: '2',
          content:
            'Hello, I am very interested in this position and I look forward to interviewing with you.',
        },
        {
          id: 3,
          key: '3',
          content:
            "I'm sorry, this position is not suitable for me. I hope you can find the right person soon.",
        },
        {
          id: 4,
          key: '4',
          content: 'I have received your message, thank you for your reply.',
        },
        {
          id: 5,
          key: '5',
          content: 'I am very interested in this position, can I send you my resume?',
        },
        {
          id: 6,
          key: '6',
          content:
            'Hello, I am very interested in this position and I look forward to interviewing with you.',
        },
      ]);
    });
    console.log('res123123', res);
    this.setState({ list: res });
  };

  onDeleteItem = (item) => {
    this.setState({
      showAlert: true,
      deleteItem: item,
      alertContent: I18n.t('page_chat_tips_delete_quick_text'),
    });
  };

  onDeleteItemConfirm = async () => {
    try {
      await this.props.imAction.deleteSession(this.state.deleteItem);
      this.setState({ showAlert: false });
    } catch (e) {
      logger.warn('onDeleteItemConfirm', e);
    }
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  onSort = () => {
    this.setState({ isSort: !this.state.isSort, list: _.cloneDeep(this.state.list) });
  };

  onDragEnd = ({ data }) => {
    this.setState({ list: data });
    // todo 保存排序结果
  };

  onAdd = (item) => {
    NavigationService.navigate('addQuickText', {
      item,
      onRefresh: () => {
        this.loadData();
      },
    });
  };

  onDrag = (item) => {
    if (item.id === 1) {
      return;
    }
    const { list } = this.state;
    const newList = list.filter((i) => i.id !== item.id);
    return newList;
  };

  renderAlert = () => {
    const { showAlert, alertContent } = this.state;
    if (!showAlert) return null;
    return (
      <AlertPro
        visible={showAlert}
        title={alertContent}
        textConfirm={I18n.t('op_confirm_title')}
        textCancel={I18n.t('op_cancel_title')}
        onConfirm={this.onDeleteItemConfirm}
        onCancel={this.onAlertCancel}
      />
    );
  };

  renderItem = ({ item, index, drag, isActive }) => {
    const { style } = this;
    const { isSort } = this.state;

    if (isSort) {
      return (
        <Touchable
          key={index}
          onLongPress={drag}
          style={[style.itemContainer, isActive && { backgroundColor: '#f0f0f0' }]}
        >
          <Text style={style.itemText}>{item.content}</Text>
          <View style={style.dragHandle}>
            <Icon type="ionicon" name="menu" size={20} color="#333" />
          </View>
        </Touchable>
      );
    } else {
      return (
        <Touchable key={index} withoutFeedback>
          <View style={style.itemContainer}>
            <Text style={style.itemText}>{item.content}</Text>
            <Touchable onPress={() => this.onAdd(item)}>
              <Icon type="feather" name="edit" size={16} color="#333" />
            </Touchable>
          </View>
        </Touchable>
      );
    }
  };

  renderList = () => {
    const { list, isSort } = this.state;
    const { style } = this;

    if (list.length === 0) return <NoData />;

    if (isSort) {
      return (
        <DraggableFlatList
          data={list}
          renderItem={this.renderItem}
          keyExtractor={(item) => item.key}
          onDragEnd={this.onDragEnd}
          activationDistance={10}
        />
      );
    } else {
      return (
        <SwipeListView
          showsVerticalScrollIndicator={false}
          disableRightSwipe
          useFlatList
          data={list}
          renderItem={this.renderItem}
          ListHeaderComponent={this.renderListHeaderComponent}
          renderHiddenItem={this.renderHiddenItem}
          rightOpenValue={-75}
          closeOnRowPress={true}
          closeOnRowBeginSwipe={true}
          closeOnScroll={true}
          useNativeDriver={false}
        />
      );
    }
  };

  renderHiddenItem = ({ item }, rowMap) => {
    const { style } = this;
    return (
      <View style={style.rowBack}>
        <Touchable
          style={[style.backRightBtn, style.backRightBtnRight]}
          onPress={() => {
            if (rowMap[item.key]) {
              rowMap[item.key].closeRow();
            }
            this.onDeleteChat(item);
          }}
        >
          <Text style={style.backTextWhite}>{I18n.t('page_resume_btn_del')}</Text>
        </Touchable>
      </View>
    );
  };

  renderListHeaderComponent = () => {
    return <View style={{}}>{this.props.renderListHeaderComponent?.()}</View>;
  };

  renderCenterComponent = () => {
    const { list } = this.state;
    const { style } = this;
    return (
      <View style={style.headerContainer}>
        <Text style={style.headerText}>{I18n.t('page_chat_quick_text')}</Text>
        <Text style={[style.headerTextNum, { marginLeft: 10 }]}>{list.length}</Text>
        <Text style={[style.headerTextNum, { fontSize: 14 }]}>/</Text>
        <Text style={style.headerTextNum}>10</Text>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { navigation } = this.props;
    const { list, isSort } = this.state;
    return (
      <View style={style.chatContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={this.renderCenterComponent}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <Touchable onPress={this.onSort}>
              <Text style={[headerStyle.rightBtn, { color: '#fff' }]}>
                {isSort ? I18n.t('page_resume_btn_save') : I18n.t('page_chat_text_sort')}
              </Text>
            </Touchable>
          }
        />
        <View style={style.subContainer}>{this.renderList()}</View>
        <View style={[style.btnContainer, { marginBottom: 5 }]}>
          <Button
            title={I18n.t('page_chat_add_quick_text')}
            buttonStyle={style.chatBtn}
            titleStyle={{ fontSize: 15 }}
            onPress={() => this.onAdd()}
          />
        </View>
        {this.renderAlert()}
        <ActionSheet page="chat" />
      </View>
    );
  }
}
