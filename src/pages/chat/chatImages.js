import React, { Component } from 'react';
import { ScrollView, View, Touchable, Image } from '../../components';
import styles from '../../themes';
import Header from '../../components/header/header';
import resIcon from '../../res';
import { deviceWidth } from '../../common';
import ViewImagesModal from './components/viewImagesModal';
import SafeView from '../../components/safeView';

function getComponentStyle(theme) {
  return {
    addFriendContainer: {
      flex: 1,
      backgroundColor: 'rgba(0,0,0,1)',
    },
    scrollViewContainer: {
      paddingTop: 16,
      flex: 1,
    },
    searchBox: {
      marginTop: 20,
    },
    searchBoxInputStyle: {
      fontSize: 16,
      marginLeft: 8,
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
    imageBox: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
  };
}
/**
 * 聊天全部图片
 * <AUTHOR>
 */
export default class ChatImages extends Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const images = props.navigation.getParam('images', []);
    this.state = {
      searchValue: '',
      images,
    };
  }

  // 输入搜索内容
  onChangeText = (text) => {
    this.setState({ searchValue: text });
  };

  // 搜索
  onSubmitEditing = async () => {
    const { searchValue } = this.state;
    if (!searchValue) {
    }
  };

  onViewImages = (index) => {
    const { images } = this.state;
    this.viewImagesModal.open({ images, index });
  };

  renderAllImages = () => {
    const { style } = this;
    const { images } = this.state;
    return (
      <View style={style.imageBox}>
        {images.map((item, index) => (
          <Touchable
            key={index.toString()}
            onPress={() => this.onViewImages(index)}
            style={{ marginBottom: 1, marginRight: index % 3 === 0 ? 0 : 1 }}
          >
            <Image
              source={item.url ? { uri: item.url } : resIcon.defaultAvatar}
              style={{ width: (deviceWidth - 3) / 4, height: (deviceWidth - 3) / 4 }}
            />
          </Touchable>
        ))}
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.addFriendContainer}>
        <Header
          title="全部图片"
          containerStyle={{ backgroundColor: '#000000' }}
          leftIconColor="#ffffff"
          titleStyle={{ color: '#ffffff' }}
          barStyle="light-content"
        />
        <ScrollView
          style={style.scrollViewContainer}
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}
        >
          {this.renderAllImages()}
          <SafeView bottomHeight={30} />
        </ScrollView>
        <ViewImagesModal
          ref={(ref) => (this.viewImagesModal = ref)}
          navigation={this.props.navigation}
          showMore={false}
          statusBarColor="#000"
        />
      </View>
    );
  }
}
