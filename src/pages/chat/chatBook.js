import React, { Component } from 'react';
import { Image, Modal, Text, Touchable, TouchableOpacity, View } from '../../components';
import { inject, observer } from 'mobx-react';
import { computed } from 'mobx';
import styles from '../../themes';
import Header from '../../components/header/header';
import resIcon from '../../res';
import SearchBox from '../../components/input/searchBox';
import BookList from './components/bookList';
import NavigationService from '../../navigationService';
import constant from '../../store/constant';
import contactDao from '../../database/dao/contactDao';
import CheckedItem from './components/checkedItem';
import Debounce from 'debounce-decorator';
import SafeView from '../../components/safeView';
import I18n from '../../i18n';
import Avatar from '../../components/avatar/avatar';
import { footerHeight } from '../../common';
import SetStatusModal from '../../components/modal/setStatusModal';

function getComponentStyle(theme) {
  return {
    chatBookContainer: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    bookContain: {
      flex: 1,
      // height: '100%',
      marginTop: -30,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      overflow: 'hidden',
    },
    createHeadBox: {},
    createHead: {
      // marginTop: -30,
      paddingTop: 15,
      // borderTopLeftRadius: 25,
      // borderTopRightRadius: 25,
      // overflow: 'hidden',
      paddingHorizontal: 15,
      backgroundColor: '#FFFFFF',
    },
    createItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: 4,
      paddingVertical: 10,
    },
    chatGroup: {
      width: 24,
      height: 24,
    },
    createText: {
      color: theme.titleFontColor,
      fontSize: theme.memoSize,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 21,
      marginLeft: 13,
      flex: 1,
    },
    newFirNumBox: {
      marginLeft: 13,
      paddingHorizontal: 9,
      height: 16,
      backgroundColor: '#FF4B5A',
      borderRadius: 9,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    newFirNum: {
      fontSize: 12,
      fontWeight: '500',
      color: '#FFFFFF',
    },
    lineHeight: {
      height: 10,
      backgroundColor: '#F5F8FB',
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
    tempView: {
      height: 40,
      backgroundColor: theme.primaryColor,
    },
    userAvatarBox: {
      borderRadius: 14,
      width: 36,
      height: 36,
      borderWidth: 4,
      borderColor: 'rgba(255,255,255,0.2)',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      shadowOffset: {
        width: -5,
        height: 0,
      },
      shadowRadius: 20,
      shadowColor: 'rgba(0,0,0,0.15)',
      shadowOpacity: 1,
      marginLeft: -5,
    },
    userAvatar: {
      width: 32,
      height: 32,
      borderRadius: 12,
    },
    specialStyleBox: {},
    specialStyle: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 14,
      marginVertical: 10,
    },
    specialFront: {
      flexDirection: 'row',
      alignItems: 'center',
      height: 20,
      backgroundColor: '#ECF5F6',
      borderRadius: 5,
      paddingHorizontal: 6,
    },
    specialText: {
      fontSize: 12,
      color: theme.primaryColor,
      marginLeft: 5,
    },
    itemInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: 18,
      paddingVertical: 10,
    },
    sessionListItem1: {
      paddingVertical: 6,
      color: '#333333',
      fontSize: 16,
      lineHeight: 22,
      marginLeft: 14,
    },
    btmLine: {
      height: 1,
      backgroundColor: 'rgba(220, 224, 235, 0.4)',
      marginLeft: 72,
      marginRight: 15,
    },
    addmodal: {
      flex: 1,
    },
    chatInformation: {
      position: 'absolute',
      right: 0,
      bottom: 0,
    },
    addBox: {
      position: 'absolute',
      top: footerHeight + 54,
      right: 14,
      backgroundColor: '#F1FEFF',
      borderRadius: 10,
      paddingLeft: 19,
      paddingRight: 11,
      paddingVertical: 5,
      width: 180,
    },
    addItem: {
      borderBottomWidth: 1,
      borderColor: '#EEEEEE',
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 10,
    },
    addItemText: {
      fontSize: 16,
      color: '#333333',
      lineHeight: 22,
      marginLeft: 16,
    },
  };
}

/**
 * 通讯录
 */
@inject('userAction', 'userStore')
@observer
export default class ChatBook extends Component {
  constructor(props) {
    super(props);
    const { createGroup = false, isSingle, title, checkedList = [], disabledList = [], bookData } =
      props.navigation.state.params || {};
    this.state = {
      searchValue: '',
      newReqCount: 0,
      createGroup,
      isSingle,
      title,
      checkedList,
      disabledList,
      bookData,
      disabledBtn: false,
      showUserOption: false,
    };
  }

  style = getComponentStyle(styles.get('theme'));

  componentDidMount() {
    this.getNewContactCount();
    global.emitter.on(constant.event.addContact, this.getNewContactCount);
    global.emitter.on(constant.event.deleteContact, this.getNewContactCount);
    global.emitter.on(constant.event.tabbarChanged, this.onTabBarChanged);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.addContact, this.getNewContactCount);
    global.emitter.off(constant.event.deleteContact, this.getNewContactCount);
    global.emitter.off(constant.event.tabbarChanged, this.onTabBarChanged);
  }

  // 管理员数量
  @computed get managersLength() {
    const { managerMembers = [] } = this.props.navigation.state.params;
    const { checkedList } = this.state;
    return managerMembers.length + checkedList.length;
  }

  onTabBarChanged = ({ name }) => {
    if (this.props.isTab && name === constant.tabs.contacts) {
      this.bookList?.initData();
    }
  };

  getNewContactCount = async () => {
    if (this.state.createGroup) return;
    try {
      const res = await contactDao.queryReqContactList({ limit: 100 });
      const count = res.filter((x) => x.isSelfReq === 1 && x.status === 0 && x.isRead === 0).length;
      this.setState({ newReqCount: count });
    } catch (error) {}
  };

  // 输入搜索内容
  onChangeText = (text) => {
    this.setState({ searchValue: text }, () => {
      if (!text) {
        this.onSearchDebounce(text);
      }
    });
  };

  onSubmitEditing = () => {
    const { searchValue } = this.state;
    this.onSearchDebounce(searchValue);
  };

  @Debounce(300)
  onSearchDebounce(text) {
    this.bookList?.search(text);
  }

  onShowNewFirs = () => {
    this.setState({ newReqCount: 0 });
    NavigationService.navigate('newFriends');
  };

  onBookListRef = (ref) => (this.bookList = ref?.wrappedInstance);

  onAdd = () => {
    NavigationService.navigate('chatAddContact');
  };

  selectItem = (data) => {
    const { isAddMemberManager, isSelectUser } = this.props.navigation.state.params;
    if (isAddMemberManager && this.managersLength > 5) {
      this.setState({ disabledBtn: true });
      toast.show(I18n.t('page_chat_tips_group_can_add_max_manager'));
      return;
    }
    if (isSelectUser && this.managersLength > 1) {
      this.setState({ disabledBtn: true });
      return;
    }
    this.setState({ checkedList: data, disabledBtn: false });
  };

  onNext = () => {
    const { addCheck, isAddMemberManager, isGoBack = true } = this.props.navigation.state.params;
    if (addCheck) {
      if (isAddMemberManager && this.managersLength > 5) {
        toast.show(I18n.t('page_chat_tips_group_can_add_max_manager'));
        return;
      }
      addCheck(this.state.checkedList);
      if (isGoBack) NavigationService.goBack();
    } else {
      NavigationService.replace('groupCreate', { checkedList: this.state.checkedList });
    }
  };

  onGroupList = () => {
    NavigationService.navigate('chatGroupList');
  };

  onTagList = () => {
    NavigationService.navigate('friendTagList');
  };

  onSelfClick = () => {
    NavigationService.navigate('chatAddContactDetail', { imId: this.props.userStore.imId });

    // this.setState({ showUserOption: !this.state.showUserOption });
  };

  onSelfInfo = () => {
    this.onSelfClick();
  };

  onSelfStatus = () => {
    this.onSelfClick();
    this.setStatusModal.show();
  };

  onSpecialAttentionClick = () => {
    NavigationService.navigate('specialUser');
  };

  renderUserSetting = () => {
    const { style } = this;
    const { showUserOption } = this.state;
    return (
      <View>
        <Modal visible={showUserOption} transparent onRequestClose={this.onSelfClick}>
          <View style={style.addmodal}>
            <TouchableOpacity
              activeOpacity={1}
              focusedOpacity={1}
              style={[{ flex: 1 }]}
              onPress={this.onSelfClick}
            >
              <View style={[style.addBox, { left: 14, top: style.addBox.top + 5 }]}>
                <Touchable onPress={this.onSelfInfo} style={style.addItem}>
                  <Image source={resIcon.chatTooltipsMedia} resizeMode="contain" />
                  <Text style={style.addItemText}>{I18n.t('page_chat_text_personal_info')}</Text>
                </Touchable>
                <Touchable onPress={this.onSelfStatus} style={style.addItem}>
                  <Image source={resIcon.chatTooltipsBroadcast} resizeMode="contain" />
                  <Text style={style.addItemText}>{I18n.t('page_chat_text_status_setting')}</Text>
                </Touchable>
              </View>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    );
  };

  rightComponent = () => {
    return (
      <Touchable onPress={this.onAdd}>
        <Image source={resIcon.chatbookAddFir} style={{ width: 25, height: 25 }} />
      </Touchable>
    );
  };

  renderHeader = () => {
    const { style } = this;
    const { newReqCount, createGroup, searchValue } = this.state;
    return (
      <View style={style.createHeadBox}>
        {/* {!createGroup ? <View style={style.tempView} /> : null} */}
        <View style={[style.createHead, { marginTop: !createGroup ? 0 : 0 }]}>
          <SearchBox
            onChangeText={this.onChangeText}
            onSubmitEditing={this.onSubmitEditing}
            value={searchValue}
            closeIconStyle={{ marginRight: 5 }}
            rightIcon={() => (
              <Touchable onPress={this.onSubmitEditing} style={style.searchTextBox}>
                <Text style={style.searchBtn}>{I18n.t('op_search_title')}</Text>
              </Touchable>
            )}
          />
          {!createGroup && !searchValue?.trim() ? (
            <View>
              {this.renderHeaderItem({
                img: resIcon.chatNewFir,
                title: I18n.t('page_chat_new_friend'),
                onPress: this.onShowNewFirs,
                newReqCount,
                marginTop: 10,
              })}
              {this.renderHeaderItem({
                img: resIcon.chatGroupList,
                title: I18n.t('page_chat_group'),
                onPress: this.onGroupList,
                newReqCount: 0,
              })}
              {/*{this.renderHeaderItem({
                img: resIcon.chatTag,
                title: I18n.t('page_friendTagList_title'),
                onPress: this.onTagList,
                newReqCount: 0,
              })}*/}
            </View>
          ) : (
            <View style={{ height: 16 }} />
          )}
        </View>
      </View>
    );
  };

  renderHeaderItem = ({ img, title, newReqCount, onPress, marginTop = 0 }) => {
    const { style } = this;
    return (
      <Touchable style={[style.createItem, { marginTop }]} onPress={onPress}>
        <Image source={img} style={style.chatGroup} resizeMode="contain" />
        <Text style={style.createText}>{title}</Text>
        {newReqCount > 0 ? (
          <View style={style.newFirNumBox}>
            <Text style={style.newFirNum}>{newReqCount}</Text>
          </View>
        ) : null}
        {/* <RightArrow style={{ marginLeft: 5 }} color="#999" /> */}
        <Image source={resIcon.chatBookArrow} style={{ marginLeft: 10 }} />
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    const { themeStyle } = styles.get(['theme']);
    const {
      createGroup,
      isSingle,
      title,
      checkedList,
      disabledList,
      bookData,
      disabledBtn,
    } = this.state;
    return (
      <View style={style.chatBookContainer}>
        {createGroup ? (
          <Header title={title || I18n.t('page_chat_select_member')} />
        ) : (
          <Header
            title={I18n.t('page_chat_text_contact_user')}
            rightComponent={this.rightComponent}
            theme="light"
            leftComponent={
              <Touchable onPress={this.onSelfClick} style={style.userAvatarBox}>
                <Avatar style={style.userAvatar} dotStyle={{ right: -2 }} showDot />
              </Touchable>
            }
          />
        )}
        {!createGroup ? <View style={style.tempView} /> : null}

        <View style={[style.bookContain, !createGroup ? {} : { marginTop: 0 }]}>
          <BookList
            ref={this.onBookListRef}
            onPick={this.selectItem}
            isCheck={createGroup && !isSingle}
            isSingle={isSingle}
            checkedList={checkedList}
            disabledList={disabledList}
            bookData={bookData}
            navigation={this.props.navigation}
            ListHeaderComponent={
              <>
                {this.renderHeader()}
                {!createGroup ? <View style={style.lineHeight} /> : <View />}
                {/* <View style={style.specialStyleBox}>
                  <View style={style.specialStyle}>
                    <View style={style.specialFront}>
                      <Image source={resIcon.filterFavActive} style={{ width: 12, height: 12 }} />
                      <Text style={style.specialText}>
                        {I18n.t('page_chat_text_special_attention')}
                      </Text>
                    </View>
                    <Touchable onPress={this.onSpecialAttentionClick}>
                      <Image source={resIcon.chatSetting} style={{ width: 24, height: 24 }} />
                    </Touchable>
                  </View>
                  <View style={style.itemInfo}>
                    <Avatar
                      avatar="https://img0.baidu.com/it/u=3779382323,1772649823&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
                      name="妈妈"
                      size={40}
                      borderRadius={16}
                      showDot={false}
                    />
                    {<Text style={[style.sessionListItem1]}>妈妈</Text>}
                  </View>
                  <View style={style.btmLine} />
                </View> */}
              </>
            }
          />
        </View>
        {createGroup && !isSingle ? (
          <>
            <CheckedItem chooses={checkedList} onNext={this.onNext} disabledBtn={disabledBtn} />
            <SafeView bottomHeight={0} />
          </>
        ) : (
          <View />
        )}
        {this.renderUserSetting()}
        <SetStatusModal ref={(ref) => (this.setStatusModal = ref)} />
      </View>
    );
  }
}
