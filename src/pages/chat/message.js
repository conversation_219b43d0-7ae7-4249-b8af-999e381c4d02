import React, { Component } from 'react';
import { AlertPro, View } from '../../components';
import { computed } from 'mobx';
import { inject, observer } from 'mobx-react';
import ChatMessageList from './components/chatMessageList';
import NavigationService from '../../navigationService';
import constant from '../../store/constant';
import chatSessionDao from '../../database/dao/chatSessionDao';
import ShowMessageTextModal from '../../components/modal/showMessageTextModal';
import VideoPlayerModal from './components/videoPlayerModal';
import ViewImagesModal from './components/viewImagesModal';
import I18n from '../../i18n';
import CurrentPageLife from '../../components/CurrentPageLife';
import ChatHeader from './components/chatHeader';
import ResumeModal from '../../components/resumeModal';
import sendMessageUtil from '../../database/sendMessageUtil';
import messageDao from '../../database/dao/messageDao';
import sentryUtil from '../../util/sentryUtil';
import InviteModal from '../enterprise/resume/components/inviteModal';
import pushService from '../../pushService';
import util from '../../util';
import TelegramAccountModal from './components/TelegramAccountModal';

function getComponentStyle() {
  return {
    container: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
  };
}

@inject('chatStore', 'userStore', 'imAction')
@observer
export default class Message extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle();
    const { session } = props.navigation.state.params || {};
    this.session = session || {};
    this.session.ownerId = props.userStore.imId;
    this.session.sessionId = Number(props.navigation.getParam('sessionId', this.session.sessionId));
    this.session.isSelf = props.userStore.imId === this.session.sessionId;
    this.isDisabled = this.session.isGroup && this.session.isDelete; // 不可用，如群已解散
    this.groupNoticeRef = {};
    this.state = {
      memberCount: 0,
      session: this.session,
      modifyTitle: this.session.title,
      groupInfo: this.session.isGroup && {
        groupId: session.sessionId,
        ownerId: session.ownerId,
        title: session.title,
      },
      alertInfo: null,
      showTgModal: false,
    };
  }

  componentDidMount() {
    pushService.clearBadge();

    if (this.isDisabled) {
      this.getGroupInfo(true);
      return;
    }
    global.emitter.on(constant.event.chatSessionChange, this.onChatSessionChange);
    global.emitter.on(constant.event.playVideo, this.onPlayAndFullScreen);
    global.emitter.on(constant.event.showTelegramModal, this.handleTelegramModal);
    // global.emitter.on(constant.event.groupInfoChange, this.onGroupInfoChange);
    // global.emitter.on(constant.event.regetGroupInfo, this.getGroupInfo);

    this.getGroupInfo(true);

    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.onWillFocus);
    this.willBlurSubscription = this.props.navigation.addListener('willBlur', this.onWillBlur);
  }

  componentWillUnmount() {
    console.log('message componentWillUnmount');
    this.isUnmount = true;
    if (this.isDisabled) return;
    global.emitter.off(constant.event.chatSessionChange, this.onChatSessionChange);
    global.emitter.off(constant.event.playVideo, this.onPlayAndFullScreen);
    global.emitter.off(constant.event.showTelegramModal, this.handleTelegramModal);
    // global.emitter.off(constant.event.groupInfoChange, this.onGroupInfoChange);
    // global.emitter.off(constant.event.regetGroupInfo, this.getGroupInfo);

    this.willFocusSubscription.remove();
    this.willBlurSubscription.remove();
  }

  onWillFocus = () => {};

  onWillBlur = () => {
    this.closeNotice();
  };

  closeNotice = () => {
    if (this.groupNoticeRef?.wrappedInstance) {
      this.groupNoticeRef?.wrappedInstance.closeNoticeSection();
    }
  };

  onPlayAndFullScreen = ({ uri }) => {
    console.log('播放的视频地址', uri);
    this.videoPlayerModal.open({ uri }, () => {});
  };

  handleTelegramModal = (data) => {
    this.setState({
      showTgModal: true,
      currentSession: data.session,
    });
  };

  onTgModalCancel = () => {
    this.setState({ showTgModal: false, currentSession: null });
  };

  onTgModalSend = (tgAccount) => {
    const { currentSession } = this.state;
    if (currentSession) {
      sendMessageUtil.sendMessage({
        type: constant.messageType.text,
        sessionId: currentSession.sessionId,
        session: currentSession,
        content: `Telegram: ${tgAccount}`,
      });
    }

    this.setState({ showTgModal: false, currentSession: null });
  };

  onTgModalError = (errorMessage) => {
    toast.show(errorMessage);
  };

  onGroupInfoChange = ({ groupId, groupInfo, isDelete }) => {
    console.log('message onGroupInfoChange', groupId, groupInfo);
    if (!this.session.isGroup || this.session.sessionId !== groupId) return;
    if (isDelete) {
      this.getGroupDisbandedAlertInfo(true);
      return;
    }
    if (groupInfo) {
      this.setState({ groupInfo });
    } else {
      this.getGroupInfo();
    }
  };

  getGroupInfo = async (isFirst) => {
    if (!this.session.isGroup) return;
    try {
      const groupInfo = await this.props.imAction.getGroupInfo(this.session.sessionId, {
        isLocal: isFirst,
      });
      this.setState({ groupInfo });
      if (isFirst && !groupInfo.isRemote) {
        this.getGroupInfo();
      }
    } catch (e) {
      logger.warn('message getGroupInfo', e);
      if (e?.code === -801) {
        // 群已解散
        this.getGroupDisbandedAlertInfo(true);
      }
    }
  };

  getGroupDisbandedAlertInfo = (show) => {
    if (this.props.userStore.imId === this.state.groupInfo?.groupOwnerId) {
      return;
    }
    const alertInfo = {
      message: I18n.t('page_chat_op_dismissed'),
      showCancel: false,
      onConfirm: () => {
        this.setState({ alertInfo: null });
        NavigationService.navigate('main');
      },
    };
    if (show) {
      this.setState({ alertInfo });
    }
    this.props.imAction.onDeleteDataByGroupDisbanded(this.session.sessionId);
    return alertInfo;
  };

  onGroupMemberChange = (memberMap) => {
    const member = memberMap.get(this.session.sessionId);
    console.debug('onGroupMemberChange', member);
    this.member = member;
    if (!member) return;
    let modifyTitle = util.handleDisplayName(member.memo || member.nickname || this.session.title);
    this.session.title = modifyTitle;
    this.session.avatar = member.avatar || this.session.avatar;
    if (modifyTitle !== this.state.modifyTitle) {
      this.setState({ modifyTitle, session: this.session });
    }
  };

  getLatestName = async () => {
    const chatSession = await chatSessionDao.getChatSession({
      ownerId: this.session?.ownerId,
      sessionId: this.session?.sessionId,
      isGroup: this.session?.isGroup,
    });
    if (!chatSession || this.isUnmount) {
      console.log('message getLatestName isUnmount');
      return;
    }
    this.session = Object.assign({}, this.session, chatSession);
    this.setState({ modifyTitle: chatSession?.title, session: this.session });
  };

  onChatSessionChange = async () => {
    console.log('message onChatSessionChange');
    await this.getLatestName();
  };

  onChatMessageListRef = (ref) => (this.chatMessageList = ref?.wrappedInstance);

  clearChatMsg = () => {
    this.chatMessageList?.cleanMessage();
  };

  @computed get title() {
    const { connectStateLabel } = this.props.chatStore;
    const { memberCount, modifyTitle } = this.state;
    const memberCountText = memberCount ? ` (${memberCount}) ` : '';
    return modifyTitle + (this.session.isGroup ? '' : memberCountText) + connectStateLabel;
  }

  onDetail = () => {
    if (this.session.isGroup) {
      const { fromGroupList } = this.props.navigation.state.params;
      NavigationService.navigate('groupSet', {
        session: this.session,
        groupInfo: this.state.groupInfo,
        fromGroupList,
        onGroupInfoChange: this.onGroupInfoChange,
        refresh: this.clearChatMsg,
        reloadMember: ({ memberCount, title }) => {
          if (memberCount) {
            this.setState({ memberCount });
          }
          if (title) {
            this.setState({ modifyTitle: title });
          }
        },
      });
      return;
    }
    NavigationService.navigate('chatDetailSet', {
      session: this.session,
      friend: this.member,
      refresh: this.clearChatMsg,
    });
  };

  onSendResume = (resumeId, resume, param) => {
    if (!resumeId) return;
    const { session } = this;
    session.resumeId = resumeId;
    sendMessageUtil.sendMessage({
      type: constant.messageType.sendResume,
      sessionId: session.sessionId,
      session,
      content: '',
      resumeId,
    });
    global.emitter.emit(constant.event.showResumeModal, {
      isOpen: false,
      page: 'chatMessage',
    });
    if (param.message) {
      const localExtra = sentryUtil.parseSafe(param.message.localExtra) || {};
      localExtra.isSend = true;
      param.message.localExtra = JSON.stringify(localExtra);
      messageDao.updateLocalExtra(param.message);
    }
  };

  render() {
    const { style } = this;
    const { groupInfo, alertInfo, memberCount, session } = this.state;
    return (
      <View style={style.container}>
        <ChatHeader
          title={this.title}
          session={session}
          onDetail={this.onDetail}
          memberCount={memberCount}
          groupInfo={groupInfo}
        />
        <ChatMessageList
          ref={this.onChatMessageListRef}
          navigation={this.props.navigation}
          session={session}
          onGroupMemberChange={this.onGroupMemberChange}
          groupInfo={groupInfo}
          memberCount={memberCount}
          isDisabled={this.isDisabled}
        />
        <ShowMessageTextModal />
        <VideoPlayerModal
          ref={(ref) => {
            this.videoPlayerModal = ref;
          }}
        />
        <ViewImagesModal navigation={this.props.navigation} statusBarColor="#fff" />
        <AlertPro
          visible={!!alertInfo}
          textConfirm={I18n.t('op_confirm_title')}
          {...(alertInfo || {})}
        />
        <ResumeModal
          getResumeId={false}
          nav={this.props.navigation}
          jobId={session.jobId}
          sendResume={this.onSendResume}
          page="chatMessage"
        />
        <InviteModal
          // ref={(ref) => (this.inviteModal = ref)}
          // onConfirm={this.onInviteConfirm}
          // item={this.item}
          // detail={detail}
          page="chatMessage"
        />
        <CurrentPageLife navigation={this.props.navigation} isSoftInputMode />
        <TelegramAccountModal
          isVisible={this.state.showTgModal}
          onCancel={this.onTgModalCancel}
          onSend={this.onTgModalSend}
          onError={this.onTgModalError}
        />
      </View>
    );
  }
}
