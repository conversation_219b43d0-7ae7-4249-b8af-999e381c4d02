import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Image, SectionList, Text, Touchable, View } from '../../components';
import styles from '../../themes';
import chatSessionDao from '../../database/dao/chatSessionDao';
import Debounce from 'debounce-decorator';
import Header from '../../components/header/header';
import Avatar from '../../components/avatar/avatar';
import SearchBox from '../../components/input/searchBox';
import SafeView from '../../components/safeView';
import sendMessageUtil from '../../database/sendMessageUtil';
import NavigationService from '../../navigationService';
import NoData from '../../components/empty/noData';
import I18n from '../../i18n';
import resIcon from '../../res';
import PermissionUtil from '../../util/permissionUtilExtra';
import constant from '../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    searchBox: {
      marginHorizontal: 18,
    },
    listContainer: {
      flex: 1,
      marginTop: 5,
    },
    sectionHeaderContainer: {
      backgroundColor: '#F5F8FB',
      paddingHorizontal: 18,
      paddingVertical: 5,
    },
    sectionHeaderText: {
      fontSize: 14,
      color: '#8E96A3',
      lineHeight: 20,
    },
    chatItemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      minHeight: 62,
      paddingHorizontal: 18,
    },
    chatItemAvatar: {
      width: 42,
      height: 42,
      borderRadius: 21,
      marginRight: 14,
    },
    chatItemName: {
      fontSize: 16,
      fontWeight: theme.fontWeightRegular,
      color: '#484848',
      lineHeight: 22,
      flex: 1,
    },
    itemSeparator: {
      height: 1,
      backgroundColor: theme.separatorColor,
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
    createItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 18,
      paddingVertical: 10,
      backgroundColor: '#FFFFFF',
    },
    chatGroup: {
      width: 40,
      height: 40,
    },
    createText: {
      color: theme.inputLabelFontColor,
      fontSize: theme.memoSize,
      fontWeight: theme.fontWeightMedium,
      lineHeight: 21,
      marginLeft: 13,
      flex: 1,
    },
  };
}

/**
 * 消息转发时的选择聊天列表页面
 */
@inject('userStore', 'imAction', 'chatAction', 'chatStore', 'meetingAction', 'callAction')
@observer
export default class ChatForwardSelectList extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.routeParams = props.navigation.state.params || {};
    const data = this.props.navigation.getParam('data', {});
    this.data = data;
    this.title = data?.title || I18n.t('page_chat_select_chat');
    this.groupList = [];
    this.friendList = [];
    this.sessionSectionList = [];
    this.state = {
      sections: [],
      searchValue: '',
    };
  }

  componentDidMount() {
    this.loadData();
    this.props.imAction.getGroupList().then((res) => {
      this.groupList = res;
    });
    this.props.chatAction.getFriends(true, false, this.routeParams.excludeSelf).then((res) => {
      this.friendList = res;
    });
  }

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async () => {
    const sessionList = await chatSessionDao.querySessionList({
      excludeSelf: this.routeParams.excludeSelf,
    });
    this.sessionSectionList = [
      {
        key: I18n.t('page_chat_recent'),
        type: 'session',
        data: sessionList,
      },
    ];
    this.onSearch('');
  };

  handleLoadResponse = async ({ result }) => {
    return {
      currentTotalCount: result.length,
      totalCount: result.length,
    };
  };

  onChangeText = (text) => {
    this.setState({ searchValue: text }, () => {
      if (!text) {
        this.onSearchDebounce(text);
      }
    });
  };

  onSubmitEditing = () => {
    const { searchValue } = this.state;
    this.onSearchDebounce(searchValue);
  };

  @Debounce(300)
  onSearchDebounce(text) {
    this.onSearch(text);
  }

  onSearch = (searchValue) => {
    let sections;
    const text = searchValue?.trim();
    if (text) {
      sections = [];
      const groupList = this.groupList.filter((item) => item.title?.includes(text));
      if (groupList.length) {
        sections.push({ key: I18n.t('page_chat_group'), type: 'group', data: groupList });
      }
      const friendList = this.friendList.filter(
        (item) => item.memo?.includes(text) || item.nickname?.includes(text)
      );
      if (friendList.length) {
        sections.push({ key: I18n.t('page_chat_friend'), type: 'friend', data: friendList });
      }
    } else {
      sections = this.sessionSectionList;
    }
    this.setState({ sections });
  };

  // 聊天
  onMessage = async (item, section) => {
    try {
      // 发起音视频
      if (this.data?.isCreateCall) {
        let callUsers = item;
        if (!section.isArray) {
          if (item.isGroup) {
            const { groupMembers } = await this.props.imAction.getGroupMemberList(
              item.sessionId,
              false
            );
            callUsers = groupMembers;
          } else {
            callUsers = [item];
          }
        }
        if (callUsers.length > 1) {
          this.onAudioCall(callUsers);
        } else {
          this.onVideoCall(callUsers[0]);
        }
        return;
      }
      const message = this.props.navigation.getParam('message');
      console.log('onMessage', section.type, item, message);
      if (!message) return;
      let session;
      if (section.type === 'group') {
        session = {
          isGroup: true,
          sessionId: item.groupId,
          title: item.title,
          avatar: item.avatar,
        };
      } else if (section.type === 'friend') {
        session = {
          isGroup: false,
          sessionId: item.imId,
          title: item.memo || item.nickname,
          avatar: item.avatar,
        };
      } else {
        session = item;
      }
      console.log('onMessage', session);
      await sendMessageUtil.sendMessage({
        ...message,
        isGroup: session.isGroup,
        sessionId: session.sessionId,
        referTo: null,
        quoteId: 0,
      });
      NavigationService.navigateMessage({
        session,
        sessionId: session.sessionId,
      });
    } catch (e) {
      logger.warn('chatForwardSelectList onMessage', e, message);
      toast.show(e?.message || I18n.t('page_chat_forward_error'));
    }
  };

  onAudioCall = async (item, session) => {
    if (this.props.meetingAction.checkInCall()) {
      return;
    }
    await PermissionUtil.requestMultiple();

    this.props.meetingAction.startCall(item);
  };

  onVideoCall = async (user) => {
    console.log(user);
    try {
      if (this.props.callAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestMultiple();
      await this.props.callAction.startCall(
        constant.callType.singleVideoCall,
        user.sessionId || user.imId
      );
    } catch (e) {}
  };

  onShowNewChat = () => {
    NavigationService.navigate('chatBook', {
      createGroup: true,
      excludeSelf: this.routeParams.excludeSelf,
      isSingle: !this.data?.isCreateCall,
      title: this.data?.isCreateCall ? I18n.t('page_chat_friend') : I18n.t('page_chat_new_chat'),
      addCheck: (item) => {
        this.onMessage(item, { type: 'friend', isArray: !!this.data?.isCreateCall });
      },
    });
  };

  renderItem = ({ item, section }) => {
    const { style } = this;
    const title = section.type === 'friend' ? item.memo || item.nickname : item.title;
    return (
      <Touchable
        onPress={() => this.onMessage(item, { ...section, isArray: false })}
        style={style.chatItemContainer}
      >
        <Avatar avatar={item.avatar || null} name={title} style={style.chatItemAvatar} />
        <Text style={style.chatItemName} numberOfLines={2}>
          {title}
        </Text>
      </Touchable>
    );
  };

  renderHeaderItem = ({ img, title, onPress, marginTop = 0 }) => {
    const { style } = this;
    return (
      <Touchable style={[style.createItem, { marginTop }]} onPress={onPress}>
        <Image source={img} style={style.chatGroup} resizeMode="contain" />
        <Text style={style.createText}>{title}</Text>
      </Touchable>
    );
  };

  renderSectionHeader = ({ section }) => {
    const params = this.data?.isCreateCall
      ? {
          img: resIcon.chatCallBook,
          title: I18n.t('page_chat_label_select_user_from_book'),
        }
      : {
          img: resIcon.newChat,
          title: I18n.t('page_forward_label_newchat'),
        };
    return (
      <View>
        <View>{this.renderHeaderItem({ ...params, onPress: this.onShowNewChat })}</View>
        <View style={this.style.sectionHeaderContainer}>
          <Text style={this.style.sectionHeaderText}>{section.key}</Text>
        </View>
      </View>
    );
  };

  renderItemSeparatorComponent = () => <View style={this.style.itemSeparator} />;

  renderListEmptyComponent = () => <NoData />;

  keyExtractor = (item) => `${item.sessionId}_${item.groupId}_${item.imId}`;

  render() {
    const { style } = this;
    const { sections, searchValue } = this.state;
    return (
      <View style={style.container}>
        <Header title={this.title} />
        <SearchBox
          onChangeText={this.onChangeText}
          style={style.searchBox}
          value={searchValue}
          closeIconStyle={{ marginRight: 5 }}
          rightIcon={() => (
            <Touchable onPress={this.onSubmitEditing} style={style.searchTextBox}>
              <Text style={style.searchBtn}>{I18n.t('op_search_title')}</Text>
            </Touchable>
          )}
        />
        <SectionList
          style={style.listContainer}
          sections={sections}
          renderSectionHeader={this.renderSectionHeader}
          renderItem={this.renderItem}
          ItemSeparatorComponent={this.renderItemSeparatorComponent}
          ListEmptyComponent={this.renderListEmptyComponent}
          keyExtractor={this.keyExtractor}
          showsVerticalScrollIndicator={false}
          stickySectionHeadersEnabled
        />
        <SafeView bottomHeight={0} />
      </View>
    );
  }
}
