import React from 'react';
import { ScrollView, View, Text, Touchable, BaseComponent, Image } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Header from '../../components/header/header';
import NavigationService from '../../navigationService';
import SettingItem from '../../components/listItem/settingItem';
import Avatar from '../../components/avatar/avatar';
import chatSessionDao from '../../database/dao/chatSessionDao';
import GroupMember from '../../database/groupMember';
import messageDao from '../../database/dao/messageDao';
import constant from '../../store/constant';
import I18n from '../../i18n';
import resIcon from '../../res';
import sendMessageUtil from '../../database/sendMessageUtil';
import RightArrow from '../../components/rightArrow';

function getComponentStyle(theme) {
  return {
    container: {
      backgroundColor: theme.primaryBgColor,
    },
    settingContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    memberListContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      backgroundColor: '#fff',
      paddingLeft: 25,
      paddingBottom: 18,
    },
    userInfo: {
      flexDirection: 'column',
      alignItems: 'flex-start',
      backgroundColor: theme.primaryBgColor,
      marginTop: 10,
      paddingRight: 18,
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 20,
    },
    nickNameStyle: {
      fontSize: theme.fontSizeXS,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBook,
      lineHeight: 22,
      width: 50,
      textAlign: 'center',
      marginTop: 8,
    },
    lgSpace: {
      backgroundColor: theme.minorBgColor,
      height: 10,
    },
    disturbTips: {
      paddingHorizontal: 18,
      fontSize: theme.fontSizeS,
      color: '#99A3BA',
    },
    specialBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 15,
      paddingTop: 15,
    },
    specialText: {
      fontSize: 15,
      color: theme.titleFontColor,
    },
    specialCount: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  };
}

@inject('chatAction', 'userStore', 'imAction')
@observer
export default class SpecialUser extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.state = {
      showAlert: false,
    };
  }

  componentDidMount() {}

  onChatDetail = () => {
    // const { userData } = this.state;
    // NavigationService.navigate('chatAddContactDetail', {
    //   imId: userData.sessionId,
    //   deleteRouteName: 'main', // 删除好友后的路由页面
    //   callback: content => {
    //     userData.friendMemo = content;
    //     this.setState({ userData });
    //   },
    // });
  };

  onSticky = async () => {
    toast.show(I18n.t('msg_in_development'));
  };

  onShowAvatar = async () => {
    toast.show(I18n.t('msg_in_development'));
  };

  onReadDel = async () => {
    toast.show(I18n.t('msg_in_development'));
  };

  onMessageFree = async () => {
    toast.show(I18n.t('msg_in_development'));

    // const { userData } = this.state;
    // const params = {
    //   key: 'isDisturb',
    //   value: !this.state.isDisturb,
    // };
    // await this.props.imAction.changeContact(userData?.sessionId, params);
    // userData.isDisturb = !this.state.isDisturb ? 1 : 0;
    // await chatSessionDao.updateSet(userData);
    // this.setState({ isDisturb: !this.state.isDisturb });
  };

  onAddMember = () => {
    toast.show(I18n.t('msg_in_development'));
    // NavigationService.push('chatBook', {
    //   createGroup: true,
    //   disabledList: this.props.groupMembers,
    //   addCheck: this.onAddMemberCallback,
    // });
  };

  onRemoveMember = () => {
    toast.show(I18n.t('msg_in_development'));
  };

  /*onAddMemberCallback = async checkedList => {
    try {
      console.log('GroupMemberManage onAddMemberCallback', checkedList);
      this.showGlobalLoading();
      const imIds = checkedList.map(item => item.imId);
      const {
        groupMembers,
        setGroupMembers,
        groupInfo: { groupId },
      } = this.props;
      await this.props.imAction.addGroupMembers({ groupId, imIds });
      const joinAt = new Date().getTime();
      const newMembers = checkedList.map(item => {
        const groupMember = new GroupMember();
        groupMember.imId = item.imId;
        return {
          ...item,
          isDelete: 0,
          role: constant.groupMemberRole.member,
          groupId,
          joinAt,
        };
      });
      setGroupMembers(groupMembers.concat(newMembers));
      const targetNames = checkedList.map(item => item.memo || item.nickname).join('、');
      await sendMessageUtil.sendGroupInvite({
        groupId,
        imIds,
        targetNames,
      });
      this.showRequestResult();
    } catch (e) {
      logger.warn('GroupMemberManage onAddMemberCallback', e);
      this.showRequestResult(e);
    }
  };*/

  renderListItem = () => {
    const { style } = this;
    const { sticky, isDisturb } = this.state;
    return (
      <View style={style.container}>
        <SettingItem
          label={I18n.t('page_chat_text_special_attention_tips')}
          onSwitch={this.onMessageFree}
          switched={true}
          maxSpacing
        />
        <SettingItem
          label={I18n.t('page_chat_text_special_attention_tips_group')}
          onSwitch={this.onSticky}
          switched={false}
        />
        <SettingItem
          label={I18n.t('page_chat_text_special_attention_tips_disturb')}
          onSwitch={this.onShowAvatar}
          switched={false}
        />
        <Text style={style.disturbTips}>
          {I18n.t('page_chat_text_special_attention_tips_disturb_desc')}
        </Text>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { userData } = this.state;
    return (
      <View style={style.settingContainer}>
        <Header title={I18n.t('page_chat_text_special_attention_setting')} />
        <View style={style.lgSpace} />
        <ScrollView style={{ flex: 1 }}>
          <View>
            <View style={style.specialBox}>
              <Text style={style.specialText} ellipsizeMode="tail" numberOfLines={1}>
                {I18n.t('page_chat_text_special_attention_title')}
              </Text>
              <View style={style.specialCount}>
                <Text style={style.specialText} ellipsizeMode="tail" numberOfLines={1}>
                  1人
                </Text>
                <RightArrow color="#999" />
              </View>
            </View>
            <View style={style.memberListContainer}>
              <Touchable style={style.userInfo} onPress={this.onChatDetail}>
                <Avatar
                  avatar={
                    userData?.avatar ||
                    'https://img0.baidu.com/it/u=3779382323,1772649823&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500'
                  }
                  name="妈妈"
                  style={style.avatar}
                  showDot={false}
                />
                <Text style={style.nickNameStyle} ellipsizeMode="tail" numberOfLines={1}>
                  妈妈
                </Text>
              </Touchable>
              <Touchable style={style.userInfo} onPress={this.onAddMember}>
                <Image source={resIcon.addGroupMember} style={style.avatar} />
              </Touchable>
              <Touchable style={style.userInfo} onPress={this.onRemoveMember}>
                <Image source={resIcon.removeGroupMember} style={style.avatar} />
              </Touchable>
            </View>
          </View>
          <View style={style.lgSpace} />

          {this.renderListItem()}
          <View style={{ height: 30 }} />
        </ScrollView>
      </View>
    );
  }
}
