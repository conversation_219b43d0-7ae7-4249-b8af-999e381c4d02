import React, { Component } from 'react';
import RNFS from 'react-native-fs';
import memoize from 'memoize-one';
import <PERSON>Viewer from 'react-native-file-viewer';
import { Button, Image, Text, View } from '../../components';
import styles from '../../themes';
import Header from '../../components/header/header';
import fileUtil from '../../util/fileUtil';
import messageDao from '../../database/dao/messageDao';
import I18n from '../../i18n';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    contentContainer: {
      flex: 1,
      alignItems: 'center',
      paddingTop: 40,
      paddingHorizontal: theme.containerPaddingHorizontal,
    },
    fileImage: {
      width: 25,
      height: 30,
      marginLeft: 10,
    },
    fileNameText: {
      fontSize: 16,
      color: theme.titleFontColor,
      marginTop: 15,
      marginHorizontal: 10,
      textAlign: 'center',
    },
    tipText: {
      fontSize: 14,
      color: theme.minorFontColor,
      marginTop: 15,
      textAlign: 'center',
    },
    btnContainerStyle: {
      width: '80%',
      marginTop: 15,
    },
  };
}

const Status = {
  notDownloaded: 'notDownload',
  downloading: 'downloading',
  downloaded: 'downloaded',
};

/**
 * 文件详情页面
 */
export default class MessageFileDetail extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.message = props.navigation.getParam('message');
    const extra = JSON.parse(this.message.extra) || {};
    this.state = {
      extra,
      fileSize: fileUtil.getDisplayFileSize(extra.fileSize),
      fileTypeImage: fileUtil.getFileTypeImage(extra.fileType || fileUtil.getFileType(extra.type)),
      downloadProgress: null,
      status: this.message.imagePath ? Status.downloaded : Status.notDownloaded,
    };
  }

  componentDidMount() {
    this.checkFileExist();
  }

  componentWillUnmount() {
    this.onStopDownload(null, true);
  }

  checkFileExist = async () => {
    try {
      const { message } = this;
      if (!message.imagePath) return;
      const exists = await RNFS.exists(message.imagePath);
      console.log('messageFileDetail checkFileExist', exists);
      if (exists) return;
      message.imagePath = '';
      this.setState({ status: Status.notDownloaded });
      await messageDao.updateMessageBySeq(message);
    } catch (e) {
      logger.warn('messageFileDetail checkFileExist', e);
    }
  };

  onMore = () => {};

  onStopDownload = (status = Status.notDownloaded, isUnmount) => {
    console.log('messageFileDetail onStopDownload', this.downloadJobId, isUnmount);
    try {
      if (this.downloadJobId) {
        RNFS.stopDownload(this.downloadJobId);
        this.downloadJobId = null;
      }
      if (!isUnmount) {
        this.setState({ downloadProgress: null, status });
      }
    } catch (e) {
      logger.warn('messageFileDetail onStopDownload', e);
    }
  };

  onDownloadProgress = ({ contentLength, bytesWritten = 0 }) => {
    console.log('messageFileDetail onDownloadProgress', bytesWritten, contentLength);
    if (bytesWritten > 0 && bytesWritten >= contentLength) {
      this.setState({ downloadProgress: null, status: Status.downloaded });
    } else {
      this.setState({
        downloadProgress: `${fileUtil.getDisplayFileSize(
          bytesWritten
        )}/${fileUtil.getDisplayFileSize(contentLength)}`,
        status: Status.downloading,
      });
    }
  };

  onDownload = async () => {
    try {
      const { message } = this;
      const fromUrl = message.content;
      const toFile = await fileUtil.getMsgFilePath(message);
      console.log('messageFileDetail onDownload', fromUrl, toFile);
      const res = RNFS.downloadFile({
        fromUrl,
        toFile,
        progressInterval: 300, // 间隔300ms更新一次进度
        discretionary: true,
        begin: this.onDownloadProgress,
        progress: this.onDownloadProgress,
      });
      this.downloadJobId = res.jobId;
      await res.promise;
      message.imagePath = `file://${toFile}`;
      await messageDao.updateMessageBySeq(message);
      this.onStopDownload(Status.downloaded);
    } catch (e) {
      this.onStopDownload(Status.notDownloaded);
      logger.warn('messageFileDetail onDownload', e);
      toast.show(e?.message || I18n.t('page_file_op_download'));
    }
  };

  onOpen = async () => {
    try {
      const { message } = this;
      const url = message.imagePath;
      console.log('messageFileDetail onOpen', url);
      await FileViewer.open(url);
    } catch (e) {
      logger.warn('messageFileDetail onOpen', e);
      toast.show(I18n.t('page_file_op_open'));
    }
  };

  statusInfo = memoize((status, fileSize) => {
    console.log('messageFileDetail statusInfo memoize', status, fileSize);
    if (status === Status.downloading) {
      return {
        onPress: this.onStopDownload,
        btnTitle: I18n.t('page_file_download_cancel'),
      };
    }
    if (status === Status.notDownloaded) {
      return {
        onPress: this.onDownload,
        btnTitle: `${I18n.t('page_file_download')}(${fileSize})`,
      };
    }
    return {
      onPress: this.onOpen,
      btnTitle: I18n.t('page_file_open_other'),
    };
  });

  render() {
    const { style } = this;
    const { extra, fileTypeImage, downloadProgress, status, fileSize } = this.state;
    // console.log('messageFileDetail render', status, fileSize);
    const statusInfo = this.statusInfo(status, fileSize);
    return (
      <View style={style.container}>
        <Header
          title={extra.fileName}
          // rightIconType="material"
          // rightIcon="more-vert"
          // rightPress={this.onMore}
        />
        <View style={style.contentContainer}>
          <Image source={fileTypeImage} style={style.fileImage} />
          <Text style={style.fileNameText}>{extra.fileName}</Text>
          <Text style={style.tipText}>
            {status === Status.downloaded ? I18n.t('page_file_open') : I18n.t('page_file_review')}
          </Text>
          <Button
            containerStyle={style.btnContainerStyle}
            title={statusInfo.btnTitle}
            onPress={statusInfo.onPress}
            btnType="chatSendResume"
            btnSize="md"
          />
          {status === Status.downloading ? (
            <Text style={style.tipText}>{downloadProgress}</Text>
          ) : null}
        </View>
      </View>
    );
  }
}
