import React from 'react';
import {
  AlertPro,
  BaseComponent,
  Icon,
  Image,
  ScrollView,
  Text,
  Touchable,
  View,
} from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Header from '../../components/header/header';
import NavigationService from '../../navigationService';
import { deviceWidth } from '../../common';
import SafeView from '../../components/safeView';
import constant from '../../store/constant';
import contactDao from '../../database/dao/contactDao';
import Avatar from '../../components/avatar/avatar';
// import SecurityWalletModal from '../home/<USER>/securityWalletModal';
import SettingItem from '../../components/listItem/settingItem';
import ModifyModal from './components/modifyModal';
import I18n from '../../i18n';
import CurrentPageLife from '../../components/CurrentPageLife';
import resIcon from '../../res';
import PermissionUtil from '../../util/permissionUtilExtra';
import callAction from '../../store/actions/callAction';
import chatMessageUtil from '../../database/chatMessageUtil';
import { onReportUser } from '../../util/reportUtil';
import LinearGradient from 'react-native-linear-gradient';
import util from '../../util';

function getComponentStyle(theme) {
  return {
    detailContainer: {
      flex: 1,
      borderBottomColor: theme.minorBgColor,
      backgroundColor: theme.minorBgColor,
    },
    scrollViewContainer: {
      flex: 1,
    },
    avatar: {
      width: 82,
      height: 82,
      borderRadius: 30,
    },
    userInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 18,
      paddingBottom: 22,
      paddingTop: 20,
      backgroundColor: theme.primaryBgColor,
    },
    userBase: {
      flex: 1,
      marginLeft: 16,
    },
    nickNameStyle: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightMedium,
      lineHeight: 22,
      marginRight: 5,
    },
    userIdStyle: {
      fontSize: theme.fontSizeM,
      color: theme.mediumFontColor,
      fontWeight: theme.fontWeightMedium,
      lineHeight: 20,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 18,
      minHeight: 60,
      borderBottomWidth: 1,
      borderBottomColor: theme.minorBgColor,
    },
    itemTitleText: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightMedium,
      color: theme.inputLabelFontColor,
      lineHeight: 22,
      flex: 1,
    },
    hideLine: {
      borderBottomWidth: 0,
    },
    mdediaContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 18,
      paddingBottom: 19,
    },
    mediaCard: {
      width: 61,
      height: 61,
      backgroundColor: theme.order_separatorColor_fine,
      borderRadius: 2,
    },
    bottomActionContainer: {
      marginTop: 20,
    },
    btnContainerStyle: {
      paddingHorizontal: I18n.locale === 'en' ? 30 : 38,
    },
    btnContainer: {
      flexDirection: 'row',
      backgroundColor: theme.primaryColor,
      height: 48,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
    },
    btnContainer1: {
      backgroundColor: '#E8EDFF',
      marginTop: 15,
    },
    btnText: {
      fontSize: theme.fontSizeL,
      color: theme.primaryBgColor,
      marginLeft: 5,
    },
    btnText1: {
      color: theme.titleFontColor,
    },
    chatIcon: {
      marginRight: 10,
    },
    lineSpace: {
      backgroundColor: theme.minorBgColor,
      height: 10,
    },
    pageFooterViewFull: {
      // backgroundColor: '#fff',
      // borderTopColor: theme.secondaryColor,
      // borderTopWidth: 1,
      paddingHorizontal: 0,
      // shadowColor: '#00000050',
      // shadowOffset: { h: 10 },
      // shadowOpacity: 0.2,
      // elevation: 0,
    },
    bottomContainer: {
      marginHorizontal: I18n.locale === 'en' ? 30 : 38,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    itemBtnContainer: {
      backgroundColor: '#E8EDFF',
      height: 48,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
      width: (deviceWidth - (I18n.locale === 'en' ? 80 : 100)) / 3,
      flexDirection: 'row',
    },
    itemText: {
      fontSize: theme.fontSizeM,
      color: theme.btnFontColor,
      textAlign: 'center',
      marginLeft: 7,
    },
    deleteContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    delText: {
      fontSize: theme.fontSizeL,
      color: 'red',
      textAlign: 'center',
    },
    actionBox: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-around',
      paddingHorizontal: 5,
    },
    actionView: {
      justifyContent: 'center',
      width: deviceWidth / 3,
    },
    actionImg: {
      width: 82,
      height: 82,
      alignSelf: 'center',
    },
    actionText: {
      fontSize: theme.fontSizeS,
      color: '#121213',
      textAlign: 'center',
      marginTop: -3,
      paddingHorizontal: 8,
    },
    delBox: {
      flexDirection: 'row',
      justifyContent: 'center',
      paddingVertical: 13,
      backgroundColor: theme.primaryBgColor,
    },
    delBoxText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 22,
      flexShrink: 100,
    },
    userNameBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    userNameCell: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    phoneInfo: {
      paddingHorizontal: 18,
      backgroundColor: theme.primaryBgColor,
    },
    phoneInfoItem: {
      paddingVertical: 10,
      borderTopWidth: 1,
      borderTopColor: 'rgba(236, 245, 246, 0.8)',
    },
    phoneInfoItemLabel: {
      fontSize: theme.fontSizeM,
      color: '#99A3BA',
      lineHeight: 20,
    },
    phoneInfoItemValue: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      lineHeight: 20,
    },
    mineQrcode: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
    },
  };
}
@inject('chatAction', 'userStore', 'imAction', 'payAction')
@observer
export default class ChatAddContactDetail extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { imId } = this.props.navigation.state.params;
    const im_id = this.props.userStore.imId;
    this.imId = imId;
    this.state = {
      userData: null, // 用户信息
      contactReq: null, // 本地加好友申请对象
      existLocal: false, // 是否存在于本地数据库
      alertInfo: null,
      isScrollingUp: false,
      isPersonPhoneMasked: false,
      maskedPhone: '',
      // email: '',
    };
    this.actionGroups = [
      { icon: resIcon.chatDetailChat, title: I18n.t('op_chat_title') },
      {
        icon: im_id == imId ? resIcon.chatDetailAudioDisabled : resIcon.chatDetailAudio,
        title: I18n.t('page_chat_text_phone'),
        disabled: im_id == imId,
      },
      {
        icon: im_id == imId ? resIcon.chatDetailVideoDisabled : resIcon.chatDetailVideo,
        title: I18n.t('feat_video'),
        disabled: im_id == imId,
      },
      {
        icon: im_id == imId ? resIcon.chatDetailTransferDisabled : resIcon.chatDetailTransfer,
        title: I18n.t('page_pay_transfer'),
        disabled: im_id == imId,
      },
    ];
  }

  componentDidMount() {
    global.emitter.on(constant.event.addContact, this.getData);
    global.emitter.on(constant.event.blackMessage, this.refreshData);
    this.getData();

    /*this.props.payAction.getEmailInfo().then(({ email }) => {
      if (email) {
        this.setState({ email });
      }
    });*/
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.addContact, this.getData);
    global.emitter.off(constant.event.blackMessage, this.refreshData);
  }

  // 切换手机号码显示状态
  togglePhoneNumberMaskedVisible = () => {
    const { isPersonPhoneMasked } = this.state;
    this.setState({ isPersonPhoneMasked: !isPersonPhoneMasked });
  };

  get isFriend() {
    return this.state.existLocal;
  }

  get isAgreeAct() {
    const { existLocal, contactReq } = this.state;
    return !existLocal && contactReq?.status === 0 && contactReq?.isSelfReq;
  }

  get isBlack() {
    const { userData } = this.state;
    if (!userData) return false;
    // 0：未拉黑,1：已拉黑好友,2：好友已拉黑你,3：相互拉黑
    return userData.blacklist_type === 1 || userData.blacklist_type === 3;
  }

  get isSelf() {
    const { userData } = this.state;
    const { imId } = this.props.userStore;
    return imId === userData?.im_id;
  }

  checkWallet = (routeName) => {
    const { userData } = this.state;
    const session = {
      sessionId: userData.im_id,
      title: userData.friend_nickname || userData.nickname || userData.account_alias,
      avatar: userData.avatar,
      friendAvatar: userData.avatar,
    };
    // console.log(userData, session);
    NavigationService.navigate(routeName, { session });
  };

  getDBUser = async (imId) => {
    const contactReq = await contactDao.findContact({ imId });
    this.setState({ contactReq });
  };

  refreshData = async (noticeData) => {
    const { imId } = this.props.navigation.state.params;
    if (noticeData.im_id?.toString() === imId?.toString()) {
      this.getData();
    }
  };

  getData = async () => {
    const { imId } = this;
    if (!imId) return;
    await Promise.all([this.getDBUser(imId), this.getUserData(imId)]);
    // await this.getPrivacySettings(imId);
  };

  getUserData = async (imId) => {
    try {
      const { user: userData, friend } = await this.props.chatAction.getFriend(imId);
      console.log('用户信息', userData);
      // 掩码处理手机号
      let maskedPhone = util.maskPhoneAndArea({
        phone: userData?.phone,
        areaCode: userData?.country_code,
        hideCambodiaAreaCode: true, // 柬埔寨手机号码不需要区号
      });
      this.setState({ userData, existLocal: friend && !friend.isHide, maskedPhone });
      return userData;
    } catch (e) {
      logger.warn('用户信息', e, imId);
      return this.state.userData;
    }
  };

  // 同意添加好友
  onAgree = async () => {
    try {
      await this.props.imAction.agreeAndAddContact(this.state.userData);
    } catch (e) {
      logger.warn('chatAddContactDetail onAgree', e);
      if (e?.message) {
        toast.show(e.message);
      }
    }
  };

  onChat = () => {
    const { userData } = this.state;
    console.log('onChat', userData);
    NavigationService.navigateMessage({
      session: {
        sessionId: userData.im_id,
        title: userData.friend_nickname || userData.nickname || userData.account_alias,
        avatar: userData.avatar,
        isSelf: this.isSelf,
      },
      sessionId: userData.im_id,
    });
  };

  onNavigate = (route, params) => {
    this.checkWallet(route, params);
  };

  onTransfer = () => {
    // this.onNavigate('transferChat');
    NavigationService.navigate('transferPerson', {
      payeeInfo: {
        cardNo: this.state.userData?.phone,
      },
    });
  };

  onReceive = () => {
    this.onNavigate('receivePayment');
  };

  onSendRedPacket = () => {
    // toast.show('开发中');
    this.onNavigate('friendRedPacket');
  };

  onRemark(name) {
    this.modifyNicknameModal.show(name);
  }

  // 修改好友昵称备注
  onModifyFriendNickname = async (content) => {
    const { userData } = this.state;
    const params = {
      key: 'nickname',
      value: content,
    };
    await this.props.imAction.changeContact(userData?.im_id, params);
    await this.getData();
    global.emitter.emit(constant.event.chatSessionChange, {});
    const { callback } = this.props.navigation.state.params;
    callback?.(content);
    toast.show(I18n.t('page_chat_tips_modify_success'));
    this.modifyNicknameModal.close();
  };

  onViewMedia = () => {
    toast.show('媒体链接及文档');
  };

  onTransferRecord = () => {
    const { userData } = this.state;
    NavigationService.navigate('friendTradeList', { data: userData });
  };

  //推荐给朋友
  onRecommend = () => {
    const { userData } = this.state;
    const message = {
      type: constant.messageType.businessCard,
      content: JSON.stringify(userData),
      isGroup: false,
      sessionId: userData.im_id,
    };
    NavigationService.navigate('chatForwardSelectList', { message });
  };

  onUpdateFriendTag = () => {
    const { userData } = this.state;
    NavigationService.navigate('updateFriendTag', {
      imId: userData.im_id,
      onRefresh: (tags) => {
        this.setState({ userData: { ...userData, tags } });
      },
    });
  };

  // 特别关注
  onSetSpecial = async () => {
    toast.show(I18n.t('msg_in_development'));
    // try {
    //   this.showGlobalLoading();
    //   const { userData } = this.state;
    //   let { isSpecial } = this;
    //   isSpecial = !isSpecial;
    //   await this.props.imAction.setSpecial(userData.im_id, isSpecial);
    //   userData.is_special = isSpecial ? 1 : 0;
    //   this.setState({ userData: { ...userData } });
    //   this.showRequestResult();
    //   const contact = await contactDao.findContact({ imId: userData.im_id });
    //   if (contact) {
    //     contact.isSpecial = userData.is_special;
    //     await contactDao.updateContact(contact);
    //   }
    // } catch (e) {
    //   console.log('chatAddContactDetail onSetSpecial', e);
    //   this.showRequestResult(e);
    // }
  };

  onSetBlack = async () => {
    try {
      this.showGlobalLoading();
      let { userData } = this.state;
      let { isBlack } = this;
      isBlack = !isBlack;
      await this.props.imAction.setBlacklist(userData.im_id, isBlack);
      userData = await this.getUserData(userData.im_id);
      const contact = await contactDao.findContact({ imId: userData.im_id });
      if (contact) {
        contact.isBlacked = userData.blacklist_type;
        await contactDao.updateContact(contact);
      }
      this.showRequestResult();

      global.emitter.emit(constant.event.blacklistChange, { imId: userData.im_id });
    } catch (e) {
      console.log('chatAddContactDetail onSetBlack', e);
      this.showRequestResult(e);
    }
  };

  onDelete = () => {
    const { userData } = this.state;
    const name = userData?.nickname || userData?.account_alias;
    this.setState({
      alertInfo: {
        title: I18n.t('page_user_delete_title'),
        message: I18n.t('page_user_delete_content', { userName: name }),
        onConfirm: this.onDeleteConfirm,
      },
    });
  };

  onAlertCancel = () => {
    this.setState({ alertInfo: null });
  };

  onDeleteConfirm = async () => {
    try {
      this.showGlobalLoading();
      const { userData } = this.state;
      await this.props.chatAction.deleteContact(userData.im_id);
      await this.props.imAction.deleteFriend({ imId: userData.im_id });
      this.setState({ alertInfo: null });
      this.showRequestResult();
      toast.show(I18n.t('page_user_delete_success'));
      const deleteRouteName = this.props.navigation.getParam('deleteRouteName');
      if (deleteRouteName) {
        NavigationService.navigate(deleteRouteName);
      } else {
        NavigationService.goBack();
      }
    } catch (error) {
      logger.error('删除联系人出错', error);
      this.showRequestResult(error);
    }
  };

  // 图片预览
  onViewImages = (imgs, index) => {
    NavigationService.navigate('viewImages', {
      images: imgs.map((x) => {
        return { url: x.uri };
      }),
      index,
    });
  };

  // 添加好友申请
  onShowContentModal = async () => {
    const { userData } = this.state;
    // 被对方拉黑了
    if (userData?.blacklist_type === 2) {
      this.setState({
        alertInfo: {
          message: I18n.t('page_user_black_tips'),
          showCancel: false,
          onConfirm: this.onAlertCancel,
        },
      });
      return;
    }
    // 对方开启的了好验证  需要对方同意才能聊天
    NavigationService.navigate('requestAddFriend', {
      userData,
    });
  };

  onActionClick = (item, index) => {
    switch (index) {
      case 0:
        if (item.disabled) return;
        this.onChat();
        break;
      case 1:
        if (item.disabled) return;
        this.onAudioCall();
        break;
      case 2:
        if (item.disabled) return;
        this.onVideoCall();
        break;
      case 3:
        if (item.disabled) return;
        this.onTransfer();
        break;

      default:
        break;
    }
  };

  onVideoCall = async () => {
    try {
      if (callAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestMultiple();
      await callAction.startCall(constant.callType.singleVideoCall, this.state.userData.im_id);
    } catch (e) {}
  };

  onAudioCall = async () => {
    try {
      if (callAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestAudioPermission();
      await callAction.startCall(constant.callType.singleAudioCall, this.state.userData.im_id);
    } catch (e) {}
  };

  goMyQrcode = () => {
    NavigationService.navigate('myQrCode');
  };

  onComplain = async () => {
    await onReportUser(this.state.userData?.im_id);
  };

  handleScroll = (event) => {
    const { contentOffset } = event.nativeEvent;
    this.setState({ isScrollingUp: contentOffset.y > 0 });
  };

  renderActionItem = (item, index) => {
    const { style } = this;
    return (
      <Touchable key={index} onPress={() => this.onActionClick(item, index)}>
        <View style={style.actionView}>
          <Image source={item.icon} style={style.actionImg} />
          <Text style={style.actionText}>{item.title}</Text>
        </View>
      </Touchable>
    );
  };

  renderBottomAction = () => {
    const { userData } = this.state;
    if (!userData) return null;
    const { globalStyle } = styles.get(['global']);
    const { imId } = this.props.userStore;
    const { style, isFriend, isAgreeAct } = this;

    // const isFriend = userData?.relation_type === 1;
    // const isAgreeAct = userData?.relation_type !== 1 && isDeleteReq;
    return (
      <LinearGradient
        colors={['rgba(245,248,251,0.3)', '#FFFFFF', '#FFFFFF']}
        style={[
          globalStyle.pageFooterViewFull,
          style.pageFooterViewFull,
          { backgroundColor: 'transparent' },
        ]}
      >
        <View>
          {/* <View style={style.bottomActionContainer}> */}
          {!this.isSelf && !isFriend ? (
            <View style={style.btnContainerStyle}>
              <Touchable
                style={style.btnContainer}
                onPress={isAgreeAct ? this.onAgree : this.onShowContentModal}
              >
                <Icon type="antdesign" name="adduser" size={17} color={style.btnText.color} />
                <Text style={style.btnText}>
                  {isAgreeAct ? I18n.t('page_user_agree') : I18n.t('page_chat_add_friend')}
                </Text>
              </Touchable>
              <Touchable
                style={[style.btnContainer, style.btnContainer1]}
                onPress={this.onTransfer}
              >
                <Image source={resIcon.opAgain} style={{ width: 16, height: 16 }} />
                <Text style={[style.btnText, style.btnText1]}>{I18n.t('page_transfer_title')}</Text>
              </Touchable>
            </View>
          ) : (
            <View style={style.actionBox}>
              {this.actionGroups.map((item, index) => this.renderActionItem(item, index))}
            </View>
          )}

          {/*{userData && isFriend ? <View style={{ height: 15 }} /> : null}
        {isFriend ? (
          <View style={style.bottomContainer}>
            <Touchable style={style.itemBtnContainer} onPress={this.onTransfer}>
              <Image source={resIcon.walletReceive} />
              <Text style={style.itemText}>{I18n.t('page_transfer_title')}</Text>
            </Touchable>
            <Touchable style={style.itemBtnContainer} onPress={this.onReceive}>
              <Image source={resIcon.walletTransfer} />
              <Text style={style.itemText}>{I18n.t('page_receive_title')}</Text>
            </Touchable>
            <Touchable style={style.itemBtnContainer} onPress={this.onSendRedPacket}>
              <Image source={resIcon.chatAccountRedpacket} />
              <Text style={style.itemText}>{I18n.t('page_red_title')}</Text>
            </Touchable>
          </View>
        ) : null}*/}
        </View>
      </LinearGradient>
    );
  };

  render() {
    const { style, isBlack, isFriend } = this;
    const { userData, alertInfo, isScrollingUp, maskedPhone, isPersonPhoneMasked } = this.state;
    const { userName } = this.props.userStore;
    // const isFriend = userData?.relation_type === 1;
    const { isSelf } = this;
    /*const medias = [
      { uri: 'https://img2.baidu.com/it/u=*********,**********&fm=26&fmt=auto' },
      { uri: 'https://img1.baidu.com/it/u=**********,**********&fm=253&fmt=auto' },
      { uri: 'https://img0.baidu.com/it/u=**********,**********&fm=26&fmt=auto' },
      { uri: 'https://img0.baidu.com/it/u=**********,**********&fm=26&fmt=auto' },
      { uri: 'https://img0.baidu.com/it/u=********,**********&fm=26&fmt=auto' },
    ];*/
    const name = userData?.nickname || userData?.account_alias;
    const item = chatMessageUtil.handleSession({
      userType: userData?.user_type,
      sessionId: userData?.im_id,
    });
    return (
      <View style={style.detailContainer}>
        <Header
          // title={I18n.t('page_user_info_title')}
          // rightIcon={isFriend && !isSelf ? 'deleteuser' : ''}
          // rightIconType="antdesign"
          // rightIconColor="#333333"
          // rightPress={this.onDelete}
          isScrollingUp={isScrollingUp}
        />
        <ScrollView style={style.scrollViewContainer} onScroll={this.handleScroll}>
          <View style={style.userInfo}>
            <Avatar
              avatar={userData?.avatar}
              name={
                item.name ||
                userData?.friend_nickname ||
                userData?.nickname ||
                userData?.account_alias
              }
              style={style.avatar}
              dotSize={14}
              dotStyle={{ right: 0 }}
              defaultAvatar={resIcon.chatDefaultAvatar}
              showDot={!isSelf}
            />
            <View style={style.userBase}>
              <View style={style.userNameBox}>
                <View style={style.userNameCell}>
                  <Text style={style.nickNameStyle}>{userData?.friend_nickname || name || ''}</Text>
                  {!isSelf ? (
                    <Touchable onPress={() => this.onRemark(userData?.friend_nickname || '')}>
                      <Image source={resIcon.chatDetailEdit} />
                    </Touchable>
                  ) : null}
                </View>
                {/* {!isSelf && isFriend ? <Image source={resIcon.filterFavActive} /> : null} */}
              </View>
              {!isSelf ? (
                <>
                  <Text style={style.userIdStyle}>
                    {I18n.t('page_chat_text_nickname')}
                    {name}
                  </Text>
                  <Text style={style.userIdStyle}>
                    {I18n.t('page_chat_label_friend_account', {
                      account: userData?.account_alias || '-',
                    })}
                  </Text>
                </>
              ) : (
                <View>
                  <Text style={style.userIdStyle}>
                    {I18n.t('page_chat_label_friend_account', {
                      account: userData?.account_alias || userName || '-',
                    })}
                  </Text>
                  <Touchable onPress={this.goMyQrcode} style={style.mineQrcode}>
                    <Text style={style.userIdStyle}>{I18n.t('page_chat_text_qrcode')}</Text>
                    <Image source={resIcon.chatAddQrcode} />
                  </Touchable>
                </View>
              )}
            </View>
          </View>
          <View style={style.phoneInfo}>
            <View style={style.phoneInfoItem}>
              <Text style={style.phoneInfoItemLabel}>{I18n.t('page_chat_text_phone')}</Text>
              {userData ? (
                <Text
                  style={style.phoneInfoItemValue}
                  onPress={this.togglePhoneNumberMaskedVisible}
                >
                  {/* {userData ? `${userData.country_code}-${userData.phone}` : ''} */}
                  {!isPersonPhoneMasked
                    ? `${maskedPhone}`
                    : `${userData.country_code}-${userData.phone}`}
                </Text>
              ) : null}
            </View>
            {/*<View style={style.phoneInfoItem}>
              <Text style={style.phoneInfoItemLabel}>{I18n.t('page_security_label_email')}</Text>
              <Text style={style.phoneInfoItemValue}>{userData?.email || email || '-'}</Text>
            </View>*/}
          </View>

          {/*
          <View style={style.lineSpace} />
          <Touchable onPress={this.onViewMedia}>
            <View style={[style.itemContainer, style.hideLine]}>
              <Text style={style.itemTitleText}>媒体链接及文档</Text>
              <RightArrow style={{ marginLeft: 5 }} color="#999" />
            </View>
          </Touchable>
          <View style={style.mdediaContainer}>
            {medias.map((p, index) => (
              <Touchable onPress={() => this.onViewImages(medias, index)}>
                <Image key={index.toString()} style={style.mediaCard} source={{ uri: p.uri }} />
              </Touchable>
            ))}
          </View> */}
          {isFriend && !isSelf ? (
            <>
              <View style={style.lineSpace} />
              {/*<SettingItem
                label={I18n.t('page_friendTagList_title')}
                value={
                  (userData?.tags && sentryUtil.parse(userData.tags, 'cacd tags')?.join(', ')) || ''
                }
                valueTextStyle={{ maxWidth: 200 }}
                onPress={this.onUpdateFriendTag}
              />*/}
              <SettingItem label={I18n.t('page_user_cell_recommend')} onPress={this.onRecommend} />
              {/* <SettingItem
                label={I18n.t('page_chat_text_special_attention')}
                onSwitch={this.onSetSpecial}
                switched={true}
              /> */}
            </>
          ) : null}
          {!isSelf ? (
            <>
              <SettingItem
                label={I18n.t('page_user_cell_black')}
                onSwitch={this.onSetBlack}
                switched={isBlack}
              />
              <SettingItem label={I18n.t('page_chat_title_complaint')} onPress={this.onComplain} />
            </>
          ) : null}
          {isFriend && !isSelf ? (
            <>
              {/*<View style={style.lineSpace} />
              <SettingItem
                label={I18n.t('page_user_cell_records')}
                onPress={this.onTransferRecord}
              />*/}
              <View style={style.lineSpace} />
              {isFriend && !isSelf ? (
                <Touchable style={style.delBox} onPress={this.onDelete}>
                  <Text style={style.delBoxText}>{I18n.t('page_chat_text_delete_friend')}</Text>
                </Touchable>
              ) : null}
            </>
          ) : null}

          <SafeView bottomHeight={180} />
        </ScrollView>

        {this.renderBottomAction(style)}

        <ModifyModal
          ref={(ref) => (this.modifyNicknameModal = ref)}
          title={I18n.t('page_user_set_remark')}
          buttonAction={this.onModifyFriendNickname}
        />
        <AlertPro
          visible={!!alertInfo}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onCancel={this.onAlertCancel}
          {...(alertInfo || {})}
        />
        <CurrentPageLife navigation={this.props.navigation} isSoftInputMode />
      </View>
    );
  }
}
