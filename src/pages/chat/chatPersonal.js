import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Icon, StatusBar, Text, Touchable, View } from '../../components';
import I18n from '../../i18n';
import styles from '../../themes';
import Chat from './chat';
import { headerHeight } from '../../common';
import NavigationService from '../../navigationService';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    headerContainer: {
      backgroundColor: theme.primaryColor,
      height: headerHeight,
      flexDirection: 'row',
    },
    headerCenterContainer: {
      flex: 3,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerLeftContainer: {
      flex: 1,
    },
    headerRightContainer: {
      flex: 1,
    },
    headerTitle: {
      color: '#fff',
      fontSize: 18,
    },
    searchContainer: {
      marginHorizontal: 16,
      marginVertical: 5,
      borderRadius: 3,
      backgroundColor: '#fff',
      minHeight: 30,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    searchText: {
      color: '#999',
      fontSize: 12,
      marginLeft: 5,
    },
  };
}

/**
 * 聊天页
 */
@inject('stores')
@observer
export default class ChatPersonal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {};
  }

  onSearch = () => {
    NavigationService.navigate('searchChat');
  };

  renderListHeaderComponent = () => {
    return (
      <Touchable onPress={this.onSearch}>
        <View style={this.style.searchContainer}>
          <Icon type="ionicon" name="search" color="#999" size={14} />
          <Text style={this.style.searchText}>{I18n.t('page_chatlist_ph_search')}</Text>
        </View>
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    const { connectStateLabel } = this.props.stores.chatStore;
    return (
      <View style={style.container}>
        <StatusBar
          backgroundColor={style.headerContainer.backgroundColor}
          barStyle="light-content"
        />
        <View style={style.headerContainer}>
          <View style={style.headerLeftContainer} />
          <View style={style.headerCenterContainer}>
            <Text style={style.headerTitle}>
              {I18n.t('menu_nav_bottom_chat')}
              {connectStateLabel}
            </Text>
          </View>
          <View style={style.headerRightContainer} />
        </View>
        <Chat
          navigation={this.props.navigation}
          renderListHeaderComponent={this.renderListHeaderComponent}
        />
      </View>
    );
  }
}
