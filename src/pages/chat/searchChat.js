import React from 'react';
import { BaseComponent, Image, ScrollView, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Avatar from '../../components/avatar/avatar';
import SearchBox from '../../components/input/searchBox';
import NavigationService from '../../navigationService';
import Debounce from 'debounce-decorator';
import I18n from '../../i18n';
import SafeView from '../../components/safeView';
import friendDao from '../../database/dao/friendDao';
import chatGroupDao from '../../database/dao/chatGroupDao';
import Header from '../../components/header/header';
import Spacing from '../../components/divider/spacing';
import avatarUtil from '../../util/avatarUtil';
import NoData from '../../components/empty/noData';
import uiUtil from '../../util/uiUtil';
import chatAction from '../../store/actions/chatAction';
import resIcon from '../../res';
import util from '../../util';
import RightArrow from '../../components/rightArrow';
import imAction from '../../store/actions/imAction';
import chatSessionDao from '../../database/dao/chatSessionDao';
import sentryUtil from '../../util/sentryUtil';

function getComponentStyle(theme) {
  return {
    pageContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    scrollView: {
      flex: 1,
    },
    searchContainer: {
      paddingHorizontal: theme.containerPaddingHorizontal,
      paddingTop: 5,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: theme.primaryBorderColor,
    },
    listTitle: {
      fontSize: theme.fontSizeM,
      color: '#8E96A3',
      marginHorizontal: theme.containerPaddingHorizontal,
      marginVertical: 10,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 18,
      paddingVertical: 10,
    },
    itemAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 14,
    },
    itemName: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 1000,
    },
    itemDateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    itemDate: {
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      paddingLeft: 15,
      minWidth: 60,
      textAlign: 'right',
    },
    itemDesc: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      marginTop: 2,
      flexShrink: 1000,
    },
    itemPhone: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightBold,
      color: theme.primaryColor,
      marginLeft: 12,
    },
    moreContainer: {
      alignItems: 'center',
    },
    moreText: {
      fontSize: theme.fontSizeM,
      color: theme.blueColor,
      marginVertical: 10,
    },
  };
}

const ChatSearchType = {
  friends: 'friends',
  chatGroups: 'chatGroups',
  messages: 'messages',
  chatSession: 'chatSession',
};

/**
 * 聊天搜索会话、聊天记录等
 */
@inject('store')
@observer
export default class SearchChat extends BaseComponent {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    const { searchValue, searchType, sessionId, session } = props.navigation.state.params || {};
    this.searchTypes = searchType
      ? [searchType]
      : [ChatSearchType.chatSession, ChatSearchType.messages];
    this.searchValue = searchValue || '';
    this.sessionId = sessionId;
    this.session = session;
    this.isShowLeast = !searchType; // 是否显示少量的，每组最多显示3条
    this.resultKey = '';
    this.limit = this.isShowLeast ? 4 : 2000;
    this.typeMap = {
      /*[ChatSearchType.friends]: {
        title: 'page_chat_contact',
        onItem: this.onItem,
      },
      [ChatSearchType.chatGroups]: {
        title: 'page_chat_group',
        onItem: this.onItem,
      },*/
      [ChatSearchType.chatSession]: {
        title: 'page_chat_recent',
        onItem: this.onItem,
      },
      [ChatSearchType.messages]: {
        title: () =>
          session
            ? I18n.t('page_chat_msg_count', {
                count: this.state.result[ChatSearchType.messages]?.length,
              })
            : I18n.t('page_chat_msg_record'),
        onItem: this.onItemByMessage,
        getDesc: (item) =>
          session
            ? sentryUtil.parse(item.content)?.content || item.content
            : I18n.t('page_chat_msg_count', { count: item.messageCount }),
      },
    };
    this.state = {
      result: {},
    };
  }

  componentDidMount() {
    if (!this.isShowLeast) {
      this.onSubmitEditing();
    }
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  getKey = (item) => (this.sessionId ? item.msgId : item.groupId || item.imId) || item.sessionId;

  getAvatarByItem = (item) => {
    return item.groupId ? avatarUtil.handleAvatar(item.avatar) : item.avatar || '';
  };

  getAvatar = (item) => {
    if (!this.session) return this.getAvatarByItem(item);
    if (item.senderId === this.session.ownerId) {
      return undefined;
    }
    if (!this.session.groupId) {
      return this.session.avatar;
    }
    return this.messagesResult?.memberMap?.get(item.senderId)?.avatar || '';
  };

  getNameByItem = (item) => (item.groupId ? item.title : item.memo || item.nickname) || item.title;

  getName = (item) => {
    if (!this.session) return this.getNameByItem(item);
    if (item.senderId === this.session.ownerId) {
      return this.props.store.userStore.userName;
    }
    if (!this.session.groupId) {
      return this.session.memo || this.session.nickname || this.session.title;
    }
    const member = this.messagesResult?.memberMap?.get(item.senderId) || {};
    return member.friendMemo || member.title || member.memo || member.nickname || '';
  };

  onSubmitEditing = () => this.onSearch(true);

  onChangeText = (text) => {
    this.searchValue = text;
    this.onSearchDebounce();
  };

  @Debounce(300)
  onSearchDebounce() {
    this.onSearch(false);
  }

  // 搜索
  onSearch = async (showLoading) => {
    const { searchValue, limit } = this;
    if (this.isUnmount || this.resultKey === searchValue) return;
    let result = {};
    let error;
    try {
      if (searchValue) {
        if (showLoading) this.showGlobalLoading();
        const tasks = [];
        this.searchTypes.forEach((type) => {
          switch (type) {
            /*case ChatSearchType.friends:
              tasks.push(friendDao.queryFriends({ searchValue, limit }));
              break;
            case ChatSearchType.chatGroups:
              tasks.push(chatGroupDao.queryGroups({ searchValue, limit }));
              break;*/
            case ChatSearchType.chatSession:
              tasks.push(
                imAction.searchChatSession({ searchValue, limit, result: this.chatSessionResult })
              );
              break;
            case ChatSearchType.messages:
              tasks.push(
                imAction.searMessageListBySession({
                  content: searchValue,
                  limit,
                  result: this.messagesResult,
                  sessionId: this.sessionId,
                  session: this.session,
                })
              );
              break;
          }
        });
        const arr = await Promise.all(tasks);
        arr.forEach((item, index) => {
          if (this.searchTypes[index] === ChatSearchType.chatSession) {
            this.chatSessionResult = item;
            result[this.searchTypes[index]] = item.data;
          } else if (this.searchTypes[index] === ChatSearchType.messages) {
            this.messagesResult = item;
            result[this.searchTypes[index]] = this.sessionId
              ? item.messageList
              : item.sessionList || [];
          } else {
            result[this.searchTypes[index]] = item;
          }
        });
      } else {
        showLoading = false;
      }
    } catch (e) {
      error = e;
    }
    if (this.isUnmount || this.searchValue !== searchValue) return;
    if (!error) {
      this.resultKey = searchValue;
    }
    this.setState({ result });
    if (showLoading) this.showRequestResult(error);
  };

  onMore = (searchType) => {
    NavigationService.push('searchChat', { searchValue: this.resultKey, searchType });
  };

  onSearchPhone = async () => {
    try {
      uiUtil.showGlobalLoading();
      const res = await chatAction.searchContactByPhone(this.resultKey);
      if (res) {
        uiUtil.showRequestResult();
        NavigationService.navigate('chatAddContactDetail', { imId: res.im_id });
      } else {
        uiUtil.showResultMessage(I18n.t('page_chat_label_nouser'));
      }
    } catch (e) {
      uiUtil.showRequestResult(e);
    }
  };

  onItem = async (item) => {
    console.log('onItem item', item);
    const isGroup = !!item.groupId;
    const session = await chatSessionDao.getChatSession({
      session: {
        sessionId: (isGroup ? item.groupId : item.imId) || item.sessionId,
        title: this.getNameByItem(item),
        avatar: item.avatar,
        isGroup,
      },
    });
    console.log('onItem session', session);
    NavigationService.navigate('chatMessage', {
      session,
      sessionId: session.sessionId,
    });
  };

  onItemByMessage = async (item) => {
    // 跳到聊天记录
    if (this.session) {
      this.session.sessionId = this.session.sessionId || this.session.groupId || this.session.imId;
      this.session.title = this.getNameByItem(this.session);
      this.session.isGroup = !!this.session.groupId;
      const session = await chatSessionDao.getChatSession({
        session: this.session,
      });
      NavigationService.push('chatMessage', {
        session,
        sessionId: session.sessionId,
        searchMessage: item,
      });
      return;
    }
    item.isGroup = !!item.groupId;
    item.sessionId = item.sessionId || item.groupId || item.imId;
    NavigationService.push('searchChat', {
      searchValue: this.resultKey,
      searchType: ChatSearchType.messages,
      sessionId: item.sessionId,
      session: item,
    });
  };

  renderItem = (item, index, typeObj) => {
    if (this.isShowLeast && index > this.limit - 2) return null;
    const { style } = this;
    const name = this.getName(item);
    return (
      <Touchable
        key={this.getKey(item)}
        onPress={() => typeObj.onItem(item)}
        style={style.itemContainer}
      >
        <Avatar avatar={this.getAvatar(item)} name={name} style={style.itemAvatar} />
        {typeObj.getDesc ? (
          <View style={{ flex: 1 }}>
            {item.msgTime ? (
              <View style={style.itemDateContainer}>
                <Text style={style.itemName} numberOfLines={1}>
                  {name}
                </Text>
                <Text style={style.itemDate} numberOfLines={1}>
                  {util.formatChatTime(item.msgTime)}
                </Text>
              </View>
            ) : (
              <Text style={style.itemName} numberOfLines={1}>
                {name}
              </Text>
            )}
            <Text style={style.itemDesc} numberOfLines={1}>
              {typeObj.getDesc(item)}
            </Text>
          </View>
        ) : (
          <Text style={style.itemName} numberOfLines={1}>
            {this.getName(item)}
          </Text>
        )}
      </Touchable>
    );
  };

  renderSearchPhone = () => {
    if (!this.isShowLeast || !this.resultKey?.match(/^\d{6,15}$/)) return null;
    const { style } = this;
    const content = (
      <Touchable onPress={this.onSearchPhone} style={style.itemContainer}>
        <Image source={resIcon.findPhone} style={style.itemAvatar} />
        <Text style={style.itemName} numberOfLines={1}>
          {I18n.t('page_chat_find_phone')}
        </Text>
        <Text style={style.itemPhone}>{this.resultKey}</Text>
      </Touchable>
    );
    return { content, type: 'phone' };
  };

  renderList = (type) => {
    const list = this.state.result[type];
    if (!list?.length) return null;
    const typeObj = this.typeMap[type];
    const content = (
      <>
        <Text style={this.style.listTitle}>
          {typeof typeObj.title === 'function' ? typeObj.title() : I18n.t(typeObj.title)}
        </Text>
        {list.map((item, index) => this.renderItem(item, index, typeObj))}
        {this.isShowLeast && list.length > this.limit - 1 ? (
          <Touchable style={this.style.moreContainer} onPress={() => this.onMore(type)}>
            <Text style={this.style.moreText}>{I18n.t('page_chat_more')}</Text>
          </Touchable>
        ) : null}
      </>
    );
    return { content, type };
  };

  render() {
    const { style } = this;
    const content = [
      // this.renderSearchPhone(),
      // this.renderList(ChatSearchType.friends),
      // this.renderList(ChatSearchType.chatGroups),
      this.renderList(ChatSearchType.chatSession),
      this.renderList(ChatSearchType.messages),
    ].filter((item) => item);
    return (
      <View style={style.pageContainer}>
        <Header title={I18n.t('op_search_title')} />
        <View style={style.searchContainer}>
          <SearchBox
            onChangeText={this.onChangeText}
            onSubmitEditing={this.onSubmitEditing}
            defaultValue={this.searchValue}
            autoFocus
            showRight
          />
        </View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={style.scrollView}
          keyboardShouldPersistTaps="handled"
        >
          {this.session ? (
            <>
              <Touchable onPress={() => this.onItem(this.session)} style={style.itemContainer}>
                <Avatar
                  avatar={this.getAvatarByItem(this.session)}
                  name={this.getName(this.session)}
                  style={style.itemAvatar}
                />
                <Text style={[style.itemName, { flex: 1 }]} numberOfLines={1}>
                  {this.getNameByItem(this.session)}
                </Text>
                <RightArrow />
              </Touchable>
              <Spacing />
            </>
          ) : null}
          {content.map((item, index) => {
            return (
              <View key={item.type}>
                {index > 0 ? <Spacing /> : null}
                {item.content}
              </View>
            );
          })}
          {!content.length && this.resultKey ? <NoData /> : null}
        </ScrollView>
        <SafeView bottomHeight={0} />
      </View>
    );
  }
}
