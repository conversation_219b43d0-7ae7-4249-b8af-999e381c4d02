import React from 'react';
import { StyleSheet, Text, View, Image } from '../../../components';
import navigationService from '../../../navigationService';
import memoize from 'memoize-one';
import I18n from '../../../i18n';
import resIcon from '../../../res';

const styles = StyleSheet.create({
  text: {
    fontSize: 12,
    color: '#A6A5A5',
    maxWidth: '65%',
    textAlign: 'center',
    marginTop: 5,
    marginBottom: 10,
    alignSelf: 'center',
  },
  sendAddFriendText: {
    fontSize: 12,
    color: '#5389F5',
  },
  redPacketBox: {
    // maxWidth: '65%',
    paddingHorizontal: 18,
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center',
    marginTop: 5,
    marginBottom: 10,
  },
  redPacketText: {
    fontSize: 12,
    color: '#A6A5A5',
    marginLeft: 6,
  },
});

/**
 * 系统消息
 * <AUTHOR>
 */
export default class MessageSystem extends React.Component {
  onSendAddFriend = async () => {
    const { currentMessage } = this.props;
    navigationService.navigate('requestAddFriend', {
      imId: currentMessage.sessionId,
    });
  };

  getTipType = memoize((extra) => {
    try {
      return extra && JSON.parse(extra);
    } catch (e) {
      return '';
    }
  });

  getRedPacketText = () => {
    const { currentMessage, user } = this.props;
    const extra = this.getTipType(currentMessage.extra);

    if (user?._id !== currentMessage.senderId) {
      return `${I18n.t('page_message_tips_be_received', {
        name: extra?.openName,
      })}`;
    }
    return `${I18n.t('page_message_tips_receive', {
      name: extra?.fromName,
    })}`;
  };

  render() {
    const { currentMessage, user } = this.props;
    if (!currentMessage) {
      return null;
    }
    const extra = this.getTipType(currentMessage.extra);
    return extra?.tipType === 'redPacket' ? (
      <View style={styles.redPacketBox}>
        <Image source={resIcon.chatLittleRedPacket} style={{ marginTop: 2 }} />
        <Text style={styles.redPacketText}>{this.getRedPacketText()}</Text>
      </View>
    ) : (
      <Text style={styles.text}>
        {currentMessage.text}
        {extra?.tipType === 'requestAddFriend' ? (
          <Text style={styles.sendAddFriendText} onPress={this.onSendAddFriend}>
            {I18n.t('page_friend_op_add')}
          </Text>
        ) : null}
      </Text>
    );
  }
}
