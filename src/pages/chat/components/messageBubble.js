import React from 'react';
import Bubble from './messageBubbleContent';
import { View, Image, StyleSheet, Text, Touchable } from '../../../components';
import MessageSendStatus from './messageSendStatus';
import constant from '../../../store/constant';
import MessageNickname from './messageNickname';
import MessageImage from './messageImage';
import MessageQuote from './messageQuote';
import MessageVideoFuture from './messageVideoFuture';
import MessageFile from './messageFile';
import MessageAudio from './messageAudio';
import MessageBusinessCard from './messageBusinessCard';
import resIcon from '../../../res';
import { inject, observer } from 'mobx-react';
import I18n from '../../../i18n';
import PermissionUtil from '../../../util/permissionUtilExtra';
import emoji from '../../../res/chatEmoji';
import sentryUtil from '../../../util/sentryUtil';
import { Color, Size } from './messageConfig';
import { Swipeable } from 'react-native-gesture-handler';
import chatMessageOptions from '../../../database/chatMessageOptions';
import MessageReqResume from './messageReqResume';
import MessageInviteInterview from './messageInviteInterview';
import MessageSendResume from './messageSendResume';
import MessageCompanyBusinessCard from './messageCompanyBusinessCard';

const styles = StyleSheet.create({
  nameContainer: {
    flexShrink: 1000,
    backgroundColor: '#ffffff',
    marginBottom: 4,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    borderBottomRightRadius: 10,
    marginRight: 60,
    // overflow: 'hidden',
    shadowColor: '#00000050',
    shadowOffset: { h: 10 },
    shadowOpacity: 0.2,
    elevation: 5,
  },

  container: {
    overflow: 'visible',
    flexShrink: 1000,
  },
  leftCallContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 40,
  },
  rightCallContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingRight: 40,
  },
  callImage: { width: 20, height: 20 },
  leftCallText: {
    fontSize: Size.textFontSize,
    color: Color.leftTextColor,
    fontWeight: '400',
    marginLeft: 14,
  },
  rightCallText: {
    fontSize: Size.textFontSize,
    color: Color.rightTextColor,
    fontWeight: '400',
    marginRight: 14,
  },
  replyContainer: {
    width: 40,
  },
  replyImageWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  replyImage: {
    width: 20,
    height: 20,
  },
  hitSlop: { top: 10, bottom: 10, left: 10, right: -60 },
});

const activeOffsetX = [-30, 30];
const failOffsetY = activeOffsetX;

function nullFun() {
  return null;
}

function isSupportQuote(props) {
  return (
    props.currentMessage &&
    chatMessageOptions.isSupportQuote(props.currentMessage, null, null, props.session)
  );
}

function onSwipeableOpen(swipeable) {
  if (isSupportQuote(swipeable.props)) {
    global.emitter.emit(constant.event.quoteMessage, { message: swipeable.props.currentMessage });
  }
  swipeable.close();
}

function renderLeftActions(showLeftAction, transX, props) {
  if (isSupportQuote(props)) {
    return (
      <View style={styles.replyContainer}>
        <View style={styles.replyImageWrapper}>
          <Image source={resIcon.reply} style={styles.replyImage} />
        </View>
      </View>
    );
  }
  return null;
}

@inject('callAction')
@observer
export default class MessageBubble extends React.Component {
  renderCustomView = (props) => {
    if (props.currentMessage.quoteMessage) {
      return <MessageQuote {...props} />;
    }
    return null;
  };

  // 重新发起呼叫
  onPress = async ({ session, currentMessage }) => {
    const extra = sentryUtil.parse(currentMessage.extra, 'mb op') || {};
    if (this.props.callAction.checkInCall()) {
      return;
    }
    if (extra.callType === constant.callType.singleVideoCall) {
      await PermissionUtil.requestMultiple();
    } else {
      await PermissionUtil.requestAudioPermission();
    }
    this.props.callAction.startCall(extra.callType, session.sessionId);
  };

  renderMessageText = (props) => {
    const extra = sentryUtil.parse(props.currentMessage.extra, 'mb rmt') || {};
    let image;
    if (extra.callType === constant.callType.singleAudioCall) {
      image = resIcon.messageAudio;
      // image = props.position === 'left' ? resIcon.messageAudio : resIcon.messageAudioWhite;
    } else {
      image = resIcon.messageVideo;
      // image = props.position === 'left' ? resIcon.messageVideo : resIcon.messageVideoWhite;
    }
    let text = '';
    if (extra.callState === constant.callState.reject) {
      text =
        props.position === 'left'
          ? I18n.t('page_call_message_reject')
          : I18n.t('page_call_message_peer_reject');
    } else if (extra.callState === constant.callState.cancel) {
      text =
        props.position === 'left'
          ? I18n.t('page_call_tips_peer_cancel')
          : I18n.t('page_call_tips_canceled');
    } else if (extra.callState === constant.callState.end) {
      const time = this.props.callAction.formatHHMMSS(extra.timeCount);
      text = `${I18n.t('page_call_message_time')} ${time}`;
    } else if (extra.callState === constant.callState.noResponse) {
      text = I18n.t('page_call_tips_no_response');
    } else if (extra.callState === constant.callState.busy) {
      text =
        props.position === 'left'
          ? I18n.t('page_call_message_self_busy')
          : I18n.t('page_call_tips_busy');
    }

    return (
      <Touchable
        onPress={() => this.onPress(props)}
        style={styles[props.position + 'CallContainer']}
      >
        <Image style={styles.callImage} source={image} />
        <Text style={styles[props.position + 'CallText']}>{text}</Text>
      </Touchable>
    );
  };

  renderText(props, text = props.currentMessage.text) {
    const bigEmoji =
      !props.currentMessage.quoteMessage && emoji.getBigEmoji(props.currentMessage.text);
    return (
      <Bubble
        {...props}
        text={text}
        bigEmoji={bigEmoji}
        noBubble={!!bigEmoji}
        renderTime={nullFun}
        renderTicks={nullFun}
        renderCustomView={this.renderCustomView}
      />
    );
  }

  render() {
    const { props } = this;
    let content;
    switch (props.currentMessage.type) {
      case constant.messageType.text:
      case constant.messageType.referTo:
        content = this.renderText(props);
        break;
      case constant.messageType.businessCard:
        content = <MessageBusinessCard {...props} />;
        break;
      case constant.messageType.reqResume:
        content = <MessageReqResume {...props} />;
        break;
      case constant.messageType.sendResume:
        content = <MessageSendResume {...props} />;
        break;
      case constant.messageType.inviteInterview:
        content = <MessageInviteInterview {...props} />;
        break;
      case constant.messageType.companyBusinessCard:
        content = <MessageCompanyBusinessCard {...props} />;
        break;
      case constant.messageType.image:
        content = <MessageImage {...props} />;
        break;
      case constant.messageType.video:
        content = <MessageVideoFuture {...props} />;
        break;
      case constant.messageType.file:
        content = <MessageFile {...props} />;
        break;
      case constant.messageType.audio:
        content = <MessageAudio {...props} />;
        break;
      case constant.messageType.call:
        content = (
          <Bubble
            {...props}
            renderTime={nullFun}
            renderTicks={nullFun}
            renderCustomView={nullFun}
            renderMessageText={this.renderMessageText}
          />
        );
        break;
      default:
        content = this.renderText(props, I18n.t('tip_unsupported_message'));
        break;
    }
    if (props.currentMessage.isGroup && !props.currentMessage.isSelfSend) {
      content = (
        <View style={styles.nameContainer}>
          <MessageNickname message={props.currentMessage} memberMap={props.memberMap} />
          {content}
        </View>
      );
    }

    return (
      <>
        <MessageSendStatus message={props.currentMessage} position={props.position} />
        <Swipeable
          friction={1}
          rightThreshold={40}
          currentMessage={props.currentMessage}
          session={props.session}
          renderLeftActions={renderLeftActions}
          onSwipeableOpen={onSwipeableOpen}
          overshootLeft={false}
          containerStyle={styles.container}
          hitSlop={styles.hitSlop}
          activeOffsetX={activeOffsetX}
          failOffsetY={failOffsetY}
        >
          {content}
        </Swipeable>
      </>
    );
  }
}
