import React from 'react';
import { ActivityIndicator, Image, StyleSheet, Touchable, View } from '../../../components';
import constant from '../../../store/constant';
import resIcon from '../../../res';
import receiptMessageUtil from '../../../database/receiptMessageUtil';
import sendMessageUtil from '../../../database/sendMessageUtil';

const styles = StyleSheet.create({
  container: {
    minWidth: 60,
    flex: 1,
    alignItems: 'flex-end',
  },
  sendingIndicator: {
    marginRight: 10,
  },
  failedImg: {
    marginRight: 10,
  },
});

const sendTimeout = 10000;

function getSendState(message, sendState = message.sendState) {
  if (
    sendState === constant.messageSendState.sending &&
    !receiptMessageUtil.existUpload(message.seq)
  ) {
    const time = new Date().getTime() - message.msgTime;
    if (time > 2000 && !receiptMessageUtil.exist(message.seq)) {
      return constant.messageSendState.failed;
    }
    return time <= 0 || time > sendTimeout ? constant.messageSendState.failed : sendState;
  }
  return sendState;
}

export default class MessageSendStatus extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      sendState: '',
    };
  }

  onResend = async () => {
    const { message } = this.props;
    await sendMessageUtil.resend({ message });
    this.setState({ sendState: message.sendState });
  };

  static getDerivedStateFromProps({ message }, prevState) {
    if (message.isSelfSend && prevState.sendState !== message.sendState) {
      return {
        sendState: getSendState(message),
      };
    }
    return null;
  }

  componentDidMount() {
    this.checkSending();
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    this.checkSending();
  }

  componentWillUnmount() {
    this.stopTimer();
  }

  checkSending = () => {
    this.stopTimer();
    const { sendState } = this.state;
    const { message } = this.props;
    const cSendState = getSendState(message, sendState);
    // console.log('MessageSendStatus checkSending' + message.seq, cSendState, sendState, message.sendState);
    if (cSendState !== constant.messageSendState.sending) {
      if (cSendState !== sendState) {
        this.setState({ sendState: cSendState });
      }
      return;
    }
    const time = Math.min(message.msgTime + sendTimeout - new Date().getTime(), sendTimeout);
    // console.log('MessageSendStatus checkSending' + message.seq, time);
    if (time > 0) {
      this.sendingTimer = setTimeout(this.checkSending, time);
    }
  };

  stopTimer = () => {
    if (this.sendingTimer) {
      clearTimeout(this.sendingTimer);
      this.sendingTimer = null;
    }
  };

  renderStatus = () => {
    const { sendState } = this.state;
    if (sendState === constant.messageSendState.successful) {
      return null;
    }
    if (sendState === constant.messageSendState.failed) {
      return (
        <Touchable onPress={this.onResend}>
          <Image source={resIcon.messageSendStatusFail} style={styles.failedImg} />
        </Touchable>
      );
    }
    return <ActivityIndicator size="small" color="#FFB731" style={styles.sendingIndicator} />;
  };

  render() {
    const { message } = this.props;
    if (!message.isSelfSend) {
      return null;
    }
    // console.log('MessageSendStatus checkSending' + message.seq, message.sendState);
    return <View style={styles.container}>{this.renderStatus()}</View>;
  }
}
