import React from 'react';
import { inject, observer } from 'mobx-react';
import chatSessionDao from '../../../database/dao/chatSessionDao';
import chatGroupDao from '../../../database/dao/chatGroupDao';
import { Image, Text, Touchable, View } from '../../../components';
import Avatar from '../../../components/avatar/avatar';
import util from '../../../util';
import NavigationService from '../../../navigationService';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import styles from '../../../themes';
import avatarUtil from '../../../util/avatarUtil';
import sentryUtil from '../../../util/sentryUtil';
import constant from '../../../store/constant';
import uiUtil from '../../../util/uiUtil';

function getStyle() {
  const theme = styles.get('theme');
  return {
    chatItem: {
      flexDirection: 'row',
      paddingVertical: 10,
      paddingHorizontal: 15,
      backgroundColor: '#fff',
    },
    topChatImage: {
      position: 'absolute',
      right: 6,
      top: 5,
    },
    chatItemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 14,
      marginRight: 14,
      // elevation: 5,
    },
    chatItemContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    chatItemInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      minHeight: 25,
    },
    chatItemInfoLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    chatItemName: {
      fontSize: 17,
      fontWeight: theme.fontWeightMedium,
      color: '#484848',
      flexShrink: 1000,
    },
    chatItemPost: {
      fontSize: 12,
      color: '#B5B5B5',
      marginLeft: 8,
    },
    strangerContainer: {
      backgroundColor: theme.primaryLightColor,
      borderRadius: 5,
      paddingHorizontal: 5,
      paddingVertical: 2,
      marginLeft: 5,
    },
    strangerText: {
      fontSize: 12,
      color: theme.primaryColor,
    },
    strangerSpace: {
      flex: 1,
    },
    chatDate: {
      fontSize: 12,
      color: '#8E96A3',
      marginLeft: 15,
    },
    itemCompany: {
      fontSize: 12,
      color: '#999',
      lineHeight: 18,
      paddingRight: 15,
      flexShrink: 1000,
    },
    chatDescInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      minHeight: 25,
    },
    chatDesc: {
      fontSize: 14,
      color: '#666',
      lineHeight: 18,
      paddingRight: 15,
      flexShrink: 1000,
    },
    atText: {
      color: '#3299FF',
    },
    chatNumBox: {
      paddingHorizontal: 5,
      paddingVertical: 1,
      borderRadius: 9,
      backgroundColor: '#FF4B5A',
    },
    chatNum: {
      fontSize: 10,
      fontWeight: '500',
      color: '#FFFFFF',
    },
    disturbBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    distubDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF4B5A',
      marginLeft: 6,
    },
    btmLine: {
      height: 1,
      backgroundColor: 'rgba(220, 224, 235, 0.4)',
      marginLeft: 79,
      marginRight: 15,
    },
  };
}

@inject('imAction', 'userStore')
@observer
export default class ChatItem extends React.Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
  }

  onMessage = () => {
    const { item, index } = this.props;
    console.log('ChatItem onMessage', index, item);
    NavigationService.navigate('chatMessage', {
      session: item,
      sessionId: item.sessionId,
    });
    item.unReadNum = 0;
    chatSessionDao.updateSession(item);
  };

  onDeleteChat = () => {
    const { item } = this.props;
    uiUtil.showAlert({
      title: item.isGroup ? I18n.t('page_chat_op_delete_p2p') : I18n.t('page_chat_op_delete_group'),
      onConfirm: this.onDeleteChatConfirm,
    });
  };

  onDeleteChatConfirm = async () => {
    try {
      uiUtil.dismissAlert();
      await this.props.imAction.deleteSession(this.props.item);
    } catch (e) {
      logger.warn('onDeleteChatConfirm', e);
    }
  };

  onSticky = async () => {
    const { item } = this.props;
    const setTopTime = item?.setTopTime > 0 ? 0 : new Date().getTime();
    item.setTopTime = setTopTime;
    await chatSessionDao.updateSet(item);
    const groupInfo = await chatGroupDao.getGroup(item?.sessionId);
    groupInfo.setTopTime = setTopTime;
    await chatGroupDao.updateGroup(groupInfo);
  };

  onShowActionSheet = () => {
    const { item } = this.props;
    const options = [
      {
        onPress: this.onSticky,
        title: item.setTopTime > 0 ? I18n.t('page_chat_cancal_top') : I18n.t('page_chat_top'),
      },
    ];
    if (!item.isGPT) {
      options.push({
        onPress: this.onDeleteChat,
        title: I18n.t('page_chat_delete'),
      });
    }
    global.emitter.emit(constant.event.showActionSheet, {
      page: 'chat',
      options,
    });
  };

  renderUnreadNum = (item) => {
    const { style } = this;
    if (item.isDisturb) {
      return (
        <View style={style.disturbBox}>
          <Image source={resIcon.messageIconDisturb} />
          {item.unReadNum > 0 ? <View style={style.distubDot} /> : null}
        </View>
      );
    }
    if (!item.unReadNum) {
      return null;
    }
    return (
      <View style={style.chatNumBox}>
        <Text style={style.chatNum}>{item.unReadNum > 99 ? '99+' : item.unReadNum}</Text>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { item } = this.props;
    const contentData = (item?.content && sentryUtil.parse(item.content, 'ci render')) || item;
    const nickname = (item?.isGroup && contentData?.nickname) || '';
    let content = contentData?.content;
    if (contentData?.isLanguage) {
      if (contentData.extraName) {
        content = I18n.t(contentData.content, {
          name: contentData.extraName,
        });
      } else if (contentData.params) {
        content = I18n.t(contentData.content, contentData.params);
      } else {
        content = I18n.t(contentData.content);
      }
    }
    content = `${nickname}${content || ''}`;

    const jobTitle = this.props.userStore.isCompany ? item.jobTitle : item.positionName;
    content = (
      <Text style={style.chatDesc} numberOfLines={1}>
        {item.atCount ? (
          <Text style={style.atText}>{I18n.t('page_chat_text_somebody_to_you')} </Text>
        ) : null}
        {content}
      </Text>
    );
    return (
      <>
        <Touchable onPress={this.onMessage} onLongPress={this.onShowActionSheet} withoutFeedback>
          <View style={{ backgroundColor: '#fff' }}>
            <View style={style.chatItem}>
              <Avatar
                avatar={avatarUtil.handleAvatar(item.avatar)}
                name={item.title}
                style={style.chatItemAvatar}
                defaultAvatar={resIcon.defaultAvatar}
                // showDot={item?.isGroup == 0}
              />
              <View style={style.chatItemContainer}>
                <View style={style.chatItemInfo}>
                  <View style={style.chatItemInfoLeft}>
                    <Text style={style.chatItemName} numberOfLines={1}>
                      {item.title}
                    </Text>
                    {jobTitle ? <Text style={style.chatItemPost}>{jobTitle}</Text> : null}
                  </View>
                  <Text style={style.chatDate}>{util.dateToLocalString6(item.lastMsgTime)}</Text>
                </View>
                <View style={style.chatDescInfo}>
                  {item.company ? (
                    <Text style={style.itemCompany} numberOfLines={1}>
                      {item.company}
                    </Text>
                  ) : (
                    content
                  )}
                  {this.renderUnreadNum(item)}
                </View>
                {item.company ? content : null}
              </View>
              {item.setTopTime ? (
                <Image source={resIcon.icTopChat} style={style.topChatImage} />
              ) : null}
            </View>
            <View style={style.btmLine} />
          </View>
        </Touchable>
      </>
    );
  }
}
