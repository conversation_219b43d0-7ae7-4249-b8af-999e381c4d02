import React from 'react';
import { Icon, Image, Text, Touchable, View } from '../../../components';
import resIcon from '../../../res';
import { statusBarHeight } from '../../../common';
import I18n from '../../../i18n';
import sendMessageUtil from '../../../database/sendMessageUtil';
import VideoRecorder from 'react-native-beautiful-video-recorder';
import { RNCamera } from 'react-native-camera';
import PermissionUtil from '../../../util/permissionUtilExtra';
import fileUtil from '../../../util/fileUtil';
import callAction from '../../../store/actions/callAction';
import deviceInfo from '../../../util/deviceInfo';
import ImagePickerUtil, { compression } from '../../../util/imagePickerUtil';

const topHeight = statusBarHeight + 4;

function getStyle() {
  return {
    buttonCloseStyle: {
      position: 'absolute',
      width: 40,
      height: 40,
      left: 18,
      top: topHeight,
      alignSelf: 'flex-start',
      // left: 36,
      // bottom: 35,
      // alignSelf: 'flex-end',
    },
    buttonSwitchCameraStyle: {
      position: 'absolute',
      top: topHeight,
      // top: 24,
      right: 18,
    },
    buttonSelectMediaStyle: {
      position: 'absolute',
      width: 40,
      height: 40,
      left: 36,
      bottom: 45,
      alignSelf: 'flex-end',
    },
    durationTextStyle: {
      marginTop: topHeight,
      color: 'white',
      textAlign: 'center',
      fontSize: 20,
      alignItems: 'center',
    },
    sendTextBox: {
      paddingHorizontal: 10,
      paddingVertical: 4,
      borderRadius: 4,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#04BE02',
    },
    sendText: {
      color: '#ffffff',
      fontSize: 16,
    },
    buttonUseStyle: { right: 24, top: 40 },
    photoBtnUseStyle: { bottom: 20 },
    controlsStyle: { height: 150 },
  };
}

/**
 * 聊天页面，底部更多功能
 * <AUTHOR>
 */
export default class MessageCamera extends React.Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.session = props.session;
    this.state = {
      path: null,
    };
    this.androidCameraPermissionOptions = {
      title: I18n.t('page_component_qr_camera_permission_title'),
      message: I18n.t('page_component_qr_camera_permission_tips', {
        appName: deviceInfo.getApplicationName(),
      }),
      buttonPositive: I18n.t('op_confirm_title'),
      buttonNegative: I18n.t('op_cancel_title'),
    };
  }

  checkDisableImage = () => {
    if (this.props.isDisableImage) {
      toast.show(I18n.t('page_groupManage_msg_no_image'));
      return true;
    }
    return false;
  };

  onShow = async () => {
    if (this.checkDisableImage()) return;
    await PermissionUtil.requestCameraPermission();
    if (callAction.checkInCall()) {
      return;
    }
    const path = await fileUtil.getMsgFilePath({
      ownerId: this.session.ownerId,
      sessionId: this.session.sessionId,
      type: 'video',
      suffix: IS_IOS ? '.mov' : '.mp4',
    });
    this.setState({ path }, () => {
      this.videoRecorder?.open({ maxLength: 15 }, async (data) => {
        try {
          await sendMessageUtil.sendVideo(data, this.session);
        } catch (e) {
          logger.warn('messageMoreFeat sendVideo', e);
        }
      });
    });
  };

  // 选择相册视频
  onVideo = async () => {
    try {
      const images = await ImagePickerUtil.openPicker();
      if (!images?.length) return;
      this.videoRecorder?.close();
      await sendMessageUtil.sendImages(images, this.session);
    } catch (e) {
      toast.show(e?.message);
    }
  };

  // 拍照发送
  onTakePhoto = async (image) => {
    try {
      this.videoRecorder?.close();
      delete image.base64;
      console.log('onTakePhoto', image);
      const imgInfo = await fileUtil.getImageInfo(image.uri);
      image.width = imgInfo.width;
      image.height = imgInfo.height;
      image.size = imgInfo.size;
      image.type = 'image';
      await compression(image);
      await sendMessageUtil.sendImage(image, this.session);
    } catch (e) {
      toast.show(e?.message);
    }
  };

  // 预览录制的视频
  onPlayVideo = () => {
    // this.videoRecorder?.close();
    // global.emitter.emit(constant.event.playVideo, video);
  };

  refVideoRecorder = (ref) => (this.videoRecorder = ref);

  renderSwitchCamera = () => {
    return <Icon name="camera-reverse" size={32} type="ionicon" color="#fff" />;
  };

  renderDone = () => {
    const { style } = this;
    return (
      <View style={style.sendTextBox}>
        <Text style={style.sendText}>{I18n.t('page_friend_add_btn')}</Text>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { path } = this.state;
    if (!path) return null;
    const recordOptions = {
      mute: false,
      maxDuration: 15,
      whiteBalance: 'auto',
      quality: RNCamera.Constants.VideoQuality['720p'],
      videoBitrate: 5 * 1000 * 1000,
      path,
    };
    if (IS_IOS) {
      recordOptions.codec = RNCamera.Constants.VideoCodec.H264;
    }
    return (
      <VideoRecorder
        ref={this.refVideoRecorder}
        type={RNCamera.Constants.Type.back}
        recordOptions={recordOptions}
        androidCameraPermissionOptions={this.androidCameraPermissionOptions}
        androidRecordAudioPermissionOptions={this.androidCameraPermissionOptions}
        buttonCloseStyle={style.buttonCloseStyle}
        buttonSwitchCameraStyle={style.buttonSwitchCameraStyle}
        buttonUseStyle={style.buttonUseStyle}
        photoBtnUseStyle={style.photoBtnUseStyle}
        controlsStyle={style.controlsStyle}
        durationTextStyle={style.durationTextStyle}
        cameraTopText={I18n.t('page_chat_tips_long_press_record')}
        renderSwitchCamera={this.renderSwitchCamera}
        onTakePhoto={this.onTakePhoto}
        onPlayVideo={this.onPlayVideo}
        renderDone={this.renderDone}
      >
        <Touchable onPress={this.onVideo} style={style.buttonSelectMediaStyle}>
          <Image source={resIcon.scanPic} resizeMode="contain" />
        </Touchable>
      </VideoRecorder>
    );
  }
}
