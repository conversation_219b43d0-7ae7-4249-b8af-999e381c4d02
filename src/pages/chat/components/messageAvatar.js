import React from 'react';
import { inject, observer } from 'mobx-react';
import Avatar from '../../../components/avatar/avatar';
import chatMessageUtil from '../../../database/chatMessageUtil';
import { StyleSheet, Touchable, Image } from '../../../components';
import NavigationService from '../../../navigationService';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import constant from '../../../store/constant';

const styles = StyleSheet.create({
  left: { marginRight: 0, marginLeft: 2, position: 'relative' },
  right: { marginLeft: 2, position: 'relative' },
  image: { position: 'absolute', bottom: 0, left: 0, width: 28, height: 8 },
});

@inject('userStore')
@observer
export default class MessageAvatar extends React.Component {
  onUserDetail = () => {
    const { currentMessage, session, groupInfo, memberMap } = this.props;
    if (currentMessage.isSelfSend) {
      NavigationService.navigate('security');
      return;
    }
    if (this.userType === constant.userType.manage) return;
    console.log('MessageAvatar', groupInfo);
    if (
      groupInfo &&
      !groupInfo.memberVisible &&
      memberMap?.get(this.props.userStore.imId)?.role === 'member'
    ) {
      toast.show(I18n.t('page_message_msg_disable_view_member'));
      return;
    }
    NavigationService.navigate('chatAddContactDetail', {
      imId: currentMessage.senderId,
      session,
    });
  };

  render() {
    const { currentMessage, session, memberMap, position } = this.props;
    if (currentMessage.isSelfSend) return null;
    const { avatar, name, userType } = chatMessageUtil.getMessageUserInfo(
      currentMessage,
      memberMap
    );
    this.userType = userType;
    return (
      <Touchable onPress={this.onUserDetail} style={styles[position]} disabled>
        <Avatar avatar={avatar} name={name} size={30} />
        {userType === constant.userType.robot ? (
          <Image source={resIcon.messageRobotAvatar} style={styles.image} resizeMode="contain" />
        ) : null}
      </Touchable>
    );
  }
}
