import React, { Component } from 'react';
import { View, Input, Touchable, Text, Keyboard } from '../../../components/index';
import styles from '../../../themes';
import I18n from '../../../i18n';
import BottomModal from '../../../components/modal/bottomModal';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: '#fff',
      flex: 1,
      paddingHorizontal: 12,
    },
    bottomContainer: {
      marginTop: 20,
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    remarkLabelText: {
      fontSize: theme.fontSizeL,
      fontWeight: '500',
      color: '#4F5866',
      lineHeight: 21,
    },
    remarkInputContainer: {
      backgroundColor: theme.inputBgColor,
      height: 48,
      borderRadius: 5,
      borderWidth: 1,
      paddingLeft: 15,
      borderColor: theme.primaryBorderColor,
    },
    remarkLabel: {
      color: theme.inputLabelFontColor,
      fontSize: 15,
      fontWeight: theme.fontWeightMedium,
    },
    remarkInput: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
    placeholderTextColor: theme.placeholderFontColor,
    sendImg: {
      flexShrink: 0,
    },
    btngroup: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 15,
      paddingHorizontal: 11,
    },
    btnContainer: {
      backgroundColor: theme.primaryColor,
      height: 40,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
    },
    cancelContainer: {
      backgroundColor: '#E8EDFF',
      marginRight: 20,
    },
    addText: {
      fontSize: theme.fontSizeL,
      color: theme.simpleFontColor,
      textAlign: 'center',
    },
  };
}
/**
 * 修改信息弹出框
 * <AUTHOR>
 */

export default class ModifyModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      content: '',
      showModal: false,
    };
  }

  show = (content) => {
    this.setState({ content, showModal: true }, () => {
      setTimeout(() => {
        this.remarkInput.focus();
      }, 300);
    });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = () => {
    const { content } = this.state;
    if (this.props.buttonAction) {
      Keyboard.dismiss();
      this.props.buttonAction(content);
    }
  };

  onContentChange = (text) => {
    this.setState({ content: text });
  };

  render() {
    const { ...rest } = this.props;
    const { title = I18n.t('page_chat_modify_title') } = this.props;
    const { content, showModal } = this.state;
    const { style } = this;
    const contentHeight = 180;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={title}
        contentHeight={contentHeight}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        {...rest}
      >
        <View style={style.pagerContainer}>
          <View style={style.bottomContainer}>
            <View style={{ flex: 1 }}>
              <Input
                placeholder={I18n.t('op_ph_input')}
                onChangeText={this.onContentChange}
                returnKeyType="done"
                inputStyle={style.remarkInput}
                inputContainerStyle={style.remarkInputContainer}
                labelStyle={style.remarkLabel}
                placeholderTextColor={style.placeholderTextColor}
                value={content}
                autoFocus
                onSubmitEditing={this.onConfirm}
                maxLength={20}
                // clearButtonMode="while-editing"
                ref={(refs) => (this.remarkInput = refs)}
              />
            </View>
          </View>
          <View style={style.btngroup}>
            <Touchable style={[style.btnContainer, style.cancelContainer]} onPress={this.close}>
              <Text>{I18n.t('op_cancel_title')}</Text>
            </Touchable>
            <Touchable style={style.btnContainer} onPress={this.onConfirm}>
              <Text style={style.addText}>{I18n.t('op_confirm_title')}</Text>
            </Touchable>
          </View>
        </View>
      </BottomModal>
    );
  }
}
