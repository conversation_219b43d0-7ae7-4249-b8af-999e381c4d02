import React, { Component } from 'react';
import { StyleSheet, Text, Touchable, View } from '../../../components';
import { WIDTH } from '../../../common';
import Avatar from '../../../components/avatar/avatar';
import NavigationService from '../../../navigationService';
import I18n from '../../../i18n';

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
    paddingHorizontal: 10,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
    width: WIDTH(230),
    flexDirection: 'column',
    marginBottom: 4,
  },
  leftContainer: {
    borderTopLeftRadius: 0,
  },
  rightContainer: {
    borderTopRightRadius: 0,
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 15,
  },
  info: {
    flexDirection: 'column',
    marginLeft: 10,
  },
  nameText: {
    fontSize: 18,
    lineHeight: 32,
    color: '#333',
    fontWeight: 'bold',
  },
  line: {
    marginTop: 10,
    marginBottom: 5,
    height: 1,
    backgroundColor: '#333',
    opacity: 0.1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 8,
  },
  statusText: {
    fontSize: 12.0,
    color: '#333333',
    opacity: 0.8,
    marginLeft: 5,
  },
});

export default class MessageBusinessCard extends Component {
  onCardDetail = () => {
    const { currentMessage } = this.props;
    const userInfo = JSON.parse(currentMessage.content);
    NavigationService.navigate('chatAddContactDetail', { imId: userInfo.im_id });
  };

  render() {
    const { position, currentMessage } = this.props;
    const isLeft = position === 'left';
    const userInfo = JSON.parse(currentMessage.content);
    return (
      <Touchable onPress={this.onCardDetail}>
        <View style={[styles.container, isLeft ? styles.leftContainer : styles.rightContainer]}>
          <View style={styles.topContainer}>
            <Avatar avatar={userInfo?.avatar} name={userInfo?.nickname} style={styles.avatar} />
            <View style={styles.info}>
              <Text style={styles.nameText} numberOfLines={1}>
                {userInfo?.nickname}
              </Text>
            </View>
          </View>
          <View style={styles.line} />
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>{I18n.t('page_message_title_business_card')}</Text>
          </View>
        </View>
      </Touchable>
    );
  }
}
