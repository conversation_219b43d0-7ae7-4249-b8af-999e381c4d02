import React, { Component } from 'react';
import { Button, ScrollView, View } from '../../../components';
import styles from '../../../themes';
import Avatar from '../../../components/avatar/avatar';
import I18n from '../../../i18n';

function getComponentStyle(theme) {
  return {
    container: {
      height: 50,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 18,
      backgroundColor: '#ffffff',
      alignItems: 'center',
      paddingBottom: 20,
      paddingTop: 20,
    },
    avatarBox: {
      flex: 1,
      flexDirection: 'row',
      height: 32,
      marginRight: 10,
    },
    avatar: {
      width: 30,
      height: 30,
      borderRadius: 37,
      marginRight: 7,
    },
  };
}

export default class CheckedItem extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  style = getComponentStyle(styles.get('theme'));

  onNextBtn = () => {
    this.props.onNext();
  };

  render() {
    const { style } = this;
    const { chooses, disabledBtn = false } = this.props;
    return (
      <View style={style.container}>
        <ScrollView style={style.avatarBox} horizontal>
          {chooses.map((item, i) => (
            <Avatar
              key={item.imId || item.im_id}
              avatar={item.avatar || ''}
              name={item.memo || item.nickname}
              style={style.avatar}
            />
          ))}
        </ScrollView>
        <Button
          btnSize="sm"
          loadingProps={{ color: '#FFB731' }}
          title={I18n.t('op_confirm_title')}
          onPress={this.onNextBtn}
          btnType="primary"
          titleStyle={{ color: '#fff' }}
          containerStyle={{ width: 77 }}
          disabled={chooses.length <= 0 || disabledBtn}
        />
      </View>
    );
  }
}
