import React from 'react';
import { StyleSheet, Text, View, Touchable, Image } from '../../../components';
import constant from '../../../store/constant';
import chatMessageUtil from '../../../database/chatMessageUtil';
import I18n from '../../../i18n';
import navigationService from '../../../navigationService';
import sentryUtil from '../../../util/sentryUtil';

const styles = StyleSheet.create({
  leftContainer: {
    borderLeftWidth: 4,
    borderLeftColor: '#C7D0D6',
    paddingLeft: 7,
  },
  rightContainer: {
    borderLeftWidth: 4,
    borderLeftColor: '#758A86',
    paddingLeft: 7,
  },
  leftName: {
    fontSize: 12,
    color: '#8E96A3',
  },
  rightName: {
    fontSize: 12,
    color: '#758A86',
  },
  img: {
    width: 30,
    height: 30,
    marginTop: 5,
  },
});
/**
 * 引用消息显示组件
 * <AUTHOR>
 */
export default class Quote extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      containerStyle: null,
    };
  }

  static getDerivedStateFromProps({ position = 'left', style }, state) {
    if (position !== state.position || style !== state.style) {
      const containerStyle = style
        ? StyleSheet.flatten([styles[`${position}Container`], style])
        : styles[`${position}Container`];
      return {
        position,
        style,
        containerStyle,
      };
    }
    return null;
  }

  onViewImage = () => {
    const { message } = this.props;
    const images = [];
    let index = 0;
    images.push({
      url: message?.imagePath || message?.content,
    });
    navigationService.navigate('viewImages', {
      images,
      index,
    });
  };

  render() {
    const { containerStyle } = this.state;
    const { message, memberMap, position = 'left' } = this.props;
    if (!message) return null;
    const { name } = chatMessageUtil.getMessageUserInfo(message, memberMap);
    let content = '';
    const source = message?.imagePath || message?.content;
    switch (message.type) {
      case constant.messageType.referTo:
      case constant.messageType.text:
        content = message?.content;
        break;
      case constant.messageType.transfer:
      case constant.messageType.transferReturn:
      case constant.messageType.transferAccept:
        content = `[${I18n.t('page_transfer_title')}]`;
        break;
      case constant.messageType.receiveCreate:
      case constant.messageType.receivePaid:
        content = `[${I18n.t('page_receive_title')}]`;
        break;
      case constant.messageType.redPacket:
      case constant.messageType.redPacketAccept:
        content = `[${I18n.t('feat_red_pocket')}]`;
        break;
      case constant.messageType.image:
        content = (
          <Touchable onPress={this.onViewImage}>
            <Image source={{ uri: source }} style={[styles.img]} />
          </Touchable>
        );
        break;
      case constant.messageType.audio:
        content = `[${I18n.t('feat_audio')}]`;
        break;
      case constant.messageType.businessCard:
        content = `[${I18n.t('page_message_title_business_card')}] ${
          sentryUtil.parse(message.content, 'q render')?.nickname
        }`;
        break;
      case constant.messageType.video:
        content = `[${I18n.t('feat_video')}]`;
        break;
      case constant.messageType.file:
        content = `[${I18n.t('feat_file')}] ${
          sentryUtil.parse(message.extra, 'q render')?.fileName
        }`;
        break;
    }
    return (
      <View style={containerStyle}>
        <Text style={styles[`${position}Name`]}>{name}:</Text>
        <Text style={styles[`${position}Name`]} numberOfLines={4}>
          {content}
        </Text>
      </View>
    );
  }
}
