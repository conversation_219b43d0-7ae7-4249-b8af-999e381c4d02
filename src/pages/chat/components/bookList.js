import React, { Component } from 'react';
import { FlatList, Icon, SectionList, Text, TouchableOpacity, View } from '../../../components';
import styles from '../../../themes';
import { inject, observer } from 'mobx-react';
import pinyin from '../../../util/pinyin';
import NavigationService from '../../../navigationService';
import constant from '../../../store/constant';
import Avatar from '../../../components/avatar/avatar';
import NoData from '../../../components/empty/noData';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      flexDirection: 'row',
      // justifyContent: 'center',
      // alignItems: 'center',
      backgroundColor: '#FFFFFF',
    },
    container2: {
      flex: 1,
      flexDirection: 'row',
      // paddingHorizontal: 18,
      // paddingRight: 10,
    },
    sessionList: {
      flex: 1,
    },
    rightBar: {
      width: 20,
      paddingRight: 8,
      height: '64%',
      paddingTop: 40,
      position: 'absolute',
      backgroundColor: 'transparent',
      right: 0,
      top: 220,
    },
    rightBarText: {
      color: '#007AFF',
      textAlign: 'center',
      lineHeight: 20,
      fontSize: 12,
      fontWeight: '600',
    },
    sessionListItemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 10,
      paddingHorizontal: 15,
    },
    itemInfo: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    sessionListItem1: {
      flex: 1,
      paddingVertical: 6,
      color: '#333333',
      fontSize: 16,
      lineHeight: 22,
    },
    memobox: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
    },
    memotitle: {
      color: '#333333',
      fontSize: 16,
      lineHeight: 22,
    },
    keyText: {
      color: theme.primaryColor,
    },
    sessionheadBox: {
      paddingVertical: 10,
      backgroundColor: '#FFFFFF',
      // borderRadius: 11,
      paddingHorizontal: 15,
    },
    chatItemAvatar: {
      width: 40,
      height: 40,
      borderRadius: 16,
      marginRight: 14,
      // elevation: 2,
      shadowColor: 'rgba(0,0,0,0.1)',
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowRadius: 5,
    },
    sessionHeader: {
      // paddingHerizontal: 10,
      color: '#007C82',
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 20,
      backgroundColor: '#F5F8FB',
      width: 28,
      height: 20,
      textAlign: 'center',
      borderRadius: 11,
    },
    itemSeparator: {
      flex: 1,
      height: 1,
      backgroundColor: '#eee',
    },
    checkboxColor: '#ffad39',
    checkboxDefaultColor: '#8E96A3',
    btmLine: {
      height: 1,
      backgroundColor: 'rgba(220, 224, 235, 0.4)',
      marginLeft: 58,
      marginRight: 15,
    },
  };
}
@inject('chatAction', 'userStore')
@observer
export default class BookList extends Component {
  constructor(props) {
    super(props);
    this.routeParams = props.navigation?.state?.params || {};
    const disabledSet = new Set();
    const { bookData, checkedList } = props;
    props.disabledList?.forEach((item) => disabledSet.add(item.imId || item.im_id));
    this.state = {
      letterArr: [], // section数组
      sections: [],
      bookData: bookData || [],
      disabledSet,
      ...(bookData ? this.createSections(bookData) : {}),
      showNodata: false,
      selectedUsers: checkedList || [],
      keyWord: '',
    };
  }

  style = getComponentStyle(styles.get('theme'));

  componentDidMount() {
    this.initData();
    global.emitter.on(constant.event.deleteContact, this.initData);
    global.emitter.on(constant.event.addContact, this.initData);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.deleteContact, this.initData);
    global.emitter.off(constant.event.addContact, this.initData);
  }

  initData = async () => {
    if (this.props.bookData) {
      if (!this.props.isIndex) {
        const arr = this.props.bookData.filter((item) => {
          item.checked = !!this.props.checkedList.some((x) => x.imId === item.imId);
          return item;
        });
        this.getSections(arr);
      }
      return;
    }
    const { includeDeleted = true, excludeSelf } = this.routeParams;
    const res = await this.props.chatAction.getFriends(
      false,
      includeDeleted,
      excludeSelf || this.props.excludeSelf
    );
    const uniqueArray = res.filter((item, index) => {
      return index === res.findIndex((obj) => obj.imId === item.imId);
    });

    const arr = uniqueArray.filter((item) => {
      item.checked = !!this.props.checkedList.some((x) => x.imId === item.imId);
      return item;
    });
    this.setState({ bookData: uniqueArray });
    this.getSections(arr);
  };

  getSections = (obj) => {
    this.setState(this.createSections(obj));
  };

  createSections = (arr) => {
    const bookObject = {};
    const pyArr = [];
    if (!arr) {
      return { letterArr: [], sections: [] };
    }
    arr.forEach((item) => {
      // 截取字符串首个字符
      const sing = (item.memo || item.nickname).substring(0, 1);
      const firstPinyin = pinyin(sing).toLocaleUpperCase().substring(0, 1);
      const list = bookObject[firstPinyin] ? bookObject[firstPinyin] : [];
      list.push(item);
      pyArr.push(firstPinyin);
      bookObject[firstPinyin] = list;
    });
    const pyArray = this.uniqueArray(pyArr.sort());
    const sortFun = function (a, b) {
      return (a.memo || a.nickname || '').localeCompare(b.memo || b.nickname || '');
    };
    const sections = Object.keys(bookObject)
      .sort()
      .map((key) => ({ key, data: bookObject[key].sort(sortFun) }));
    return { letterArr: pyArray, sections, showNodata: true };
  };

  uniqueArray(arr) {
    const hash = [];
    for (let i = 0; i < arr.length; i += 1) {
      if (hash.indexOf(arr[i]) === -1) {
        hash.push(arr[i]);
      }
    }
    return hash;
  }

  handleRightBarPress(item) {
    const { sections } = this.state;
    this.sectionlist.scrollToLocation({
      animated: true,
      itemIndex: 0,
      sectionIndex: sections.findIndex((x) => x.key === item),
    });
  }

  selectedDetail(item) {
    if (this.props.isSingle && this.props.navigation) {
      const { addCheck } = this.props.navigation.state.params;
      addCheck(item);
      return;
    }
    if (this.props.isCheck) {
      this.selected(item);
    } else {
      NavigationService.navigate('chatAddContactDetail', {
        imId: item.imId,
        callback: () => {
          this.initData();
        },
      });
    }
  }

  selected = (item) => {
    const { sections, selectedUsers } = this.state;
    let checkedItems = '';
    if (this.props.isSingle) {
      let data = sections.flatMap((it) => it.data);
      data.forEach((x) => {
        if (x.imId == item.imId && !x.checked) {
          x.checked = true;
        } else {
          x.checked = false;
        }
      });
    } else {
      item.checked = !item.checked;
      if (item.checked) {
        selectedUsers.push(item);
      } else {
        selectedUsers.splice(
          selectedUsers.findIndex((x) => x.imId === item.imId),
          1
        );
      }
    }
    this.setState({ selectedUsers, sections: sections.slice() });
    if (this.props.isSingle) {
      checkedItems = item.checked ? item : '';
    } else {
      // checkedItems = sections.flatMap(it => it.data).filter(it => it.checked);
      checkedItems = this.state.selectedUsers;
    }
    this.props.onPick(checkedItems);
  };

  search = (key) => {
    const { bookData } = this.state;
    let sections = [];
    bookData.forEach((item) => {
      const sing = (item.memo || item.nickname).substring(0, 1);
      const firstPinyin = pinyin(sing).toLowerCase();
      if (
        item.memo?.toLowerCase()?.indexOf(key?.toLowerCase()) > -1 ||
        item.nickname?.toLowerCase()?.indexOf(key?.toLowerCase()) > -1 ||
        firstPinyin.indexOf(key?.toLowerCase()) > -1 ||
        item.imId?.toString()?.indexOf(key) > -1
      ) {
        sections.push(item);
      }
    });
    this.setState({ keyWord: key });
    this.getSections(sections);
  };

  /* 手指滑动，触发事件 */
  scrollSectionList(event, length) {
    const touch = event.nativeEvent.touches[0];
    if (touch.locationY >= 0 && touch.locationY <= 20 * length) {
      const index = touch.locationY / 20;
      if (index <= length) {
        this.sectionlist.scrollToLocation({
          animated: true,
          itemIndex: 0,
          sectionIndex: parseInt(index, 10),
        });
      }
    }
  }

  /* 右侧索引 */
  sectionItemView() {
    const { style } = this;
    const { letterArr } = this.state;
    return (
      <View style={style.rightBar}>
        <FlatList
          ref={(w) => {
            this.sectionIndexlist = w;
          }}
          showsVerticalScrollIndicator={false}
          scrollEnabled={true}
          renderItem={({ item }) => (
            <TouchableOpacity onPress={() => this.handleRightBarPress(item)}>
              <Text style={style.rightBarText}>{item}</Text>
            </TouchableOpacity>
          )}
          data={letterArr}
          keyExtractor={(item, index) => index + item}
        />
      </View>
    );
  }

  renderHead = ({ section }) => {
    const { style } = this;
    return (
      <View style={style.sessionheadBox}>
        <Text style={[style.sessionHeader]}>{section.key}</Text>
      </View>
    );
  };

  renderName = (item) => {
    const { keyWord } = this.state;
    const { style } = this;
    const name = item.memo || item.nickname;
    let memoArr = '';
    let key = keyWord;
    if (item.memo?.toLowerCase()?.indexOf(keyWord?.toLowerCase()) > -1) {
      const index = item.memo?.toLowerCase()?.indexOf(keyWord?.toLowerCase());
      const name = item.memo;
      key = name.substr(index, keyWord.length);
      memoArr = [name.substring(0, index), name.substring(index + keyWord.length)];
    }
    if (!memoArr && item.nickname?.toLowerCase()?.indexOf(keyWord?.toLowerCase()) > -1) {
      const name = item.memo ? `${item.memo} (${item.nickname})` : item.nickname;
      const index = name?.toLowerCase()?.indexOf(keyWord?.toLowerCase());
      key = name.substr(index, keyWord.length);
      memoArr = [name.substring(0, index), name.substring(index + keyWord.length)];
    }
    if (!memoArr && item.imId?.toString()?.indexOf(keyWord) > -1) {
      const name = item.memo ? `${item.memo} (${item.imId})` : `${item.nickname} (${item.imId})`;
      const index = name?.toLowerCase()?.indexOf(keyWord?.toLowerCase());
      memoArr = [name.substring(0, index), name.substring(index + keyWord.length)];
    }
    if (memoArr) {
      return (
        <View style={style.memobox}>
          <Text style={[style.memotitle]}>{memoArr[0]}</Text>
          <Text style={[style.memotitle, style.keyText]}>{key}</Text>
          <Text style={[style.memotitle]}>{memoArr[1]}</Text>
        </View>
      );
    }
    return <Text style={[style.sessionListItem1]}>{name}</Text>;
  };

  renderItem = ({ item, index, section }) => {
    // console.log('item111', { item, index, section });
    const { style } = this;
    const { isCheck } = this.props;
    const name = item.memo || item.nickname;
    const { disabledSet, keyWord } = this.state;
    const isDisabled = disabledSet.has(item.imId);
    return (
      <TouchableOpacity
        onPress={() => this.selectedDetail(item, index, section)}
        disabled={isDisabled}
      >
        <View style={[style.sessionListItemContainer, isCheck ? { paddingRight: 30 } : {}]}>
          <View style={style.itemInfo}>
            <Avatar
              avatar={item.avatar || ''}
              name={name}
              style={style.chatItemAvatar}
              showDot={false}
            />
            {keyWord ? this.renderName(item) : <Text style={[style.sessionListItem1]}>{name}</Text>}
          </View>
          {isCheck ? (
            <Icon
              name={isDisabled || item.checked ? 'check-circle' : 'checkbox-blank-circle-outline'}
              size={22}
              type="material-community"
              color={item.checked ? style.checkboxColor : style.checkboxDefaultColor}
              style={{ marginLeft: 5 }}
            />
          ) : null}
        </View>
        <View style={style.btmLine} />
      </TouchableOpacity>
    );
  };

  keyExtractor = (item, index) => index + item;

  onSectionListRef = (ref) => (this.sectionlist = ref);

  render() {
    const { style } = this;
    const { sections, showNodata } = this.state;
    const { isIndex = true } = this.props;
    return (
      <View style={style.container}>
        <View style={style.container2}>
          <SectionList
            ref={this.onSectionListRef}
            style={style.sessionList}
            stickySectionHeadersEnabled
            renderItem={this.renderItem}
            renderSectionHeader={this.renderHead}
            sections={sections}
            keyExtractor={this.keyExtractor}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={<View style={{ height: 100 }} />}
            ListEmptyComponent={showNodata ? <NoData /> : null}
            keyboardShouldPersistTaps="handled"
            {...this.props}
          />
        </View>
        {isIndex ? <View>{this.sectionItemView()}</View> : null}
      </View>
    );
  }
}
