import React from 'react';
import { FlatList, Icon, Text, Touchable, View } from '../../../components';
import { WIDTH } from '../../../common';
import NavigationService from '../../../navigationService';

function getStyle() {
  return {
    container: {
      height: WIDTH(230),
    },
    scrollView: {
      flex: 1,
      paddingTop: 10,
      paddingBottom: 100,
    },
    itemWrapper: {
      marginHorizontal: 16,
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e5e5',
    },
    titleText: {
      fontSize: 14,
      color: '#333',
    },
    bottomBtns: {
      backgroundColor: '#fff',
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      height: 40,
      // shadowColor: 'black',
      // shadowOffset: { h: 2 },
      // shadowOpacity: 0.2,
      // elevation: 3,
    },
    btnItemLine: {
      width: 1,
      height: 15,
      backgroundColor: '#e5e5e5',
    },
    btnItem: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      height: 40,
    },
    btnItemText: {
      fontSize: 12,
      color: '#333',
      marginLeft: 3,
    },
  };
}

/**
 * 聊天页面，底部快捷文本
 * <AUTHOR>
 */
export default class MessageQuickText extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.style = getStyle();
    this.state = {
      quickTextList: [
        {
          id: 1,
          content: 'Hello! I am interested in this position, can I send you my resume?',
        },
        {
          id: 2,
          content:
            'Hello, I am very interested in this position and I look forward to interviewing with you.',
        },
        {
          id: 3,
          content:
            "I'm sorry, this position is not suitable for me. I hope you can find the right person soon.",
        },
        {
          id: 4,
          content: 'I have received your message, thank you for your reply.',
        },
        {
          id: 5,
          content: 'I am very interested in this position, can I send you my resume?',
        },
        {
          id: 6,
          content:
            'Hello, I am very interested in this position and I look forward to interviewing with you.',
        },
      ],
    };
  }

  onAdd = () => {
    NavigationService.navigate('addQuickText');
  };
  onManage = () => {
    NavigationService.navigate('chatQuickTextList');
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable
        onPress={() => this.props.onPressQuickText(item.content)}
        style={style.itemWrapper}
        disableDebounce
      >
        <Text style={style.titleText} numberOfLines={3}>
          {item.content}
        </Text>
      </Touchable>
    );
  };

  keyExtractor = (item) => item.id;

  render() {
    const { hide } = this.props;
    if (hide) {
      return null;
    }
    const { style } = this;
    const { quickTextList } = this.state;
    return (
      <View style={style.container}>
        <FlatList
          style={style.scrollView}
          data={quickTextList}
          renderItem={this.renderItem}
          keyExtractor={this.keyExtractor}
          ListFooterComponent={() => <View style={{ height: 10 }} />}
          showsVerticalScrollIndicator={false}
        />

        <View style={style.bottomBtns}>
          <Touchable style={style.btnItem} onPress={this.onAdd}>
            <Icon type="material" name="playlist-add" size={20} color="#2089DC" />
            <Text style={style.btnItemText}>添加</Text>
          </Touchable>
          <View style={style.btnItemLine} />
          <Touchable style={style.btnItem} onPress={this.onManage}>
            <Icon type="foundation" name="folder-add" size={20} color="#2089DC" />
            <Text style={style.btnItemText}>管理</Text>
          </Touchable>
        </View>
      </View>
    );
  }
}
