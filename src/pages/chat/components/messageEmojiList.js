import React from 'react';
import { FlatList, Icon, Text, Touchable, View } from '../../../components';
import { deviceWidth, WIDTH } from '../../../common';
import emoji from '../../../res/chatEmoji';

function getStyle() {
  const paddingHorizontal = 8;
  const itemWith = Math.floor((deviceWidth - paddingHorizontal * 2) / 8);
  return {
    container: {
      height: WIDTH(230),
    },
    scrollView: {
      flex: 1,
      paddingHorizontal,
    },
    itemWrapper: {
      alignItems: 'center',
      justifyContent: 'center',
      width: itemWith,
      height: itemWith,
    },
    titleText: {
      fontSize: itemWith / 1.5,
      color: '#000',
    },
    removeContainer: {
      position: 'absolute',
      right: 8 + itemWith / 4,
      bottom: 8,
    },
  };
}

/**
 * 聊天页面，底部表情列表
 * <AUTHOR>
 */
export default class MessageEmojiList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.style = getStyle();
  }

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable
        onPress={() => this.props.onAddEmoji(item.emoji)}
        style={style.itemWrapper}
        disableDebounce
      >
        <Text style={style.titleText} numberOfLines={1}>
          {item.emoji}
        </Text>
      </Touchable>
    );
  };

  getItemLayout = (item, index) => {
    const { height } = this.style.itemWrapper;
    return {
      length: height,
      offset: index * height,
      index,
    };
  };

  keyExtractor = (item) => item.code;

  render() {
    const { hide, onRemoveText } = this.props;
    if (hide) {
      return null;
    }
    const { style } = this;
    return (
      <View style={style.container}>
        <FlatList
          style={style.scrollView}
          numColumns={8}
          data={emoji.getEmojiArray()}
          renderItem={this.renderItem}
          keyExtractor={this.keyExtractor}
          getItemLayout={this.getItemLayout}
        />
        <Touchable onPress={onRemoveText} style={style.removeContainer} disableDebounce>
          <Icon type="'material-community'" name="backspace" size={26} color="#333" />
        </Touchable>
      </View>
    );
  }
}
