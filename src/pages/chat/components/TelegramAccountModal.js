// TelegramAccountModal.js
import React, { Component, PureComponent } from 'react';
import { View, Text, Keyboard, TextInput, TouchableOpacity, Modal } from '../../../components';
import I18n from '../../../i18n';
import Toast from 'react-native-easy-toast';

const tgModalStyles = {
  containerOuter: {
    width: '100%',
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 20,
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 14,
    padding: 20,
    alignItems: 'center',
    width: '100%',
  },
  title: {
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
  },
  input: {
    width: '100%',
    height: 44,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#e9e9e9',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 15,
    width: '48%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 14,
  },
  sendButton: {
    backgroundColor: '#3498db',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 15,
    width: '48%',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 14,
  },
};

export default class TelegramAccountModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      tgAccount: '',
    };
  }

  componentDidUpdate(prevProps) {
    // 当弹窗从隐藏变为显示时，重置输入框
    if (!prevProps.isVisible && this.props.isVisible) {
      this.setState({ tgAccount: '' });
    }
  }

  componentWillUnmount() {}

  onChangeTgAccount = (text) => {
    this.setState({ tgAccount: text });
  };

  onCancel = () => {
    // 调用父组件的取消方法
    this.props.onCancel && this.props.onCancel();
  };

  onSend = () => {
    const { tgAccount } = this.state;
    if (!tgAccount.trim()) {
      this.onTgModalError(I18n.t('page_chat_tg_account_empty'));
      return;
    }

    // 调用父组件的发送方法
    this.props.onSend && this.props.onSend(tgAccount.trim());
    this.setState({ tgAccount: '' });
  };

  onTgModalError = (errorMessage) => {
    this.toast.show(errorMessage);
  };

  render() {
    const { isVisible } = this.props;

    return (
      <Modal
        onRequestClose={this.onCancel}
        animationType="fade"
        presentationStyle="overFullScreen"
        transparent
        visible={isVisible}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={tgModalStyles.containerOuter}
          onPress={Keyboard.dismiss}
        >
          <View style={tgModalStyles.container}>
            <Text style={tgModalStyles.title}>{I18n.t('page_chat_enter_tg_account')}</Text>
            <TextInput
              ref={(ref) => (this.tgInput = ref)}
              style={tgModalStyles.input}
              placeholder={I18n.t('page_chat_tg_account_placeholder')}
              value={this.state.tgAccount}
              onChangeText={this.onChangeTgAccount}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="done"
              onSubmitEditing={this.onSend}
            />
            <View style={tgModalStyles.buttonContainer}>
              <TouchableOpacity style={tgModalStyles.cancelButton} onPress={this.onCancel}>
                <Text style={tgModalStyles.cancelButtonText}>{I18n.t('op_cancel_title')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={tgModalStyles.sendButton} onPress={this.onSend}>
                <Text style={tgModalStyles.sendButtonText}>{I18n.t('page_chat_send')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </Modal>
    );
  }
}
