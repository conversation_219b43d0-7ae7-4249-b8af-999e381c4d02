// TelegramAccountModal.js
import React, { Component } from 'react';
import { TextInput, TouchableOpacity, Platform } from 'react-native';
import { View, Text, KeyboardAvoidingView, Keyboard } from '../../../components';
import Modal from 'react-native-modal';
import I18n from '../../../i18n';

const tgModalStyles = {
  containerOuter: {
    width: '100%',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 14,
    padding: 20,
    alignItems: 'center',
    // 在Android上增加阴影效果
    ...Platform.select({
      android: {
        elevation: 5,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  title: {
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
  },
  input: {
    width: '100%',
    height: 44,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#e9e9e9',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 15,
    width: '48%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 14,
  },
  sendButton: {
    backgroundColor: '#3498db',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 15,
    width: '48%',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 14,
  },
};

class TelegramAccountModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tgAccount: '',
    };
  }

  componentDidUpdate(prevProps) {
    // 当弹窗从隐藏变为显示时，重置输入框
    if (!prevProps.isVisible && this.props.isVisible) {
      this.setState({ tgAccount: '' });
    }
  }

  componentWillUnmount() {
    // 确保在组件卸载时清理所有可能的事件监听器
    if (this.focusTimeout) {
      clearTimeout(this.focusTimeout);
    }
  }

  onChangeTgAccount = (text) => {
    this.setState({ tgAccount: text });
  };

  onCancel = () => {
    // 调用父组件的取消方法
    this.props.onCancel && this.props.onCancel();
  };

  onSend = () => {
    const { tgAccount } = this.state;
    if (!tgAccount.trim()) {
      this.props.onError && this.props.onError(I18n.t('page_chat_tg_account_empty'));
      return;
    }

    // 调用父组件的发送方法
    this.props.onSend && this.props.onSend(tgAccount.trim());
    this.setState({ tgAccount: '' });
  };

  render() {
    const { isVisible } = this.props;

    return (
      <Modal
        isVisible={isVisible}
        onBackdropPress={this.onCancel}
        backdropOpacity={0.5}
        animationIn="fadeIn"
        animationOut="fadeOut"
        useNativeDriver
        style={{ margin: 20, justifyContent: 'center' }}
        // 设置为true可以避免键盘弹出时整个聊天页面被顶起
        avoidKeyboard={true}
        // 键盘弹出时保持弹窗居中
        onModalShow={() => {
          // 在iOS上需要特殊处理，确保弹窗居中
          if (Platform.OS === 'ios') {
            // 延迟执行以确保Modal已经准备好
            this.focusTimeout = setTimeout(() => {
              if (this.tgInput) {
                this.tgInput.focus();
              }
            }, 100);
          }
        }}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: Platform.OS === 'ios' ? 1 : 0 }}
        >
          <View style={tgModalStyles.containerOuter}>
            <View style={tgModalStyles.container}>
              <Text style={tgModalStyles.title}>{I18n.t('page_chat_enter_tg_account')}</Text>
              <TextInput
                ref={(ref) => (this.tgInput = ref)}
                style={tgModalStyles.input}
                placeholder={I18n.t('page_chat_tg_account_placeholder')}
                value={this.state.tgAccount}
                onChangeText={this.onChangeTgAccount}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="done"
                onSubmitEditing={this.onSend}
              />
              <View style={tgModalStyles.buttonContainer}>
                <TouchableOpacity style={tgModalStyles.cancelButton} onPress={this.onCancel}>
                  <Text style={tgModalStyles.cancelButtonText}>{I18n.t('op_cancel_title')}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={tgModalStyles.sendButton} onPress={this.onSend}>
                  <Text style={tgModalStyles.sendButtonText}>{I18n.t('page_chat_send')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }
}

export default TelegramAccountModal;
