import React from 'react';
import { inject, observer } from 'mobx-react';
import DocumentPicker from 'react-native-document-picker';
import { Image, Text, Touchable, View, AlertPro } from '../../../components';
import resIcon from '../../../res';
import styles from '../../../themes';
import { WIDTH } from '../../../common';
import I18n from '../../../i18n';
import ImagePickerUtil from '../../../util/imagePickerUtil';
import sendMessageUtil from '../../../database/sendMessageUtil';
import constant from '../../../store/constant';
import PermissionUtil from '../../../util/permissionUtilExtra';

function getStyle() {
  const theme = styles.get('theme');
  const itemWith = WIDTH(88.5);
  return {
    container: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: WIDTH(10),
      paddingVertical: WIDTH(10),
      height: WIDTH(230),
    },
    itemWrapper: {
      alignItems: 'center',
      width: itemWith,
      marginBottom: WIDTH(10),
    },
    itemContainer: {
      alignItems: 'center',
    },
    img: {
      width: WIDTH(60),
      height: WIDTH(60),
    },
    titleText: {
      fontSize: 13,
      fontWeight: theme.fontWeightRegular,
      color: '#464646',
      lineHeight: 24,
      marginTop: WIDTH(0),
    },
    imgVoiceBox: {
      width: WIDTH(58),
      height: WIDTH(58),
      borderRadius: 22,
      backgroundColor: '#f5f6f8',
      justifyContent: 'center',
      alignItems: 'center',
    },
    imgVoice: {
      width: WIDTH(30),
      height: WIDTH(30),
    },
  };
}

const FeatItem = function ({ item, style }) {
  return (
    <Touchable onPress={item.onPress} style={style.itemWrapper}>
      <View style={style.itemContainer}>
        {item.isVoice ? (
          <View style={style.imgVoiceBox}>
            <Image source={item.img} style={style.imgVoice} />
          </View>
        ) : (
          <Image source={item.img} style={style.img} />
        )}

        <Text style={style.titleText} numberOfLines={1}>
          {item.title}
        </Text>
      </View>
    </Touchable>
  );
};

/**
 * 聊天页面，底部更多功能
 * <AUTHOR>
 */

@inject('meetingAction', 'callAction', 'imAction', 'chatAction', 'userStore', 'companyStore')
@observer
export default class MessageMoreFeat extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};

    this.style = getStyle();
    this.session = props.navigation.getParam('session');
    const enabled = !props.session.isGroup && !props.session.isManage && !props.session.isSelf;
    this.features = [
      {
        title: I18n.t('feat_video'),
        img: resIcon.messageFeatVideo,
        onPress: this.onRequestVideoCall,
        enabled,
      },
      {
        title: I18n.t('photo_lib'),
        img: resIcon.messageFeatPhoto,
        onPress: this.onPhoto,
        enabled: true,
      },
      {
        title: I18n.t('feat_camera'),
        img: resIcon.messageFeatCamera,
        onPress: props.onCamera,
        enabled: true,
      },
      {
        title: I18n.t('feat_change_job'),
        img: resIcon.featChangeJob,
        onPress: this.onChangeJob,
        enabled: props.userStore.isCompany,
      },
      {
        title: I18n.t('feat_company_business_card'),
        img: resIcon.featCompanyBusiness,
        onPress: this.onCompanyBusiness,
        enabled: props.userStore.isCompany,
      },
      {
        title: I18n.t('feat_document'),
        img: resIcon.messageFeatDocument,
        onPress: this.onDocument,
        enabled: true,
      },
      {
        title: I18n.t('feat_audio'),
        img: resIcon.messageVoice,
        onPress: this.onVoice,
        enabled: true,
        isVoice: true,
      },
    ];
    this.state = {
      showAlert: false,
    };
  }

  onChangeJob = () => {
    global.emitter.emit(constant.event.showSelectJobModal, {
      page: 'main',
      data: { id: this.session.jobId },
      noNeedAll: true,
      title: I18n.t('page_resume_text_has_published_jobs'),
      onConfirm: this.onSelectJob,
    });
  };

  onSelectJob = (job) => {
    console.debug('onSelectJob', job);
    if (this.session.jobId === job.id) return;
    this.session.jobId = job.id;
    this.session.jobTitle = job.title;
    sendMessageUtil.sendMessage({
      type: constant.messageType.job,
      content: '',
      sessionId: this.session.sessionId,
      session: this.session,
      localExtra: JSON.stringify({ job }),
      jobTitle: job.title,
    });
  };

  onCompanyBusiness = async () => {
    try {
      const { companyInfo } = this.props.companyStore;
      // console.debug('onCompanyBusiness', toJS(companyInfo));
      const content = {
        employerId: companyInfo.employerId,
        company: companyInfo.company,
        logo: companyInfo.logo,
      };
      await sendMessageUtil.sendMessage({
        type: constant.messageType.companyBusinessCard,
        isGroup: this.session.isGroup,
        content: JSON.stringify(content),
        sessionId: this.session.sessionId,
        session: this.session,
      });
    } catch (e) {
      console.warn('onCompanyBusiness', e);
      toast.show(e?.message);
    }
  };

  getData = async () => {
    const { groupMembers } = await this.props.imAction.getGroupMemberList(
      this.session?.sessionId,
      true
    );
    return groupMembers.filter((x) => x.imId !== this.props.userStore.imId);
  };

  onRequestVideoCall = () => {
    this.setState({ showAlert: true });
  };
  onVideoCallConfirm = async () => {
    this.setState({ showAlert: false });
    this.onVideoCall();
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  onVideoCall = async () => {
    if (this.props.callAction.checkInCall()) {
      return;
    }
    await PermissionUtil.requestMultiple();
    this.props.callAction.startCall(constant.callType.singleVideoCall, this.session.sessionId);
  };

  checkDisableImage = () => {
    if (this.props.isDisableImage) {
      toast.show(I18n.t('page_groupManage_no_image'));
      return true;
    }
    return false;
  };

  onPhoto = async () => {
    if (this.checkDisableImage()) return;
    try {
      const images = await ImagePickerUtil.openPicker();
      await sendMessageUtil.sendImages(images, this.session);
    } catch (e) {
      logger.warn('onPhoto', e);
    }
  };

  onDocument = async () => {
    if (this.checkDisableImage()) return;
    try {
      const [file] = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: false,
        // copyTo: 'cachesDirectory', // copyTo:"cachesDirectory" | "documentDirectory"
      });
      console.log('onDocument', file);
      await sendMessageUtil.sendFile(file, this.session);
    } catch (e) {
      if (DocumentPicker.isCancel(e)) return;
      logger.warn('onDocument', e);
      if (e?.message) {
        toast.show(e.message);
      }
    }
  };

  onVoice = () => {
    if (this.props.onVoice) {
      this.props.onVoice();
    }
  };

  render() {
    const { hide } = this.props;
    if (hide) {
      return null;
    }
    const { style, features } = this;
    return (
      <>
        <View style={[style.container, this.session?.isGroup ? { height: WIDTH(125) } : {}]}>
          {features
            .filter((x) => x.enabled)
            .map((item) => (
              <FeatItem key={item.title} item={item} style={style} />
            ))}
        </View>
        <AlertPro
          visible={this.state.showAlert}
          title={I18n.t('page_chat_confirm_use_video_call')}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onConfirm={this.onVideoCallConfirm}
          onCancel={this.onAlertCancel}
        />
      </>
    );
  }
}
