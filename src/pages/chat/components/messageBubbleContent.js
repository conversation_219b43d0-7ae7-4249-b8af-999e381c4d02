import React from 'react';
import { View } from '../../../components';
import MessageBubbleContainer from './messageBubbleContainer';

export default class MessageBubbleContent extends React.Component {
  renderMessageText() {
    if (this.props.currentMessage) {
      const { containerStyle, wrapperStyle, optionTitles, ...messageTextProps } = this.props;
      if (this.props.renderMessageText) {
        return this.props.renderMessageText(messageTextProps);
      }
    }
    return null;
  }

  render() {
    return (
      <MessageBubbleContainer {...this.props}>
        <View style={{ flexShrink: 1000 }}>
          {this.props.renderCustomView?.(this.props)}
          {this.renderMessageText()}
        </View>
      </MessageBubbleContainer>
    );
  }
}
