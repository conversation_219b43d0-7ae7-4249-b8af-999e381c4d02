import React, { Component } from 'react';
import { ActivityIndicator, Icon, StyleSheet, Text, Touchable, View } from '../../../components';
import { deviceWidth } from '../../../common';
import Video from 'react-native-video';
import I18n from '../../../i18n';

const maxWidth = deviceWidth * 0.35;
const maxHeight = deviceWidth * 0.35;

const styles = StyleSheet.create({
  videoContainer: {
    position: 'relative',
  },
  backgroundVideo: {
    width: maxWidth,
    height: maxHeight,
    borderRadius: 5,
    overflow: 'hidden',
    backgroundColor: '#cccccc80',
  },
  player: {
    position: 'absolute',
    right: 0,
    left: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerExtras: {
    flexDirection: 'column',
  },
  errorText: {
    fontSize: 14,
    color: '#333333',
  },
});

const Status = {
  loading: 'loading', // 视频加载中
  loaded: 'loaded', // 加载完成，可播放状态
  playing: 'playing', // 播放中
  error: 'error', // 加载失败
};

/**
 * 视频类型消息
 * <AUTHOR>
 */
export default class MessageVideo extends Component {
  constructor(props) {
    super(props);
    const data = props.currentMessage;
    // this.poster = `${data?.imagePath ||
    //   data?.content}?x-oss-process=video/snapshot,t_3000,m_fast`; // 第一帧视频图片
    this.state = {
      source: null,
      videoStyle: { width: 80, height: 120 },
      paused: true, // 暂停
      poster: null, // 视频封面
      status: null,
    };
  }

  static getDerivedStateFromProps({ currentMessage }, state) {
    const uri = currentMessage.imagePath || currentMessage.content;
    const posterUrl = currentMessage.content;
    console.log('MessageVideo getDerivedStateFromProps', currentMessage);
    // const extra = currentMessage.extra && JSON.parse(currentMessage.extra);
    if (uri !== state.uri || posterUrl !== state.posterUrl) {
      return {
        uri,
        posterUrl,
        poster: posterUrl ? `${posterUrl}?x-oss-process=video/snapshot,t_0,m_fast` : posterUrl,
        source: { uri },
        // source: { uri: uri || 'https://media.w3.org/2010/05/sintel/trailer.mp4' },
        // videoStyle: getVideoWH(extra || {}),
      };
    }
    return null;
  }

  /**
   * 媒体开始加载时调用的回调函数。
   * @param res
   */
  onLoadStart = (res) => {
    console.log('MessageVideo onLoadStart', res);
    // {"drm": null, "src": {"isNetwork": true, "type": "",
    // "uri": "http://kaixin-test.oss-cn-hongkong.aliyuncs.com/_/app/560F9B89-F407-4C80-98FD-28227F7D7DB4/1637128743985.mov"},
    // "target": 15645}
    this.setState({ status: Status.loading });
  };

  /**
   * 当第一个视频帧准备好显示时调用的回调函数。这是海报被移除的时候。仅支持iOS
   */
  onReadyForDisplay = () => {
    console.log('MessageVideo onReadyForDisplay');
  };

  /**
   * 当媒体加载并准备播放时调用的回调函数
   */
  onLoad = (res) => {
    console.log('MessageVideo onLoad', res);
    this.setState({ status: Status.loaded });
    // "naturalSize": {"height": 1080, "orientation": "portrait", "width": 1920},
    // "naturalSize": {"height": 1080, "orientation": "landscape", "width": 1920},
    // if (this.state.videoStyle) return;
    // this.setState({ videoStyle: getVideoWH(res.naturalSize) });
  };

  onPlayAndFullScreen = async () => {
    console.log('MessageVideo onPlayAndFullScreen');
    this.player?.presentFullscreenPlayer();
    this.setState({ paused: false, status: Status.playing });
  };

  /**
   * 当播放器到达媒体末尾时调用的回调函数。
   */
  onEnd = () => {
    console.log('MessageVideo onEnd');
    this.setState({ paused: true, status: Status.loaded });
    // this.player.restoreUserInterfaceForPictureInPictureStopCompleted(true);
  };

  /**
   * 无法加载视频时的回调
   */
  onError = (e) => {
    logger.warn('MessageVideo onError', e);
    this.setState({ status: Status.error });
  };

  onReLoad = () => {
    console.log('MessageVideo onReLoad');
  };

  /**
   * 远程视频缓冲时回调
   */
  onBuffer = () => {
    console.log('MessageVideo onBuffer');
    this.setState({ status: Status.loading });
  };

  onSave = () => {
    // let response = await this.player?.save();
    // console.log('response', response);
    // let path = response.uri;
  };

  renderAction = () => {
    const { status } = this.state;
    if (status === Status.loading) {
      return (
        <View style={styles.player}>
          <ActivityIndicator animating color="#999999" size="small" />
        </View>
      );
    }
    if (status === Status.error) {
      return (
        <Touchable style={[styles.player, styles.playerExtras]} onPress={this.onReLoad}>
          <Icon type="material" name="error" size={32} color="#DC143C" />
          <Text style={styles.errorText}>{I18n.t('page_video_load_error')}</Text>
        </Touchable>
      );
    }
    if (status === Status.loaded) {
      return (
        <Touchable onPress={this.onPlayAndFullScreen} style={styles.player}>
          <Icon type="antdesign" name="playcircleo" size={36} color="#fff" />
        </Touchable>
      );
    }
    return null;
  };

  initVideoRef = (ref) => (this.player = ref);

  render() {
    const { source, videoStyle, paused, poster } = this.state;
    console.log('MessageVideo render', paused, poster);
    return (
      <View style={[styles.videoContainer, videoStyle]}>
        <Video
          style={[styles.backgroundVideo, videoStyle]}
          source={source} // Can be a URL or a local file.
          ref={this.initVideoRef}
          controls={false}
          fullscreen={false}
          autoPlay={true}
          pictureInPicture={false} // [iOS] 是否以画中画模式播放
          // preventsDisplaySleepDuringVideoPlayback={false}
          repeat={false} // 是否重复播放
          resizeMode="cover" // 视频的自适应伸缩铺放行为
          poster={poster} // 封面图
          posterResizeMode="cover"
          paused={paused} // 控制暂停、播放
          muted={false} // 控制静音
          volume={1} // 0静音 1是正常的
          fullscreenAutorotate={false}
          playInBackground={false} // 当应用程序输入背景音频时，音频继续播放。
          playWhenInactive={false} // [iOS]当显示控制或通知中心时，视频继续播放。
          ignoreSilentSwitch="ignore" // [iOS] ignore | 服从 - 当'忽略'时，音频仍然会播放与iOS硬静音开关设置为静音。当“服从”时，音频将切换开关。当未指定时，将照常继承音频设置。
          progressUpdateInterval={250} // [iOS] Interval to fire onProgress（默认为〜250ms ）
          onLoadStart={this.onLoadStart} // 视频开始加载时的回调
          onLoad={this.onLoad} // 当媒体加载并准备播放时调用的回调函数
          onEnd={this.onEnd} // 播放完成时的回调
          onError={this.onError} // 当视频无法加载时
          // onProgress={() => {}} // 回调每250毫秒〜与currentTime的
          onBuffer={this.onBuffer} // 当远程视频正在缓冲时回调
        />
        {this.renderAction()}
      </View>
    );
  }
}
