import React, { Component } from 'react';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import sentryUtil from '../../../util/sentryUtil';
import { MessageComponents } from './messageComponents';

/**
 * 邀请面试消息
 */
export default class MessageInviteInterview extends Component {
  onSendResume = () => {
    const { currentMessage } = this.props;
    const content = sentryUtil.parseSafe(currentMessage.content);
    if (content?.jobApplyId) {
      NavigationService.navigate('interviewDetail', { isChat: true, id: content.jobApplyId });
    }
  };

  render() {
    return (
      <MessageComponents
        position={this.props.position}
        img={resIcon.chatInterviewReq}
        text={I18n.t('page_chat_invite_interview')}
        onPress={this.onSendResume}
        btnTitle={I18n.t('page_chat_link_view_interview')}
      />
    );
  }
}
