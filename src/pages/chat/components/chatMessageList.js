import React, { Component } from 'react';
import { Icon, KeyboardAvoidingView, Text, Touchable } from '../../../components';
import { inject, observer } from 'mobx-react';
import { GiftedChat } from 'react-native-gifted-chat';
import constant from '../../../store/constant';
import messageDao from '../../../database/dao/messageDao';
import chatMessageUtil from '../../../database/chatMessageUtil';
import sendMessageUtil from '../../../database/sendMessageUtil';
import { reset } from '../../../components/softInputMode';
import promiseUtil from '../../../util/promiseUtil';
import { renderMessage } from './messageContainer';
import MessageInput from './messageInput';
import chatSessionDao from '../../../database/dao/chatSessionDao';
import chatMessageOptions from '../../../database/chatMessageOptions';
import I18n from '../../../i18n';
import messageReadUtil from '../../../database/messageReadUtil';
import chatAction from '../../../store/actions/chatAction';

function getStyle() {
  return {
    container: {
      flex: 1,
    },
    messagesContainerStyle: { backgroundColor: '#F7F7F7' },
    scrollToBottomContainer: {
      position: 'absolute',
      bottom: 10,
      right: 10,
      flex: 0,
      backgroundColor: 'white',
      width: 40,
      height: 40,
      borderRadius: 50,
      alignItems: 'center',
      justifyContent: 'center',
    },
    newMsgNumText: {
      fontSize: 16,
      color: '#333333',
      width: '100%',
      textAlign: 'center',
    },
  };
}

@inject('userStore', 'chatStore', 'imAction')
@observer
export default class ChatMessageList extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.messageList = [];
    this.memberMap = new Map();
    this.searchMessage = props.navigation.getParam('searchMessage');
    this.detectViewHeightThreshold = 200;
    this.realState = {
      showScrollBottom: false,
      latestMsgTime: 0,
    };
    this.state = {
      messages: [],
      isLoadingEarlier: false,
      loadEarlier: false,
      minInputToolbarHeight: 62,
      memberMap: this.memberMap,
      ...this.realState,
    };
    this.scrollToBottomTime = 0;
    this.listViewProps = {
      ref: this.initList,
      style: { backgroundColor: 'transparent' },
      contentContainerStyle: { flexGrow: 1, justifyContent: 'flex-end' },
      showsVerticalScrollIndicator: false,
      keyExtractor: this.keyExtractor,
      onScroll: this.onScroll,
      onScrollBeginDrag: this.onScrollBeginDrag,
      onScrollEndDrag: this.onScrollEndDrag,
      windowSize: 121,
      // TODO RN新版 Android设置这个会影响onScroll，iOS暂未测试，暂时注释
      // maintainVisibleContentPosition: {
      //   autoscrollToTopThreshold: 10,
      //   minIndexForVisible: 0,
      // },
    };
  }

  get session() {
    return this.props.session;
  }

  get sessionId() {
    return this.props.session.sessionId;
  }

  componentDidMount() {
    if (this.props.isDisabled) return;
    global.emitter.on(constant.event.receiveMessage, this.onReceiveMessage);
    global.emitter.on(constant.event.chatMessageRead, this.onMessageRead);
    global.emitter.on(constant.event.groupMemberChange, this.onGroupMemberChange);
    global.emitter.on(constant.event.messageChange, this.onMessageChange);
    global.emitter.on(constant.event.clearAllMessage, this.cleanMessage);
    global.emitter.on(constant.event.appStateChange, this.onAppStateChange);
    global.emitter.on(constant.event.blackMessage, this.onFriendChange);
    global.emitter.on(constant.event.blacklistChange, this.onFriendChange);
    global.emitter.on(constant.event.deleteContact, this.onFriendChange);
    global.emitter.on(constant.event.addContact, this.onFriendChange);
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.willFocus);
    this.onRefresh();
  }

  componentWillUnmount() {
    this.clearRead();
    chatAction.setCurrentSession();
    if (this.props.isDisabled) return;
    reset();
    global.emitter.off(constant.event.receiveMessage, this.onReceiveMessage);
    global.emitter.off(constant.event.chatMessageRead, this.onMessageRead);
    global.emitter.off(constant.event.groupMemberChange, this.onGroupMemberChange);
    global.emitter.off(constant.event.messageChange, this.onMessageChange);
    global.emitter.off(constant.event.clearAllMessage, this.cleanMessage);
    global.emitter.off(constant.event.appStateChange, this.onAppStateChange);
    global.emitter.off(constant.event.blackMessage, this.onFriendChange);
    global.emitter.off(constant.event.blacklistChange, this.onFriendChange);
    global.emitter.off(constant.event.deleteContact, this.onFriendChange);
    global.emitter.off(constant.event.addContact, this.onFriendChange);
    this.willFocusSubscription.remove();
  }

  onLongPress = (_, message) => {
    chatMessageOptions.msgLongPressOptions(
      this.giftedChat,
      message,
      this.memberMap,
      this.props.groupInfo,
      this.session
    );
  };

  onAppStateChange = ({ nextAppState }) => {
    if (nextAppState !== 'active') {
      this.clearRead();
    } else {
      sendMessageUtil.sendMsgReadByCheck({ messages: this.messageList, session: this.session });
    }
  };

  willFocus = () => {
    sendMessageUtil.sendMsgReadByCheck({ messages: this.messageList, session: this.session });
  };

  clearRead = () => {
    if (this.searchMessage) {
      chatSessionDao.updateUnReadNum({
        ownerId: this.session.ownerId,
        sessionId: this.session.sessionId,
        isGroup: this.session.isGroup,
        unReadNum: 0,
      });
    } else {
      chatSessionDao.saveSessionByMsgList(this.messageList);
    }
  };

  cleanMessage = () => {
    this.messageList = [];
    this.setState({ messages: [], loadEarlier: false });
  };

  keyExtractor = (item) => {
    const { name, user_type } = chatMessageUtil.getMessageUserInfo(item, this.memberMap);
    const { groupInfo } = this.props;
    return `${item._id}_${item.updateAt || 0}_${name}_${user_type}_${groupInfo?.memberVisible}`;
  };

  onMessageChange = ({ message, onMerge, isRefresh }) => {
    if (!message || typeof onMerge !== 'function') return;
    const currentMessage = this.messageList.find((item) =>
      chatMessageUtil.isSameMsg(item, message)
    );
    if (currentMessage) {
      onMerge(currentMessage);
      if (isRefresh) {
        this.setState({ messages: this.messageList.slice() });
      }
    }
  };

  onMessageRead = (data) => {
    if (this.sessionId === data?.sessionId) {
      const messages = messageReadUtil.onReadByMessageList(this.messageList, data);
      if (messages) {
        this.setState({ messages });
      }
    }
  };

  onReceiveMessage = ({ message, sessionId = message.sessionId }) => {
    try {
      if (!sessionId || sessionId !== this.sessionId) return;
      console.log('chatMessageList onReceiveMessage', message);
      if (Array.isArray(message)) {
        message.forEach(this.appendNewMsg);
        this.updateMessagesAndRead(message);
        return;
      }
      this.appendNewMsg(message);
      this.updateMessagesAndRead([message]);
    } catch (e) {
      console.warn('chatMessageList onReceiveMessage', message.seq, e);
    }
  };

  appendNewMsg = (message) => {
    chatMessageUtil.convertMessage(message);
    this.messageList = chatMessageUtil.appendNewMessage(this.messageList, message);
  };

  updateMessagesAndRead = async (messages) => {
    await chatMessageUtil.checkQuoteMessage(this.messageList);
    this.setState({ messages: this.messageList.slice() });
    sendMessageUtil.sendMsgReadByCheck({ messages, session: this.session });
  };

  get ownerId() {
    return this.props.userStore.imId;
  }

  onGroupMemberChange = ({ groupId, isLocal }) => {
    console.log('chatMessageList onGroupMemberChange', groupId, isLocal, this.session);
    if (groupId !== this.session.sessionId || !this.session.isGroup) return;
    this.onRefreshMember(isLocal, true);
  };

  onFriendChange = (data) => {
    console.log('chatMessageList onFriendChange', data, this.session);
    if (data.imId === this.session.sessionId && !this.session.isGroup) {
      this.onRefreshMember(true, true);
    }
  };

  onRefreshMember = async (isLocal, isState) => {
    this.memberMap = await this.props.imAction.getCurrentMessageMemberMap(this.session, isLocal);
    if (isState) {
      this.setState({ memberMap: this.memberMap });
    }
    this.props.onGroupMemberChange(this.memberMap);
    return this.memberMap;
  };

  onRefresh = async (isLoadingEarlier = false) => {
    // if (this.time && new Date().getTime() - this.time < 1000) {
    //   console.warn('ChatMessageList 刷新频率太高了');
    //   return;
    // }
    if (isLoadingEarlier) {
      this.setState({
        isLoadingEarlier,
      });
    }
    let loadEarlier = this.state.loadEarlier;
    try {
      const msgTime = this.messageList.length
        ? this.messageList[this.messageList.length - 1].msgTime
        : Number.MAX_SAFE_INTEGER;
      const param = {
        ownerId: this.ownerId,
        sessionId: this.sessionId,
        msgTime,
        limit: 20,
      };
      const isSearch = this.searchMessage && !this.messageList.length;
      if (isSearch) {
        param.limit = 10000;
        param.minMsgTime = this.searchMessage.msgTime;
      }
      const tasks = [messageDao.queryMessageList(param)];
      if (!this.memberMap.size) {
        tasks.push(this.onRefreshMember(true, false));
      }
      const [res] = await promiseUtil.batchRequestMin(tasks, isLoadingEarlier ? undefined : 0);

      // console.log('res =======> ', res);
      // 筛选群组pay讯息, 不能给其他使用者看到
      const filteredRes = res.filter(
        (item) => item.isSelfSend || !item.content?.includes('___pay_type=')
      );
      loadEarlier = isSearch || filteredRes.length === param.limit;
      const earlierMessages = chatMessageUtil.convertMessageList(filteredRes);
      sendMessageUtil.sendMsgReadByCheck({
        messages: earlierMessages.slice(),
        session: this.session,
      });
      this.messageList = chatMessageUtil.appendEarlierMessages(
        this.messageList,
        earlierMessages,
        true
      );
      await chatMessageUtil.checkQuoteMessage(this.messageList);
      console.log('chatMessageList onRefresh', this.messageList.length);
    } catch (e) {
      console.warn('chatMessageList onRefresh', e);
    }
    this.time = new Date().getTime();
    const isFirst = this.state.memberMap.size === 0;
    this.setState(
      {
        messages: this.messageList.slice(),
        isLoadingEarlier: false,
        loadEarlier,
        memberMap: this.memberMap,
      },
      () => {
        if (isFirst) {
          this.onRefreshMember(false, true);
          if (this.searchMessage) {
            setTimeout(this.scrollToTop, 500);
            // InteractionManager.runAfterInteractions(this.scrollToTop);
          }
        }
      }
    );
  };

  onLoadEarlier = () => {
    console.log('chatMessageList onLoadEarlier');
    this.onRefresh(true);
  };

  onSend = async ([message]) => {
    try {
      console.log('chatMessageList onSend', message);
      await sendMessageUtil.sendMessage({
        type: message.referTo ? constant.messageType.referTo : constant.messageType.text,
        referTo: message.referTo,
        isGroup: this.session?.isGroup,
        content: message.text,
        sessionId: this.sessionId,
        quoteMessage: message.quoteMessage,
        quoteId: message.quoteMessage?.msgId,
        extra: JSON.stringify({
          fromName: message.user?.name,
          ...(this.session?.isGroup
            ? { groupName: this.session.groupTitle || this.session.title }
            : {}),
        }),
        session: this.session,
      });
    } catch (e) {
      console.warn('chatMessageList onSend', e);
    }
  };

  renderInputToolbar = (props) => (
    <MessageInput
      navigation={this.props.navigation}
      session={this.session}
      memberMap={this.state.memberMap}
      scrollToBottomFun={this.scrollToBottom}
      onMinInputToolbarHeight={this.onMinInputToolbarHeight}
      {...props}
    />
  );

  onMinInputToolbarHeight = (minInputToolbarHeight, callback) =>
    this.setState({ minInputToolbarHeight }, callback);

  initGiftedChat = (ref) => (this.giftedChat = ref);

  initList = ref => (this.flatListRef = ref);

  scrollToBottom = () => {
    // console.log('chatMessageList scrollToBottom');
    // this.giftedChat?.scrollToBottom(false);
    // this.giftedChat?._messageContainerRef?.current?.scrollToOffset({
    //   offset: 0,
    //   animated: true,
    // });
    this.flatListRef?.scrollToOffset({
      offset: 0,
      animated: true,
    });
    this.scrollToBottomTime = global.elapsedRealtime();
    this.handleScrollY({ y: 0 });
  };

  scrollToTop = () => {
    // console.log('chatMessageList scrollToTop');
    this.flatListRef?.scrollToEnd({
      animated: false,
    });
  };

  onScroll = event => {
    // console.log('chatMessageList onScroll', event.nativeEvent.contentOffset.y);
    // 执行滚动到底部事假时，跳过onScroll处理
    if (global.elapsedRealtime() - this.scrollToBottomTime > 1000) {
      this.handleScrollY(event.nativeEvent.contentOffset);
    }
  };

  handleScrollY = ({ y }) => {
    const showScrollBottom = y >= this.detectViewHeightThreshold;
    let { latestMsgTime } = this.realState;
    if (y <= 0) {
      latestMsgTime = 0;
    } else if (!latestMsgTime && this.messageList?.length) {
      latestMsgTime = this.messageList[0].msgTime;
    }
    // console.log('chatMessageList onScroll handleScrollY', showScrollBottom, y, latestMsgTime);
    if (
      showScrollBottom !== this.realState.showScrollBottom ||
      latestMsgTime !== this.realState.latestMsgTime
    ) {
      this.realState.showScrollBottom = showScrollBottom;
      this.realState.latestMsgTime = latestMsgTime;
      this.setState(this.realState);
    }
  };

  getNewMsgNum = () => {
    const { messageList } = this;
    const { latestMsgTime } = this.state;
    if (latestMsgTime && messageList.length) {
      const length = messageList.length;
      for (let i = 0; i < length; i++) {
        if (messageList[i].msgTime <= latestMsgTime) {
          return i;
        }
      }
    }
    return 0;
  };

  onScrollBeginDrag = (event) => {
    // console.log('chatMessageList onScrollBeginDrag', event.nativeEvent.contentOffset);
    global.emitter.emit(constant.event.foldPanel);
  };

  onScrollEndDrag = event => {
    // console.log('chatMessageList onScrollEndDrag', event.nativeEvent.contentOffset.y);
    this.handleScrollY(event.nativeEvent.contentOffset);
  };

  renderChatFooter = (num) => {
    const { showScrollBottom } = this.state;
    // console.log('chatMessageList renderChatFooter', showScrollBottom, num);
    if (showScrollBottom || num) {
      return (
        <Touchable
          activeOpacity={1}
          onPress={this.scrollToBottom}
          style={this.style.scrollToBottomContainer}
        >
          {num ? (
            <Text style={this.style.newMsgNumText}>{num}</Text>
          ) : (
            <Icon name="chevron-down" type="ionicon" size={30} color="#26305F" />
          )}
        </Touchable>
      );
    }
    return null;
  };

  render() {
    let { messages, isLoadingEarlier, loadEarlier, minInputToolbarHeight, memberMap } = this.state;
    const { groupInfo, memberCount } = this.props;
    chatAction.setCurrentSession(this.session, this.memberMap, groupInfo);
    console.log('chatMessageList render');

    const newMsgNum = IS_ANDROID && this.getNewMsgNum();
    if (newMsgNum) {
      // 如果用户在查看较早消息时，不渲染新消息
      messages = messages.slice(newMsgNum);
    }

    const content = (
      <GiftedChat
        ref={this.initGiftedChat}
        onLongPress={this.onLongPress}
        messages={messages}
        messagesList={messages}
        memberMap={memberMap}
        onSend={this.onSend}
        onLoadEarlier={this.onLoadEarlier}
        isLoadingEarlier={isLoadingEarlier}
        infiniteScroll={loadEarlier}
        loadEarlier={loadEarlier}
        renderMessage={renderMessage}
        renderInputToolbar={this.renderInputToolbar}
        groupInfo={groupInfo}
        memberCount={memberCount}
        user={this.props.chatStore.user}
        listViewProps={this.listViewProps}
        minInputToolbarHeight={minInputToolbarHeight}
        // shouldUpdateMessage={shouldUpdateMessage}
        label={I18n.t('page_chat_history')}
        minComposerHeight={0}
        maxComposerHeight={0}
        renderAvatarOnTop={false}
        // alignTop
        showUserAvatar
        showAvatarForEveryMessage
        scrollToBottom={false}
        isKeyboardInternallyHandled={false}
        navigation={this.props.navigation}
        session={this.session}
        extraData={{ memberMap, groupInfo, memberCount }}
        messagesContainerStyle={this.style.messagesContainerStyle}
        renderChatFooter={() => this.renderChatFooter(newMsgNum)}
      />
    );
    if (IS_ANDROID) {
      return content;
    }
    return (
      <KeyboardAvoidingView style={this.style.container} behavior="padding">
        {content}
      </KeyboardAvoidingView>
    );
  }
}
