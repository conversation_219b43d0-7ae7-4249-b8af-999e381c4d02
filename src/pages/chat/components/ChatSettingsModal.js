// ChatSettingsModal.js
import React, { Component } from 'react';
import { TextInput, TouchableOpacity, Platform } from 'react-native';
import { View, Text, KeyboardAvoidingView } from '../../../components';
import Modal from 'react-native-modal';
import I18n from '../../../i18n';

const chatSettingsModalStyles = {
  containerOuter: {
    width: '100%',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 14,
    padding: 20,
    alignItems: 'stretch',
    // 在Android上增加阴影效果
    ...Platform.select({
      android: {
        elevation: 5,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    color: '#333',
    width: 80,
    textAlign: 'right',
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    fontSize: 14,
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  radioGroup: {
    flexDirection: 'row',
    flex: 1,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radioButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ccc',
    marginRight: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: '#3498db',
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3498db',
  },
  radioText: {
    fontSize: 14,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    backgroundColor: '#e9e9e9',
    borderRadius: 4,
    paddingVertical: 12,
    paddingHorizontal: 20,
    width: '48%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  confirmButton: {
    backgroundColor: '#3498db',
    borderRadius: 4,
    paddingVertical: 12,
    paddingHorizontal: 20,
    width: '48%',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
};

class ChatSettingsModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      chatAccount: props.initialChatAccount || '',
      chatPassword: props.initialChatPassword || '',
      chatStatus: props.initialChatStatus || 'open', // 'open' 或 'disabled'
    };
  }

  componentDidUpdate(prevProps) {
    // 当弹窗从隐藏变为显示时，重置输入框
    if (!prevProps.isVisible && this.props.isVisible) {
      this.setState({
        chatAccount: this.props.initialChatAccount || '',
        chatPassword: this.props.initialChatPassword || '',
        chatStatus: this.props.initialChatStatus || 'open',
      });
    }
  }

  componentWillUnmount() {
    // 确保在组件卸载时清理所有可能的事件监听器
    if (this.focusTimeout) {
      clearTimeout(this.focusTimeout);
    }
  }

  onChangeChatAccount = (text) => {
    this.setState({ chatAccount: text });
  };

  onChangeChatPassword = (text) => {
    this.setState({ chatPassword: text });
  };

  onChangeChatStatus = (status) => {
    this.setState({ chatStatus: status });
  };

  onCancel = () => {
    // 调用父组件的取消方法
    this.props.onCancel && this.props.onCancel();
  };

  onConfirm = () => {
    const { chatAccount, chatPassword, chatStatus } = this.state;
    
    // 基本验证
    if (!chatAccount.trim()) {
      this.props.onError && this.props.onError('聊天账号不能为空');
      return;
    }

    if (!chatPassword.trim()) {
      this.props.onError && this.props.onError('聊天密码不能为空');
      return;
    }

    // 调用父组件的确认方法
    this.props.onConfirm && this.props.onConfirm({
      chatAccount: chatAccount.trim(),
      chatPassword: chatPassword.trim(),
      chatStatus,
    });
  };

  renderRadioButton = (value, label) => {
    const isSelected = this.state.chatStatus === value;
    return (
      <TouchableOpacity
        style={chatSettingsModalStyles.radioOption}
        onPress={() => this.onChangeChatStatus(value)}
      >
        <View style={[
          chatSettingsModalStyles.radioButton,
          isSelected && chatSettingsModalStyles.radioButtonSelected
        ]}>
          {isSelected && <View style={chatSettingsModalStyles.radioButtonInner} />}
        </View>
        <Text style={chatSettingsModalStyles.radioText}>{label}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    const { isVisible } = this.props;
    const { chatAccount, chatPassword } = this.state;

    return (
      <Modal
        isVisible={isVisible}
        onBackdropPress={this.onCancel}
        backdropOpacity={0.5}
        animationIn="fadeIn"
        animationOut="fadeOut"
        useNativeDriver
        style={{ margin: 20, justifyContent: 'center' }}
        avoidKeyboard={true}
        onModalShow={() => {
          // 在iOS上需要特殊处理，确保弹窗居中
          if (Platform.OS === 'ios') {
            // 延迟执行以确保Modal已经准备好
            this.focusTimeout = setTimeout(() => {
              if (this.chatAccountInput) {
                this.chatAccountInput.focus();
              }
            }, 100);
          }
        }}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: Platform.OS === 'ios' ? 1 : 0 }}
        >
          <View style={chatSettingsModalStyles.containerOuter}>
            <View style={chatSettingsModalStyles.container}>
              {/* 聊天账号输入 */}
              <View style={chatSettingsModalStyles.inputRow}>
                <Text style={chatSettingsModalStyles.label}>聊天账号:</Text>
                <TextInput
                  ref={(ref) => (this.chatAccountInput = ref)}
                  style={chatSettingsModalStyles.input}
                  placeholder="请输入聊天账号"
                  value={chatAccount}
                  onChangeText={this.onChangeChatAccount}
                  autoCapitalize="none"
                  autoCorrect={false}
                  returnKeyType="next"
                  onSubmitEditing={() => {
                    if (this.chatPasswordInput) {
                      this.chatPasswordInput.focus();
                    }
                  }}
                />
              </View>

              {/* 聊天密码输入 */}
              <View style={chatSettingsModalStyles.inputRow}>
                <Text style={chatSettingsModalStyles.label}>设置聊天天密码:</Text>
                <TextInput
                  ref={(ref) => (this.chatPasswordInput = ref)}
                  style={chatSettingsModalStyles.input}
                  placeholder="请输入密码"
                  value={chatPassword}
                  onChangeText={this.onChangeChatPassword}
                  secureTextEntry={true}
                  autoCapitalize="none"
                  autoCorrect={false}
                  returnKeyType="done"
                  onSubmitEditing={this.onConfirm}
                />
              </View>

              {/* 聊天状态选择 */}
              <View style={chatSettingsModalStyles.radioRow}>
                <Text style={chatSettingsModalStyles.label}>聊天状态:</Text>
                <View style={chatSettingsModalStyles.radioGroup}>
                  {this.renderRadioButton('open', '开启')}
                  {this.renderRadioButton('disabled', '未启用')}
                </View>
              </View>

              {/* 按钮组 */}
              <View style={chatSettingsModalStyles.buttonContainer}>
                <TouchableOpacity style={chatSettingsModalStyles.cancelButton} onPress={this.onCancel}>
                  <Text style={chatSettingsModalStyles.cancelButtonText}>取消</Text>
                </TouchableOpacity>
                <TouchableOpacity style={chatSettingsModalStyles.confirmButton} onPress={this.onConfirm}>
                  <Text style={chatSettingsModalStyles.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }
}

export default ChatSettingsModal;
