import React from 'react';
import { Image, StyleSheet, Text, View } from '../../../components';
import resIcon from '../../../res';
import audioPlayUtil from '../../../util/audioPlayUtil';
import ImageAnimation from '../../../components/images/imageAnimation';
import memoize from 'memoize-one';
import messageFileDownloadUtil from '../../../database/messageFileDownloadUtil';
import messageDao from '../../../database/dao/messageDao';
import I18n from '../../../i18n';
import sentryUtil from '../../../util/sentryUtil';
import MessageBubbleContainer from './messageBubbleContainer';
import { Color } from './messageConfig';

const styles = StyleSheet.create({
  leftTouchableContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 60,
    position: 'relative',
  },
  rightTouchableContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    flexShrink: 1000,
  },
  leftTouchableContainer2: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 40,
  },
  rightTouchableContainer2: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 60,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 40,
  },
  rightContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingLeft: 60,
  },
  leftWrapperStyle: {
    marginRight: 0,
    // marginLeft: 0,
  },
  rightWrapperStyle: {
    marginLeft: 0,
  },
  leftPlayImage: {
    width: 14,
    height: 17,
    marginRight: 15,
  },
  rightPlayImage: {
    width: 14,
    height: 17,
    marginLeft: 15,
  },
  leftTimeLengthText: {
    fontSize: 16,
    color: Color.leftTextColor,
    minWidth: 36,
  },
  rightTimeLengthText: {
    fontSize: 16,
    color: Color.rightTextColor,
    minWidth: 36,
  },
  unreadView: {
    width: 10,
    height: 10,
    borderRadius: 4,
    backgroundColor: '#FF4B5A',
    borderColor: '#fff',
    borderWidth: 2,
    position: 'absolute',
    right: -16,
    bottom: 16,
  },
  player: {
    marginRight: 4,
  },
  audioImg: {
    marginRight: 5,
    width: 80,
  },
});

/**
 * 语音类型消息
 * <AUTHOR>
 */
export default class MessageAudio extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isPlaying: false,
      timeLeft: 0,
    };
  }

  componentDidMount() {
    this.willBlurSubscription = this.props.navigation.addListener('willBlur', this.onWillBlur);
    messageFileDownloadUtil.checkAndAddDownloadQueue(this.props.currentMessage);
  }

  componentWillUnmount() {
    console.log('MessageAudio componentWillUnmount', this.isPlaying);
    this.willBlurSubscription.remove();
    this.onWillBlur();
  }

  onWillBlur = () => {
    console.log('MessageAudio onWillBlur', this.isPlaying);
    if (this.isPlaying) {
      audioPlayUtil.stopPlayer(true).catch((e) => logger.warn('MessageAudio stopPlayer', e));
    }
  };

  componentDidUpdate(prevProps, prevState, snapshot) {
    messageFileDownloadUtil.checkAndAddDownloadQueue(this.props.currentMessage, true);
  }

  onPlayAudio = async () => {
    const { currentMessage } = this.props;
    try {
      const notDownload = await messageFileDownloadUtil.checkAndAddDownloadQueue(currentMessage);
      if (notDownload) {
        logger.warn('MessageAudio onPlayAudio 还未下载', currentMessage.content);
        toast.show(I18n.t('page_chat_download'));
        return;
      }
      console.log('MessageAudio onPlayAudio', this.isPlaying, currentMessage.imagePath);
      if (this.isPlaying) {
        await audioPlayUtil.stopPlayer();
        return;
      }
      this.isPlaying = true;
      await this.setPlayed();
      await audioPlayUtil.startPlayer(currentMessage.imagePath, this.onPlayBackListener);
    } catch (e) {
      this.isPlaying = false;
      logger.warn('MessageAudio onPlayAudio', e, currentMessage.content);
    }
  };

  onPlayBackListener = (playBack) => {
    console.log('MessageAudio onPlayBackListener', playBack);
    this.isPlaying = !playBack.isComplete;
    this.setState({
      isPlaying: this.isPlaying,
      timeLeft: playBack.duration ? playBack.duration - playBack.currentPosition : 0,
    });
  };

  onLongPress = () => {
    const { onLongPress, currentMessage } = this.props;
    onLongPress?.(this.context, currentMessage);
  };

  getAnimImages = memoize((position) => {
    return [resIcon.audioPlaying1, resIcon.audioPlaying2, resIcon.audioPlaying3];
    /*return position === 'left'
      ? [resIcon.audioPlayLeft1, resIcon.audioPlayLeft2, resIcon.audioPlayLeft3]
      : [resIcon.audioPlayRight1, resIcon.audioPlayRight2, resIcon.audioPlayRight3];*/
  });

  isPlayed = memoize((extra, isSelfSend) => {
    if (isSelfSend) return true;
    try {
      return (extra && sentryUtil.parse(extra, 'ma ip'))?.isPlayed;
    } catch (e) {
      return false;
    }
  });

  setPlayed = async () => {
    const { currentMessage } = this.props;
    const isPlayed = this.isPlayed(currentMessage.extra, currentMessage.isSelfSend);
    if (isPlayed) return;
    const extra = (currentMessage.extra && sentryUtil.parse(currentMessage.extra, 'ma sp')) || {};
    extra.isPlayed = true;
    currentMessage.extra = JSON.stringify(extra);
    await messageDao.updateMessageBySeq(currentMessage);
  };

  render() {
    const { position, currentMessage } = this.props;
    const { isPlaying, timeLeft } = this.state;
    const animImages = this.getAnimImages(position);
    const isPlayed = this.isPlayed(currentMessage.extra, currentMessage.isSelfSend);
    return (
      <View
        style={[
          styles[`${position}TouchableContainer`],
          currentMessage?.isGroup ? { marginRight: 0 } : {},
        ]}
      >
        <MessageBubbleContainer
          {...this.props}
          onPress={this.onPlayAudio}
          onLongPress={this.onLongPress}
          wrapperStyle={styles[`${position}WrapperStyle`]}
        >
          <View style={styles[`${position}TouchableContainer2`]}>
            <Image
              source={position == 'right' ? resIcon.audioPlay : resIcon.audioPlayGray}
              style={styles.player}
            />
            <ImageAnimation
              images={animImages}
              isStop={!isPlaying}
              defaultIndex={0}
              interval={250}
              style={styles.audioImg}
              resizeMode="cover"
            />
            {/* <Image source={resIcon.audioTimeline} style={styles.audioImg} /> */}
            <Text style={styles[`${position}TimeLengthText`]} numberOfLines={3}>
              {isPlaying
                ? audioPlayUtil.formatMMSSByMillisecond(timeLeft)
                : audioPlayUtil.formatMMSS(currentMessage.timeLength)}
            </Text>
          </View>
        </MessageBubbleContainer>
        {!isPlayed ? <View style={styles.unreadView} /> : null}
      </View>
    );
  }
}
