import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  BackHandler,
  Image,
  Keyboard,
  TextInput,
  Touchable,
  View,
  Text,
  ScrollView,
} from '../../../components';
import resIcon from '../../../res';
import styles from '../../../themes';
import { setAdjustResize } from '../../../components/softInputMode';
import MessageMoreFeat from './messageMoreFeat';
import NavigationService from '../../../navigationService';
import MessageEmojiList from './messageEmojiList';
import MessageQuickText from './messageQuickText';
import constant from '../../../store/constant';
import inputUtil from '../../../util/inputUtil';
import MessageQuoteInput from './messageQuoteInput';
import MessageRecordAudio from './messageRecordAudio';
import I18n from '../../../i18n';
import util from '../../../util';
import MessageCamera from './messageCamera';
import chatMessageUtil from '../../../database/chatMessageUtil';
import ImagePickerUtil from '../../../util/imagePickerUtil';
import sendMessageUtil from '../../../database/sendMessageUtil';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingTop: 14,
      paddingRight: 6,
      paddingLeft: 10,
      backgroundColor: '#ffffff',
      paddingVertical: 10,
      paddingBottom: 14,
      borderBottomWidth: 1,
      borderBottomColor: '#F5F8FB',
    },
    containerInput: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      flex: 1,
      paddingLeft: 10,
      paddingRight: 1,
      backgroundColor: '#F7F7F7',
      borderRadius: 10,
    },
    containerInputFocus: {
      // borderColor: theme.stressColor,
    },
    selectionColor: theme.primaryColor,
    placeholderTextColor: '#9BA7B2',
    textInput: {
      flex: 1,
      maxHeight: 88,
      // marginHorizontal: 10,
    },
    textStyle: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      minHeight: 30,
      lineHeight: 26,
      marginVertical: 5,
      textAlignVertical: 'center',
      alignItems: 'center',
    },
    placeholderText: {
      color: '#9BA7B2',
    },
    emojiImgContainer: {
      paddingBottom: 4,
    },
    btnImg: {
      width: 30,
      height: 30,
    },
    btnImgMd: {
      width: 30,
      height: 30,
      marginRight: 4,
      marginLeft: 10,
      marginBottom: 1,
    },
    img: {
      width: 30,
      height: 30,
      marginBottom: 5,
    },
    quickTextBox: {
      marginRight: 10,
      marginBottom: 3,
      height: 32,
      justifyContent: 'center',
      alignItems: 'center',
    },
    quickTextBtn: {
      paddingHorizontal: 6,
      paddingVertical: 5,
      backgroundColor: theme.primaryColor,
      borderRadius: 30,
    },
    quickText: {
      fontSize: 12,
      color: '#fff',
    },
  };
}

const panels = {
  moreFeat: 'moreFeat',
  emoji: 'emoji',
  quickText: 'quickText',
};

/**
 * 聊天页面，底部输入组件
 * <AUTHOR>
 */
@inject('imAction', 'userStore')
@observer
export default class MessageInput extends React.Component {
  constructor(props) {
    super(props);
    this.atList = [];
    this.state = {
      text: '',
      isFocus: false,
      displayPanel: '',
      quoteMessage: null,
      isRecordAudio: false,
    };
    this.style = getStyle();
  }

  componentDidMount() {
    global.emitter.on(constant.event.foldPanel, this.onHandlePanel);
    global.emitter.on(constant.event.quoteMessage, this.onQuoteMessage);
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.didFocusSubscription = this.props.navigation.addListener('didFocus', this.didPageFocus);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.foldPanel, this.onHandlePanel);
    global.emitter.off(constant.event.quoteMessage, this.onQuoteMessage);
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.didFocusSubscription.remove();
  }

  onBackButtonPressAndroid = () => {
    const { displayPanel } = this.state;
    if (displayPanel && this.props.navigation.isFocused()) {
      this.setState({ displayPanel: '' });
      return true;
    }
    return false;
  };

  onCamera = () => {
    this.messageCamera?.onShow();
  };

  onPhoto = async () => {
    if (this.groupSetting.isDisableImage) {
      toast.show(I18n.t('page_groupManage_msg_no_image'));
      return;
    }
    try {
      const { session } = this.props;
      const images = await ImagePickerUtil.openPicker({
        // width: 200,
        // height: 200,
        imageCount: 6,
        isCrop: false,
      });
      if (!images?.length) return;
      await sendMessageUtil.sendImages(images, session);
    } catch (e) {
      logger.warn('onPhoto', e);
    }
  };

  onHandlePanel = () => {
    Keyboard.dismiss();
    this.setState({ displayPanel: '' });
  };

  onQuoteMessage = ({ message }) => {
    this.setState({ quoteMessage: message }, this.inputFocus);
  };

  didPageFocus = () => {
    if (this.requestFocus) {
      this.requestFocus = false;
      setTimeout(() => {
        this.textInput?.focus();
      }, 50);
    }
  };

  initTextInput = (ref) => (this.textInput = ref);

  onChangeText = (text) => {
    console.log('MessageInput onChangeText', text);
    const res = inputUtil.handleAtName(this.atList, text);
    this.atList = res.atList;
    this.setState({ text: res.text }, () => {
      this.sendText = null;
    });
    this.scrollView?.scrollToEnd();
  };

  onKeyPress = (key) => {
    // Backspace
    // console.log('MessageInput onKeyPress', key);
    if (key === '@') {
      this.onAt();
    }
  };

  onAt = async () => {
    const { session } = this.props;
    // const { session, memberMap } = this.props;
    if (!session.isGroup) return;
    // const bookData = [...memberMap.values()].filter(
    //   item => item.imId !== session.ownerId && !item.isDelete
    // ); // 可以给memo重新赋值测试昵称显示
    // console.log('11111111', bookData);
    const { groupMembers } = await this.props.imAction.getGroupMemberList(session.sessionId, false);
    NavigationService.push('chatBook', {
      createGroup: true,
      title: I18n.t('page_chat_tip_title'),
      bookData: groupMembers.filter((item) => item.imId !== session.ownerId && !item.isDelete),
      addCheck: this.onAtCallback,
    });
    this.requestFocus = true;
  };

  onAtCallback = (checkedList) => {
    console.log('messageInput onAtCallback', checkedList.length, this.atList.length);
    const text =
      checkedList
        .map((item) => {
          item.atName = `@${item.memo || item.nickname} `;
          return item.atName;
        })
        .join(' ') + ' ';
    this.atList = this.atList.concat(checkedList);
    this.onChangeText(this.state.text + text.substring(1));
  };

  onAddEmoji = (emoji) => {
    this.onChangeText(this.state.text + emoji + '');
  };

  onRemoveText = () => {
    const { text } = this.state;
    if (text) {
      let arr = [];
      for (let textElement of text) {
        arr.push(textElement);
      }
      arr.splice(arr.length - 1, 1);
      this.onChangeText(arr.join(''));
    }
  };

  onFocus = () => {
    console.log('messageInput onFocus');
    setAdjustResize();
    this.setState({ isFocus: true, displayPanel: '' });
    this.props.scrollToBottomFun?.();
  };

  onBlur = () => this.setState({ isFocus: false, text: this.state.text.trim() });

  onVoice = () => {
    const { groupSetting } = this;
    if (groupSetting.isNoSpeaking) {
      toast.show(I18n.t('page_message_msg_no_speaking'));
      return;
    }
    if (groupSetting.isDisableImage) {
      toast.show(I18n.t('page_groupManage_msg_no_image'));
      return;
    }
    if (this.state.isRecordAudio) {
      this.setState({ isRecordAudio: false }, this.inputFocus);
    } else {
      this.setState({ isRecordAudio: true, displayPanel: '' });
    }
  };

  onShowEmoji = () => this.showPanel(panels.emoji);

  showKeyboard = () => {
    this.setState({ displayPanel: '' }, this.inputFocus);
  };

  inputFocus = () => {
    setTimeout(() => {
      this.textInput?.focus();
    }, 0);
  };

  onSendText = async (content) => {
    if (typeof content !== 'string') {
      content = '';
    }
    const { onSend } = this.props;
    if (onSend) {
      const { quoteMessage } = this.state;
      const text = content || this.state.text;
      if (!text?.trim()) return;

      if (this.groupSetting.isDisableLink && util.includeUrl(text)) {
        toast.show(I18n.t('page_message_msg_disable_link'));
        return;
      }
      // 处理防止发送多次
      if (this.sendText === text) return;
      this.sendText = text;

      const existSensitiveWords = await chatMessageUtil.checkSensitiveWords(text);
      if (existSensitiveWords) {
        this.sendText = null;
        return;
      }

      let referTo = '';
      if (this.atList.length) {
        const referIds = [];
        const referNames = [];
        this.atList.forEach((item) => {
          referIds.push(item.imId);
          referNames.push(item.atName.substring(1));
        });
        referTo = `${referIds.join('-')}++${referNames.join(' |')}`;
      }
      onSend({ text: text.trim(), referTo, quoteMessage }, true);
      this.onChangeText('');
      this.setState({ quoteMessage: null });
      this.atList = [];
    }
  };

  onMoreFeat = () => {
    if (this.groupSetting.isDisableImage) {
      toast.show(I18n.t('page_groupManage_msg_no_image'));
      return;
    }
    this.showPanel(panels.moreFeat);
  };

  showPanel = (panel) => {
    let { displayPanel } = this.state;
    displayPanel = displayPanel !== panel ? panel : '';
    if (displayPanel) {
      Keyboard.dismiss();
    }
    if (this.groupSetting.isNoSpeaking) {
      toast.show(I18n.t('page_message_msg_no_speaking'));
      return;
    }
    this.setState({ displayPanel, isRecordAudio: false }, () => {
      setTimeout(() => {
        this.scrollView?.scrollToEnd();
        if (!displayPanel) {
          this.textInput?.focus();
        }
      }, 50);
    });
  };

  onInputSizeChanged = () => this.props.onInputSizeChanged({ height: this.containerHeight });

  onContainerLayout = (layoutEvent) => {
    const { height } = layoutEvent.nativeEvent.layout;
    console.log('onContainerLayout', height);
    if (this.containerHeight !== height) {
      this.containerHeight = height;
      this.props.onMinInputToolbarHeight(height, this.onInputSizeChanged);
    }
  };

  get groupSetting() {
    const { groupInfo, memberMap } = this.props;
    if (!groupInfo || !memberMap) return {};
    const member = memberMap.get(this.props.userStore.imId);
    console.log('groupSetting member', member);
    const isMember = member?.role === 'member';
    return {
      isNoSpeaking: isMember && (groupInfo.noSpeaking || member.noSpeaking),
      isDisableLink: isMember && !groupInfo.enableMedia,
      isDisableImage: isMember && !groupInfo.enableMedia,
    };
  }

  onShowQuickText = () => {
    this.showPanel(panels.quickText);
  };

  sendQucikText = (text) => {
    this.onSendText(text);
  };

  refMessageCamera = (ref) => (this.messageCamera = ref);

  render() {
    const { style, groupSetting } = this;
    let { text, isFocus, displayPanel, quoteMessage, isRecordAudio } = this.state;
    const { memberMap, session, minInputToolbarHeight, memberCount } = this.props;
    if (groupSetting.isNoSpeaking) {
      isRecordAudio = false;
      isFocus = false;
      displayPanel = '';
      text = '';
    }
    if (groupSetting.isDisableImage) {
      isRecordAudio = false;
      if (displayPanel && displayPanel !== panels.emoji) {
        displayPanel = '';
      }
    }
    const isSend = session.isGPT || text?.trim();
    return (
      <>
        <MessageQuoteInput
          message={quoteMessage}
          memberMap={memberMap}
          minInputToolbarHeight={minInputToolbarHeight}
        />
        <View onLayout={this.onContainerLayout}>
          <View style={[style.container, isFocus || displayPanel ? { paddingBottom: 14 } : {}]}>
            {session.isGPT ? null : displayPanel !== panels.quickText ? (
              <View style={style.quickTextBox}>
                <Touchable onPress={this.onShowQuickText} style={style.quickTextBtn}>
                  <Text style={style.quickText}>{I18n.t('page_chat_quick_text')}</Text>
                </Touchable>
              </View>
            ) : (
              <Touchable
                onPress={this.onShowQuickText}
                style={[style.emojiImgContainer, { marginRight: 5, marginBottom: 3 }]}
              >
                <Image source={resIcon.messageKeyboard} style={style.btnImg} resizeMode="contain" />
              </Touchable>
            )}
            {isRecordAudio ? (
              <MessageRecordAudio session={session} />
            ) : (
              <View style={[style.containerInput, isFocus && style.containerInputFocus]}>
                {displayPanel === panels.emoji ? (
                  <ScrollView style={style.textInput} ref={(ref) => (this.scrollView = ref)}>
                    <Touchable onPress={this.showKeyboard}>
                      <Text style={[style.textStyle, !text && style.placeholderText]}>
                        {text || I18n.t('page_chat_ph_send')}
                      </Text>
                    </Touchable>
                  </ScrollView>
                ) : (
                  <TextInput
                    ref={this.initTextInput}
                    style={[
                      style.textInput,
                      style.textStyle,
                      IS_IOS ? { lineHeight: 0, paddingTop: 6, paddingBottom: 4 } : {},
                    ]}
                    selectionColor={style.selectionColor}
                    placeholderTextColor={style.placeholderText.color}
                    placeholder={I18n.t('page_chat_ph_send')}
                    onChangeText={this.onChangeText}
                    onKeyPress={this.onKeyPress}
                    onFocus={this.onFocus}
                    onBlur={this.onBlur}
                    maxLength={500}
                    value={text}
                    editable={!groupSetting.isNoSpeaking}
                    multiline
                  />
                )}
                {session.isGPT ? null : (
                  <Touchable onPress={this.onShowEmoji}>
                    <Image
                      source={
                        displayPanel === panels.emoji
                          ? resIcon.messageKeyboard
                          : resIcon.messageEmoji
                      }
                      style={style.img}
                      resizeMode="center"
                    />
                  </Touchable>
                )}
              </View>
            )}
            <Touchable
              onPress={isSend ? this.onSendText : this.onMoreFeat}
              notHideKeyboard={text}
              style={style.emojiImgContainer}
            >
              <Image
                source={
                  isRecordAudio
                    ? resIcon.messageKeyboard
                    : isSend
                    ? resIcon.messageSend
                    : resIcon.messageMoreFeat
                }
                style={style.btnImgMd}
                resizeMode="contain"
              />
            </Touchable>
          </View>
          <MessageMoreFeat
            hide={displayPanel !== panels.moreFeat}
            navigation={this.props.navigation}
            isDisableImage={groupSetting.isDisableImage}
            memberMap={memberMap}
            memberCount={memberCount}
            session={session}
            onCamera={this.onCamera}
            onVoice={this.onVoice}
          />
          <MessageEmojiList
            hide={displayPanel !== panels.emoji}
            onAddEmoji={this.onAddEmoji}
            onRemoveText={this.onRemoveText}
          />
          <MessageQuickText
            hide={displayPanel !== panels.quickText}
            onPressQuickText={this.sendQucikText}
          />
          {groupSetting.isNoSpeaking || (session.isGroup && !memberCount) ? (
            <View
              style={{
                position: 'absolute',
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: '#ffffff90',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Text style={{ color: '#33333370' }}>
                {session.isGroup && !memberCount ? '' : I18n.t('page_message_label_no_speaking')}
              </Text>
            </View>
          ) : null}
        </View>
        <MessageCamera
          ref={this.refMessageCamera}
          session={session}
          isDisableImage={groupSetting.isDisableImage}
        />
      </>
    );
  }
}
