import React from 'react';
import { StyleSheet, Text } from '../../../components';
import chatMessageUtil from '../../../database/chatMessageUtil';
import { Color } from './messageConfig';

const styles = StyleSheet.create({
  nickname: {
    color: Color.usernameColor,
    fontSize: 12,
    // marginBottom: 4,
    // marginRight: 70,
    paddingHorizontal: 10,
    paddingTop: 6,
    fontWeight: 'bold',
  },
});

/**
 * 消息头像旁边的用户名称
 */
export default class MessageNickname extends React.Component {
  render() {
    const { message, memberMap } = this.props;
    if (message.isSelfSend || !message.isGroup) {
      return null;
    }
    const { name, nameColor } = chatMessageUtil.getMessageUserInfo(message, memberMap);
    return (
      <Text style={[styles.nickname, { color: nameColor }]} numberOfLines={1}>
        {name}
      </Text>
    );
  }
}
