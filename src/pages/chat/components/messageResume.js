import React, { Component } from 'react';
import { StyleSheet, Text, Touchable, View } from '../../../components';
import messageDao from '../../../database/dao/messageDao';
import SentryUtil from '../../../util/sentryUtil';
import JobItem from '../../../components/listItem/jobItem';
import { inject, observer } from 'mobx-react';
import ResumeItem from '../../../components/listItem/resumeItem';
import util from '../../../util';
import moment from 'moment';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import jobService from '../../../api/jobService';
import jobAction from '../../../store/actions/job';
import ResumeService from '../../../api/resumeService';
import companyAction from '../../../store/actions/company';

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 0,
    marginHorizontal: 12,
    marginBottom: 20,
    borderRadius: 5,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 3,
  },
});

/**
 * 简历消息，企业查看简历时，点按聊天发送的消息类型
 * <AUTHOR>
 */
@inject('userStore')
@observer
export default class MessageResume extends Component {
  constructor(props) {
    super(props);
    this.localExtra = SentryUtil.parseSafe(props.currentMessage.localExtra) || {};
    this.state = {
      job: this.localExtra.job,
      resume: this.localExtra.resume,
    };
  }

  componentDidMount() {
    this.getJobDetail();
    this.getResumeDetail();
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  hasJob = (jobId = this.props.currentMessage.jobId) => {
    return jobId && jobId !== '0';
  };

  getJobDetail = async () => {
    try {
      // if (this.props.userStore.isCompany) return;
      let { job } = this.state;
      // 上次获取时间小于一小时，暂不刷新
      if (job?.getTime && Date.now() - job.getTime < *********) return;
      let { jobId } = this.props.currentMessage;
      if (!this.hasJob(jobId)) return;
      if (this.props.userStore.isCompany) {
        job = await companyAction.queryJobsByJobId(jobId);
      } else {
        job = await jobService.querySingleJobs(jobId);
        await jobAction.checkAndFlagCommunicated(jobId, this.localExtra);
      }
      job.getTime = Date.now();
      this.localExtra.job = job;
      this.props.currentMessage.localExtra = JSON.stringify(this.localExtra);
      messageDao.updateLocalExtra(this.props.currentMessage);
      if (this.isUnmount) return;
      this.setState({ job });
    } catch (e) {
      console.warn('messageJob getJobDetail', e);
    }
  };

  getResumeDetail = async () => {
    try {
      const { resumeId, jobId } = this.props.currentMessage;
      // 个人端且有职位ID的情况下，会显示职位信息，所以不请求简历信息
      if (!this.props.userStore.isCompany && this.hasJob(jobId)) return;
      let { resume } = this.state;
      // 上次获取时间小于一小时，暂不刷新
      if (resume?.getTime && Date.now() - resume.getTime < 36000000) return;
      if (!resumeId) return;
      if (this.props.userStore.isCompany) {
        resume = await companyAction.getResumeDetail(resumeId);
      } else {
        resume = await ResumeService.getResumeDetail(resumeId);
      }
      resume.getTime = Date.now();
      this.localExtra.resume = resume;
      this.props.currentMessage.localExtra = JSON.stringify(this.localExtra);
      messageDao.updateLocalExtra(this.props.currentMessage);
      if (this.isUnmount) return;
      this.setState({ resume });
    } catch (e) {
      console.warn('messageJob getJobDetail', e);
    }
  };

  onPress = () => {
    const { job, resume } = this.state;
    if (this.props.userStore.isCompany) {
      NavigationService.push('resumeDetail', {
        item: this.localExtra.jobApply || { jobId: job?.id, resumeId: resume.resumeId },
        isSearch: true,
        isChat: true,
      });
    } else if (job?.id) {
      NavigationService.push('jobDetail', { detail: job, isChat: true });
    } else {
      NavigationService.push('resumeWebview', {
        resumeId: this.props.currentMessage.resumeId,
        title: I18n.t('page_resume_text_previews_title'),
        des: resume?.name,
      });
    }
  };

  render() {
    const { currentMessage } = this.props;
    const { job, resume } = this.state;
    if (resume) {
      return (
        <Touchable onPress={this.onPress} withoutFeedback>
          <View
            style={{
              backgroundColor: '#fff',
              marginHorizontal: 12,
              marginBottom: 20,
              marginTop: 15,
              paddingHorizontal: 15,
              paddingTop: 15,
              borderRadius: 10,
              shadowColor: 'rgba(0, 0, 0, 0.05)',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 1,
              shadowRadius: 2,
              elevation: 3,
            }}
          >
            <ResumeItem
              item={resume}
              intervieweeAvatar={resume.profile?.avatar}
              intervieweeName={util.getUserDisplayName(resume.profile)}
              careerExperience={resume.careerProfile?.careerExperience}
              qualificationLabel={resume.careerProfile?.qualificationId?.label}
              schoolName={resume.schoolName || this.localExtra.jobApply?.schoolName}
            />
            {job?.title ? (
              <View
                style={{
                  borderTopColor: '#E0E0E0',
                  borderTopWidth: 0.5,
                  marginTop: 5,
                  paddingVertical: 10,
                }}
              >
                <Text
                  style={{
                    color: '#333',
                    fontSize: 13,
                  }}
                >
                  {moment(currentMessage.msgTime).format('YYYY/MM/DD') + '  '}
                  {I18n.t('page_chat_job', { job: job.title })}
                </Text>
              </View>
            ) : null}
          </View>
        </Touchable>
      );
    }
    if (this.props.userStore.isCompany || !job) return null;
    return (
      <JobItem item={job} onPress={this.onPress} container={styles.container} withoutFeedback />
    );
  }
}
