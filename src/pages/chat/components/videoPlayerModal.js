import React, { Component } from 'react';
import {
  ActivityIndicator,
  Icon,
  StyleSheet,
  Text,
  Touchable,
  View,
  Modal,
  TouchableWithoutFeedback,
  Slider,
} from '../../../components';
import { deviceWidth, deviceHeight, statusBarHeight, footerHeight } from '../../../common';
import Video from 'react-native-video';
import Debounce from 'debounce-decorator';
import I18n from '../../../i18n';

const topHeight = statusBarHeight + 4;

const styles = StyleSheet.create({
  modal: {
    alignItems: 'center',
    justifyContent: 'center',
    width: deviceWidth,
    height: deviceHeight,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: 'rgba(0,0,0,1)',
    width: deviceWidth,
    height: deviceHeight,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: deviceWidth,
    height: deviceHeight,
    position: 'relative',
  },
  backgroundVideo: {
    width: deviceWidth,
    height: deviceHeight,
  },
  player: {
    position: 'absolute',
    right: 0,
    left: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerExtras: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#333333',
    marginTop: 8,
  },
  buttonCloseStyle: {
    position: 'absolute',
    left: 18,
    top: Math.min(topHeight, 32),
    zIndex: 999,
  },
  control: {
    flexDirection: 'row',
    height: 44,
    alignItems: 'center',
    // backgroundColor: 'rgba(0, 0, 0, 0.8)',
    position: 'absolute',
    paddingHorizontal: 18,
    bottom: Math.max(footerHeight, 32),
    left: 0,
  },
  time: {
    fontSize: 14,
    color: '#ffffff',
    marginHorizontal: 10,
  },
});

const Status = {
  loading: 'loading', // 视频加载中
  loaded: 'loaded', // 加载完成，可播放状态
  playing: 'playing', // 播放中
  error: 'error', // 加载失败
};

function formatTime(second) {
  let i = 0,
    s = Math.round(second);
  if (s > 60) {
    i = Math.round(s / 60);
    s = Math.round(s % 60);
  }
  // 补零
  let zero = function (v) {
    return v >> 0 < 10 ? '0' + v : v;
  };
  return [zero(i), zero(s)].join(':');
}

/**
 * 视频类型消息
 * <AUTHOR>
 */
export default class VideoPlayerModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      videoStyle: null,
      isPlaying: true, // 暂停
      poster: null, // 视频封面
      status: null, // 加载状态
      playUrl: '', // 视频播放地址
      duration: 0, // 视频播放时长
      showVideoControl: false, // 是否显示视频控制组件
      currentTime: 0, // 视频当前播放的时间
      playFromBeginning: false, // 是否从头开始播放
    };
  }

  // static getDerivedStateFromProps({ currentMessage }, state) {
  //   const uri = currentMessage.imagePath || currentMessage.content;
  //   const posterUrl = currentMessage.content;
  //   console.log('MessageVideo getDerivedStateFromProps', currentMessage);
  //   // const extra = currentMessage.extra && JSON.parse(currentMessage.extra);
  //   if (uri !== state.uri || posterUrl !== state.posterUrl) {
  //     return {
  //       uri,
  //       posterUrl,
  //       poster: posterUrl ? `${posterUrl}?x-oss-process=video/snapshot,t_0,m_fast` : posterUrl,
  //       source: { uri }, // test video url 'https://media.w3.org/2010/05/sintel/trailer.mp4'
  //       // videoStyle: getVideoWH(extra || {}),
  //     };
  //   }
  //   return null;
  // }

  /// 打开视频播放窗口
  open = ({ uri }, callback) => {
    this.callback = callback;
    this.setState({
      playUrl: uri,
      isOpen: true,
      isPlaying: true,
    });
  };

  /// 关闭视频modal
  close = () => {
    console.log('MessageVideo close');
    this.videoPlayer?.seek(0);
    this.setState({ isPlaying: false, isOpen: false, currentTime: 0 });
  };

  /// 媒体开始加载时调用的回调函数。
  onLoadStart = (res) => {
    console.log('MessageVideo onLoadStart', res);
    this.setState({ status: Status.loading });
  };

  /// 当第一个视频帧准备好显示时调用的回调函数。这是海报被移除的时候。仅支持iOS
  onReadyForDisplay = () => {
    console.log('MessageVideo onReadyForDisplay');
    // this.setState({ status: Status.playing });
  };

  /// 当媒体加载并准备播放时调用的回调函数
  onLoad = (res) => {
    console.log('MessageVideo onLoad', res);
    this.setState({ status: Status.playing, duration: res.duration });
  };

  onPlayAndFullScreen = () => {
    console.log('MessageVideo onPlayAndFullScreen');
    this.videoPlayer?.presentFullscreenPlayer();
    this.setState({ isPlaying: true, status: Status.playing });
  };

  /// 当播放器到达媒体末尾时调用的回调函数。
  onEnd = () => {
    console.log('MessageVideo onEnd');
    this.setState({
      status: Status.loaded,
      currentTime: 0,
      isPlaying: false,
      playFromBeginning: true,
    });
  };

  /// 无法加载视频时的回调
  onError = (e) => {
    logger.warn('MessageVideo onError', e);
    this.setState({ status: Status.error });
  };

  /// 视频加载出错后，点击重新播放
  onReLoad = () => {
    console.log('MessageVideo onReLoad');
    this.onRePlay();
  };

  /// 视频播放结束，重新播放
  onRePlay = () => {
    console.log('MessageVideo onRePlay');
    this.videoPlayer?.seek(0);
    this.setState({ isPlaying: true, status: Status.playing });
  };

  /// 远程视频缓冲时回调
  onBuffer = () => {
    console.log('MessageVideo onBuffer');
    // if (IS_ANDROID) {
    //   this.setState({ status: Status.loading });
    // }
  };

  /// 视频进度更新
  onProgressChanged = (data) => {
    if (this.state.isPlaying) {
      this.setState({
        currentTime: data.currentTime,
      });
    }
  };

  /// 控制播放器工具栏的显示和隐藏
  @Debounce(300)
  hideControl() {
    if (this.state.showVideoControl) {
      this.setState({
        showVideoControl: false,
      });
    } else {
      this.setState(
        {
          showVideoControl: true,
        },
        () => {
          setTimeout(() => {
            this.setState({
              showVideoControl: false,
            });
          }, 5000);
        }
      );
    }
  }

  /// 进度条值改变
  onSliderValueChanged(currentTime) {
    this.videoPlayer.seek(currentTime);
    if (this.state.isPlaying) {
      this.setState({
        currentTime,
      });
    } else {
      this.setState({
        currentTime,
        isPlaying: true,
      });
    }
  }

  /// 点击了工具栏上的播放按钮
  onControlPlayPress() {
    this.onPressPlayButton();
  }

  /// 点击了播放器正中间的播放按钮
  onPressPlayButton() {
    let isPlay = !this.state.isPlaying;
    this.setState({
      isPlaying: isPlay,
    });
    if (this.state.playFromBeginning) {
      this.videoPlayer?.seek(0);
      this.setState({
        playFromBeginning: false,
      });
    }
  }

  /// 保存视频
  onSave = () => {
    // let response = await this.videoPlayer?.save();
    // console.log('response', response);
    // let path = response.uri;
  };

  renderAction = () => {
    const { status } = this.state;
    if (status === Status.loading) {
      return (
        <View style={styles.player}>
          <ActivityIndicator animating color="#999999" size="small" />
        </View>
      );
    }
    if (status === Status.error) {
      return (
        <View style={styles.player}>
          <Touchable style={[styles.playerExtras]} onPress={this.onReLoad}>
            <Icon type="material" name="error" size={42} color="#DC143C" />
            <Text style={styles.errorText}>{I18n.t('page_chat_text_video_load_error')}</Text>
          </Touchable>
        </View>
      );
    }
    // if (status === Status.loaded) {
    //   return (
    //     <View style={styles.player}>
    //       <Touchable onPress={this.onRePlay}>
    //         <Icon type="antdesign" name="playcircleo" size={48} color="#fff" />
    //       </Touchable>
    //     </View>
    //   );
    // }
    return null;
  };

  initVideoRef = (ref) => (this.videoPlayer = ref);

  render() {
    const {
      videoStyle,
      isPlaying,
      poster,
      isOpen,
      playUrl,
      duration,
      showVideoControl,
    } = this.state;
    return (
      <Modal visible={isOpen} transparent animationType="fade" onRequestClose={this.close}>
        <View style={styles.modal}>
          <TouchableWithoutFeedback onPress={this.close}>
            <View style={styles.backdrop} />
          </TouchableWithoutFeedback>
          <View style={styles.container}>
            <Video
              ref={this.initVideoRef}
              source={{ uri: playUrl }} // Can be a URL or a local file.
              controls={false}
              fullscreen={false}
              autoPlay={true}
              pictureInPicture={false} // [iOS] 是否以画中画模式播放
              repeat={false} // 是否重复播放
              resizeMode="contain" // 视频的自适应伸缩铺放行为
              // poster={poster} // 封面图
              posterResizeMode="cover"
              paused={!isPlaying} // 控制暂停、播放
              muted={false} // 控制静音
              rate={1.0}
              volume={1.0} // 0静音 1是正常的
              fullscreenAutorotate={false}
              playInBackground={false} // 当应用程序输入背景音频时，音频继续播放。
              playWhenInactive={false} // [iOS]当显示控制或通知中心时，视频继续播放。
              ignoreSilentSwitch="ignore" // [iOS] ignore | 服从 - 当'忽略'时，音频仍然会播放与iOS硬静音开关设置为静音。当“服从”时，音频将切换开关。当未指定时，将照常继承音频设置。
              progressUpdateInterval={250.0} // [iOS] Interval to fire onProgress（默认为〜250ms ）
              onLoadStart={this.onLoadStart} // 视频开始加载时的回调
              onLoad={this.onLoad} // 当媒体加载并准备播放时调用的回调函数
              onEnd={this.onEnd} // 播放完成时的回调
              onError={this.onError} // 当视频无法加载时
              onProgress={this.onProgressChanged} // 回调每250毫秒〜与currentTime的
              onBuffer={this.onBuffer} // 当远程视频正在缓冲时回调
              style={[styles.backgroundVideo, videoStyle]}
            />
            {this.renderAction()}

            <Touchable onPress={this.close} style={styles.buttonCloseStyle}>
              <Icon type="ionicon" name="close-outline" size={36} color="#ffffff" />
            </Touchable>

            <TouchableWithoutFeedback
              onPress={() => {
                this.hideControl();
              }}
            >
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: deviceWidth,
                  height: deviceHeight,
                  backgroundColor: isPlaying ? 'transparent' : 'rgba(0, 0, 0, 0.2)',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {isPlaying ? null : (
                  <Touchable
                    withoutFeedback
                    onPress={() => {
                      this.onPressPlayButton();
                    }}
                  >
                    <Icon type="antdesign" name="playcircleo" size={48} color="#fff" />
                  </Touchable>
                )}
              </View>
            </TouchableWithoutFeedback>
            {showVideoControl ? (
              <View style={[styles.control, { width: deviceWidth }]}>
                <Touchable
                  activeOpacity={0.3}
                  onPress={() => {
                    this.onControlPlayPress();
                  }}
                >
                  <Icon
                    type="ionicon"
                    name={isPlaying ? 'pause' : 'play'}
                    size={24}
                    color="#ffffff"
                  />
                </Touchable>
                <Text style={styles.time}>{formatTime(this.state.currentTime)}</Text>
                <Slider
                  style={{ flex: 1 }}
                  maximumTrackTintColor="#333333"
                  minimumTrackTintColor="#999999"
                  thumbTintColor="#ffffff"
                  value={this.state.currentTime}
                  minimumValue={0}
                  maximumValue={duration}
                  onValueChange={(currentTime) => {
                    this.onSliderValueChanged(currentTime);
                  }}
                />
                <Text style={[styles.time, { marginRight: 0 }]}>{formatTime(duration)}</Text>
              </View>
            ) : null}
          </View>
        </View>
      </Modal>
    );
  }
}
