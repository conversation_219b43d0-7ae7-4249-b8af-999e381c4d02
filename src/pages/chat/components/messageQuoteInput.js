import React, { Component } from 'react';
import { Image, StyleSheet, Touchable, View } from '../../../components';
import resIcon from '../../../res';
import constant from '../../../store/constant';
import Quote from './Quote';

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 15,
    flexDirection: 'row',
    position: 'absolute',
    left: 0,
    right: 0,
  },
  contentContainer: {
    flex: 1,
  },
  name: {
    fontSize: 12,
    color: '#8E96A3',
  },
});

/**
 * 引用的消息，显示在输入框上方
 * <AUTHOR>
 */
export default class MessageQuoteInput extends Component {
  onViewDetail = () => {
    global.emitter.emit(constant.event.quoteMessage, { message: null });
  };

  render() {
    const { message, memberMap, minInputToolbarHeight } = this.props;
    if (!message) return null;
    return (
      <View style={[styles.container, { bottom: minInputToolbarHeight }]}>
        <Quote message={message} memberMap={memberMap} style={styles.contentContainer} />
        <Touchable onPress={this.onViewDetail} notHideKeyboard>
          <Image source={resIcon.messageCancelQuote} />
        </Touchable>
      </View>
    );
  }
}
