import React, { Component } from 'react';
import { Icon, StyleSheet, Touchable, View, Image } from '../../../components';
import { deviceWidth, px2dp } from '../../../common';
import constant from '../../../store/constant';
import sentryUtil from '../../../util/sentryUtil';
import MessageTime from './messageTime';
import { Size } from './messageConfig';

const styles = StyleSheet.create({
  leftVideoContainer: {
    position: 'relative',
    marginLeft: Size.bubbleArrowSize,
    marginBottom: 4,
  },
  rightVideoContainer: {
    position: 'relative',
    marginRight: Size.bubbleArrowSize,
    marginBottom: 4,
  },
  backgroundVideo: {
    width: 1,
    height: 1,
    borderRadius: 5,
    overflow: 'hidden',
    backgroundColor: '#cccccc80',
  },
  player: {
    position: 'absolute',
    right: 0,
    left: 0,
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

function getPosterUrl(url) {
  if (url.startsWith('http')) {
    return `${url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast,ar_auto`;
  }
  if (IS_IOS && url.startsWith('file://')) {
    return url.replace('file://', '');
  }
  return url;
}

function getImageWH({ posterWidth, posterHeight }) {
  if (!posterWidth || !posterHeight) return null;
  const maxWidth = deviceWidth * 0.4;
  const maxHeight = deviceWidth * 0.4;
  posterWidth = px2dp(posterWidth);
  posterHeight = px2dp(posterHeight);
  const rateW = maxWidth / posterWidth;
  const rateH = maxHeight / posterHeight;
  let rate;
  if (rateW >= 1 && rateH >= 1) {
    rate = 1;
  } else {
    rate = Math.min(rateW, rateH);
  }
  posterWidth *= rate;
  posterHeight *= rate;
  return { width: posterWidth, height: posterHeight };
}

/**
 * 视频类型消息
 * <AUTHOR>
 */
export default class MessageVideoFuture extends Component {
  constructor(props) {
    super(props);
    this.state = {
      videoStyle: null,
      poster: null, // 视频封面
    };
  }

  static getDerivedStateFromProps({ currentMessage }, state) {
    console.log('MessageVideo getDerivedStateFromProps', currentMessage);
    const uri = currentMessage.content || currentMessage.imagePath;
    const posterUrl = currentMessage.content;
    const extra = currentMessage.extra && sentryUtil.parse(currentMessage.extra, 'mvf gds');
    // const getUrl = currentMessage.isSelfSend ? extra?.posterUrl || uri : posterUrl;

    const getUrl = currentMessage?.sendState === 2 ? extra?.posterUrl : uri;
    if (uri !== state.uri || posterUrl !== state.posterUrl) {
      return {
        uri,
        posterUrl,
        poster: getPosterUrl(getUrl),
        // poster: posterUrl ? `${posterUrl}?x-oss-process=video/snapshot,t_0,m_fast` : posterUrl,
        videoStyle: getImageWH(extra || {}),
      };
    }
    return null;
  }

  onPlayAndFullScreen = async () => {
    console.log('MessageVideo onPlayer Click');
    const { currentMessage } = this.props;
    const uri = currentMessage.imagePath || currentMessage.content;
    global.emitter.emit(constant.event.playVideo, { uri });
  };

  onLongPress = () => {
    const { onLongPress, currentMessage } = this.props;
    if (currentMessage?.sendState === 2) {
      return;
    }
    onLongPress?.(this.context, currentMessage);
  };

  render() {
    const { videoStyle, poster } = this.state;
    const { position, currentMessage } = this.props;
    return (
      <View
        style={[
          styles[`${position}VideoContainer`],
          currentMessage?.isGroup
            ? { alignSelf: 'center', marginRight: Size.bubbleArrowSize, marginTop: 4 }
            : {},
          videoStyle,
        ]}
      >
        <Image
          source={{ uri: poster }}
          style={[styles.backgroundVideo, videoStyle]}
          onLongPress={this.onLongPress}
          resizeMode="contain"
        />
        <MessageTime {...this.props} isFull />
        <Touchable
          onPress={this.onPlayAndFullScreen}
          onLongPress={this.onLongPress}
          style={styles.player}
        >
          <Icon type="antdesign" name="playcircleo" size={36} color="#fff" />
        </Touchable>
      </View>
    );
  }
}
