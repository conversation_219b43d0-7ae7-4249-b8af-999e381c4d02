import React, { Component } from 'react';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import { MessageComponents } from './messageComponents';

/**
 * 已发送简历消息
 */
export default class MessageSendResume extends Component {
  onView = () => {
    const { currentMessage } = this.props;
    NavigationService.navigate('resumeDetail', {
      item: { jobId: currentMessage.jobId, resumeId: currentMessage.resumeId },
      isSearch: true,
      isChat: true,
    });
  };

  render() {
    return (
      <MessageComponents
        position={this.props.position}
        color="#FAECEC"
        img={resIcon.chatResumeSend}
        text={I18n.t('page_chat_text_resume_msg')}
        onPress={this.onView}
        btnTitle={I18n.t('page_chat_link_view_interview')}
        btnType="viewRecord"
      />
    );
  }
}
