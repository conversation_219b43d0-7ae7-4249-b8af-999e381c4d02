import React, { Component } from 'react';
import { StyleSheet, Text, Touchable, View } from '../../../components';
import { WIDTH } from '../../../common';
import Avatar from '../../../components/avatar/avatar';
import NavigationService from '../../../navigationService';
import I18n from '../../../i18n';
import sentryUtil from '../../../util/sentryUtil';
import { CommonStyles } from './messageConfig';
import { inject, observer } from 'mobx-react';

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
    paddingHorizontal: 10,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
    width: WIDTH(230),
    flexDirection: 'column',
    marginBottom: 4,
  },
  leftContainer: {
    borderBottomLeftRadius: 0,
  },
  rightContainer: {
    borderBottomRightRadius: 0,
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 15,
  },
  info: {
    flexDirection: 'column',
    marginLeft: 10,
    flexShrink: 1000,
  },
  nameText: {
    fontSize: 15,
    color: '#333',
    fontWeight: 'bold',
  },
  line: {
    marginTop: 10,
    marginBottom: 5,
    height: 1,
    backgroundColor: '#333',
    opacity: 0.1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 8,
  },
  statusText: {
    fontSize: 12.0,
    color: '#333333',
    opacity: 0.8,
    marginLeft: 5,
  },
});

/**
 * 公司名片
 */
@inject('userStore')
@observer
export default class MessageCompanyBusinessCard extends Component {
  onCardDetail = () => {
    NavigationService.navigate('companyDetail', {
      employerId: this.company.employerId,
      isPreview: this.props.userStore.isCompany,
    });
  };

  render() {
    const { position, currentMessage } = this.props;
    const company = sentryUtil.parseSafe(currentMessage.content);
    if (!company) return null;
    this.company = company;
    const isLeft = position === 'left';
    return (
      <Touchable onPress={this.onCardDetail} withoutFeedback>
        <View
          style={[
            styles.container,
            CommonStyles.shadow,
            isLeft ? styles.leftContainer : styles.rightContainer,
          ]}
        >
          <View style={styles.topContainer}>
            <Avatar avatar={company.logo || ''} name={company.company} style={styles.avatar} />
            <View style={styles.info}>
              <Text style={styles.nameText} numberOfLines={1}>
                {company.company}
              </Text>
            </View>
          </View>
          <View style={styles.line} />
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>{I18n.t('feat_company_business_card')}</Text>
          </View>
        </View>
      </Touchable>
    );
  }
}
