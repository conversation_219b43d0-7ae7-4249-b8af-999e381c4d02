import React from 'react';
import { But<PERSON>, Text, Touchable, View } from '../../../components';
import I18n from '../../../i18n';
import Avatar from '../../../components/avatar/avatar';
import styles from '../../../themes';
import navigationService from '../../../navigationService';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      marginTop: 10,
      marginHorizontal: 10,
      paddingHorizontal: 12,
      paddingVertical: 12,
      backgroundColor: '#fff',
      borderRadius: 5,
    },
    hintText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 10,
    },
    userContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexShrink: 1000,
      flex: 1,
    },
    nameText: {
      fontSize: theme.fontSizeXL,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginHorizontal: 10,
      flexShrink: 1000,
    },
  };
}

/**
 * 添加好友卡片
 * <AUTHOR>
 */
export default class AddFriendCard extends React.PureComponent {
  constructor(props) {
    super(props);
    this.style = getStyle();
  }

  onDetail = () => {
    navigationService.navigate('chatAddContactDetail', {
      imId: this.props.friend.imId,
    });
  };

  onAdd = () => {
    navigationService.navigate('requestAddFriend', {
      imId: this.props.friend.imId,
    });
  };

  render() {
    const { friend } = this.props;
    const { style } = this;
    const name = friend.memo || friend.nickname;
    return (
      <View style={style.container}>
        <Text style={style.hintText}>{I18n.t('page_chat_add_friend_hint')}</Text>
        <View style={style.bottomContainer}>
          <Touchable onPress={this.onDetail} style={style.userContainer}>
            <Avatar avatar={friend.avatar} name={name} size={40} />
            <Text style={style.nameText} numberOfLines={2}>
              {name}
            </Text>
          </Touchable>
          <Button
            title={I18n.t('page_chat_add_friend')}
            btnSize="sm"
            containerStyle={{ minWidth: 60 }}
            paddingHorizontal={10}
            onPress={this.onAdd}
          />
        </View>
      </View>
    );
  }
}
