import React from 'react';
import { Image, StyleSheet, Text, View } from '../../../components';
import moment from 'moment';
import { Color } from './messageConfig';
import resIcon from '../../../res';
import chatMessageOptions from '../../../database/chatMessageOptions';
import sentryUtil from '../../../util/sentryUtil';
import messageDao from '../../../database/dao/messageDao';

const styles = StyleSheet.create({
  timeContainer: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingRight: 5,
    paddingBottom: 5,
  },
  timeContainer2: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  timeContainerFull: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.25)',
    borderRadius: 5,
    flexDirection: 'row',
    paddingRight: 5,
    paddingBottom: 5,
  },
  timeText: {
    fontSize: 12,
    color: Color.timeColor,
    marginRight: 5,
  },
  timeTextFull: {
    fontSize: 12,
    color: '#ffffff',
    marginRight: 5,
  },
  timeTextOpacity: {
    fontSize: 12,
    color: '#ffffff',
    marginRight: 5,
    opacity: 0.8,
  },
  readImg: {
    width: 18,
    height: 10,
    marginBottom: 3,
  },
});

export default class MessageTime extends React.Component {
  renderRead(currentMessage, groupInfo) {
    if (!chatMessageOptions.isShowRead(currentMessage, groupInfo)) return null;
    let { isRead, isArrived, readExtra } = currentMessage;
    let img;
    if (isRead) {
      img = resIcon.chatMsgRead2;
    } else if (isArrived) {
      img = resIcon.chatMsgRead1;
    } else {
      /*readExtra = readExtra && sentryUtil.parse(readExtra, 'mt rr');
      if (!readExtra?.receiverIds?.length && currentMessage.msgId) {
        img = resIcon.chatMsgRead2;
        currentMessage.isRead = 1;
        currentMessage.isArrived = 1;
        messageDao.updateMessageRead({
          sessionId: currentMessage.sessionId,
          ownerId: currentMessage.ownerId,
          isRead: 1,
          isArrived: 1,
          msgIds: [currentMessage.msgId],
        });
      } else {
        img = resIcon.chatMsgRead0;
      }*/
      img = resIcon.chatMsgRead0;
    }
    // console.log('MessageTime renderRead', isRead, isArrived);
    return <Image source={img} style={styles.readImg} />;
  }

  render() {
    const {
      groupInfo,
      currentMessage,
      position,
      isFull,
      styleKey = isFull ? 'timeContainerFull' : 'timeContainer',
      timeTextStyleKey = isFull ? 'timeTextFull' : 'timeText',
    } = this.props;
    return (
      <View style={styles[styleKey]}>
        <Text style={styles[timeTextStyleKey]}>
          {moment(currentMessage.msgTime).format('HH:mm')}
        </Text>
        {position === 'right' ? this.renderRead(currentMessage, groupInfo) : null}
      </View>
    );
  }
}
