import React from 'react';
import AudioRecorderPlayer, {
  AudioSourceAndroidType,
  AudioEncoderAndroidType,
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
} from 'react-native-audio-recorder-player';
import { Image, PanResponder, Text, View } from '../../../components';
import styles from '../../../themes';
import { deviceHeight, WIDTH } from '../../../common';
import fileUtil from '../../../util/fileUtil';
import { inject, observer } from 'mobx-react';
import sendMessageUtil from '../../../database/sendMessageUtil';
import BigDecimal from '../../../util/BigDecimal';
import resIcon from '../../../res';
import ImageAnimation from '../../../components/images/imageAnimation';
import audioPlayUtil from '../../../util/audioPlayUtil';
import PermissionUtil from '../../../util/permissionUtilExtra';
import I18n from '../../../i18n';

function getStyle() {
  const theme = styles.get('theme');
  const modalSize = 124;
  return {
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      marginHorizontal: 0,
      backgroundColor: theme.primaryColor,
      borderRadius: 10,
      height: 42,
    },
    containerActive: {
      backgroundColor: theme.primaryColor,
    },
    text: {
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightRegular,
      color: theme.primaryBgColor,
    },
    modalContentContainer: {
      position: 'absolute',
      bottom: deviceHeight * 0.5,
      left: WIDTH((375 - modalSize) / 2),
      right: 0,
      zIndex: 10000,
      backgroundColor: theme.primaryColor,
      borderRadius: 10,
      width: WIDTH(modalSize),
      height: WIDTH(modalSize),
      alignItems: 'center',
    },
    modalContentContainerActive: {
      backgroundColor: '#FF4B5A',
    },
    modalImage: {
      marginTop: WIDTH(20),
    },
    modalRecordAnimImage: {
      position: 'absolute',
      top: WIDTH(50),
    },
    modalTipText: {
      position: 'absolute',
      bottom: 0,
      minHeight: WIDTH(32),
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: '#fff',
      alignSelf: 'center',
      textAlign: 'center',
    },
    modalTimeText: {
      marginTop: WIDTH(14),
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightBold,
      color: '#fff',
    },
  };
}

const Status = {
  normal: 'normal', // 正常
  recording: 'recording', // 按住在说话
  cancel: 'cancel', // 上滑取消
  invalid: 'invalid', // 说话时间太短
};

const audioSet = {
  AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
  AudioSourceAndroid: AudioSourceAndroidType.MIC,
  AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
  AVNumberOfChannelsKeyIOS: 2,
  AVFormatIDKeyIOS: AVEncodingOption.aac,
};

/**
 * 聊天页面，底部录制语音组件
 * <AUTHOR>
 */
@inject('userStore', 'callAction')
@observer
export default class MessageRecordAudio extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      timeLenStr: audioPlayUtil.formatMMSSByMillisecond(0),
      status: Status.normal,
    };
    this.style = getStyle(props.userStore.isCompany);
    this.audioRecordAnimImages = [
      resIcon.audioRecordAnim1,
      resIcon.audioRecordAnim2,
      resIcon.audioRecordAnim3,
    ];
    this.statusInfos = {
      [Status.normal]: {
        containerStyle: this.style.container,
        btnText: I18n.t('page_audio_state_touch'),
        hideModal: true,
      },
      [Status.recording]: {
        containerStyle: [this.style.container, this.style.containerActive],
        btnText: I18n.t('page_audio_state_send'),
        textColor: '#fff',
        modalContentContainerStyle: this.style.modalContentContainer,
        modalTipText: I18n.t('page_audio_state_touch_move'),
        recording: true,
      },
      [Status.cancel]: {
        containerStyle: [this.style.container, this.style.containerActive],
        btnText: I18n.t('page_audio_state_cancel'),
        textColor: '#fff',
        modalContentContainerStyle: [
          this.style.modalContentContainer,
          this.style.modalContentContainerActive,
        ],
        modalImage: resIcon.audioCancelRecord,
        modalTipText: I18n.t('page_audio_state_touch_cancel'),
      },
      [Status.invalid]: {
        containerStyle: this.style.container,
        btnText: I18n.t('page_audio_state_touch'),
        modalContentContainerStyle: this.style.modalContentContainer,
        modalImage: resIcon.audioRecordInvalid,
        modalTipText: I18n.t('page_audio_state_time'),
      },
    };
    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        console.log('MessageRecordAudio onStartShouldSetPanResponderCapture');
        this.isPanResponderRelease = false;
        this.onStartRecord();
        return true;
      },
      onPanResponderMove: (evt, gestureState) => {
        // 最近一次的移动距离为gestureState.move{X,Y}
        // 从成为响应者开始时的累计手势移动距离为gestureState.d{x,y}
        console.log(
          'MessageRecordAudio onPanResponderMove',
          gestureState.moveX,
          gestureState.moveY
        );
        this.onMove(gestureState);
      },
      onPanResponderRelease: () => {
        // 用户放开了所有的触摸点，且此时视图已经成为了响应者。
        // 一般来说这意味着一个手势操作已经成功完成。
        console.log('MessageRecordAudio onPanResponderRelease');
        this.isPanResponderRelease = true;
        this.onStopRecord();
      },
      onShouldBlockNativeResponder: () => {
        // 返回一个布尔值，决定当前组件是否应该阻止原生组件成为JS响应者
        // 默认返回true。目前暂时只支持android。
        console.log('MessageRecordAudio onShouldBlockNativeResponder');
        return true;
      },
    });
    this.audioRecorderPlayer = new AudioRecorderPlayer();
    this.timeLength = 0;
  }

  componentWillUnmount() {
    this.clearRecordInvalidTimer();
    this.audioRecorderPlayer?.removeRecordBackListener();
  }

  onRecordBackListener = (e) => {
    console.log('MessageRecordAudio onRecordBackListener', e.currentPosition);
    this.timeLength = e.currentPosition;
    this.setState({
      timeLenStr: audioPlayUtil.formatMMSSByMillisecond(e.currentPosition),
    });
  };

  onStartRecord = async () => {
    if (this.props.callAction.checkInCall()) {
      return;
    }
    try {
      const hasAudio = await PermissionUtil.requestAudioPermission();
      const hasStorage = await PermissionUtil.requestExternalStoragePermission();

      if (!hasAudio || !hasStorage || this.isPanResponderRelease) return;
      console.log('MessageRecordAudio onStartRecord', hasAudio, hasStorage);
      const { session, userStore } = this.props;
      let filePath;
      if (IS_IOS) {
        filePath = fileUtil.randomFileName('.m4a');
      } else {
        filePath = await fileUtil.getMsgFilePath({
          sessionId: session.sessionId,
          ownerId: userStore.imId,
          suffix: '.m4a',
          type: 'audio',
        });
      }
      console.log('MessageRecordAudio onStartRecord filePath', filePath);
      await audioPlayUtil.stopPlayer(true);
      const result = await this.audioRecorderPlayer.startRecorder(filePath, audioSet);
      this.startRecorderTime = Date.now();
      console.log('MessageRecordAudio onStartRecord result', this.isPanResponderRelease, result);
      this.audioRecorderPlayer.addRecordBackListener(this.onRecordBackListener);
      this.timeLength = 0;
      this.isCancel = false;
      this.clearRecordInvalidTimer();
      this.setState(
        {
          status: Status.recording,
          timeLenStr: audioPlayUtil.formatMMSSByMillisecond(0),
        },
        () => {
          if (this.isPanResponderRelease) {
            this.onStopRecord();
          }
        }
      );
    } catch (e) {
      logger.warn('MessageRecordAudio onStartRecord', e);
      toast.show(e?.message || I18n.t('page_audio_state_fail'));
    }
  };

  clearRecordInvalidTimer = () => {
    if (this.recordInvalidHandle) {
      clearTimeout(this.recordInvalidHandle);
      this.recordInvalidHandle = null;
    }
  };

  onRecordInvalid = async (result) => {
    this.setState({ status: Status.invalid });
    this.recordInvalidHandle = setTimeout(() => {
      this.setState({ status: Status.normal });
    }, 1500);
    return fileUtil.deleteFile(result);
  };

  onStopRecord = async () => {
    try {
      console.log(
        'MessageRecordAudio onStopRecord start',
        this.state.status,
        this.isPanResponderRelease,
        this.startRecorderTime
      );
      if (
        this.state.status === Status.normal ||
        !this.isPanResponderRelease ||
        !this.startRecorderTime
      )
        return;
      const time = Math.max(Date.now() - this.startRecorderTime, 0);
      if (time < 200) {
        setTimeout(this.onStopRecord, 210 - time);
        return;
      }
      this.startRecorderTime = 0;
      const result = await this.audioRecorderPlayer.stopRecorder();
      console.log('MessageRecordAudio onStopRecord', result);
      this.audioRecorderPlayer.removeRecordBackListener();
      if (this.isCancel) {
        this.setState({ status: Status.normal });
        console.log('MessageRecordAudio onStopRecord 删除', result);
        return fileUtil.deleteFile(result);
      }
      if (this.timeLength < 1000) {
        console.log('MessageRecordAudio onStopRecord 时间太短', this.timeLength, result);
        return this.onRecordInvalid(result);
      }
      this.setState({ status: Status.normal });
      console.log('MessageRecordAudio onStopRecord 发送', result);
      await sendMessageUtil.sendAudio(
        {
          filePath: result,
          fileSize: 0,
          timeLength: parseFloat(new BigDecimal(this.timeLength).divide(1000).toFixed(1)),
        },
        this.props.session
      );
    } catch (e) {
      this.setState({ status: Status.normal });
      logger.warn('MessageRecordAudio onStopRecord', e);
    }
  };

  onMove = ({ moveY }) => {
    this.isCancel = moveY < deviceHeight * 0.85;
    this.setState({ status: this.isCancel ? Status.cancel : Status.recording });
  };

  get statusInfo() {
    return this.statusInfos[this.state.status];
  }

  renderPanel = (statusInfo) => {
    if (statusInfo.hideModal) return null;
    const { timeLenStr } = this.state;
    const { style } = this;
    return (
      <View style={statusInfo.modalContentContainerStyle}>
        {statusInfo.recording ? (
          <>
            <Text style={style.modalTimeText}>{timeLenStr}</Text>
            <ImageAnimation
              images={this.audioRecordAnimImages}
              style={style.modalRecordAnimImage}
            />
          </>
        ) : null}
        {statusInfo.modalImage ? (
          <Image source={statusInfo.modalImage} style={style.modalImage} />
        ) : null}
        <Text style={style.modalTipText}>{statusInfo.modalTipText}</Text>
      </View>
    );
  };

  render() {
    const { style, statusInfo } = this;
    return (
      <>
        <View style={statusInfo.containerStyle} {...this._panResponder.panHandlers}>
          <Text style={[style.text, { color: statusInfo.textColor || '#ffffff' }]}>
            {statusInfo.btnText}
          </Text>
        </View>
        {this.renderPanel(statusInfo)}
      </>
    );
  }
}
