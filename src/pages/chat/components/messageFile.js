import React from 'react';
import { Image, StyleSheet, Text, View } from '../../../components';
import fileUtil from '../../../util/fileUtil';
import navigationService from '../../../navigationService';
import sentryUtil from '../../../util/sentryUtil';
import MessageBubbleContainer from './messageBubbleContainer';
import { Color, Size } from './messageConfig';
import MessageTime from './messageTime';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    minHeight: 60,
    paddingBottom: 5,
    marginBottom: 4,
  },
  fileNameContainer: { flexDirection: 'row' },
  fileImage: {
    width: 25,
    height: 30,
    marginLeft: 10,
  },
  leftFileNameText: {
    fontSize: Size.textFontSize,
    color: Color.leftTextColor,
    flexShrink: 1000,
  },
  rightFileNameText: {
    fontSize: Size.textFontSize,
    color: Color.rightTextColor,
    flexShrink: 1000,
  },
  fileSizeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  fileSizeText: {
    fontSize: 12,
    color: Color.timeColor,
  },
});

/**
 * 文件类型消息
 * <AUTHOR>
 */
export default class MessageFile extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      fileName: null,
      fileSize: null,
      fileTypeImage: null,
    };
  }

  static getDerivedStateFromProps({ currentMessage }, state) {
    if (currentMessage.extra !== state.extra) {
      const extra = currentMessage.extra && sentryUtil.parse(currentMessage.extra, 'mf gds');
      return {
        extra: currentMessage.extra,
        fileName: extra?.fileName,
        fileSize: fileUtil.getDisplayFileSize(extra?.fileSize),
        fileTypeImage: fileUtil.getFileTypeImage(
          extra?.fileType || fileUtil.getFileType(extra?.type)
        ),
      };
    }
    return null;
  }

  onViewImage = () => {
    const { currentMessage } = this.props;
    navigationService.navigate('messageFileDetail', {
      message: currentMessage,
    });
  };

  onLongPress = () => {
    const { onLongPress, currentMessage } = this.props;
    onLongPress?.(this.context, currentMessage);
  };

  render() {
    const { fileName, fileSize, fileTypeImage } = this.state;
    const { position } = this.props;
    return (
      <MessageBubbleContainer
        {...this.props}
        onPress={this.onViewImage}
        onLongPress={this.onLongPress}
        wrapperStyle={styles.container}
        hideTime
      >
        <View style={styles.fileNameContainer}>
          <Text style={styles[`${position}FileNameText`]} numberOfLines={3}>
            {fileName}
          </Text>
          <Image source={fileTypeImage} style={styles.fileImage} />
        </View>
        <View style={styles.fileSizeContainer}>
          <Text style={styles.fileSizeText}>{fileSize}</Text>
          <MessageTime {...this.props} styleKey="timeContainer2" />
        </View>
      </MessageBubbleContainer>
    );
  }
}
