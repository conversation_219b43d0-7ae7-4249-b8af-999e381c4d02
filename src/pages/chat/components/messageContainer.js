import React from 'react';
import { Message, MessageText } from 'react-native-gifted-chat';
import moment from 'moment';
import { Text, Touchable, View } from '../../../components';
import MessageAvatar from './messageAvatar';
import MessageBubble from './messageBubble';
import constant from '../../../store/constant';
import MessageSystem from './messageSystem';
import I18n from '../../../i18n';
import MessageBigEmoji from './messageBigEmoji';
import { Color, isHideMessageDate, Size } from './messageConfig';
import Configs from '../../../configs';
import MessageJob from './messageJob';
import MessageResume from './messageResume';

const styles = {
  messageContainerStyle: {
    left: { marginTop: 3, marginLeft: 5 },
    right: { marginTop: 3, marginRight: 5 },
  },
  messageTextStyle: {
    left: { color: Color.leftTextColor },
    right: { color: Color.rightTextColor },
  },
  messageLinkStyle: {
    left: {
      color: Color.leftLinkColor,
    },
    right: {
      color: Color.rightLinkColor,
    },
  },
  messageCustomTextStyle: {
    fontSize: Size.textFontSize,
    lineHeight: parseInt(Size.textFontSize * 1.5, 10),
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0,
  },
  dayContainerStyle: {
    alignItems: 'center',
    alignSelf: 'center',
    marginVertical: 20,
    borderRadius: 10,
    paddingHorizontal: 11,
    paddingVertical: 2,
    backgroundColor: Color.dateBackground,
  },
  dayTextStyle: { color: Color.dateColor, fontSize: 12 },
  parsePatternStyle: {
    left: { color: Color.leftBubbleBackground },
    right: { color: Color.rightBubbleBackground },
  },
};

function renderBubble(props) {
  return <MessageBubble {...props} />;
}

const leftTextEnd = [
  {
    text: '####',
    pattern: /#{4}$/,
    style: styles.parsePatternStyle.left,
  },
];

const rightTextEnd = [
  {
    text: leftTextEnd[0].text,
    pattern: leftTextEnd[0].pattern,
    style: styles.parsePatternStyle.right,
  },
];

const rightTextEndRead = [
  {
    text: '######',
    pattern: /#{6}$/,
    style: styles.parsePatternStyle.right,
  },
];

function parsePatterns(linkStyle, props) {
  return props?.position === 'left'
    ? leftTextEnd
    : Configs.disableMessageRead(props?.groupInfo)
    ? rightTextEnd
    : rightTextEndRead;
}

function renderMessageText(props) {
  if (props.bigEmoji) {
    return <MessageBigEmoji {...props} />;
  }
  const obj = parsePatterns(null, props)[0];
  return (
    <MessageText
      {...props}
      currentMessage={{ text: `${props.text || props.currentMessage.text}${obj.text}` }}
      parsePatterns={parsePatterns}
      optionTitles={[
        I18n.t('page_chat_text_phone'),
        I18n.t('page_chat_text_text'),
        I18n.t('op_cancel_title'),
      ]}
      textStyle={styles.messageTextStyle}
      linkStyle={styles.messageLinkStyle}
      customTextStyle={styles.messageCustomTextStyle}
    />
  );
}

function renderSystemMessage(props) {
  if (props.currentMessage.type === constant.messageType.job) {
    return <MessageJob {...props} />;
  }
  if (props.currentMessage.type === constant.messageType.resume) {
    return <MessageResume {...props} />;
  }
  return <MessageSystem {...props} />;
}

function renderAvatar(props) {
  return <MessageAvatar {...props} />;
}

function renderDay(props) {
  if (isHideMessageDate(props)) {
    return null;
  }
  return (
    <View style={styles.dayContainerStyle}>
      <Text style={styles.dayTextStyle}>
        {moment(props.currentMessage.msgTime).format('YYYY/MM/DD')}
      </Text>
    </View>
  );
}

function onPress(props) {
  global.emitter.emit(constant.event.foldPanel);
  // const extra = sentryUtil.parse(props.currentMessage.extra, 'mc op') || {};
  //this.props.callAction.startCall(extra.callType);
}

export function renderMessage(props) {
  return (
    <Touchable activeOpacity={1} onPress={() => onPress(props)}>
      <Message
        {...props}
        renderDay={renderDay}
        renderAvatar={renderAvatar}
        renderBubble={renderBubble}
        renderSystemMessage={renderSystemMessage}
        renderMessageText={renderMessageText}
        containerStyle={styles.messageContainerStyle}
      />
    </Touchable>
  );
}
