import React from 'react';
import { inject, observer } from 'mobx-react';
import chatSessionDao from '../../../database/dao/chatSessionDao';
import chatGroupDao from '../../../database/dao/chatGroupDao';
import { AlertPro, Image, Text, Touchable, View } from '../../../components';
import Avatar from '../../../components/avatar/avatar';
import util from '../../../util';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import styles from '../../../themes';
import avatarUtil from '../../../util/avatarUtil';
import PermissionUtil from '../../../util/permissionUtilExtra';

function getStyle() {
  const theme = styles.get('theme');
  return {
    chatItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 15,
      paddingHorizontal: 15,
      backgroundColor: '#fff',
    },
    topChatImage: {},
    chatItemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 20,
      // shadowColor: '#000000',
      // shadowOffset: { width: 0, height: 4 },
      // shadowOpacity: 0.2,
      // shadowRadius: 14,
      marginRight: 14,
    },
    chatItemContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    chatItemInfo: {
      flexShrink: 1000,
    },
    chatItemContent: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 5,
    },
    chatItemNameBox: {
      flexDirection: 'row',
      alignItems: 'center',
      flexShrink: 1000,
      marginRight: 25,
    },
    chatItemName: {
      fontSize: 16,
      fontWeight: theme.fontWeightMedium,
      color: '#484848',
      lineHeight: 22,
      marginRight: 5,
    },

    chatDate: {
      fontSize: 12,
      fontWeight: theme.fontWeightMedium,
      color: '#99A3BA',
      marginLeft: 10,
    },
    chatDateReceive: {
      color: '#FF4B5A',
    },
    btmLine: {
      height: 1,
      backgroundColor: 'rgba(220, 224, 235, 0.4)',
      marginLeft: 79,
      marginRight: 15,
    },
  };
}

@inject('imAction', 'meetingAction')
@observer
export default class CallItem extends React.Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.state = {
      // showActionSheet: false,
      alertContent: false,
      showAlert: false,
    };
  }

  // 发起音视频
  onMessage = async () => {
    try {
      const { item, index } = this.props;
      let callUsers = item;
      if (item.isGroup) {
        const { groupMembers } = await this.props.imAction.getGroupMemberList(
          item.sessionId,
          false
        );
        callUsers = groupMembers;
      } else {
        callUsers = [item];
      }
      this.onAudioCall(callUsers);
    } catch (e) {
      logger.warn('chat callItem onAudioCall', e, message);
      toast.show(e?.message);
    }
  };

  onAudioCall = async (item, session) => {
    if (this.props.meetingAction.checkInCall()) {
      return;
    }
    await PermissionUtil.requestMultiple();
    this.props.meetingAction.startCall([item]);
  };

  onDeleteChat = () => {
    const { item } = this.props;
    this.setState({
      showAlert: true,
      alertContent: item.isGroup
        ? I18n.t('page_chat_op_delete_p2p')
        : I18n.t('page_chat_op_delete_group'),
    });
  };

  onDeleteChatConfirm = async () => {
    try {
      await this.props.imAction.deleteSession(this.props.item);
    } catch (e) {
      logger.warn('onDeleteChatConfirm', e);
    }
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  onAlertConfirm = () => {
    this.setState({ showAlert: false });
    switch (this.actionIndex) {
      case 1:
        this.onDeleteChatConfirm();
        break;
    }
  };

  onActionSheetSelect = (index) => {
    console.log('ChatItem onActionSheetSelect', index);
    // this.setState({ showActionSheet: false });
    this.actionIndex = index;
    switch (index) {
      case 0:
        this.onSticky();
        break;
      case 1:
        this.onDeleteChat();
        break;
    }
  };

  onSticky = async () => {
    const { item } = this.props;
    const setTopTime = item?.setTopTime > 0 ? 0 : new Date().getTime();
    item.setTopTime = setTopTime;
    await chatSessionDao.updateSet(item);
    const groupInfo = await chatGroupDao.getGroup(item?.sessionId);
    groupInfo.setTopTime = setTopTime;
    await chatGroupDao.updateGroup(groupInfo);
  };

  renderAlert = () => {
    const { showAlert, alertContent } = this.state;
    if (!showAlert) return null;
    return (
      <AlertPro
        visible={showAlert}
        title={alertContent}
        textConfirm={I18n.t('op_confirm_title')}
        textCancel={I18n.t('op_cancel_title')}
        onConfirm={this.onAlertConfirm}
        onCancel={this.onAlertCancel}
      />
    );
  };

  render() {
    const { style } = this;
    const { item } = this.props;
    let contentData = item;
    try {
      contentData = JSON.parse(item?.content);
    } catch (error) {
      contentData = item;
    }
    return (
      <>
        <Touchable onPress={this.onMessage} style={{ backgroundColor: '#fff' }} activeOpacity={1}>
          <View style={style.chatItem}>
            <Avatar
              avatar={avatarUtil.handleAvatar(item.avatar)}
              name={item.title}
              style={style.chatItemAvatar}
              defaultAvatar={resIcon.chatDefaultAvatar}
              showDot={false}
            />
            <View style={style.chatItemContainer}>
              <View style={style.chatItemInfo}>
                <View style={style.chatItemNameBox}>
                  <Text style={style.chatItemName} numberOfLines={1}>
                    {item.title}
                  </Text>
                  {item.sessionId % 2 == 0 ? <Image source={resIcon.filterFavActive} /> : null}
                </View>
                <View style={style.chatItemContent}>
                  <Image
                    source={item.sessionId % 2 == 0 ? resIcon.chatReceive : resIcon.chatCall}
                  />
                  <Text
                    style={[style.chatDate, item.sessionId % 2 == 0 ? style.chatDateReceive : {}]}
                  >
                    {util.dateToLocalString6(item.lastMsgTime / 1000)}
                  </Text>
                </View>
              </View>
              <Image source={item.sessionId % 2 == 1 ? resIcon.chatAudio : resIcon.chatVideo} />
            </View>
          </View>
          <View style={style.btmLine} />
        </Touchable>
        {this.renderAlert()}
      </>
    );
  }
}
