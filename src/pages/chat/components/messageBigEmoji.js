import React from 'react';
import Lottie from 'lottie-react-native';
import { StyleSheet, Text, Touchable, View } from '../../../components';

const styles = StyleSheet.create({
  container: { width: 100, height: 100, justifyContent: 'center', alignItems: 'center' },
  lottie: { width: 90, height: 90, marginTop: 2 },
  emojiText: { fontSize: 76, color: '#000', textAlign: 'center', textAlignVertical: 'center' },
});

export default class MessageBigEmoji extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showBigEmoji: true,
    };
  }

  onPlay = () => {
    const { showBigEmoji } = this.state;
    console.log('onPlay', showBigEmoji);
    if (showBigEmoji) {
      this.lottie?.play();
    } else {
      this.setState({ showBigEmoji: true });
    }
  };

  onAnimationFinish = (isCancelled) => {
    console.log('onAnimationFinish', isCancelled);
    if (!isCancelled) {
      this.setState({ showBigEmoji: false });
    }
  };

  refLottie = (ref) => (this.lottie = ref);

  onLongPress = () => {
    const { onLongPress, currentMessage } = this.props;
    onLongPress?.(this.context, currentMessage);
  };

  render() {
    const { bigEmoji, currentMessage } = this.props;
    const { showBigEmoji } = this.state;
    return (
      <Touchable onPress={this.onPlay} onLongPress={this.onLongPress} withoutFeedback>
        <View style={styles.container}>
          {showBigEmoji ? (
            <Lottie
              ref={this.refLottie}
              onAnimationFinish={this.onAnimationFinish}
              style={styles.lottie}
              source={{ uri: bigEmoji }}
              autoPlay
              loop={false}
            />
          ) : (
            <Text style={styles.emojiText}>{currentMessage.text}</Text>
          )}
        </View>
      </Touchable>
    );
  }
}
