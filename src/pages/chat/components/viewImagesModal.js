import React from 'react';
import ActionSheet from 'react-native-actionsheet';
import { BackHandler } from 'react-native';
import Modal from 'react-native-modalbox';
import { AlertPro, BaseComponent, StyleSheet, View } from '../../../components';
import I18n from '../../../i18n';
import constant from '../../../store/constant';
import ImageViewer from '../../../components/images/imageViewer';
import { statusBarHeight } from '../../../common';

const styles = StyleSheet.create({
  modal: {
    backgroundColor: 'rgba(0,0,0,1)',
    flex: 1,
  },
});

/**
 * 查看聊天图片Modal
 * <AUTHOR>
 */
export default class ViewImagesModal extends BaseComponent {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      netImage: props?.avatar,
      imgCanLoad: true,
      images: [],
      index: 0,
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    global.emitter.on(constant.event.viewChatImages, this.open);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.viewChatImages, this.open);
    BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
  }

  // 如果開啟圖片時按下倒退鍵，則隱藏圖片視窗，而非退出聊天視窗
  handleBackButtonClick = () => {
    if (this.state.isOpen && this.props.navigation?.isFocused()) {
      this.close();
      return true;
    }
    return false;
  };

  open = ({ images, index }) => {
    console.log(images, index);
    this.setState({ images, index, netImage: images[index].url, isOpen: true });
  };

  close = () => {
    this.setState({ isOpen: false });
  };

  actionSheetSelect = (index) => {
    if (index === 0) {
      this.imageViewer?.savePhoto();
    }
  };

  refImageViewer = (ref) => (this.imageViewer = ref);

  render() {
    const { isOpen, index, images } = this.state;
    if (!isOpen) return null;
    const { showMore = true, coverScreen = false, statusBarColor } = this.props;
    return (
      <Modal
        isOpen={isOpen}
        backdropPressToClose
        onClosed={this.close}
        swipeToClose={false}
        position="center"
        entry="center"
        coverScreen={coverScreen}
      >
        <View style={styles.modal}>
          {statusBarColor ? (
            <View style={{ backgroundColor: statusBarColor, height: statusBarHeight }} />
          ) : null}
          <ImageViewer
            ref={this.refImageViewer}
            onClick={this.close}
            imageUrls={images}
            index={index}
            showMore={showMore}
            showDownload
          />
          <ActionSheet
            ref={(ref) => {
              this.ActionSheet = ref;
            }}
            options={[I18n.t('page_avatar_show_op_save'), I18n.t('op_cancel_title')]}
            cancelButtonIndex={1}
            onPress={this.actionSheetSelect}
          />
        </View>
      </Modal>
    );
  }
}
