import React, { Component } from 'react';
import { StyleSheet, Touchable } from '../../../components';
import Quote from './Quote';
import constant from '../../../store/constant';
import navigationService from '../../../navigationService';
import promiseUtil from '../../../util/promiseUtil';

const styles = StyleSheet.create({
  left: {
    borderBottomWidth: 1,
    borderBottomColor: '#F5F8FB',
    paddingBottom: 10,
  },
  right: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingBottom: 10,
  },
});

/**
 * 被引用的消息
 * <AUTHOR>
 */
export default class MessageQuote extends Component {
  onViewDetail = async () => {
    await promiseUtil.sleep(20); // 防止输入法还未关闭，进入下个页面，导致下面空白
    const {
      currentMessage: { quoteMessage },
    } = this.props;
    // console.log('MessageQuote onViewDetail', quoteMessage);
    switch (quoteMessage.type) {
      case constant.messageType.text:
      case constant.messageType.referTo:
        global.emitter.emit(constant.event.showQuoteMessageText, {
          content: quoteMessage.content,
        });
        break;
      case constant.messageType.transfer:
        break;
      case constant.messageType.receiveCreate:
        break;
      case constant.messageType.redPacket:
        break;
      case constant.messageType.image:
        navigationService.navigate('viewImages', {
          images: [{ url: quoteMessage.imagePath || quoteMessage.content }],
          index: 0,
        });
        break;
      case constant.messageType.audio:
        break;
      case constant.messageType.video:
        break;
      case constant.messageType.file:
        break;
      case constant.messageType.none:
        break;
    }
  };

  render() {
    const { currentMessage, memberMap, position } = this.props;
    return (
      <Touchable onPress={this.onViewDetail} style={styles[position]}>
        <Quote message={currentMessage.quoteMessage} memberMap={memberMap} position={position} />
      </Touchable>
    );
  }
}
