import React, { Component } from 'react';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import constant from '../../../store/constant';
import sentryUtil from '../../../util/sentryUtil';
import { MessageComponents } from './messageComponents';

/**
 * 请求简历
 */
export default class MessageReqResume extends Component {
  onSendResume = () => {
    global.emitter.emit(constant.event.showResumeModal, {
      isOpen: true,
      page: 'chatMessage',
      message: this.props.currentMessage,
    });
  };

  render() {
    const localExtra = sentryUtil.parseSafe(this.props.currentMessage.localExtra);
    const isSend = !!localExtra?.isSend;
    return (
      <MessageComponents
        position={this.props.position}
        img={resIcon.chatResumeReq}
        text={I18n.t('page_chat_req_resume_left')}
        onPress={this.onSendResume}
        btnTitle={I18n.t(isSend ? 'page_chat_has_been_sent' : 'page_chat_btn_send_msg')}
        btnDisabled={isSend}
      />
    );
  }
}
