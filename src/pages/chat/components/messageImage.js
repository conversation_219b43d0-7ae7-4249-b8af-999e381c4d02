import React, { Component } from 'react';
import { Image, StyleSheet, Touchable } from '../../../components';
import { deviceWidth, px2dp } from '../../../common';
import constant from '../../../store/constant';
import messageDao from '../../../database/dao/messageDao';
import promiseUtil from '../../../util/promiseUtil';
import sentryUtil from '../../../util/sentryUtil';
import MessageTime from './messageTime';
import { Size } from './messageConfig';
import fileUtil from '../../../util/fileUtil';

const styles = StyleSheet.create({
  leftTouchable: {
    marginLeft: Size.bubbleArrowSize,
    marginBottom: 4,
    alignSelf: 'center',
  },
  rightTouchable: {
    marginRight: Size.bubbleArrowSize,
    marginBottom: 4,
  },
  img: {
    width: 1,
    height: 1,
    borderRadius: 5,
  },
});

const maxWidth = deviceWidth * 0.25;
const maxHeight = maxWidth * 2;
const minWidth = deviceWidth * 0.2;
const minHeight = deviceWidth * 0.2;

function getImageWH(size) {
  if (!(size?.width && size.height)) return null;
  let { width, height } = size;
  width = px2dp(width);
  height = px2dp(height);
  const rate = Math.min(maxWidth / width, maxHeight / height);
  width = Math.max(minWidth, width * rate);
  height = Math.max(minHeight, height * rate);
  return { width, height };
}

/**
 * 图片类型消息
 * <AUTHOR>
 */
export default class MessageImage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      source: null,
      imageStyle: null,
    };
  }

  static getDerivedStateFromProps({ currentMessage }, state) {
    const uri = currentMessage.imagePath || currentMessage.content;
    // console.log('MessageImage getDerivedStateFromProps', uri);
    if (uri !== state.uri) {
      const extra = currentMessage.extra && sentryUtil.parse(currentMessage.extra, 'mi gds');
      console.log('MessageImage getDerivedStateFromProps', uri, extra);
      return {
        uri,
        source: { uri },
        imageStyle: getImageWH(extra || {}),
      };
    }
    return null;
  }

  onLoad = (res) => {
    console.log('MessageImage onLoad', !!this.state.imageStyle, res.nativeEvent.source);
    if (this.state.imageStyle) return;
    fileUtil.getImageSize(res.nativeEvent.source.uri, true).then((size) => {
      this.setState({ imageStyle: getImageWH(size) });
    });
  };

  onLongPress = () => {
    const { onLongPress, currentMessage } = this.props;
    onLongPress?.(this.context, currentMessage);
  };

  onViewImage = async () => {
    const { currentMessage, session } = this.props;
    const param = {
      sessionId: session.sessionId,
    };
    const tasks = [messageDao.queryMediasMessageList(param)];
    const [res] = await promiseUtil.batchRequestMin(tasks, 0);
    const images = [];
    let index = -1;
    let hasCurrent = false;
    for (let i = res.length - 1; i >= 0; i--) {
      if (res[i].type === constant.messageType.image) {
        images.push({
          url: res[i].imagePath || res[i].content,
        });
        if (hasCurrent) continue;
        hasCurrent = currentMessage.seq === res[i].seq;
        index++;
      }
    }
    global.emitter.emit(constant.event.viewChatImages, {
      images,
      index,
    });
  };

  // onViewImage = () => {
  //   const { messagesList, currentMessage } = this.props;
  //   const images = [];
  //   let index = -1;
  //   let hasCurrent = false;
  //   for (let i = messagesList.length - 1; i >= 0; i--) {
  //     if (messagesList[i].type === constant.messageType.image) {
  //       images.push({
  //         url: messagesList[i].imagePath || messagesList[i].content,
  //       });
  //       if (hasCurrent) continue;
  //       hasCurrent = currentMessage.seq === messagesList[i].seq;
  //       index++;
  //     }
  //   }
  //   navigationService.navigate('viewImages', {
  //     images,
  //     index,
  //   });
  // };

  render() {
    const { source, imageStyle } = this.state;
    const { position, currentMessage } = this.props;
    console.log('MessageImage render', imageStyle, source);
    return (
      <Touchable
        onPress={this.onViewImage}
        onLongPress={this.onLongPress}
        style={[
          styles[`${position}Touchable`],
          currentMessage?.isGroup && position === 'left'
            ? {
                marginTop: 4,
                marginRight: Size.bubbleArrowSize,
              }
            : {},
        ]}
      >
        <Image
          source={source}
          style={[styles.img, imageStyle]}
          onLoad={this.onLoad}
          resizeMode="cover"
        />
        <MessageTime {...this.props} isFull />
      </Touchable>
    );
  }
}
