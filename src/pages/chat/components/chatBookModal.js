import React from 'react';
import Modal from 'react-native-modalbox';
import { AlertPro, BaseComponent, Text, Touchable, View } from '../../../components';
import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';
import SearchBox from '../../../components/input/searchBox';
import BookList from './bookList';
import CheckedItem from './checkedItem';
import Debounce from 'debounce-decorator';
import SafeView from '../../../components/safeView';
import styles from '../../../themes';

function getComponentStyle(theme) {
  return {
    modal: {
      height: '94%',
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      // flex: 1,
    },
    container: {
      flex: 1,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      overflow: 'hidden',
      backgroundColor: '#ffffff',
    },
    headerContainer: {
      // height: headerHeight,
      height: 50,
      paddingHorizontal: 18,
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    headerTitleContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: deviceWidth * 0.2,
      width: deviceWidth * 0.6,
      alignItems: 'center',
      justifyContent: 'center',
    },
    bookContain: {
      flex: 1,
    },
    createHead: {
      paddingHorizontal: 15,
      backgroundColor: '#FFFFFF',
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
    addUser: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#333333',
      marginBottom: 13,
    },
    closeBox: {
      position: 'absolute',
      right: 0,
      top: 30,
      display: 'flex',
    },
    closeText: {
      fontSize: 14,
      color: '#333333',
      textAlign: 'right',
      paddingRight: 14,
      minWidth: 60,
    },
  };
}

/**
 * 选择通讯录好友Modal
 * <AUTHOR>
 */
export default class ChatBookModal extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      searchValue: '',
      checkedList: [],
      disabledList: [],
      disabledBtn: false,
      showAlert: false,
    };
  }

  open = ({ disabledList }) => {
    this.setState({ disabledList, isOpen: true });
  };

  close = () => {
    this.setState({ isOpen: false, checkedList: [] });
  };

  // 输入搜索内容
  onChangeText = (text) => {
    this.setState({ searchValue: text }, () => {
      if (!text) {
        this.onSearchDebounce(text);
      }
    });
  };

  onSubmitEditing = () => {
    const { searchValue } = this.state;
    this.onSearchDebounce(searchValue);
  };

  @Debounce(300)
  onSearchDebounce(text) {
    this.bookList?.search(text);
  }

  onBookListRef = (ref) => (this.bookList = ref?.wrappedInstance);

  selectItem = (data) => {
    this.setState({ checkedList: data, disabledBtn: false });
  };

  onNext = () => {
    console.log('onNext', this.props.fromSingle);
    if (this.props.fromSingle) {
      this.setState({ showAlert: true });
      return;
    }
    this.handleAddUser();
  };

  onAlertConfirm = () => {
    this.setState({ showAlert: false });
    this.handleAddUser();
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  handleAddUser = () => {
    this.props.addCheck(this.state.checkedList);
    this.close();
  };

  renderHeader = () => {
    const { style } = this;
    const { searchValue } = this.state;
    return (
      <View style={style.createHead}>
        <Text style={style.addUser}>{I18n.t('page_call_text_less_add_user')}</Text>
        <SearchBox
          onChangeText={this.onChangeText}
          onSubmitEditing={this.onSubmitEditing}
          value={searchValue}
          closeIconStyle={{ marginRight: 5 }}
          rightIcon={() => (
            <Touchable onPress={this.onSubmitEditing} style={style.searchTextBox}>
              <Text style={style.searchBtn}>{I18n.t('op_search_title')}</Text>
            </Touchable>
          )}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    const { isOpen, checkedList, disabledList, disabledBtn, showAlert } = this.state;
    const { coverScreen = true } = this.props;
    const headerStyle = styles.get('header');
    return (
      <Modal
        isOpen={isOpen}
        backdropPressToClose={true}
        onClosed={this.close}
        swipeToClose={false}
        position="bottom"
        entry="center"
        coverScreen={coverScreen}
        style={style.modal}
      >
        <View style={style.container}>
          <View style={style.headerContainer}>
            <Touchable onPress={this.close} style={style.closeBox}>
              <Text style={style.closeText}>{I18n.t('checkNotifyModal_btn_close')}</Text>
            </Touchable>
            {/* <GoBack callback={this.close} />
            <View style={style.headerTitleContainer}>
              <Text style={headerStyle.center} numberOfLines={1}>
                {I18n.t('page_chat_friend')}
              </Text>
            </View> */}
          </View>

          <View style={style.bookContain}>
            {this.renderHeader()}
            <BookList
              ref={this.onBookListRef}
              onPick={this.selectItem}
              isCheck={true}
              isSingle={false}
              checkedList={checkedList}
              disabledList={disabledList}
              excludeSelf
            />
          </View>
          <CheckedItem chooses={checkedList} onNext={this.onNext} disabledBtn={disabledBtn} />
          <SafeView bottomHeight={0} />
        </View>
        <AlertPro
          visible={showAlert}
          title={I18n.t('page_call_text_add_user')}
          message={I18n.t('page_call_text_add_user_tips')}
          messageStyle={{ fontSize: 14 }}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onConfirm={this.onAlertConfirm}
          onCancel={this.onAlertCancel}
        />
      </Modal>
    );
  }
}
