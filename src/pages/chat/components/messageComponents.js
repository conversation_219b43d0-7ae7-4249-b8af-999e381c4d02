import React from 'react';
import { Button, Image, StyleSheet, Text, View } from '../../../components';
import { WIDTH } from '../../../common';
import LinearGradient from 'react-native-linear-gradient';
import { CommonStyles } from './messageConfig';

const styles = StyleSheet.create({
  container: {
    paddingTop: 14,
    paddingBottom: 12,
    paddingHorizontal: 12,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 15,
    width: WIDTH(230),
    flexDirection: 'column',
    marginBottom: 4,
  },
  rightContainer: {
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 0,
  },
  topContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 14,
  },
  topText: {
    fontSize: 15,
    color: '#333333',
    marginLeft: 5,
    flexShrink: 1000,
  },
});

/**
 * 背景色渐变气泡
 */
export function MessageLinearGradient({ children, position, color = '#D0E5FF', useGradient }) {
  if (useGradient) {
    return (
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 0.3 }}
        colors={[color, '#FFFFFF']}
        style={[styles.container, styles[`${position}Container`], CommonStyles.shadow]}
      >
        {children}
      </LinearGradient>
    );
  }
  return (
    <View style={[styles.container, styles[`${position}Container`], CommonStyles.shadow]}>
      {children}
    </View>
  );
}

/**
 * 按钮卡片消息
 */
export function MessageComponents({
  position,
  color,
  img,
  text,
  onPress,
  btnTitle,
  btnType = 'chatSendResume',
  btnDisabled = false,
}) {
  return (
    <MessageLinearGradient position={position} color={color}>
      <View style={styles.topContainer}>
        <Image source={img} />
        <Text style={styles.topText}>{text}</Text>
      </View>
      <Button
        title={btnTitle}
        onPress={onPress}
        btnType={btnType}
        btnSize="s36"
        disabled={btnDisabled}
      />
    </MessageLinearGradient>
  );
}
