import React from 'react';
import { Clipboard, StyleSheet, Touchable, View } from '../../../components';
// import resIcon from '../../../res';
import { isSameUser } from 'react-native-gifted-chat/lib/utils';
import MessageTime from './messageTime';
import { Color, isHideMessageDate, CommonStyles } from './messageConfig';

const borderRadius = 12;
const paddingTop = 5;
const paddingBottom = 10;
const paddingHorizontal = 10;

const styles = {
  left: StyleSheet.create({
    touchable: {
      // flex: 1,
      // alignItems: 'flex-start',
      flex: 1,
      flexShrink: 1000,
    },
    container: {
      // flexDirection: 'row',
      marginBottom: 4,
    },
    wrapper: {
      borderTopRightRadius: borderRadius,
      borderTopLeftRadius: borderRadius,
      borderBottomRightRadius: borderRadius,
      backgroundColor: Color.leftBubbleBackground,
      // marginRight: 60,
      paddingHorizontal,
      // paddingVertical,
      paddingTop,
      paddingBottom,
      flexShrink: 1000,
      paddingLeft: 10,
    },
    containerToNext: {
      borderBottomLeftRadius: 3,
    },
    wrapperToPrevious: {
      borderTopLeftRadius: borderRadius,
    },
  }),
  right: StyleSheet.create({
    touchable: {
      // flex: 1,
      // alignItems: 'flex-end',
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      flexShrink: 1000,
      marginBottom: 4,
    },
    wrapper: {
      borderTopLeftRadius: borderRadius,
      borderTopRightRadius: borderRadius,
      borderBottomLeftRadius: borderRadius,
      backgroundColor: Color.rightBubbleBackground,
      marginLeft: 0,
      paddingHorizontal,
      // paddingVertical,
      paddingTop,
      paddingBottom,
      flexShrink: 1000,
    },
    containerToNext: {
      borderBottomRightRadius: 3,
    },
    wrapperToPrevious: {
      borderTopRightRadius: borderRadius,
    },
  }),
  bubbleArrow: {
    width: 7,
    height: 7,
  },
  timeContainer: {
    position: 'absolute',
    right: 10,
    bottom: 5,
  },
  timeText: {
    fontSize: 12,
    color: Color.timeColor,
  },
  noBubble: {
    backgroundColor: 'transparent',
  },
};

/*function BubbleArrow({
  targetPosition,
  position,
  noBubble,
  hasPrevious,
  arrowImg,
  bubbleArrowStyle,
}) {
  if (targetPosition !== position) return null;
  if (position === targetPosition && !noBubble && !hasPrevious) {
    return (
      <Image
        source={arrowImg}
        style={[styles.bubbleArrow, bubbleArrowStyle, !noBubble && CommonStyles.shadow]}
      />
    );
  }
  return <View style={styles.bubbleArrow} />;
}*/

export default class MessageBubbleContainer extends React.Component {
  onLongPress = () => {
    const { currentMessage } = this.props;
    if (this.props.onLongPress) {
      this.props.onLongPress(this.context, this.props.currentMessage);
    } else if (currentMessage && currentMessage.text) {
      const { optionTitles } = this.props;
      const options =
        optionTitles && optionTitles.length > 0
          ? optionTitles.slice(0, 2)
          : ['Copy Text', 'Cancel'];
      const cancelButtonIndex = options.length - 1;
      this.context.actionSheet().showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex,
        },
        (buttonIndex) => {
          switch (buttonIndex) {
            case 0:
              Clipboard.setString(currentMessage.text);
              break;
            default:
              break;
          }
        }
      );
    }
  };

  render() {
    const {
      position,
      children,
      currentMessage,
      previousMessage,
      noBubble,
      wrapperStyle,
      hideTime,
      // bubbleArrowLeft = resIcon.chatBubbleLeft,
      // bubbleArrowRight = resIcon.chatBubbleRight,
      // bubbleArrowStyle,
      onPress,
      touchableProps = {},
    } = this.props;

    const hasPrevious =
      currentMessage &&
      previousMessage &&
      position &&
      isSameUser(currentMessage, previousMessage) &&
      isHideMessageDate(this.props);
    return (
      <Touchable
        withoutFeedback
        onPress={onPress}
        onLongPress={this.onLongPress}
        // onPress={() => this.onPress(props)}
        style={styles[position].touchable}
        accessibilityTraits="text"
        {...touchableProps}
      >
        <View style={styles[position].container}>
          {/* <BubbleArrow
            targetPosition="left"
            position={position}
            noBubble={noBubble}
            hasPrevious={hasPrevious}
            arrowImg={bubbleArrowLeft}
            bubbleArrowStyle={bubbleArrowStyle}
          /> */}
          <View
            style={[
              styles[position].wrapper,
              hasPrevious && styles[position].wrapperToPrevious,
              (position === 'right' || !currentMessage?.isGroup) &&
                !noBubble &&
                CommonStyles.shadow,
              noBubble && styles.noBubble,
              !currentMessage?.isGroup && position === 'left' && { marginRight: 60 },
              wrapperStyle,
            ]}
          >
            {children}
            {hideTime ? null : <MessageTime {...this.props} />}
          </View>
          {/* <BubbleArrow
            targetPosition="right"
            position={position}
            noBubble={noBubble}
            hasPrevious={hasPrevious}
            arrowImg={bubbleArrowRight}
            bubbleArrowStyle={bubbleArrowStyle}
          /> */}
        </View>
      </Touchable>
    );
  }
}
