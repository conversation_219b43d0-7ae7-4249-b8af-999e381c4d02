import { StyleSheet } from '../../../components';
import { WIDTH } from '../../../common';

export const Color = {
  leftBubbleBackground: '#ffffff', // 消息气泡
  rightBubbleBackground: '#D0E5FF', // 消息气泡
  leftTextColor: '#333333', // 文本消息
  rightTextColor: '#333333', // 文本消息
  leftLinkColor: '#5D6CC1', // 识别为链接时
  rightLinkColor: '#333333d0', // 识别为链接时
  usernameColor: '#666666', // 识别为链接时
  dateBackground: 'rgba(255,255,255,0.57)', // 日期背景
  dateColor: '#A6A5A5', // 日期
  timeColor: '#666666', // 每条消息的时间
};

export const Size = {
  bubbleArrowSize: 7, // 消息气泡那个箭头大小
  bubblePaymentWidth: 240, // 红包、转账、收款消息气泡宽
  textFontSize: WIDTH(15),
};

export const CommonStyles = StyleSheet.create({
  shadow: {
    shadowColor: '#00000050',
    shadowOffset: { h: 10 },
    shadowOpacity: 0.2,
    elevation: 5,
  },
});

const spaceTime = 60 * 60 * 1000; // 消息间隔一小时显示时间

/**
 * 消息间隔大于1小时时，显示日期
 */
export function isHideMessageDate({ previousMessage, currentMessage }) {
  return previousMessage?.msgTime && currentMessage.msgTime - previousMessage.msgTime < spaceTime;
}
