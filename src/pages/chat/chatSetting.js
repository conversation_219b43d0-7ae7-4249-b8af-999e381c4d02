import React, { Component } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, View } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Header from '../../components/header/header';
import I18n from '../../i18n';
import SettingItem from '../../components/listItem/settingItem';
import messageDao from '../../database/dao/messageDao';
import chatSessionDao from '../../database/dao/chatSessionDao';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
  };
}

/**
 * 聊天设置
 * <AUTHOR>
 */
@inject('imAction')
@observer
export default class ChatSetting extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
    this.state = {
      showCleanChatAlert: false,
    };
  }

  onAlertConfirm = async () => {
    try {
      this.onAlertCancel();
      await Promise.all([messageDao.clearAll(), chatSessionDao.deleteAllSession()]);
      toast.show(I18n.t('page_settings_chat_clean_success'));
    } catch (e) {
      logger.warn('ChatSetting 清空聊天记录失败', e);
      toast.show(e?.message || I18n.t('page_settings_chat_clean_fail'));
    }
  };

  onAlertCancel = () => this.setState({ showCleanChatAlert: false });

  onCleanChat = () => this.setState({ showCleanChatAlert: true });

  render() {
    const { style } = this;
    const { showCleanChatAlert } = this.state;
    return (
      <View style={style.container}>
        <Header title={I18n.t('menu_nav_bottom_chat')} />
        <ScrollView>
          <SettingItem
            onPress={this.onCleanChat}
            label={I18n.t('page_settings_chat_clean')}
            maxSpacing
          />
        </ScrollView>
        <AlertPro
          visible={showCleanChatAlert}
          message={I18n.t('page_settings_chat_clean_tips')}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onConfirm={this.onAlertConfirm}
          onCancel={this.onAlertCancel}
        />
      </View>
    );
  }
}
