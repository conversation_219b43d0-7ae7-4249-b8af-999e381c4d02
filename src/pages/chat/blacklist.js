import React, { Component } from 'react';
import { Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Header from '../../components/header/header';
import PageFlatList from '../../components/list/pageFlatList';
import Avatar from '../../components/avatar/avatar';
import navigationService from '../../navigationService';
import constant from '../../store/constant';
import I18n from '../../i18n';

function getComponentStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.primaryBgColor,
      marginTop: 1,
      paddingHorizontal: theme.containerPaddingHorizontal,
      paddingVertical: 15,
    },
    itemAvatar: {
      width: 40,
      height: 40,
      borderRadius: 40,
      marginRight: 14,
    },
    itemTitle: {
      color: '#333333',
      fontSize: 16,
      lineHeight: 22,
      flexShrink: 1000,
    },
  };
}

/**
 * 黑名单列表
 * <AUTHOR>
 */
@inject('imAction')
@observer
export default class Blacklist extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle();
  }

  componentDidMount() {
    global.emitter.on(constant.event.blacklistChange, this.onBlacklistChange);
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.onWillFocus);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.blacklistChange, this.onBlacklistChange);
    this.willFocusSubscription.remove();
  }

  refPageFlatList = (ref) => (this.pageFlatList = ref);

  onBlacklistChange = () => {
    this.blacklistChange = true;
  };

  onWillFocus = () => {
    if (this.blacklistChange) {
      this.pageFlatList?.onRefresh();
    }
  };

  loadData = async () => {
    const result = await this.props.imAction.getBlacklist();
    return {
      totalCount: result.length,
      result,
    };
  };

  onUserDetail = (item) => {
    navigationService.navigate('chatAddContactDetail', { imId: item.im_id });
  };

  renderItem = ({ item }) => {
    const { style } = this;
    const name = item.friend_nickname || item.nickname;
    return (
      <Touchable onPress={() => this.onUserDetail(item)}>
        <View style={style.itemContainer}>
          <Avatar avatar={item.avatar || ''} name={name} style={style.itemAvatar} />
          <Text style={style.itemTitle} numberOfLines={1}>
            {name}
          </Text>
        </View>
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header title={I18n.t('page_settings_privacy_black')} />
        <PageFlatList
          ref={this.refPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
          itemId="im_id"
          ListFooterComponent={null}
          // ItemSeparatorComponent={() => <View style={{ height: 1, marginVertical: 5 }} />}
        />
      </View>
    );
  }
}
