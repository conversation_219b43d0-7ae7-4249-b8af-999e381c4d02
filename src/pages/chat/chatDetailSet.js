import React from 'react';
import { AlertPro, BaseComponent, ScrollView, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import Header from '../../components/header/header';
import NavigationService from '../../navigationService';
import SettingItem from '../../components/listItem/settingItem';
import Avatar from '../../components/avatar/avatar';
import chatSessionDao from '../../database/dao/chatSessionDao';
import messageDao from '../../database/dao/messageDao';
import constant from '../../store/constant';
import I18n from '../../i18n';
import { onReportUser } from '../../util/reportUtil';
import chatAction from '../../store/actions/chatAction';

function getComponentStyle(theme) {
  return {
    container: {
      backgroundColor: theme.minorBgColor,
    },
    settingContainer: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },

    memberListContainer: {
      backgroundColor: '#fff',
      paddingHorizontal: 18,
      paddingBottom: 15,
      paddingTop: 10,
    },
    userInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.primaryBgColor,
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 20,
      marginRight: 10,
    },
    nickNameStyle: {
      fontSize: theme.fontSizeXL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
    },
    descStyle: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
      marginTop: 3,
    },
  };
}

@inject('chatAction', 'userStore', 'stores', 'imAction')
@observer
export default class ChatDetailSet extends BaseComponent {
  constructor(props) {
    super(props);
    this.session = this.props.navigation.getParam('session');
    const friend = this.props.navigation.getParam('friend');
    this.state = {
      isDisturb: !!this.session.isDisturb,
      sticky: this.session.setTopTime > 0,
      userData: this.session,
      showAlert: false,
      isScrollingUp: false,
      isBlack: friend?.isBlacked === 1 || friend?.isBlacked === 3 || false,
    };
  }

  style = getComponentStyle(this.props.stores.userStore.themeStyle);

  get isSelf() {
    return this.session.sessionId === this.props.stores.userStore.imId;
  }

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    if (this.session.isGPT) return;
    const { userData: session } = this.state;
    console.log('session', session);
    const { friend } = await chatAction.getFriend(session?.sessionId, false, true);
    console.log('friend', friend);
    // const params = {
    //   key: 'isDisturb',
    // };
    // const friendSet = await this.props.imAction.getContact(session?.sessionId, params);
    // console.log('friendSet', friendSet);
    // if (friendSet?.contact?.is_disturb != !!session.isDisturb) {
    //   session.isDisturb = !friendSet?.contact?.is_disturb ? 1 : 0;
    //   await chatSessionDao.updateSet(session);
    // }
    this.setState({
      userData: session,
      isDisturb: !!session.isDisturb,
      // isDisturb: friendSet?.contact?.is_disturb || !!session.isDisturb,
      sticky: session.setTopTime > 0,
      isBlack: friend.isBlacked === 1 || friend.isBlacked === 3,
    });
  };

  onChatDetail = () => {
    const { userData } = this.state;
    NavigationService.navigate('chatAddContactDetail', {
      imId: userData.sessionId,
      deleteRouteName: 'main', // 删除好友后的路由页面
      callback: (content) => {
        userData.friendMemo = content;
        this.setState({ userData });
      },
    });
  };

  onSticky = async () => {
    const { userData, sticky } = this.state;
    userData.setTopTime = sticky ? 0 : new Date().getTime();
    await chatSessionDao.updateSet(userData);
    this.setState({ sticky: !sticky });
  };

  onShowAvatar = async () => {
    toast.show(I18n.t('msg_in_development'));
  };

  onReadDel = async () => {
    toast.show(I18n.t('msg_in_development'));
  };

  onMessageFree = async () => {
    const { userData } = this.state;
    // const params = {
    //   key: 'isDisturb',
    //   value: !this.state.isDisturb,
    // };
    // await this.props.imAction.changeContact(userData?.sessionId, params);
    userData.isDisturb = !this.state.isDisturb ? 1 : 0;
    await chatSessionDao.updateSet(userData);
    this.setState({ isDisturb: !this.state.isDisturb });
  };

  onSearchChat = () => {
    NavigationService.navigate('searchChatMsg', {
      session: this.state.userData,
    });
  };

  // 清空聊天记录
  onClear = () => {
    this.setState({ showAlert: true });
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  onClearConfirm = async () => {
    const { userData } = this.state;
    try {
      await messageDao.clearMessageList({
        ownerId: userData.ownerId,
        sessionId: userData.sessionId,
      });
      userData.content = '';
      await chatSessionDao.falseDeleteSessionExtra({
        ownerId: userData.ownerId,
        sessionId: userData.sessionId,
        isGroup: false,
        content: '',
      });
      // const { refresh } = this.props.navigation.state.params;
      // refresh();
      global.emitter.emit(constant.event.clearAllMessage);
      this.setState({ showAlert: false });
      NavigationService.goBack();
    } catch (error) {
      toast.show('error: ' + error);
      logger.error('删除聊天记录出错', error);
    }
  };

  onAddMember = () => {
    return toast.show(I18n.t('msg_in_development'));
    /*NavigationService.push('chatBook', {
      createGroup: true,
      disabledList: this.props.groupMembers,
      addCheck: this.onAddMemberCallback,
    });*/
  };

  /*onAddMemberCallback = async checkedList => {
    try {
      console.log('GroupMemberManage onAddMemberCallback', checkedList);
      this.showGlobalLoading();
      const imIds = checkedList.map(item => item.imId);
      const {
        groupMembers,
        setGroupMembers,
        groupInfo: { groupId },
      } = this.props;
      await this.props.imAction.addGroupMembers({ groupId, imIds });
      const joinAt = new Date().getTime();
      const newMembers = checkedList.map(item => {
        const groupMember = new GroupMember();
        groupMember.imId = item.imId;
        return {
          ...item,
          isDelete: 0,
          role: constant.groupMemberRole.member,
          groupId,
          joinAt,
        };
      });
      setGroupMembers(groupMembers.concat(newMembers));
      const targetNames = checkedList.map(item => item.memo || item.nickname).join('、');
      await sendMessageUtil.sendGroupInvite({
        groupId,
        imIds,
        targetNames,
      });
      this.showRequestResult();
    } catch (e) {
      logger.warn('GroupMemberManage onAddMemberCallback', e);
      this.showRequestResult(e);
    }
  };*/

  onComplain = async () => {
    await onReportUser(this.state.userData?.sessionId);
  };

  onSetBlack = async () => {
    try {
      this.showGlobalLoading();
      let { isBlack } = this.state;
      isBlack = !isBlack;
      await this.props.imAction.setBlacklist(this.session.sessionId, isBlack);
      // userData = await this.getUserData(userData.im_id);
      // const contact = await contactDao.findContact({ imId: userData.im_id });
      // if (contact) {
      //   contact.isBlacked = userData.blacklist_type;
      //   await contactDao.updateContact(contact);
      // }
      this.setState({ isBlack });
      this.showRequestResult();

      global.emitter.emit(constant.event.blacklistChange, { imId: this.session.sessionId });
    } catch (e) {
      console.log('chatAddContactDetail onSetBlack', e);
      this.showRequestResult(e);
    }
  };

  handleScroll = (event) => {
    const { contentOffset } = event.nativeEvent;
    this.setState({ isScrollingUp: contentOffset.y > 0 });
  };

  renderListItem = () => {
    const { style } = this;
    const { sticky, isDisturb, isBlack } = this.state;
    return (
      <View style={style.container}>
        {this.session.isGPT ? null : (
          <SettingItem
            label={I18n.t('page_chat_set_cell_notice')}
            onSwitch={this.onMessageFree}
            switched={isDisturb}
            maxSpacing
          />
        )}
        <SettingItem
          label={I18n.t('page_chat_set_cell_top')}
          onSwitch={this.onSticky}
          switched={sticky}
          maxSpacing={!!this.session.isGPT}
        />
        {/* <SettingItem
          label={I18n.t('page_chat_text_show_avatar')}
          onSwitch={this.onShowAvatar}
          switched={sticky}
        />
        <SettingItem onPress={this.onReadDel} label={I18n.t('page_chat_text_burn_after_reading')} /> */}
        {this.session.isGPT ? null : (
          <SettingItem
            label={I18n.t('page_user_cell_black')}
            onSwitch={this.onSetBlack}
            switched={isBlack}
            maxSpacing
          />
        )}
        <SettingItem
          onPress={this.onClear}
          label={I18n.t('page_settings_chat_clean')}
          hideRightArrow
          maxSpacing={!!this.session.isGPT}
        />
        {/*<SettingItem onPress={this.onSearchChat} label={I18n.t('page_chat_set_cell_history')} />*/}
        <SettingItem onPress={this.onComplain} label={I18n.t('page_chat_title_complaint')} />
      </View>
    );
  };

  getDesc = (userData) => {
    if (!userData) return null;
    if (this.props.stores.userStore.isCompany) {
      return userData.jobTitle;
    }
    if (userData.positionName) {
      return `${userData.company || ''}-${userData.positionName}`;
    }
    return userData.company;
  };

  render() {
    const { style } = this;
    const { userData, showAlert, isScrollingUp } = this.state;
    const name = userData && userData.title;
    const desc = this.getDesc(userData);
    return (
      <View style={style.settingContainer}>
        <Header title={I18n.t('page_chat_set_title')} isScrollingUp={isScrollingUp} />
        <ScrollView style={{ flex: 1 }} onScroll={this.handleScroll}>
          <View style={style.memberListContainer}>
            <Touchable style={style.userInfo} onPress={this.onChatDetail} disabled>
              <Avatar
                avatar={userData?.avatar || ''}
                name={name}
                style={style.avatar}
                showDot={false}
              />
              <View style={{ flex: 1 }}>
                <Text style={style.nickNameStyle} ellipsizeMode="tail">
                  {name}
                </Text>
                {desc ? (
                  <Text style={style.descStyle} ellipsizeMode="tail">
                    {desc}
                  </Text>
                ) : null}
              </View>
            </Touchable>
            {/*{!this.isSelf ? (
              <Touchable style={style.userInfo} onPress={this.onAddMember}>
                <Image source={resIcon.addGroupMember} style={style.avatar} />
              </Touchable>
            ) : null}*/}
          </View>

          {this.renderListItem()}
          <View style={{ height: 30 }} />
        </ScrollView>
        <AlertPro
          visible={showAlert}
          message={I18n.t('page_chat_set_clean_content', { name })}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onConfirm={this.onClearConfirm}
          onCancel={this.onAlertCancel}
        />
      </View>
    );
  }
}
