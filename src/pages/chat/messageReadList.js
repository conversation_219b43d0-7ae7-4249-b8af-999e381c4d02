import React, { Component } from 'react';
import { Icon, Image, ScrollView, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Header from '../../components/header/header';
import resIcon from '../../res';
import SafeView from '../../components/safeView';
import I18n from '../../i18n';
import Avatar from '../../components/avatar/avatar';
import sentryUtil from '../../util/sentryUtil';
import util from '../../util';
import ChatMessageReadExtra from '../../database/chatMessageReadExtra';
import chatAction from '../../store/actions/chatAction';

function getComponentStyle(theme) {
  return {
    pageContainer: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    listContainer: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 15,
      marginTop: 10,
      paddingHorizontal: 12,
      paddingTop: 15,
    },
    listHeaderContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    listHeaderLabel: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#FF8646',
    },
    listHeaderLabel2: {
      color: '#333333',
    },
    listItemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 10,
    },
    listItemRightContainer: {
      flex: 1,
      marginLeft: 10,
      justifyContent: 'center',
    },
    listItemNameContainer: {
      flexDirection: 'row',
    },
    listItemName: {
      fontSize: 16,
      color: '#333333',
      flex: 1,
    },
    listItemTime: {
      fontSize: 12,
      color: '#9BA7B2',
      marginTop: 3,
    },
    listItemAccount: {
      fontSize: 12,
      color: '#9BA7B2',
      marginLeft: 10,
    },
    showAllContainer: {
      paddingTop: 5,
      paddingBottom: 10,
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },
    showAllText: {
      color: '#5D6CC1',
      fontSize: 12,
      paddingRight: 3,
    },
  };
}

/**
 * 消息已读列表
 */
@inject('stores')
@observer
export default class MessageReadList extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      data: {
        read: {
          list: [],
          remind: 0,
        },
        arrived: {
          list: [],
          remind: 0,
        },
        undelivered: {
          list: [],
          remind: 0,
        },
      },
      showAllSet: new Set(),
    };
  }

  componentDidMount() {
    this.onLoad();
  }

  onLoad = async () => {
    const { message, memberMap } = this.props.navigation.state.params || {};
    if (!message) return;
    this.isGroup = message.isGroup;
    const readExtra =
      (message.readExtra && sentryUtil.parse(message.readExtra, 'mrl ol')) ||
      new ChatMessageReadExtra();
    let readMap = readExtra.readMap || {};
    if (this.isGroup) {
      if (!readExtra.receiverIds?.length) {
        readExtra.receiverIds = Object.keys(readMap).map((imId) => Number(imId));
      }
    } else {
      if (!readExtra.receiverIds?.length) {
        readExtra.receiverIds = [message.sessionId];
      }
      // 兼容旧版，新版已没有readExtra.readObj
      if (readExtra.readObj && (readExtra.readObj.arrivedTime || readExtra.readObj.readTime)) {
        readMap = { [readExtra.receiverIds[0]]: readExtra.readObj };
      }
    }

    console.log('MessageReadList onLoad message', message);
    const { data } = this.state;
    let user;
    let total = 0;
    for (let imId of readExtra.receiverIds) {
      if (!imId || imId === message.ownerId) continue;
      user = memberMap.get(imId);
      if (!user) {
        user = await chatAction.getFriend(imId, true, true).then(
          (r) => r.friend,
          () => null
        );
        if (!user) continue;
      }
      total++;
      if (readMap[imId]?.readTime) {
        data.read.list.push({
          user,
          displayTime: util.getAgoTime(readMap[imId]?.readTime),
        });
      } else if (readMap[imId]?.arrivedTime) {
        data.arrived.list.push({
          user,
          displayTime: util.getAgoTime(readMap[imId]?.arrivedTime),
        });
      } else {
        data.undelivered.list.push({ user });
      }
    }
    data.read.remind = total - data.read.list.length;
    data.arrived.remind = data.read.remind - data.arrived.list.length;
    data.undelivered.remind = data.arrived.remind - data.undelivered.list.length;
    this.setState({ data: { ...data } });
  };

  renderPanel = (data, labelKey, img, labelStyle) => {
    if (!data?.list?.length) return null;
    let { list, remind } = data;
    const { style } = this;
    const { showAllSet } = this.state;
    const num = list.length;
    const showAllBtn = num > 5 && !showAllSet.has(labelKey);
    if (showAllBtn) {
      list = list.slice(0, 5);
    }
    return (
      <View style={style.listContainer}>
        <View style={style.listHeaderContainer}>
          <Text style={[style.listHeaderLabel, labelStyle]}>
            {I18n.t(labelKey)}
            {remind ? I18n.t('page_MessageReadList_remind', { remind }) : null}
          </Text>
          <Image source={img} />
        </View>
        {list.map((item) => (
          <View style={style.listItemContainer} key={item.imId}>
            <Avatar
              avatar={item.user.avatar}
              name={item.user.memo || item.user.nickname}
              size={40}
            />
            <View style={style.listItemRightContainer}>
              <View style={style.listItemNameContainer}>
                <Text style={style.listItemName} numberOfLines={1}>
                  {item.user.memo || item.user.nickname}
                </Text>
                {/*{showAccount ? <Text style={style.listItemAccount}>{'a12456'}</Text> : null}*/}
              </View>
              {item.displayTime ? <Text style={style.listItemTime}>{item.displayTime}</Text> : null}
            </View>
          </View>
        ))}
        {showAllBtn ? (
          <Touchable
            onPress={() => {
              showAllSet.add(labelKey);
              this.setState({ showAllSet });
            }}
          >
            <View style={style.showAllContainer}>
              <Text style={style.showAllText}>{I18n.t('page_MessageReadList_show_all')}</Text>
              <Icon type="entypo" name="chevron-down" color="#8E96A3" size={16} />
            </View>
          </Touchable>
        ) : (
          <View style={style.showAllContainer} />
        )}
      </View>
    );
  };

  render() {
    const { style } = this;
    const { data } = this.state;
    return (
      <View style={style.pageContainer}>
        <Header title={I18n.t('page_MessageReadList_title')} />
        <ScrollView>
          {this.renderPanel(data.read, 'page_MessageReadList_read', resIcon.chatMsgRead2)}
          {this.renderPanel(
            data.arrived,
            'page_MessageReadList_arrived',
            resIcon.chatMsgRead1,
            style.listHeaderLabel2
          )}
          {this.renderPanel(
            data.undelivered,
            'page_MessageReadList_undelivered',
            resIcon.chatMsgRead1,
            style.listHeaderLabel2
          )}
          <SafeView bottomHeight={20} />
        </ScrollView>
      </View>
    );
  }
}
