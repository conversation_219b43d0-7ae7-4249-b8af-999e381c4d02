import React from 'react';
import { StatusBar, Text, Touchable, View, BaseComponent } from '../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../themes';
import Avatar from '../../components/avatar/avatar';
import SearchBox from '../../components/input/searchBox';
import NavigationService from '../../navigationService';
import messageDao from '../../database/dao/messageDao';
import chatMessageUtil from '../../database/chatMessageUtil';
import Debounce from 'debounce-decorator';
import util from '../../util';
import I18n from '../../i18n';
import SafeView from '../../components/safeView';
import PageFlatList from '../../components/list/pageFlatList';

function getComponentStyle(theme) {
  return {
    chatBookContainer: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    bookContain: {
      flex: 1,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    cancelTxt: {
      marginLeft: 10,
    },
    containerStyle: {
      flex: 1,
      backgroundColor: '#FFFFFF',
      borderRadius: 25,
      paddingHorizontal: 0,
      borderWidth: 1,
      borderColor: '#EFF1F3',
      borderStyle: 'solid',
    },
    itemContainer: {
      height: 65,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      paddingHorizontal: 18,
    },
    itemAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 14,
    },
    itemInfo: {
      flex: 1,
    },
    itemNameInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemNameText: {
      fontSize: theme.memoSize,
      fontWeight: theme.fontWeightRegular,
      color: theme.titleFontColor,
      lineHeight: 21,
      flex: 1,
      paddingRight: 15,
    },
    itemContentText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: theme.tradeUsdColor,
      lineHeight: 17,
    },
    itemDateText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      color: theme.tradeUsdColor,
      lineHeight: 17,
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
  };
}

/**
 * 查找聊天内容
 */
@inject('imAction')
@observer
export default class SearchChatMsg extends BaseComponent {
  constructor(props) {
    super(props);
    this.session = this.props.navigation.getParam('session');
    this.memberMap = new Map();
    this.state = {
      historyMsgList: [],
      searchValue: '',
    };
  }

  style = getComponentStyle(styles.get('theme'));

  componentDidMount() {
    this.props.imAction.getCurrentMessageMemberMap(this.session, true).then((map) => {
      this.memberMap = map;
    });
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  onSubmitEditing = () => this.onSearchDebounce();

  onChangeText = (text) => {
    this.searchValue = text;
    this.setState({ searchValue: text });
    this.onSearchDebounce();
  };

  @Debounce(300)
  onSearchDebounce(showLoading) {
    // if (this.isUnmount) return;
    this.onSearch(showLoading);
  }

  // 搜索
  onSearch = async (showLoading) => {
    const { searchValue } = this;
    if (this.isUnmount || this.resultKey === searchValue) return;
    let historyMsgList = [];
    if (searchValue) {
      const { session } = this;
      if (showLoading) this.showGlobalLoading();
      historyMsgList = await messageDao.queryHistoryMessage({
        ownerId: session.ownerId,
        sessionId: session.sessionId,
        content: searchValue,
      });
    } else {
      showLoading = false;
    }
    if (this.isUnmount || this.searchValue !== searchValue) {
      return;
    }
    this.resultKey = searchValue;
    this.setState({ historyMsgList });
    if (showLoading) this.showRequestResult();
  };

  onCancel = () => {
    NavigationService.goBack();
  };

  onMessage = (item) => {
    NavigationService.push('chatMessage', {
      session: this.session,
      sessionId: this.session.sessionId,
      searchMessage: item,
    });
  };

  renderItem = ({ item }) => {
    const { style } = this;
    const { avatar, name } = chatMessageUtil.getMessageUserInfo(item, this.memberMap);
    return (
      <Touchable onPress={() => this.onMessage(item)}>
        <View style={style.itemContainer}>
          <Avatar avatar={avatar} name={name} style={style.itemAvatar} />
          <View style={style.itemInfo}>
            <View style={style.itemNameInfo}>
              <Text style={style.itemNameText} numberOfLines={1}>
                {name}
              </Text>
              <Text style={style.itemDateText}>{util.dateToLongString(item.msgTime)}</Text>
            </View>
            <Text style={style.itemContentText} numberOfLines={1}>
              {item.content}
            </Text>
          </View>
        </View>
      </Touchable>
    );
  };

  keyExtractor = (item) => item.seq;

  render() {
    const { style } = this;
    const { historyMsgList, searchValue } = this.state;
    return (
      <View style={style.chatBookContainer}>
        <StatusBar backgroundColor="#F5F8FB" />
        <View style={style.searchContainer}>
          <SearchBox
            onChangeText={this.onChangeText}
            onSubmitEditing={this.onSubmitEditing}
            containerStyle={style.containerStyle}
            autoFocus
            value={searchValue}
            closeIconStyle={{ marginRight: 5 }}
            rightIcon={() => (
              <Touchable onPress={this.onSubmitEditing} style={style.searchTextBox}>
                <Text style={style.searchBtn}>{I18n.t('op_search_title')}</Text>
              </Touchable>
            )}
          />
          <Text style={style.cancelTxt} onPress={this.onCancel}>
            {I18n.t('op_cancel_title')}
          </Text>
        </View>
        <PageFlatList
          style={style.bookContain}
          data={historyMsgList}
          keyExtractor={this.keyExtractor}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
        <SafeView bottomHeight={0} />
      </View>
    );
  }
}
