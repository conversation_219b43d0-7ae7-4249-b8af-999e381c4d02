import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Icon, StatusBar, Text, Touchable, View } from '../../components';
import styles from '../../themes';
import Chat from './chat';
import { headerHeight } from '../../common';
import I18n from '../../i18n';
import NavigationService from '../../navigationService';
import constant from '../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    headerContainer: {
      backgroundColor: theme.primaryBgColor,
      height: headerHeight,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    headerLeftContainer: {
      height: '100%',
      maxWidth: '70%',
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 14,
    },
    jobText: {
      color: '#333',
      fontWeight: 'bold',
      fontSize: 16,
      marginRight: 3,
    },
    headerNotifyContainer: { height: '100%', justifyContent: 'center', paddingHorizontal: 14 },
  };
}

/**
 * 企业聊天会话列表页
 */
@inject('stores')
@observer
export default class ChatEnterprise extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      selectedJob: { id: 0, title: I18n.t('page_job_company_all_job') },
    };
  }

  onSearch = () => {
    NavigationService.navigate('searchChat');
  };

  onShowSelectJob = () => {
    global.emitter.emit(constant.event.showSelectJobModal, {
      page: 'main',
      data: this.state.selectedJob,
      title: I18n.t('page_job_text_select_job'),
      onConfirm: this.onSelectJob,
    });
  };

  onSelectJob = (selectedJob) => {
    if (selectedJob.id === this.state.selectedJob.id) return;
    this.selectedJob = selectedJob;
    this.setState({ selectedJob });
    this.chat?.onSearch();
  };

  refChat = (ref) => (this.chat = ref?.wrappedInstance);

  filterSessionList = (sessionList) => {
    if (this.selectedJob?.id) {
      return sessionList.filter((item) => item.jobId === this.selectedJob.id);
    }
    return sessionList;
  };

  render() {
    const { style } = this;
    const { selectedJob } = this.state;
    return (
      <View style={style.container}>
        <StatusBar
          backgroundColor={style.headerContainer.backgroundColor}
          barStyle="dark-content"
        />
        <View style={style.headerContainer}>
          <Touchable style={style.headerLeftContainer} onPress={this.onShowSelectJob}>
            <Text style={style.jobText} numberOfLines={1}>
              {selectedJob?.title}
            </Text>
            <Icon type="entypo" name="chevron-down" color="#333" size={18} />
          </Touchable>
          <Touchable style={style.headerNotifyContainer} onPress={this.onSearch}>
            <Icon type="ionicon" name="search" color="#333" size={22} />
          </Touchable>
        </View>
        <Chat
          ref={this.refChat}
          navigation={this.props.navigation}
          filterSessionList={this.filterSessionList}
        />
      </View>
    );
  }
}
