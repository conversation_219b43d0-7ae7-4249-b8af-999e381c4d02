import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { View, TouchableOpacity } from 'react-native';
import { WebView } from 'react-native-webview';
import { Header, Icon } from 'react-native-elements';
import Share from 'react-native-share';
import { headerStyle, companyDetailStyle } from '../themes';
import GoBack from '../components/goback';
// import ShareUtile from "../util/ShareUtil";
import I18n, { setLanguage } from '../i18n';
import NavigationService from '../navigationService';
import I18nUtil from '../util/I18nUtil';
import articleHtml from './enterprise/home/<USER>/articleHtml';

export default class Page extends Component {
  share(message, url, des) {
    // console.log('title', title);
    console.log('url', url);
    console.log('des', des);
    const options = {
      message,
      url,
      failOnCancel: false,
    };

    Share.open(options)
      .then(() => {
        this.toast.show(I18n.t('page_share_tips_text'));
      })
      .catch(() => {
        // toast.show(I18n.t('page_component_qr_share_error'));
      });

    // ShareUtile.shareboard(des, '', url, title, [2, 7, 21, 24], (code, message) => {
    //   // console.log('share', code, message);
    //   if (code === 200) {
    //     this.toast.show(I18n.t('page_share_tips_text'));
    //   } else {
    //     this.toast.show(message);
    //   }
    //   // this.setState({ result: message });
    // });
  }

  onMessage = (event) => {
    console.log('onMessage', event.nativeEvent.data);
    const data = JSON.parse(event.nativeEvent.data);
    switch (data?.type) {
      case 'login':
      case 'register':
      case 'forget':
        NavigationService.goBack();
        break;
      case 'changeLanguage':
        this.onSwitchLanguage(data?.language);
        break;

      default:
        break;
    }
  };

  onSwitchLanguage = (language) => {
    console.log('onSwitchLanguage', language);
    setLanguage(language);
    this.forceUpdate();
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    const { callback } = this.props.navigation.state.params || {};
    callback && callback();
    global.emitter.emit('languageChange', true);
  };

  render() {
    const { navigation } = this.props;
    const {
      params: { url, title, des, showShare = true, headerBgColor, isLoginPage, isMessage },
    } = this.props.navigation.state;
    const html = articleHtml.replace('articleDescription', url);

    return (
      <View style={companyDetailStyle.jobContainer}>
        <Header
          statusBarProps={{
            barStyle: 'light-content',
            backgroundColor: headerBgColor || '#2089DC',
          }}
          containerStyle={[
            headerStyle.wrapper,
            headerBgColor ? { backgroundColor: headerBgColor } : {},
          ]}
          centerComponent={{ text: isLoginPage ? I18n.t(title) : title, style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            showShare ? (
              <TouchableOpacity
                style={{ height: 30, width: 30, marginTop: 8 }}
                onPress={() => this.share(title, url, des)}
              >
                <Icon type="simple-line-icon" name="share-alt" size={20} color="white" />
              </TouchableOpacity>
            ) : null
          }
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <WebView
          source={isMessage ? { html } : { uri: url }}
          startInLoadingState
          domStorageEnabled
          javaScriptEnabled
          onMessage={this.onMessage}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
