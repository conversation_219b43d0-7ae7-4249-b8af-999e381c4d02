import React, { Component } from 'react';
import { View, Alert, Platform, PermissionsAndroid, StatusBar } from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import DeviceInfo from '../util/deviceInfo';
import ImageViewer from '../components/imageViewer';
import res from '../res';
import I18n from '../i18n';

class ImageShow extends Component {
  requestExternalStoragePermission = async () => {
    try {
      const apiLevel = await DeviceInfo.getApiLevel();
      if (apiLevel > 28) return true;
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: I18n.t('page_dynamic_visit_photo_lib'),
          message: I18n.t('page_dynamic_visit_photo_lib_desc'),
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        return true;
      }
      return false;
    } catch (err) {
      console.error('Failed to request permission ', err);
      return false;
    }
  };

  downloadImage = async (uri) => {
    if (!uri) return;
    const hasPermission = await this.requestExternalStoragePermission();
    if (!hasPermission) {
      Alert.alert('', I18n.t('page_dynamic_set_photo_lib_permission'), [
        {
          text: I18n.t('page_resume_btn_sure'),
          onPress: () => {},
        },
      ]);
      return;
    }
    try {
      const timestamp = new Date().getTime(); // 获取当前时间错
      const random = String(Math.random() * 1000000); // 六位随机数
      const dirs =
        Platform.OS === 'ios' ? RNFS.LibraryDirectoryPath : RNFS.ExternalCachesDirectoryPath; // 外部文件，共享目录的绝对路径（仅限android）
      const downloadDest = `${dirs}/${timestamp + random}.jpg`;
      const options = {
        fromUrl: uri,
        toFile: downloadDest,
        background: true,
        begin: () => {
          // console.log('begin', res);
          // console.log('contentLength:', res.contentLength / 1024 / 1024, 'M');
        },
      };
      await RNFS.downloadFile(options).promise;
      await CameraRoll.saveToCameraRoll(downloadDest);

      Alert.alert('', I18n.t('page_dynamic_save_photo_success'), [
        {
          text: I18n.t('page_resume_btn_sure'),
          onPress: () => {},
        },
      ]);
    } catch (e) {
      Alert.alert('', `${I18n.t('page_dynamic_save_photo_fail')}！\n${e}`, [
        {
          text: I18n.t('page_resume_btn_sure'),
          onPress: () => {},
        },
      ]);
    }
  };

  savePhoto() {
    const {
      params: { images, index },
    } = this.props.navigation.state;
    const urlData = images[index];
    CameraRoll.saveToCameraRoll(urlData.url)
      .then(() => {
        Alert.alert('', I18n.t('page_dynamic_save_photo_success'), [
          {
            text: I18n.t('page_resume_btn_sure'),
            onPress: () => {},
          },
        ]);
      })
      .catch((error) => {
        Alert.alert('', `${I18n.t('page_dynamic_save_photo_fail')}！\n${error}`, [
          {
            text: I18n.t('page_resume_btn_sure'),
            onPress: () => {},
          },
        ]);
      });
  }

  render() {
    const {
      params: { images, index },
    } = this.props.navigation.state;
    return (
      <View style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#000" />
        <ImageViewer
          imageUrls={images}
          enableImageZoom
          index={index}
          saveToLocalByLongPress
          menuContext={{
            saveToLocal: I18n.t('page_dynamic_save_photo'),
            cancel: I18n.t('page_job_permission_cancel'),
          }}
          failImageSource={res.noData}
          onChange={(i) => {
            console.log(i);
          }}
          onClick={() => {
            this.props.navigation.pop();
          }}
          onSave={(url) => {
            Platform.OS === 'ios' ? this.savePhoto(url) : this.downloadImage(url);
          }}
        />
      </View>
    );
  }
}
export default ImageShow;
