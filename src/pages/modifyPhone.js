import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, TouchableOpacity, Text, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { Input, <PERSON><PERSON>, Header, Icon } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import CountryCode from '../components/countryCode';
import CommonKeyboardAvoidingView from '../components/commonKeyboardAvoidingView';
import {
  headerStyle,
  titleColor,
  subTitleColor,
  settingStyle,
  modifyPhoneStyle,
  phColor,
  desColor,
  bgColor,
  registerStyle,
} from '../themes';
import I18n from '../i18n';
import LoadingModal from '../components/loadingModal';
import regExp from '../util/regExp';
import { normalizePhone } from '../common/special';
import constant from '../store/constant';
import validateUtil from '../util/validateUtil';
import InputImageCaptchaModal from '../components/modal/inputImageCaptchaModal';

let timeout = null;
let keyBoardIsShow = false;

@inject('personStore', 'userAction', 'loginAction', 'resumeAction')
@observer
export default class RegisterPhone extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      region: constant.defaultRegion,
      regionCode: constant.defaultRegion.code,
      regionCodeLabel: constant.defaultRegion.label,
      phone: '',
      code: '',
      sending: false,
      seconds: 60,
      isOpenSelectedCountryCode: false,
      showLoading: false,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.goBack();
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    if (timeout) {
      clearInterval(timeout);
    }
  }

  onSubmitPasswordForm = async () => {
    const {
      personStore: { me },
    } = this.props;
    this.onLostBlur();
    const { phone, code, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    if (code.trim() === '') {
      this.toast.show(I18n.t('page_login_op_code_required'));
      return;
    }
    this.setState({ showLoading: true });
    this.props.userAction
      .generalModifyPhone({
        newMobile: normalizePhone(phone, regionCode),
        regionCode,
        verificationCode: code,
      })
      .then(
        (res) => {
          this.setState({ showLoading: false });
          if (res && res.successful) {
            this.toast.show(res.message);
            this.props.resumeAction.getResumes();
            this.props.userAction.getCurrUserInfo();
            setTimeout(() => {
              this.goBack();
            }, 1000);
          } else {
            if (me && me.mobile) {
              this.toast.show(
                res.message
                  ? I18n.t('page_bin_phone_success')
                  : I18n.t('page_setting_toast_change_password_error')
              );
              return;
            }
            this.toast.show(
              res.message ? res.message : I18n.t('page_setting_toast_change_password_error')
            );
          }
        },
        (err) => {
          console.log(err);
          this.setState({ showLoading: false });
        }
      );
  };

  onSelectedCountryCode = (item) => {
    if (item) {
      this.setState({ region: item, regionCodeLabel: item.label, regionCode: item.code });
    }
    this.setState({ isOpenSelectedCountryCode: false });
  };

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  setCode = (text) => {
    this.setState({
      code: text,
    });
  };

  setPhone = (text) => {
    this.setState({
      phone: text,
    });
  };

  sendCode = async (imageCaptcha) => {
    this.onLostBlur();
    try {
      const { phone, regionCode, region, sending } = this.state;
      if (sending) return;
      const mobile = validateUtil.validatePhone(phone, region);
      if (!mobile) return;
      const res = await this.inputImageCaptchaModal.sendImPhoneCode(
        {
          param: {
            mobile,
            regionCode,
            imageCaptcha,
          },
          onConfirm: this.sendCode,
        },
        this.props.loginAction.sendRegisterCode
      );
      if (res && res.successful) {
        toast.show(I18n.t('page_login_op_sendcode_success'));
        this.runSendCodeTimeout();
      } else {
        if (res.message) {
          toast.show(res.message);
        } else {
          toast.show(I18n.t('page_login_op_sendcode_error'));
        }
      }
    } catch (e) {
      console.log('sendVerifyCode error', err);
      this.setState({ sending: false });
    }
    return;
    const { phone, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    this.setState({ sending: true });
    this.props.loginAction
      .sendRegisterCode(normalizePhone(phone, regionCode), regionCode)
      .then((res) => {
        if (res && res.successful) {
          this.toast.show(I18n.t('page_register_op_sendcode_success'));
          // 数秒
          this.runSendCodeTimeout();
        } else {
          this.toast.show(I18n.t('page_register_op_sendcode_error'));
          this.setState({ sending: false });
        }
      });
  };

  openCountryCodeModal = (value) => {
    Keyboard.dismiss();
    this.setState({ isOpenSelectedCountryCode: value });
  };

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = false;
  }

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onShowModal) {
      await fuc.onShowModal();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  runSendCodeTimeout = () => {
    this.setState({ sending: true });
    timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds === 0) {
        this.setState({ seconds: 60, sending: false });
        clearInterval(timeout);
      }
    }, 1000);
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={15}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  renderHeader = () => {
    const {
      personStore: { me },
    } = this.props;
    return (
      <Header
        statusBarProps={{
          barStyle: 'light-content',
          backgroundColor: '#2089DC',
        }}
        containerStyle={headerStyle.wrapper}
        centerComponent={{
          text:
            me && me.mobile
              ? I18n.t('page_setting_text_modify_phone')
              : I18n.t('page_guide_bind_phone'),
          style: headerStyle.center,
        }}
        leftComponent={this.renderLeftIcon()}
        innerContainerStyles={{ justifyContent: 'center' }}
      />
    );
  };

  renderPhoneIcon = () => (
    <View style={[modifyPhoneStyle.leftContainer]}>
      <Icon type="font-awesome" name="mobile-phone" size={20} color={desColor} />
    </View>
  );

  renderCodeIcon = () => (
    <TouchableOpacity
      onPress={() => {
        this.openCountryCodeModal(true);
      }}
    >
      <View style={modifyPhoneStyle.leftContainer}>
        <Text numberOfLines={1} style={{ fontSize: 12, color: subTitleColor, textAlign: 'center' }}>
          {' '}
          {this.state.regionCodeLabel}{' '}
        </Text>
        <Icon type="font-awesome" name="angle-down" size={20} color={desColor} />
      </View>
    </TouchableOpacity>
  );

  renderPhoneTex = () => {
    const {
      personStore: { me },
    } = this.props;
    return (
      <View style={modifyPhoneStyle.phoneTextDesc}>
        {me && me.mobile ? (
          <Text style={{ fontSize: 12, color: subTitleColor }}>
            {I18n.t('page_setting_label_now_bind_phone')}
          </Text>
        ) : (
          <Text style={{ display: 'none' }} />
        )}
        <Text style={{ fontSize: 14, color: titleColor, marginTop: 6 }}>
          {me && me.mobile
            ? `${me.mobile.substr(0, 3)}****${me.mobile.substr(7)}`
            : I18n.t('page_setting_label_no_phone')}
        </Text>
      </View>
    );
  };

  renderForm = () => (
    <View style={registerStyle.formContent}>
      <View style={[registerStyle.formGroup, registerStyle.formGroupLine]}>
        <View style={registerStyle.formPhoneSection}>
          <View style={registerStyle.formPhoneInputWrap}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={registerStyle.formControl}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_register_ph_phone')}
              placeholderTextColor={phColor}
              onChangeText={this.setPhone}
              value={this.state.phone}
              maxLength={15}
              keyboardType="numeric"
              leftIcon={this.renderCodeIcon()}
            />
          </View>
        </View>
      </View>
      <Text style={{ backgroundColor: bgColor, height: 10 }} />
      <View style={[registerStyle.formGroup, registerStyle.formGroupLine]}>
        <View style={registerStyle.formSendCodeSection}>
          <View style={registerStyle.formSendCodeInputWrap}>
            <Input
              inputContainerStyle={registerStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_register_ph_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCode}
              value={this.state.code}
              maxLength={6}
              keyboardType="numeric"
              // leftIcon={this.renderPhoneIcon()}
            />
          </View>
          <View style={registerStyle.formSendCodeBtnWrap}>
            <Button
              title={
                this.state.sending
                  ? `${I18n.t('page_register_btn_sendcode')}(${this.state.seconds})`
                  : I18n.t('page_login_btn_sendcode')
              }
              titleStyle={{ color: '#fff', fontSize: 16 }}
              buttonStyle={registerStyle.btnSendCode}
              disabled={this.state.sending}
              onPress={this.sendCode}
            />
          </View>
        </View>
      </View>
    </View>
  );

  render() {
    const { isOpenSelectedCountryCode, showLoading } = this.state;
    const {
      personStore: { me },
    } = this.props;
    return (
      <View style={registerStyle.page}>
        <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
          <View style={[registerStyle.container, { paddingHorizontal: 0 }]}>
            {this.renderHeader()}
            {this.renderPhoneTex()}
            <CommonKeyboardAvoidingView
              behavior="padding"
              style={[registerStyle.formSection, { height: 110 }]}
            >
              {this.renderForm()}
            </CommonKeyboardAvoidingView>

            <TouchableOpacity
              style={[settingStyle.buttonStyle, modifyPhoneStyle.buttonExtro]}
              onPress={this.onSubmitPasswordForm}
            >
              <Text style={{ fontSize: 16, color: '#fff' }}>
                {me && me.mobile
                  ? I18n.t('page_setting_label_confirm_modify_phone')
                  : I18n.t('page_guide_bind_phone')}
              </Text>
            </TouchableOpacity>
            <Text style={modifyPhoneStyle.text1}>{I18n.t('page_setting_label_text1')}</Text>
            <Text style={modifyPhoneStyle.text2}>{I18n.t('page_setting_label_text2')}</Text>
          </View>
        </TouchableWithoutFeedback>
        <CountryCode isOpen={isOpenSelectedCountryCode} onSelected={this.onSelectedCountryCode} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={showLoading} loadingTips={false} />
        <InputImageCaptchaModal ref={(ref) => (this.inputImageCaptchaModal = ref)} />
      </View>
    );
  }
}
