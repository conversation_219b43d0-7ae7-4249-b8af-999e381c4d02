import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { Header } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { globalStyle, headerStyle, dynamicStyle, resumeEditStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('resumeStore', 'resumeAction')
@observer
export default class Appraise extends Component {
  constructor(props) {
    super(props);
    this.tempInputText = '';
    this.state = {
      count: 200,
      inputText: '',
    };
  }

  componentDidMount() {
    const { currentResume } = this.props.resumeStore;
    this.tempInputText = currentResume && currentResume.training ? currentResume.training : '';
    this.setState({
      count: 200 - (currentResume && currentResume.training ? currentResume.training.length : 0),
      inputText: currentResume && currentResume.training ? currentResume.training : '',
    });
  }

  onPress = () => {
    const { navigation } = this.props;
    const { currentResume } = this.props.resumeStore;
    const postData = {};
    postData.name = currentResume.name;
    postData.hobby = currentResume.hobby ? currentResume.hobby : '';
    postData.training = this.state.inputText;
    postData.completeness = currentResume.completeness;
    postData.description = currentResume.description ? currentResume.description : '';

    this.props.resumeAction.updateResumeAttributes(currentResume.resumeId, postData).then((res) => {
      if (res && res.successful) {
        this.toast.show(res.message);
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
        this.props.resumeAction.getResumes();
      } else {
        this.toast.show(res.message);
      }
    });
  };

  onChangeText = (text) => {
    this.setState({
      count: 200 - text.length,
      inputText: text,
    });
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: I18n.t('page_resume_label_training'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.onPress}>
              <Text style={[headerStyle.rightBtn, { color: '#fff' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <View style={dynamicStyle.container}>
          <TextInput
            style={[dynamicStyle.inputText, resumeEditStyle.editInputText]}
            blurOnSubmit={false}
            placeholder={I18n.t('page_resume_label_training')}
            value={this.state.msgText}
            underlineColorAndroid="transparent"
            multiline
            maxLength={200}
            textAlignVertical="top"
            ref={(ref) => {
              this.inputText = ref;
            }}
            onChangeText={this.onChangeText}
            defaultValue={this.tempInputText}
          />
          <Text style={dynamicStyle.editTextCount}>
            {I18n.t('page_dynamicedit_tips_textcount1')}
            <Text style={dynamicStyle.numberCount}>{this.state.count}</Text>
            {I18n.t('page_dynamicedit_tips_textcount2')}
          </Text>
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
