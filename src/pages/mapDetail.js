import React, { Component } from 'react';
import MapView, { PROVIDER_GOOGLE, Marker } from 'react-native-maps';
import { View, Text, TouchableOpacity } from 'react-native';
import { Clipboard } from '../components';
import { inject, observer } from 'mobx-react';
import { Header, Icon } from 'react-native-elements';
import { headerStyle, titleColor, desColor, companyDetailStyle, baseBlueColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('jobStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onCopy = (text) => {
    Clipboard.setString(text);
    console.log('text', text);
    global.toast.show(I18n.t('page_dynamic_text_copy_success'));
  };

  render() {
    const { navigation } = this.props;
    const { companyDetail } = this.props.jobStore;
    const region = {
      latitude:
        companyDetail && companyDetail.googleLatitude
          ? parseFloat(companyDetail.googleLatitude)
          : 11.5448729,
      longitude:
        companyDetail && companyDetail.googleLatitude
          ? parseFloat(companyDetail.googleLongitude)
          : 104.8921668,
      latitudeDelta: 0.002,
      longitudeDelta: 0.002,
    };
    return (
      <View style={companyDetailStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: companyDetail ? companyDetail.company : '',
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <View
          style={{
            paddingHorizontal: 10,
            paddingVertical: 10,
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
            <Text style={{ fontSize: 16, color: titleColor, marginRight: 10 }}>
              {I18n.t('page_job_company_address')}
            </Text>
            <TouchableOpacity
              onPress={() => this.onCopy(companyDetail ? companyDetail.address : '')}
            >
              <Text style={{ color: baseBlueColor, fontSize: 14 }}>
                {I18n.t('page_dynamic_text_copy')}
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: '92%',
              flexDirection: 'row',
              justifyContent: 'flex-start',
            }}
          >
            <Icon type="material" name="location-on" size={16} color={desColor} />
            <Text
              numberOfLines={2}
              style={{ color: '#333333', fontSize: 14 }}
              onLongPress={() => this.onCopy(companyDetail ? companyDetail.address : '')}
            >
              {companyDetail ? companyDetail.address : ''}
            </Text>
          </View>
        </View>
        {companyDetail && companyDetail.googleLatitude && (
          <View style={{ flex: 1 }}>
            <MapView style={{ flex: 1 }} initialRegion={region} provider={PROVIDER_GOOGLE}>
              <Marker
                coordinate={region}
                description={companyDetail && companyDetail.address ? companyDetail.address : ''}
              />
            </MapView>
          </View>
        )}
      </View>
    );
  }
}
