import React, { Component } from 'react';
import { ScrollView, View } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Input, Header, Icon, ListItem } from 'react-native-elements';
import { headerStyle, titleColor, desColor, jobStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import { RVW } from '../common';

@inject('resumeStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      majorList: [],
    };
  }

  componentDidMount() {
    const {
      resumeStore: { majorList },
    } = this.props;
    this.setState({ majorList });
  }

  onSelectMajor = (item) => {
    const { navigation } = this.props;
    if (navigation.state.params) {
      navigation.state.params.onSelect(item);
      navigation.goBack();
    }
  };

  onChangeText = (text) => {
    const {
      resumeStore: { majorList },
    } = this.props;
    let temp = majorList;
    temp = temp.filter((item) => item.label.indexOf(text) > -1);
    if (!text) {
      this.setState({ majorList });
    } else {
      this.setState({ majorList: temp });
    }
  };

  renderMajorList = () => {
    const { majorList } = this.state;
    return majorList.map((item) => (
      <ListItem
        key={item.value}
        containerStyle={{
          borderBottomColor: '#eee',
          borderBottomWidth: 0.5,
          paddingHorizontal: 12,
          paddingVertical: 12,
          backgroundColor: '#fff',
        }}
        onPress={() => this.onSelectMajor(item)}
        title={item.label}
        titleStyle={{
          color: titleColor,
          fontSize: 14,
        }}
      />
    ));
  };

  renderSearch = () => {
    return (
      <View style={jobStyle.jobExpectedSearch}>
        <Input
          inputContainerStyle={{
            backgroundColor: '#fff',
            marginVertical: 10,
            borderBottomWidth: 0,
            borderRadius: 6,
            marginLeft: 0,
            paddingVertical: 2,
            height: 38,
          }}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          onChangeText={(text) => this.onChangeText(text)}
          clearButtonMode="while-editing"
          returnKeyType="search"
          placeholder={I18n.t('page_resume_ph_edu_subject')}
          placeholderTextColor={desColor}
          leftIcon={<Icon type="evilicon" name="search" size={30} color={desColor} />}
          leftIconContainerStyle={{
            marginLeft: 2,
          }}
        />
      </View>
    );
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_edu_subject'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderSearch()}
        <ScrollView>
          <View style={{ backgroundColor: '#fff' }}>{this.renderMajorList()}</View>
        </ScrollView>
      </View>
    );
  }
}
