import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, Input } from 'react-native-elements';
import { globalStyle, headerStyle, resumeStyle, titleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import regExp from '../util/regExp';

export default class Phone extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputText: this.props.navigation.state.params.email,
    };
  }

  onChangeText = (text) => {
    this.setState({
      inputText: text,
    });
  };

  saveEmail = () => {
    const { navigation } = this.props;
    if (!regExp.email.test(this.state.inputText.trim())) {
      this.toast.show(I18n.t('page_register_op_email_error'));
      return;
    }
    this.props.navigation.state.params.editEmail(this.state.inputText);
    navigation.goBack();
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_email_edit'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              onPress={() => {
                this.saveEmail();
              }}
            >
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <Input
          onChangeText={this.onChangeText}
          placeholder={I18n.t('page_forgot_ph_email')}
          style={[resumeStyle.phoneInput, { paddingHorizontal: 0 }]}
          inputContainerStyle={[resumeStyle.inputContainerStyle, { paddingLeft: 16 }]}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          defaultValue={this.props.navigation.state.params.email}
          maxLength={30}
          underlineColorAndroid="transparent"
          containerStyle={{
            width: '100%',
          }}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
