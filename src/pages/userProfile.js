import React, { Component } from 'react';
import ActionSheet from 'react-native-actionsheet';
import { ScrollView, View, Text, TouchableOpacity } from 'react-native';
import Toast from 'react-native-easy-toast';
import { <PERSON>er, ListItem, Icon } from 'react-native-elements';
import { inject, observer } from 'mobx-react';
import { headerStyle, baseBlueColor, userProfileStyle } from '../themes';
import I18n from '../i18n';
import { getAvatarSource } from '../res';
import Avatar from '../components/avatar';

@inject('personStore', 'dynamicAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      blockStatus: false,
    };
  }

  blockedUser = (value) => {
    const {
      params: { data },
    } = this.props.navigation.state;
    if (value) {
      this.props.dynamicAction.blockedUser(data.userId).then((res) => {
        if (res) {
          if (res.successful) {
            this.setState({ blockStatus: true });
          }
          if (res.message) {
            this.toast.show(res.message);
          }
        }
      });
    } else {
      this.props.dynamicAction.unblockedUser(data.userId).then((res) => {
        if (res) {
          if (res.successful) {
            this.setState({ blockStatus: false });
          }
          if (res.message) {
            this.toast.show(res.message);
          }
        }
      });
    }
  };

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onShowModal) {
      await fuc.onShowModal();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    if (fuc && fuc.onRefreshDynamic) {
      await fuc.onRefreshDynamic();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  showStatuSheet = () => {
    this.statusActionSheet.show();
  };

  formatData(array) {
    const list = array.map((item) => item.label);
    list.push(I18n.t('page_job_actionsheet_cancel'));
    return list;
  }

  statusSheetSelect = (index) => {
    const {
      personStore: { reporterList },
    } = this.props;
    const {
      params: { data },
    } = this.props.navigation.state;
    if (index !== reporterList.length) {
      const param = reporterList[index];
      this.props.dynamicAction.reportedUser(data.userId, { type: param.value }).then((res) => {
        if (res && res.message) {
          this.toast.show(res.message);
        }
      });
    }
  };

  previewDetail = (url) => {
    if (!url) {
      return;
    }
    const { navigation } = this.props;
    navigation.navigate('previewImage', { images: [{ url }], index: 0 });
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  render() {
    const {
      personStore: { reporterList },
    } = this.props;
    const {
      params: { data },
    } = this.props.navigation.state;
    return (
      <View style={userProfileStyle.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_dynamic_userprofile_setting'),
            style: headerStyle.center,
          }}
          leftComponent={this.renderLeftIcon()}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          <View style={userProfileStyle.userCard}>
            <TouchableOpacity
              onPress={() => {
                this.previewDetail(data.avatar);
              }}
            >
              <Avatar size={50} rounded source={getAvatarSource(data.avatar)} />
            </TouchableOpacity>
            <View style={userProfileStyle.userContainer}>
              <Text style={userProfileStyle.userTitle}>
                {data.userName ? data.userName : I18n.t('page_dynamic_title_ay_username')}
              </Text>
            </View>
          </View>
          <ListItem
            title={I18n.t('page_dynamic_bolcked_other')}
            switch={{
              value: this.state.blockStatus,
              trackColor: baseBlueColor,
              onValueChange: this.blockedUser,
            }}
            titleStyle={userProfileStyle.listItemLeftText}
            containerStyle={[userProfileStyle.listItemContent, { marginTop: 5 }]}
          />
          <ListItem
            title={I18n.t('page_dynamic_reportered_other')}
            onPress={this.showStatuSheet}
            chevron
            titleStyle={userProfileStyle.listItemLeftText}
            containerStyle={[userProfileStyle.listItemContent]}
          />
        </ScrollView>
        <ActionSheet
          ref={(ref) => {
            this.statusActionSheet = ref;
          }}
          title={I18n.t('page_dynamic_reportered_other')}
          options={this.formatData(reporterList)}
          cancelButtonIndex={reporterList ? reporterList.length : 0}
          onPress={this.statusSheetSelect}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
