import React, { Component } from 'react';
import Picker from 'react-native-picker';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import util from '../util';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const work = this.props.navigation.state.params.data;
    this.isEdit = this.props.navigation.state.params.edit;
    this.state = {
      postData: work
        ? Object.assign({}, work)
        : {
            company: '',
            fromDate: '',
            toDate: '',
            description: '',
            title: '',
          },
      company: work.company,
      title: work.title,
    };
  }

  componentWillUnmount() {
    Picker.hide();
  }

  onChangeCompany = (text) => {
    const temp = this.state.postData;
    temp.company = text;
    this.setState({
      postData: temp,
    });
  };

  onChangeTitle = (text) => {
    const temp = this.state.postData;
    temp.title = text;
    this.setState({
      postData: temp,
    });
  };

  onFocus = () => {
    Picker.hide();
  };

  publish = () => {
    Keyboard.dismiss();
    const { currentResume } = this.props.resumeStore;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.company) {
      this.toast.show(I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work'));
      return;
    }
    if (!temp.fromDate) {
      this.toast.show(I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_work_enter'));
      return;
    }
    if (
      parseFloat(util.dateToTimestamp(temp.fromDate)) >
      parseFloat(util.dateToTimestamp(temp.toDate))
    ) {
      this.toast.show(I18n.t('page_resume_tips_not_before'));
      return;
    }
    if (!temp.title) {
      this.toast.show(I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work_name'));
      return;
    }
    if (!temp.description) {
      this.toast.show(I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work_des'));
      return;
    }
    temp.fromDate += '-1';
    if (
      (temp.toDate && temp.toDate.indexOf(I18n.t('page_resume_work_exprience_up_to_now')) > -1) ||
      !temp.toDate
    ) {
      temp.toDate = null;
    } else {
      temp.toDate += '-1';
    }
    if (this.isEdit) {
      this.props.resumeAction
        .updateResumeWork(temp.experienceId, currentResume.resumeId, temp)
        .then(
          (res) => {
            this.showRequestResult(res);
          },
          (err) => {
            console.log(err);
          }
        );
    } else {
      this.props.resumeAction.addResumeWork(currentResume.resumeId, temp).then(
        (res) => {
          this.showRequestResult(res);
        },
        (err) => {
          console.log(err);
        }
      );
    }
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      Picker.hide();
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  createDateData(type) {
    const today = new Date();
    const currentY = today.getFullYear();
    const currentM = today.getMonth() + 1;
    const date = [];
    const nowMap = {};
    nowMap[I18n.t('page_resume_work_exprience_up_to_now')] = [' '];
    if (type === 2) {
      date.push(nowMap);
    }
    for (let i = currentY; i > 1990; i -= 1) {
      const month = [];
      for (let j = 1; j < 13; j += 1) {
        month.push((Array(2).join('0') + j).slice(-2));
      }
      const _date = {};
      if (i === currentY) {
        _date[i] = month.slice(0, currentM);
      } else {
        _date[i] = month;
      }
      date.push(_date);
    }
    return date;
  }

  showPickerPress(type) {
    Keyboard.dismiss();
    const work = this.state.postData;
    let title = '';
    let picData = [];
    let select = [];
    if (type === 2) {
      title = I18n.t('page_resume_label_work_enter');
      picData = this.createDateData(1);
      if (work.fromDate) {
        select = work.fromDate.split('-');
      } else {
        select = ['2019', '01'];
      }
    } else if (type === 3) {
      title = I18n.t('page_resume_label_work_leave');
      picData = this.createDateData(2);
      if (work.toDate) {
        select = work.toDate.split('-');
      } else {
        select = [I18n.t('page_resume_work_exprience_up_to_now')];
      }
    }
    Picker.init({
      pickerData: picData,
      pickerTitleText: title,
      selectedValue: select,
      pickerCancelBtnText: I18n.t('page_resume_btn_cancel'),
      pickerConfirmBtnText: I18n.t('page_resume_btn_sure'),
      pickerToolBarBg: [50, 165, 231, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [255, 255, 255, 1],
      pickerCancelBtnColor: [255, 255, 255, 1],
      pickerTitleColor: [255, 255, 255, 1],
      onPickerConfirm: (data) => {
        if (type === 2) {
          const temp = this.state.postData;
          temp.fromDate = data.join('-');
          this.setState({
            postData: temp,
          });
        } else if (type === 3) {
          const temp = this.state.postData;
          temp.toDate = data.join('-');
          this.setState({
            postData: temp,
          });
        }
      },
      onPickerCancel: () => {},
      onPickerSelect: () => {},
    });
    Picker.show();
  }

  formatDate = (date) => {
    if (this.isEdit) {
      return util.dateToYM1String2(date);
    }
    return date ? util.dateToYMString(date) : '';
  };

  render() {
    const { navigation } = this.props;
    const work = this.state.postData;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_work_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_work')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work'),
            inputStyle: resumeStyle.inputText,
            containerStyle: { height: 44 },
            onChangeText: this.onChangeCompany,
            onFocus: this.onFocus,
            defaultValue: this.state.company,
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_work_enter')}
          chevron
          hideChevron
          rightTitle={work.fromDate ? util.dateToYMString(work.fromDate) : ''}
          onPress={() => this.showPickerPress(2)}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_work_leave')}
          chevron
          rightTitle={this.formatDate(work.toDate)}
          onPress={() => this.showPickerPress(3)}
          titleStyle={resumeStyle.listItemLeftText}
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_work_name')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work_name'),
            inputStyle: resumeStyle.inputText,
            containerStyle: { height: 44 },
            onChangeText: this.onChangeTitle,
            onFocus: this.onFocus,
            defaultValue: this.state.title,
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_work_des')}
          chevron
          onPress={() => {
            Picker.hide();
            navigation.navigate('resumeWorkDes', {
              edit: (des) => {
                const temp = this.state.postData;
                temp.description = des;
                this.setState({
                  postData: temp,
                });
              },
              des: this.state.postData.description,
            });
          }}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
