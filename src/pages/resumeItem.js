import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Icon } from 'react-native-elements';
import ExpandableText from 'rn-expandable-text';
import {
  resumePreStyle,
  globalStyle,
  resumeStyle,
  baseBlueColor,
  desColor,
  bgColor,
} from '../themes';
import TimeLineList from '../components/timeLineList';
import Res, { getAvatarSource } from '../res';
import I18n from '../i18n';
import { deviceWidth } from '../common';
import util from '../util';
import Image from '../components/image';
import Avatar from '../components/avatar';
import Touchable from '../components/touchable';

@inject('resumeStore', 'resumeAction')
@observer
class ResumeItem extends Component {
  goSubPage(subNav, data) {
    const { nav } = this.props;
    if (data) {
      nav.navigate(subNav, {
        data,
        onRefreshList: () => {
          this.props.resumeAction.getResumes();
        },
      });
    } else {
      nav.navigate(subNav);
    }
  }

  refresh(resume) {
    this.props.resumeAction.refreshResume(resume.resumeId).then((res) => {
      if (res && res.successful) {
        this.toast.show(I18n.t('page_resume_refresh_success'));
        this.props.resumeAction.getResumes();
      }
    });
  }

  preview(resume) {
    this.props.nav.navigate('resumeWebview', {
      resumeId: resume ? resume.resumeId : 0,
      title: I18n.t('page_resume_text_previews_title'),
      des: resume ? resume.name : '',
    });
  }

  onSave = () => {
    const { currentResume } = this.props.resumeStore;
    const postData = {};
    postData.name = currentResume.name;
    postData.hobby = currentResume.hobby ? currentResume.hobby : '';
    postData.training = currentResume.training ? currentResume.training : '';
    postData.completeness = currentResume.completeness;
    postData.description = currentResume.description ? currentResume.description : '';

    this.props.resumeAction
      .updateResumeAttributes(currentResume.resumeId, { ...postData, status: 1 })
      .then((res) => {
        if (res && res.successful) {
          this.toast.show(res.message);

          this.props.resumeAction.getResumes();
        } else {
          this.toast.show(res.message);
        }
      });
  };

  renderPagination = (resumeList, currentIndex) => {
    if (resumeList && resumeList.length > 1) {
      return (
        <View style={{ alignItems: 'center' }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            {resumeList.map((item, index) => (
              <View
                key={`${index + 1}`}
                style={[
                  resumeStyle.paginationContainer,
                  { backgroundColor: currentIndex === index ? baseBlueColor : '#D8D8D8' },
                ]}
              />
            ))}
          </View>
        </View>
      );
    }
    return <View style={{ height: 12 }} />;
  };

  renderHeader = (resume, resumeList, index) => (
    <View>
      <View style={resumeStyle.headerBgView1} />
      <View style={[resumeStyle.headerBgView2, resume.status == 0 ? { height: 66 } : {}]} />
      <View style={[resumeStyle.headerView, resume.status == 0 ? { height: 124 } : {}]}>
        {this.renderPagination(resumeList, index)}
        {resume.status == 0 ? (
          <View style={resumeStyle.saveBox}>
            <Text style={resumeStyle.draftText}>{I18n.t('page_home_text_draft')}</Text>
            <Touchable onPress={this.onSave}>
              <Text style={resumeStyle.saveText}>{I18n.t('page_resume_btn_save')}</Text>
            </Touchable>
          </View>
        ) : null}
        <Text
          style={{
            height: 19,
            fontSize: 14,
            marginTop: 6,
            marginBottom: 5,
            marginRight: 45,
          }}
          numberOfLines={1}
        >
          {resume ? resume.name : ''}
        </Text>
        <View style={resumeStyle.infoView}>
          <Text style={resumeStyle.separateText}>
            {I18n.t('page_resume_label_ratio')}
            {resume ? `${resume.completeness}%` : '0'}
          </Text>
          <Text style={resumeStyle.separateText}>
            {I18n.t('page_resume_label_time')}
            {resume ? resume.updateTime : '0'}
          </Text>
        </View>
        <View style={resumeStyle.separateView} />
        <View style={resumeStyle.itemsView}>
          <TouchableOpacity style={resumeStyle.item} onPress={() => this.preview(resume)}>
            <View style={resumeStyle.item}>
              <Image source={Res.iconPreview} style={resumeStyle.itemIcon} />
              <Text style={resumeStyle.itemText}>{I18n.t('page_resume_btn_preview')}</Text>
            </View>
          </TouchableOpacity>
          {resume.status == 1 ? (
            <TouchableOpacity style={resumeStyle.item} onPress={() => this.refresh(resume)}>
              <View style={resumeStyle.item}>
                <Image source={Res.iconRefresh} style={resumeStyle.itemIcon} />
                <Text style={resumeStyle.itemText}>{I18n.t('page_resume_btn_refresh')}</Text>
              </View>
            </TouchableOpacity>
          ) : null}
          <TouchableOpacity
            style={resumeStyle.item}
            onPress={() => {
              this.props.nav.navigate('resumeManager');
            }}
          >
            <View style={resumeStyle.item}>
              <Image source={Res.iconSettings} style={resumeStyle.itemIcon} />
              <Text style={resumeStyle.itemText}>{I18n.t('page_resume_btn_buy')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  renderCellHeader = (title, subNav, status, must, data) => (
    <TouchableOpacity
      style={resumePreStyle.cellHeader}
      onPress={() => {
        this.goSubPage(subNav, data);
      }}
    >
      <View style={[resumePreStyle.cellHeaderContent, { justifyContent: 'space-between' }]}>
        <View style={resumePreStyle.flexDirectionRowNormal}>
          <View style={resumePreStyle.cellHeaderTag} />
          <Text style={resumePreStyle.cellHeaderText}>{title}</Text>
          <Text style={[resumePreStyle.cellHeaderText, { color: '#F5A623' }]}>
            {must ? '*' : ''}
          </Text>
        </View>
        {/* <TouchableOpacity
          onPress={() => {
            this.goSubPage(subNav, data);
          }}
        >
          
        </TouchableOpacity> */}
        <View
          style={{
            height: 40,
            width: 120,
            flexDirection: 'row',
            justifyContent: 'flex-end',
            alignItems: 'center',
          }}
        >
          <Text style={{ marginRight: 10, color: '#F5A623', fontSize: 12 }}>
            {status ? '' : I18n.t('page_resume_label_null')}
          </Text>
          <Image source={Res.iconEdit} style={resumePreStyle.icon} />
        </View>
      </View>
      {status ? <View style={resumePreStyle.cellHeaderSep} /> : <View />}
    </TouchableOpacity>
  );

  renderInfoIcon = (name, text) => (
    <View style={[resumePreStyle.flexDirectionRowNormal, { marginHorizontal: 4 }]}>
      <Image source={name} style={resumePreStyle.icon} />
      <Text numberOfLines={1} style={[resumePreStyle.subTitleText, { maxWidth: 80 }]}>
        {text}
      </Text>
    </View>
  );

  renderInfo = (resume) => {
    const prof = resume.profile;
    const edus = resume.educations;
    const edu = edus && edus.length > 0 ? edus[0] : {};
    const sex = prof && prof.sexId ? prof.sexId.value : 3;
    let name = '';
    let color = '';

    switch (sex) {
      case 1:
        name = 'gender-male';
        color = baseBlueColor;
        break;
      case 2:
        name = 'gender-female';
        color = 'red';
        break;

      default:
        name = 'gender-male-female';
        color = desColor;
        break;
    }
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(I18n.t('page_resume_label_profile'), 'resumeProfile', true, true)}
        <View style={[resumePreStyle.cellContent]}>
          <View style={resumePreStyle.infoCellTop}>
            <Avatar size={50} rounded source={getAvatarSource(prof.avatar)} />
            <View style={[resumePreStyle.infoCellTopRight, { width: '75%' }]}>
              <View style={resumePreStyle.flexDirectionRowNormal}>
                <Text
                  numberOfLines={1}
                  style={[resumePreStyle.titleText, resumePreStyle.infoNameText]}
                >
                  {util.getUserDisplayName(prof)}
                </Text>
                <Icon
                  type="material-community"
                  name={name}
                  size={20}
                  color={color}
                  reverseColor="black"
                />
              </View>
              <View
                style={[resumePreStyle.flexDirectionRowNormal, resumePreStyle.infoCellTopRightInfo]}
              >
                {this.renderInfoIcon(
                  Res.iconBirth,
                  prof.birthday
                    ? `${util.getAge(prof.birthday)}${I18n.t('page_resume_ph_years_old')}`
                    : ''
                )}
                {edu && edu.qualificationId && edu.qualificationId.label ? (
                  this.renderInfoIcon(
                    Res.iconEdu,
                    edu && edu.qualificationId ? edu.qualificationId.label : ''
                  )
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
                {edu && edu.fromDate && edu.toDate ? (
                  this.renderInfoIcon(
                    Res.iconEduTime,
                    edu && edu.fromDate && edu.toDate
                      ? `${util.getYesrBetween(edu.fromDate, edu.toDate)}${I18n.t(
                          'page_job_text_year'
                        )}`
                      : ''
                  )
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
            </View>
          </View>
          <View style={[resumePreStyle.flexDirectionRowNormal]}>
            <Text style={resumePreStyle.titleText}>
              {prof.locationId ? prof.locationId.label : ''}
              <Text style={resumePreStyle.desText}>({I18n.t('page_resume_label_now_city')})</Text>
            </Text>
            {prof.address ? <View style={resumePreStyle.separateLine} /> : <Text />}
            {prof.address ? (
              <Text style={resumePreStyle.titleText}>
                {prof.address}
                <Text style={resumePreStyle.desText}>({I18n.t('page_resume_label_city')})</Text>
              </Text>
            ) : (
              <Text />
            )}
          </View>
          {prof.mobile ? (
            <Text style={[resumePreStyle.subTitleText, resumePreStyle.phoneText]}>
              {prof.mobile ? prof.mobile : ''}
            </Text>
          ) : (
            <Text style={{ display: 'none' }} />
          )}
          {prof.email ? (
            <Text style={[resumePreStyle.subTitleText, resumePreStyle.emailText]}>
              {prof.email ? prof.email : ''}
            </Text>
          ) : (
            <Text style={{ display: 'none' }} />
          )}
        </View>
      </View>
    );
  };

  renderCareer = (resume) => {
    const career = resume.careerProfile;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_career'),
          'resumeCareer',
          !!career.careerPosition,
          true,
          career
        )}
        {career.careerPosition ? (
          <View style={resumePreStyle.cellContent}>
            <View style={resumePreStyle.likeJobAreaView}>
              <Text style={[resumePreStyle.likeJobTypeText, resumePreStyle.careerTitleView]}>
                {I18n.t('page_resume_label_career_level')}:{' '}
                <Text style={resumePreStyle.subTitleText}>
                  {career.careerLevel ? career.careerLevel.label : ''}
                </Text>
              </Text>
              <Text style={[resumePreStyle.likeJobTypeText, resumePreStyle.careerTitleView]}>
                {I18n.t('page_resume_label_career_recent')}
                {I18n.t('page_resume_label_career_pos')}:{' '}
                <Text style={resumePreStyle.subTitleText}>
                  {career.careerPosition ? career.careerPosition : ''}
                </Text>
              </Text>
              <Text style={[resumePreStyle.likeJobTypeText, resumePreStyle.careerTitleView]}>
                {I18n.t('page_resume_label_career_recent')}
                {I18n.t('page_resume_label_career_category')}:{' '}
                <Text style={resumePreStyle.subTitleText}>
                  {career.careerCategory ? career.careerCategory.label : ''}
                </Text>
              </Text>
              <Text style={[resumePreStyle.likeJobTypeText, resumePreStyle.careerTitleView]}>
                {I18n.t('page_resume_label_career_recent')}
                {I18n.t('page_resume_label_career_industry')}:{' '}
                <Text style={resumePreStyle.subTitleText}>
                  {career.careerIndustry ? career.careerIndustry.label : ''}
                </Text>
              </Text>
            </View>
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderLikeJobCenter = (list) => {
    let text = '';
    for (let i = 0, j = list.length; i < j; i++) {
      const item = list[i].label;
      text = `${text}${i !== 0 ? ` / ${item}` : item}`;
    }
    return (
      <View style={[resumePreStyle.flexDirectionRowNormal, { maxWidth: '70%' }]}>
        <Text style={resumePreStyle.likeJobTypeText} numberOfLines={1}>
          {text}
        </Text>
      </View>
    );
  };

  renderLikeJobButtom = (list) => {
    let text = '';
    for (let i = 0, j = list.length; i < j; i++) {
      const item = list[i].label;
      text = `${text}${i !== 0 ? ` / ${item}` : item}`;
    }
    return (
      <View style={resumePreStyle.flexDirectionRowNormal}>
        <Text style={[resumePreStyle.likeJobTypeText, { marginTop: 0 }]} numberOfLines={1}>
          {text}
        </Text>
      </View>
    );
  };

  renderLikeJob = (resume) => {
    const likeJob = resume.intention;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_job_like'),
          'jobIntensionAdd',
          !!likeJob.reqJobTitle,
          true,
          likeJob
        )}
        {likeJob.reqJobTitle ? (
          <View style={[resumePreStyle.cellContent]}>
            <View style={[resumePreStyle.likeJobDirectionRowBetween]}>
              <View
                style={[
                  resumePreStyle.flexDirectionRowNormal,
                  {
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  },
                ]}
              >
                {likeJob.categoryIds.length > 0 ? (
                  <Text style={[resumePreStyle.titleText, { fontSize: 14 }]}>
                    {likeJob.categoryIds[0].label}
                  </Text>
                ) : (
                  <View />
                )}
                <View
                  style={{
                    width: 10,
                    height: 1,
                    backgroundColor: desColor,
                    marginLeft: 8,
                    marginRight: 8,
                  }}
                />
                <Text
                  style={[resumePreStyle.titleText, { fontSize: 14, width: '40%' }]}
                  numberOfLines={1}
                >
                  {likeJob.reqJobTitle ? likeJob.reqJobTitle : ''}
                </Text>
                <Text style={[resumePreStyle.likeJobMoneyText, { textAlign: 'right' }]}>
                  {likeJob.salary.label ? likeJob.salary.label : ''}
                </Text>
              </View>
              <View
                style={[
                  resumePreStyle.flexDirectionRowNormal,
                  resumePreStyle.likeJobAreaView,
                  { justifyContent: 'space-between' },
                ]}
              >
                {likeJob.locationIds.length > 0 ? (
                  this.renderLikeJobCenter(likeJob.locationIds)
                ) : (
                  <View />
                )}
                <Text style={[resumePreStyle.likeJobTypeText, { marginLeft: 10 }]}>
                  {likeJob.jobTerm.label ? likeJob.jobTerm.label : ''}
                </Text>
              </View>
            </View>
            {likeJob.industryIds.length > 0 ? (
              this.renderLikeJobButtom(likeJob.industryIds)
            ) : (
              <View />
            )}
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderEducationDetail(item) {
    return (
      <View style={{ marginBottom: 20, marginLeft: 7, flex: 1 }}>
        <Text style={[resumePreStyle.timelineTimeText]}>
          {`${item.fromDate ? util.dateToYM1String(item.fromDate) : ''} - ${
            item.toDate ? util.dateToYM1String(item.toDate) : ''
          }`}
        </Text>
        <Text style={[resumePreStyle.titleText, resumePreStyle.timelineTitleText]}>
          {item.name}
        </Text>
        <Text style={[resumePreStyle.timelineSubText]}>
          {`${item.qualificationId ? item.qualificationId.label : ''} | ${
            item.major ? item.major : ''
          }`}
        </Text>
      </View>
    );
  }

  renderEducation = (resume) => {
    const edu = resume.educations
      .slice()
      .sort((item1, item2) => (item1.fromDate < item2.fromDate ? 1 : -1));
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_education'),
          'resumeEducation',
          !!(edu && edu.length > 0),
          true
        )}
        {edu && edu.length > 0 ? (
          <TimeLineList
            itemKey="eduId"
            data={edu}
            renderDetail={this.renderEducationDetail}
            conStyle={{ marginTop: 5 }}
          />
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderLanguageDetail(language) {
    const levelWidth = (deviceWidth - 120) / 5;
    return language.map((item) => (
      <View key={item.id}>
        <Text style={[resumePreStyle.timelineTimeText, { marginTop: 5 }]}>
          {item.languageId.label}
        </Text>
        <View style={[resumePreStyle.flexDirectionRowNormal]}>
          <View
            style={{
              backgroundColor: baseBlueColor,
              width: levelWidth * item.languageLevelId.value,
              height: 5,
              marginTop: 3,
            }}
          />
          <Text style={{ color: desColor, fontSize: 10, marginLeft: 3 }}>
            {item.languageLevelId.label}
          </Text>
        </View>
      </View>
    ));
  }

  renderLanguage = (resume) => {
    const language = resume.languageLevels;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_language'),
          'resumeLanguage',
          !!(language && language.length > 0),
          true
        )}
        {language && language.length > 0 ? (
          <View style={[resumePreStyle.cellContent, { marginTop: 3 }]}>
            {this.renderLanguageDetail(language)}
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderJobDetail(item) {
    return (
      <View
        style={{
          marginBottom: 20,
          marginLeft: 7,
          marginRight: 18,
          flex: 1,
        }}
      >
        <Text style={[resumePreStyle.timelineTimeText]}>
          {`${item.fromDate ? util.dateToYM1String(item.fromDate) : ''} - ${util.dateToYM1String(
            item.toDate
          )}`}
        </Text>
        <Text style={[resumePreStyle.titleText, resumePreStyle.timelineTitleText]}>
          {item.company ? item.company : ''}
        </Text>
        <Text style={[resumePreStyle.timelineSubText]}>{item.title ? item.title : ''}</Text>
        <Text style={[resumePreStyle.timelineDesText]}>
          {item.description ? item.description : ''}
        </Text>
      </View>
    );
  }

  renderJob = (resume) => {
    const exp = resume.experiences
      .slice()
      .sort((item1, item2) => (item1.fromDate < item2.fromDate ? 1 : -1));
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_work_exp'),
          'resumeWork',
          !!(exp && exp.length > 0),
          true
        )}
        {exp && exp.length > 0 ? (
          <TimeLineList
            itemKey="experienceId"
            data={exp}
            renderDetail={this.renderJobDetail}
            conStyle={{ marginTop: 5 }}
          />
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderSkillInfo(item) {
    return (
      <View
        style={[
          resumePreStyle.flexDirectionRowNormal,
          {
            marginLeft: 7,
            marginRight: 18,
            flex: 1,
            justifyContent: 'space-between',
            marginTop: 8,
          },
        ]}
      >
        <Text style={[resumePreStyle.titleText, { maxWidth: '80%' }]}>
          {item.name ? item.name : ''}
        </Text>
        <Text
          style={[
            resumePreStyle.timelineSubText,
            { marginTop: 0, marginLeft: 20, textAlign: 'right' },
          ]}
        >
          {item.years ? item.years : 0}
          {I18n.t('page_job_text_year')}
        </Text>
      </View>
    );
  }

  renderSkill = (resume) => {
    const skill = resume.skills;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_skill'),
          'resumeSkill',
          !!(skill && skill.length > 0),
          true
        )}
        {skill && skill.length > 0 ? (
          <TimeLineList
            itemKey="id"
            data={skill}
            renderDetail={this.renderSkillInfo}
            hiddenLine
            conStyle={{ marginTop: 5, marginBottom: 15 }}
          />
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderTraining = (resume) => {
    const tra = resume.training;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(I18n.t('page_resume_label_training'), 'resumeTraining', !!tra)}
        {tra ? (
          <View style={resumePreStyle.cellContent}>
            <ExpandableText
              numberOfLines={2}
              style={[resumePreStyle.timelineDesText, resumePreStyle.appraiseText]}
              unexpandView={() => null}
              expandView={() => (
                <Text style={resumePreStyle.readeMoreText}>{I18n.t('page_resume_label_more')}</Text>
              )}
            >
              {tra}
            </ExpandableText>
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderHobby = (resume) => {
    const hob = resume.hobby;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(I18n.t('page_resume_label_hobby'), 'resumeHobby', !!hob)}
        {hob ? (
          <View style={resumePreStyle.cellContent}>
            <ExpandableText
              numberOfLines={2}
              style={[resumePreStyle.timelineDesText, resumePreStyle.appraiseText]}
              unexpandView={() => null}
              expandView={() => (
                <Text style={resumePreStyle.readeMoreText}>{I18n.t('page_resume_label_more')}</Text>
              )}
            >
              {hob}
            </ExpandableText>
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderqualificationInfo(item) {
    return (
      <View
        style={[
          resumePreStyle.flexDirectionRowNormal,
          {
            marginBottom: 20,
            marginLeft: 7,
            flex: 1,
            marginRight: 15,
          },
        ]}
      >
        <View style={{ flex: 1 }}>
          <Text style={[resumePreStyle.timelineTimeText]}>
            {item.obtained ? item.obtained.toString() : ''}
          </Text>
          <Text style={[resumePreStyle.titleText, resumePreStyle.timelineTitleText]}>
            {item.name ? item.name : ''}
          </Text>
          <Text style={[resumePreStyle.timelineSubText]}>{item.issued ? item.issued : ''}</Text>
        </View>
        <Image
          source={{ uri: item.photo }}
          style={{
            marginTop: 10,
            width: 70,
            height: 70,
            backgroundColor: bgColor,
          }}
        />
      </View>
    );
  }

  renderQualification = (resume) => {
    const qua = resume.qualifications;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(
          I18n.t('page_resume_label_qualification'),
          'resumeCertificate',
          !!(qua && qua.length > 0)
        )}
        {qua && qua.length > 0 ? (
          <TimeLineList
            itemKey="id"
            data={qua}
            renderDetail={this.renderqualificationInfo}
            conStyle={{ marginTop: 5 }}
          />
        ) : (
          <View />
        )}
      </View>
    );
  };

  renderAppraise = (resume) => {
    const des = resume.description;
    return (
      <View style={resumePreStyle.cell}>
        {this.renderCellHeader(I18n.t('page_resume_label_appraise'), 'resumeAppraise', !!des)}
        {des ? (
          <View style={resumePreStyle.cellContent}>
            <ExpandableText
              numberOfLines={2}
              style={[resumePreStyle.timelineDesText, resumePreStyle.appraiseText]}
              unexpandView={() => null}
              expandView={() => (
                <Text style={resumePreStyle.readeMoreText}>{I18n.t('page_resume_label_more')}</Text>
              )}
            >
              {des}
            </ExpandableText>
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };

  render() {
    const { isHome, resume, resumeList, index } = this.props;
    return (
      <View style={{ ...globalStyle.container, width: deviceWidth, height: '100%' }}>
        <ScrollView>
          {isHome ? this.renderHeader(resume, resumeList, index) : <View />}
          {this.renderInfo(resume, isHome)}
          {this.renderCareer(resume, isHome)}
          {this.renderLikeJob(resume, isHome)}
          {this.renderEducation(resume, isHome)}
          {this.renderLanguage(resume, isHome)}
          {this.renderJob(resume, isHome)}
          {this.renderSkill(resume, isHome)}
          {this.renderTraining(resume, isHome)}
          {this.renderHobby(resume, isHome)}
          {this.renderQualification(resume, isHome)}
          {this.renderAppraise(resume, isHome)}
        </ScrollView>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}

export default ResumeItem;
