import React, { Component } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { inject, observer } from 'mobx-react';
// import { View } from 'react-navigation';
import { Header } from 'react-native-elements';
import GoBack from '../components/goback';
import { headerStyle, globalStyle } from '../themes';
import I18n from '../i18n';

@inject('stores')
@observer
export default class Page extends Component {
  render() {
    const { navigation } = this.props;
    return (
      <View style={globalStyle.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          leftComponent={<GoBack navigation={navigation} />}
          centerComponent={{ text: I18n.t('page_about_title'), style: headerStyle.center }}
        />
        <ScrollView style={{ padding: 15 }}>
          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              paddingTop: 10,
              paddingBottom: 5,
            }}
          >
            CamHR Profile
          </Text>
          <Text style={{ fontSize: 14 }}>
            CAMHR is Cambodia’s leading human resources and Recruitment Company, specializing in
            providing human capital resources and assets. Launched in 2009 by CAMHR INFORMATION
            (CAMBODIA) CO. LTD, we have grown from specializing in online recruitment to offline,
            sourcing employees of all standards for businesses and organizations across the country.
          </Text>

          <Text style={{ fontSize: 14, marginTop: 10 }}>
            Our success and expertise has led to thousands of jobseekers using our services every
            month to search the more than 5,000 live job ads, and our shift to focus on providing
            companies with a tool to recruit the best staff possible has seen CAMHR become
            Cambodia’s elite recruitment agency.
          </Text>

          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              paddingTop: 10,
              paddingBottom: 5,
            }}
          >
            Our Vision
          </Text>
          <Text style={{ fontSize: 14 }}>The leading HR solutions provider in Cambodia.</Text>

          <Text
            style={{
              fontSize: 16,
              fontWeight: 'bold',
              paddingTop: 10,
              paddingBottom: 5,
            }}
          >
            Our Mission
          </Text>
          <Text style={{ fontSize: 14, marginBottom: 50 }}>
            To provide competent and professional staff to deliver quality service and achieve our
            clients' goals. 
          </Text>
        </ScrollView>
      </View>
    );
  }
}
