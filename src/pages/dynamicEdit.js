import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { inject, observer } from 'mobx-react';
import { Text, View, TextInput, TouchableOpacity, ScrollView, Keyboard } from 'react-native';
import { Header, Icon, CheckBox } from 'react-native-elements';
import ImagePicker from 'react-native-image-crop-picker';
import ActionSheet from 'react-native-actionsheet';
import { dynamicStyle, globalStyle, headerStyle, bgColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import LoadingModal from '../components/loadingModal';
import util from '../util';
import Image from '../components/image';

@inject('dynamicStore', 'dynamicAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.showLoading = false;
    this.state = {
      msgText: '',
      disabledPublish: false,
      checked: false,
      imageList: [],
      count: 1000,
      postData: {
        anonymous: 0,
        content: '',
        images: [],
      },
    };
  }

  componentDidMount() {
    global.emitter.on('hideLoading', this.onHideLoading);
  }

  componentWillUnmount() {
    global.emitter.off('hideLoading', this.onHideLoading);
  }

  onHideLoading = () => {
    this.showLoading = false;
  };

  publish = async () => {
    try {
      Keyboard.dismiss();
      const temp = Object.assign({}, this.state.postData);
      temp.anonymous = this.state.checked ? 0 : 1;
      temp.content = this.state.msgText;
      if (!temp.content && temp.images.length === 0) {
        this.toast.show(I18n.t('page_dynamic_content_not_be_null'));
        return;
      }
      this.showLoading = true;
      this.setState({ disabledPublish: true });
      const data = await this.props.dynamicAction.publishDynamic(temp);
      if (data && data.successful) {
        this.toast.show(data.message);
        if (this.props.navigation.state.params) {
          this.props.navigation.state.params.onSelect();
        }
        this.showLoading = false;
        this.setState({ disabledPublish: false });
        setTimeout(() => {
          this.props.navigation.goBack();
        }, 1000);
      } else {
        this.showLoading = false;
        this.setState({ disabledPublish: false });
        this.toast.show(data && data.message);
      }
    } catch (error) {
      console.log('出错了', error);
      this.showLoading = false;
      this.setState({ disabledPublish: false });
    }
  };

  checkBoxPress = () => {
    this.setState({ checked: !this.state.checked });
  };

  show = () => {
    this.ActionSheet.show();
  };

  actionSheetSelect = (index) => {
    if (index === 0) {
      this.pickSingleWithCamera();
    } else if (index === 1) {
      this.openPicLib();
    }
  };

  uploadMore(imgArray) {
    const form = new FormData();
    for (let i = 0; i < imgArray.length; i += 1) {
      const file = { uri: imgArray[i].uri, type: 'multipart/form-data', name: 'image.jpg' };
      form.append('images', file);
    }
    this.props.dynamicAction.uploadDynamicImage(form).then((res) => {
      if (res && res.data) {
        const temp = this.state.postData;
        temp.images = res.data.map((item) => item.http);
        this.setState({
          postData: temp,
          imageList: this.state.imageList.concat(res.data.map((item) => item.http)),
        });
      }
    });
  }

  upload(image) {
    const form = new FormData();
    const file = { uri: image.path, type: 'multipart/form-data', name: 'image.jpg' };
    form.append('images', file);
    this.props.dynamicAction.uploadDynamicImage(form).then((res) => {
      if (res && res.data) {
        const temp = this.state.postData;
        temp.images = temp.images.concat(res.data.map((item) => item.http));
        this.setState({
          postData: temp,
          imageList: this.state.imageList.concat(res.data.map((item) => item.http)),
        });
      }
    });
  }

  pickSingleWithCamera = () => {
    ImagePicker.openCamera({
      cropping: true,
      width: 750,
      height: 750,
    })
      .then((image) => {
        this.upload(image);
      })
      .catch((e) => console.log(e));
  };

  previewDetail = (i) => {
    const { navigation } = this.props;
    const imgArr = [];
    this.state.imageList.forEach((item) => {
      imgArr.push({ url: item });
    });
    navigation.navigate('previewImage', { images: imgArr, index: i });
  };

  openPicLib = () => {
    const dataToPost = [];
    if (global.IS_IOS) {
      ImagePicker.openPicker({
        multiple: true,
        waitAnimationEnd: false,
        cropping: true,
        cropperCircleOverlay: false,
        compressImageMaxWidth: 750,
        compressImageMaxHeight: 750,
        compressImageQuality: 0.8,
        mediaType: 'photo',
        maxFiles: 9 - this.state.imageList.length,
      })
        .then((images) => {
          for (let i = 0; i < images.length; i += 1) {
            dataToPost.push({
              uri: images[i].path,
              width: images[i].width,
              height: images[i].height,
              mime: images[i].mime,
            });
          }
          this.uploadMore(dataToPost);
        })
        .catch((e) => console.log(e));
    } else {
      ImagePicker.openPicker({
        cropping: true,
        cropperCircleOverlay: false,
        compressImageQuality: 1,
        width: 750,
        height: 750,
        mediaType: 'photo',
      })
        .then((image) => {
          this.upload(image);
        })
        .catch((e) => console.log(e));
    }
  };

  deleteImage = (index) => {
    const imgA = this.state.imageList;
    imgA.splice(index, 1);
    const temp = Object.assign({}, this.state.postData);
    temp.images.splice(index, 1);
    this.setState({
      imageList: imgA,
      postData: temp,
    });
  };

  createImageViews() {
    const { imageList } = this.state;
    let mainView;
    if (this.state.imageList != null && this.state.imageList.length >= 9) {
      mainView = null;
    } else {
      mainView = (
        <TouchableOpacity onPress={this.show}>
          <View
            style={
              (imageList.length + 1) % 3 === 0
                ? dynamicStyle.editImageBackView
                : dynamicStyle.editImageBackViewRight
            }
          >
            <Icon name="camera" type="entypo" color="#999" size={36} />
          </View>
        </TouchableOpacity>
      );
    }
    return (
      <View style={dynamicStyle.eidtImageContent}>
        {this.state.imageList.map((item, i) => (
          <TouchableOpacity
            style={
              (i + 1) % 3 === 0
                ? dynamicStyle.editImageContainerRight
                : dynamicStyle.editImageContainer
            }
            key={`${item}`}
            onPress={() => this.previewDetail(i)}
          >
            <View style={dynamicStyle.deletIcon}>
              <Icon
                name="ios-close-circle"
                type="ionicon"
                onPress={() => this.deleteImage(i)}
                color="#F6A46B"
                size={24}
              />
            </View>
            <Image source={{ uri: item }} style={[dynamicStyle.editImage]} />
          </TouchableOpacity>
        ))}
        {mainView}
      </View>
    );
  }

  render() {
    const { navigation } = this.props;
    const { disabledPublish } = this.state;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: I18n.t('page_dynamicedit_text_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              disabled={disabledPublish}
              onPress={() =>
                util.HandlerOnceTap(() => {
                  this.publish();
                })
              }
            >
              <Text style={[headerStyle.rightBtn, { color: disabledPublish ? bgColor : '#fff' }]}>
                {I18n.t('page_dynamicedit_btn_publishdynamic')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={dynamicStyle.editContent}>
            <View style={{ backgroundColor: '#fff' }}>
              <TextInput
                style={[dynamicStyle.inputText, dynamicStyle.editInputText]}
                blurOnSubmit={false}
                placeholder={I18n.t('page_dynamic_ph_dynamic')}
                underlineColorAndroid="transparent"
                multiline
                maxLength={1000}
                ref={(ref) => {
                  this.inputText = ref;
                }}
                onChangeText={(text) => {
                  this.setState({
                    count: 1000 - text.length,
                    msgText: text,
                  });
                }}
                textAlignVertical="top"
              />
              <Text style={dynamicStyle.editTextCount}>
                {I18n.t('page_dynamicedit_tips_textcount1')}
                <Text style={dynamicStyle.numberCount}>{this.state.count}</Text>
                {I18n.t('page_dynamicedit_tips_textcount2')}
              </Text>
            </View>
            <View style={[dynamicStyle.imageContainer]}>{this.createImageViews()}</View>
            <View style={dynamicStyle.line} />
            <View style={[dynamicStyle.editInfoContent, { flexShrink: 100 }]}>
              <CheckBox
                center
                title={I18n.t('page_comment_text_anonymous')}
                textStyle={
                  this.state.checked
                    ? dynamicStyle.inputBtnNiChecked
                    : dynamicStyle.inputBtnNiNormal
                }
                containerStyle={[dynamicStyle.inputBtnNiContent, dynamicStyle.editheaderBg]}
                checked={this.state.checked}
                onPress={this.checkBoxPress}
              />
              {this.state.checked ? (
                <Text style={[dynamicStyle.inputBtnSend, { flexShrink: 100 }]}>
                  {I18n.t('page_dynamicedit_tips_anonymous')}
                </Text>
              ) : null}
            </View>
          </View>
        </ScrollView>
        <ActionSheet
          ref={(ref) => {
            this.ActionSheet = ref;
          }}
          title={I18n.t('page_dynamic_tips_image_select')}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.actionSheetSelect}
        />
        <LoadingModal isOpen={this.showLoading} loadingTips={false} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
