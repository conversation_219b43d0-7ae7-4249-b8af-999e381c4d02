import React, { Component } from 'react';
import { Button, View } from 'react-native';
import { Header } from 'react-native-elements';
import I18n from '../i18n';
import { baseBlueColor, bgColor, headerStyle, resourcesStyle } from '../themes';
import StatusBar from '../components/statusBar';
import ScrollableHorizontalTabView from '../components/scrollableHorizontalTabView';
import ResourcesList from '../components/resourcesList';
import GoBack from '../components/goback';

/**
 * 职业顾问
 */

export default class Resources extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {}

  renderTabs = () => {
    const { navigation } = this.props;
    return (
      <View style={resourcesStyle.typeContainer}>
        <ScrollableHorizontalTabView
          tabBarBackgroundColor="#ffffff"
          tabBarInactiveTextColor="#8E96A3"
          tabBarActiveTextColor={baseBlueColor}
          underlineColor={baseBlueColor}
          tabBarStyle={resourcesStyle.tabBarStyle}
          tabBarTextStyle={resourcesStyle.tabBarTextStyle}
          activeTabTextStyle={resourcesStyle.activeTabTextStyle}
          tabStyles={resourcesStyle.tabStyles}
          scrollableTabBar={true}
          initialPage={0}
          isHorizontalScroll
          underlineHeight={4}
          underlineBottomPosition={5}
          underlineBottomWidth={0.5}
        >
          <ResourcesList
            key="0"
            filterType="0"
            nav={navigation}
            tabLabel={{ label: I18n.t('page_adviser_tab_resume') }}
          />
          <ResourcesList
            key="1"
            filterType="1"
            nav={navigation}
            tabLabel={{ label: I18n.t('page_adviser_tab_resumedemo') }}
          />
          <ResourcesList
            key="2"
            filterType="2"
            nav={navigation}
            tabLabel={{ label: I18n.t('page_adviser_tab_interview') }}
          />
          <ResourcesList
            key="3"
            filterType="3"
            nav={navigation}
            tabLabel={{ label: I18n.t('page_adviser_tab_career') }}
          />
          <ResourcesList
            key="4"
            filterType="4"
            nav={navigation}
            tabLabel={{ label: I18n.t('page_adviser_tab_FAQ') }}
          />
        </ScrollableHorizontalTabView>
      </View>
    );
  };

  render() {
    return (
      <View style={{ flex: 1, backgroundColor: bgColor }}>
        {/* <StatusBar containerStyle={{ height: 0 }} /> */}
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          leftComponent={<GoBack iconColor="white" navigation={this.props.navigation} />}
          containerStyle={resourcesStyle.header}
          outerContainerStyles={{ borderBottomWidth: 0 }}
          centerComponent={{
            text: I18n.t('page_guide_text_consultant'),
            style: headerStyle.center,
          }}
        />
        {this.renderTabs()}
      </View>
    );
  }
}
