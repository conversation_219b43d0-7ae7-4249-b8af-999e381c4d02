/**
 * 功能：上传附件
 * 描述：
 * Author:孙宇强
 */
import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity, Image, Alert } from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import ActionSheet from 'react-native-actionsheet';
import ImagePicker from 'react-native-image-crop-picker';
import { Header } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import DeviceInfo from '../util/deviceInfo';
import { globalStyle, headerStyle, uploadAnnexStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import resIcon from '../res';
import LoadingModal from '../components/loadingModal';
import constant from '../store/constant';

@inject('userAction', 'resumeAction')
@observer
export default class UploadAnnex extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLoading: false,
    };
  }

  onCameraUpload = () => {
    ImagePicker.openCamera({
      cropping: true,
      width: 400,
      height: 400,
    })
      .then((image) => {
        this.onImageUpload(image);
      })
      .catch((e) => console.log(e));
  };

  onAlbumUpload = () => {
    ImagePicker.openPicker({
      cropping: true,
      width: 400,
      height: 400,
    })
      .then((image) => {
        this.onImageUpload(image);
      })
      .catch((e) => console.log(e));
  };

  transformFileType = (fileType) => {
    if (fileType === constant.resumeFileUploadType.pdf) {
      return constant.resumeFileType.pdf;
    }
    if (fileType === constant.resumeFileUploadType.doc) {
      return constant.resumeFileType.doc;
    }
    if (fileType === constant.resumeFileUploadType.docx) {
      return constant.resumeFileType.docx;
    }
    if (fileType === constant.resumeFileUploadType.png) {
      return constant.resumeFileType.png;
    }
    if (fileType === constant.resumeFileUploadType.jpg) {
      return constant.resumeFileType.jpg;
    }
    return '';
  };

  /**
   * 上传文件
   * 格式限制为jpg、png、pdf、doc、docx
   * iOS和部分Android机型能获取到uri、type、name、size
   * 部分Android机型只能获取到uri；name和type根据uri来截取
   * 部分Android机型只能获取到uri、size、type；name使用默认
   */
  onFileUpload = async () => {
    try {
      let res = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
      });
      console.log('onFileUpload', res, res.name, res.uri, res.type, res.size);
      if (Array.isArray(res) && res.length) {
        res = res[0];
      }
      if (res && res.name) {
        const typeIndex = res.name.lastIndexOf('.');
        const fileType = res.name.substring(typeIndex + 1, res.name.length);
        if (
          fileType === constant.resumeFileType.pdf ||
          fileType === constant.resumeFileType.doc ||
          fileType === constant.resumeFileType.docx ||
          fileType === constant.resumeFileType.png ||
          fileType === constant.resumeFileType.jpg
        ) {
          if (res.size > 4 * 1024 * 1024) {
            this.showSizeError();
          } else {
            const form = new FormData();
            const file = {
              uri: res.uri,
              type: 'multipart/form-data',
              name: encodeURIComponent(decodeURIComponent(res.name)),
            };

            form.append('file', file);
            this.onUpload(form);
          }
        } else {
          this.showTypeError();
        }
      } else if (res && res.type) {
        if (
          res.type === constant.resumeFileUploadType.pdf ||
          res.type === constant.resumeFileUploadType.doc ||
          res.type === constant.resumeFileUploadType.docx ||
          res.type === constant.resumeFileUploadType.png ||
          res.type === constant.resumeFileUploadType.jpg
        ) {
          const fileType = this.transformFileType(res.type);
          const fileName = `${I18n.t('menu_nav_bottom_resume')}.${fileType}`;
          if (res.size > 4 * 1024 * 1024) {
            this.showSizeError();
          } else {
            const form = new FormData();
            const file = {
              uri: res.uri,
              type: 'multipart/form-data',
              name: encodeURIComponent(decodeURIComponent(fileName)),
            };
            form.append('file', file);
            this.onUpload(form);
          }
        } else {
          this.showTypeError();
        }
      } else {
        const fileNameIndex = res.uri.lastIndexOf('/');
        const typeIndex = res.uri.lastIndexOf('.');
        const fileName = res.uri.substring(fileNameIndex + 1, res.uri.length);
        const fileType = res.uri.substring(typeIndex + 1, res.uri.length);
        if (
          fileType === constant.resumeFileType.pdf ||
          fileType === constant.resumeFileType.doc ||
          fileType === constant.resumeFileType.docx ||
          fileType === constant.resumeFileType.png ||
          fileType === constant.resumeFileType.jpg
        ) {
          if (res.size > 4 * 1024 * 1024) {
            this.showSizeError();
          } else {
            const form = new FormData();
            const file = {
              uri: res.uri,
              type: 'multipart/form-data',
              name: encodeURIComponent(decodeURIComponent(fileName)),
            };

            form.append('file', file);
            this.onUpload(form);
          }
        } else {
          this.showTypeError();
        }
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        throw err;
      }
    }
  };

  showTypeError = () => {
    Alert.alert(
      I18n.t('page_resume_annex_type_error_title'),
      I18n.t('page_resume_annex_type_error_content'),
      [
        {
          text: I18n.t('page_resume_annex_type_error_ok'),
          onPress: () => {},
        },
      ]
    );
  };

  showSizeError = () => {
    Alert.alert(
      I18n.t('page_resume_annex_type_error_title'),
      I18n.t('page_resume_annex_size_content'),
      [
        {
          text: I18n.t('page_resume_annex_type_error_ok'),
          onPress: () => {},
        },
      ]
    );
  };

  onImageUpload = (image) => {
    let name = 'image.jpg';
    if (global.IS_IOS) {
      name = image.filename;
    }
    const form = new FormData();
    const file = {
      uri: IS_IOS ? `file:///${image.path}` : image.path,
      type: 'multipart/form-data',
      name,
    };
    form.append('file', file);
    this.onUpload(form);
  };

  onUpload = (form) => {
    this.setState({ showLoading: true });
    this.props.resumeAction.addAnnexResume(form).then(
      (res) => {
        this.setState({ showLoading: false });
        this.props.resumeAction.getAnnexResumes();
        if (res && res.successful) {
          this.toast.show(res.message);
          setTimeout(() => {
            this.props.navigation.goBack();
          }, 1000);
        } else {
          this.toast.show(res.message || I18n.t('page_resume_annex_size_content'));
        }
      },
      (err) => {
        this.setState({ showLoading: false });
        this.toast.show((err && err.message) || I18n.t('page_resume_annex_size_content'));
      }
    );
  };

  onActionSheet = (index) => {
    if (index === 0) {
      this.onCameraUpload();
    } else if (index === 1) {
      this.onAlbumUpload();
    } else if (index === 2) {
      if (global.IS_IOS) {
        if (parseFloat(DeviceInfo.getSystemVersion(), 10) < 11) {
          this.toast.show(I18n.t('page_resume_annex_version_error'));
          return;
        }
      }
      this.onFileUpload();
    }
  };

  renderMiddle = () => {
    return (
      <View>
        <TouchableOpacity
          style={uploadAnnexStyle.uploadContainer}
          onPress={() => this.props.navigation.navigate('scanUpload')}
        >
          <Image style={uploadAnnexStyle.imageContainer} source={resIcon.resumeScan} />
          <Text style={uploadAnnexStyle.uploadText}>{I18n.t('page_resume_annex_scan')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={uploadAnnexStyle.uploadContainer}
          onPress={() => this.actionSheet.show()}
        >
          <Image style={uploadAnnexStyle.imageContainer} source={resIcon.resumePhone} />
          <Text style={uploadAnnexStyle.uploadText}>{I18n.t('page_resume_annex_phone')}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderBottom = () => {
    return (
      <View style={uploadAnnexStyle.bottomContainer}>
        <Text style={uploadAnnexStyle.remarkText}>{I18n.t('page_resume_annex_tips')}</Text>
      </View>
    );
  };

  render() {
    const { navigation } = this.props;
    const { showLoading } = this.state;
    return (
      <View style={{ ...globalStyle.container, flex: 1, backgroundColor: '#fff' }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_annex_button_upload'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
        />
        <View style={uploadAnnexStyle.topContainer}>
          <Text style={uploadAnnexStyle.titleText}>{I18n.t('page_resume_annex_select')}</Text>
          <Text style={uploadAnnexStyle.subTitleText}>{I18n.t('page_resume_annex_support')}</Text>
          {this.renderMiddle()}
        </View>
        {this.renderBottom()}
        <ActionSheet
          title={I18n.t('page_scan_upload_actionSheet_title')}
          ref={(ref) => {
            this.actionSheet = ref;
          }}
          options={[
            I18n.t('page_scan_upload_actionSheet_camera'),
            I18n.t('page_scan_upload_actionSheet_album'),
            I18n.t('page_scan_upload_actionSheet_file'),
            I18n.t('page_resume_annex_cancel'),
          ]}
          cancelButtonIndex={3}
          onPress={this.onActionSheet}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={showLoading} loadingTips={false} />
      </View>
    );
  }
}
