import React, { Component } from 'react';
import { Text, View, FlatList, TouchableOpacity, InteractionManager } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Header } from 'react-native-elements';
import { globalStyle, headerStyle, messageStyle, desColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import res from '../res';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';
import userService from '../api/userService';
import jobService from '../api/jobService';
import moment from 'moment';
import constant from '../store/constant';
import sendMessageUtil from '../database/sendMessageUtil';

export const commonStyle = {
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#666',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    backgroundColor: '#fff',
    position: 'relative', // 保证 badge 绝对定位基于 tab
  },
  activeTab: {
    backgroundColor: '#379ceb',
    borderColor: '#f00',
  },
  tabText: {
    fontSize: 14,
  },
  activeTabText: {
    color: '#fff',
  },
  badge: {
    position: 'absolute',
    right: 5,
    width: 19,
    height: 19,
    top: '50%',
    marginTop: -10, // 负的badge高度的一半
    borderRadius: 10,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
  },
  pushItem: {
    borderBottomWidth: 0.5,
    borderBottomColor: '#999',
  },
  pushContent: {
    fontSize: 14,
    color: '#333',
  },
  pushCreatedAt: {
    fontSize: 12,
    color: '#333',
  },
};

@inject('messageStore', 'userAction')
@observer
export default class Message extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.page = 1;
    this.state = {
      showLoading: false,
      hasMore: false,
      hasMorePush: false,
      pushMessageList: [],
      refreshing: true,
      activeTab: 'jobPush', // 默认选中职位推送消息
      showTgModal: false,
      currentSession: null,
    };
  }

  componentDidMount() {
    InteractionManager.runAfterInteractions(() => {
      this.onRefreshPushMessage();
    });
  }

  componentWillUnmount() {}

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.page = 1;
    await this.getMessageInbox();
    this.setState({ refreshing: false });
  };

  onLoadMore = async () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.page += 1;
      this.isLoadingMore = true;
      await this.getMessageInbox();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  getMessageInbox = async () => {
    try {
      await this.props.userAction.queryMessageInbox({
        page: this.page,
        size: 10,
        topics: ['notification'],
      });
      this.props.userAction.statsMessage();
      const { list, totalCount } = this.props.messageStore;
      this.setState({ hasMore: list.length < parseFloat(totalCount) });
      this.setState({ showLoading: false });
    } catch (error) {
      this.setState({ showLoading: false, refreshing: false });
    }
  };

  // 处理推送消息数据
  handlePushMessageData = (messages) => {
    return messages.map((item) => {
      return {
        ...item,
        createAt: item.createAt
          ? moment(parseFloat(item.createAt) * 1000).format('YYYY-MM-DD HH:mm:ss')
          : '',
      };
    });
  };

  onRefreshPushMessage = async () => {
    try {
      this.setState({ refreshing: true });
      const res = await userService.queryNewJobMessage({ limit: 10 });
      this.props.userAction.statsMessage();
      const formattedMessages = this.handlePushMessageData(res);

      this.setState({
        pushMessageList: formattedMessages,
        refreshing: false,
        hasMorePush: res.length >= 10,
      });
    } catch (error) {
      this.setState({ refreshing: false });
    }
  };

  onLoadMorePushMessage = async () => {
    try {
      if (!this.state.hasMorePush || this.isLoadingMore) return;

      this.isLoadingMore = true;
      const { pushMessageList } = this.state;
      const beginSeqId =
        pushMessageList.length > 0 ? pushMessageList[pushMessageList.length - 1].id : 0;

      const res = await userService.queryNewJobMessage({ limit: 10, beginSeqId });
      const formattedMessages = this.handlePushMessageData(res);

      this.isLoadingMore = false;
      this.setState({
        pushMessageList: this.state.pushMessageList.concat(formattedMessages),
        hasMorePush: res.length >= 10,
      });
    } catch (error) {
      this.isLoadingMore = false;
      this.setState({ refreshing: false });
    }
  };

  goDetail = async (item) => {
    this.props.navigation.navigate('messageDetails', { details: item });
    this.props.userAction.statsMessage();
  };

  goJobDetail = async (item) => {
    try {
      const { navigation } = this.props;
      if (item.jobId) {
        this.setState({ showLoading: true });
        await jobService.adJobClick(item?.clickData);
        await userService.clearNewJobMessageBadge();
        this.props.userAction.statsMessage();
        this.setState({ showLoading: false }, () => {
          navigation.navigate('jobDetail', { detail: { ...item, id: item.jobId } });
        });
      }
    } catch (error) {
      this.setState({ showLoading: false });
    }
  };

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderPushMessageItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        this.goJobDetail(item);
      }}
    >
      <View style={commonStyle.pushItem}>
        <View style={messageStyle.contentWrap}>
          <Text style={commonStyle.pushContent} numberOfLines={2}>
            {item.content}
          </Text>
        </View>
        <View style={messageStyle.footer}>
          <Text style={commonStyle.pushCreatedAt}>{item.createAt}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderMessageList = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        this.goDetail(item);
      }}
    >
      <View style={messageStyle.item}>
        {item.cover && (
          <View style={messageStyle.coverWrap}>
            <Image style={messageStyle.cover} source={{ uri: item.cover }} />
          </View>
        )}
        <View style={messageStyle.contentWrap}>
          <Text style={messageStyle.content} numberOfLines={2}>
            {item.subject}
          </Text>
        </View>
        <View style={messageStyle.footer}>
          <Text style={messageStyle.createdAt}>{item.sendAt}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderFooter = (hasMore) => {
    if (hasMore) {
      return (
        <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
          {I18n.t('page_message_text_loading')}
        </Text>
      );
    }
    const { activeTab, pushMessageList } = this.state;
    const { list } = this.props.messageStore;
    const currentList = activeTab === 'jobPush' ? pushMessageList : list;
    if (currentList && currentList.length > 0) {
      return (
        <Text style={{ textAlign: 'center', paddingVertical: 12, color: '#999' }}>
          {I18n.t('list_empty_data')}
        </Text>
      );
    }
    return null;
  };

  renderTabs = () => {
    const { activeTab } = this.state;
    const { newPushMessageCount } = this.props.messageStore;
    return (
      <View style={commonStyle.tabsContainer}>
        <TouchableOpacity
          style={[commonStyle.tab, activeTab === 'jobPush' && commonStyle.activeTab]}
          onPress={() => this.setState({ activeTab: 'jobPush' })}
        >
          <Text style={[commonStyle.tabText, activeTab === 'jobPush' && commonStyle.activeTabText]}>
            {I18n.t('page_mine_push_job_msg')}
          </Text>
          {newPushMessageCount > 0 && (
            <View style={commonStyle.badge}>
              <Text style={commonStyle.badgeText}>{newPushMessageCount}</Text>
            </View>
          )}
        </TouchableOpacity>
        <View style={{ width: 1, backgroundColor: '#666' }} />
        <TouchableOpacity
          style={[commonStyle.tab, activeTab === 'system' && commonStyle.activeTab]}
          onPress={() => {
            this.setState({ activeTab: 'system' }, () => {
              this.onRefresh();
            });
          }}
        >
          <Text style={[commonStyle.tabText, activeTab === 'system' && commonStyle.activeTabText]}>
            {I18n.t('page_mine_system_msg')}
          </Text>
          {this.props.messageStore.unread > 0 && (
            <View style={commonStyle.badge}>
              <Text style={commonStyle.badgeText}>{this.props.messageStore.unread}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  renderAllView = () => {
    const { list } = this.props.messageStore;
    const { activeTab, pushMessageList, hasMorePush, hasMore } = this.state;
    if (activeTab === 'jobPush') {
      return (
        <View style={[messageStyle.list, { backgroundColor: '#ffffff' }]}>
          <FlatList
            data={pushMessageList.slice()}
            initialNumToRender={1}
            ListEmptyComponent={this.renderEmptyComponent}
            renderItem={this.renderPushMessageItem}
            keyExtractor={(item, index) => index + item}
            showsVerticalScrollIndicator={false}
            horizontal={false}
            ListFooterComponent={() => this.renderFooter(hasMorePush)}
            refreshing={this.state.refreshing}
            onRefresh={this.onRefreshPushMessage}
            onEndReachedThreshold={0.1}
            onEndReached={() => this.onLoadMorePushMessage()}
          />
        </View>
      );
    }

    return (
      <View style={[globalStyle.container, messageStyle.container]}>
        <View style={messageStyle.list}>
          <FlatList
            data={list.slice()}
            initialNumToRender={1}
            ListEmptyComponent={this.renderEmptyComponent}
            renderItem={this.renderMessageList}
            keyExtractor={(item, index) => index + item}
            showsVerticalScrollIndicator={false}
            horizontal={false}
            ListFooterComponent={() => this.renderFooter(hasMore)}
            refreshing={this.state.refreshing}
            onRefresh={this.onRefresh}
            onEndReachedThreshold={0.1}
            onEndReached={() => this.onLoadMore()}
          />
        </View>
      </View>
    );
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={messageStyle.page}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_message_text_message_notice'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderTabs()}
        {this.renderAllView()}
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />

        {/* Telegram账号输入弹窗 */}
      </View>
    );
  }
}
