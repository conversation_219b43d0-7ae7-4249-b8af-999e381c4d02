import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  View,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Text,
} from 'react-native';
import { I<PERSON>, Header } from 'react-native-elements';
import { globalStyle, headerStyle } from '../themes';
import ResumeItem from './resumeItem';
import Session from '../api/session';
import I18n from '../i18n';
import res from '../res';
import Image from '../components/image';
import { deviceWidth } from '../common';
import constant from '../store/constant';

@inject('resumeStore', 'resumeAction')
@observer
class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentIndex: 1,
    };
  }

  componentDidMount() {
    global.emitter.on('refreshList', this.onRefreshList);
    global.emitter.on(constant.topic.onlineResumeChanged, this.onRefreshList);
    if (Platform.OS.toLowerCase() === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }

    this.isLogin();
  }

  componentWillUnmount() {
    global.emitter.off('refreshList', this.onRefreshList);
    global.emitter.off(constant.topic.onlineResumeChanged, this.onRefreshList);
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  onRefreshList = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.resumeAction.getResumes();
        this.setState({ currentIndex: 1 });
        this.scrollView.scrollTo({ x: 0, y: 0, animated: true });
      }
    });
  };

  onBackAndroid = () => {
    // if (this.props.navigation.state.routeName !== 'ResumeTab') {
    //   this.props.navigation.pop();
    // }
    // 禁用返回键
    if (this.props.navigation.isFocused()) {
      // 判断  该页面是否处于聚焦状态
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp(); // 直接退出APP
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT, ToastAndroid.CENTER);
      return true;
    }
    // 回调函数onBackAndroid中的return true是必不可少的 --- 大坑，信你个鬼， 必须为false，不然会有bug
    return false;
  };

  isLogin() {
    Session.isLogin().then((isLogin) => {
      if (!isLogin) {
        // this.props.navigation.navigate('login');
        return Promise.resolve();
      }
      return this.props.resumeAction.getResumes();
    });
  }

  onScroll = ({
    nativeEvent: {
      contentOffset: { x },
    },
  }) => {
    const { resumeList } = this.props.resumeStore;
    const currentIndex = Math.ceil(x / deviceWidth) + 1;

    if (currentIndex > resumeList.length) {
      return;
    }
    this.props.resumeAction.changeCurrentResume(currentIndex - 1);
    this.setState({ currentIndex });
  };

  checkList(list) {
    let result = false;
    if (list) {
      if (list.length > 0) {
        result = true;
      }
    }
    return result ? I18n.t('page_resume_label_full') : I18n.t('page_resume_label_null');
  }

  checkObject(obj) {
    let result = false;
    if (obj) {
      result = true;
    }
    return result ? I18n.t('page_resume_label_full') : I18n.t('page_resume_label_null');
  }

  newResume() {
    const { resumeList } = this.props.resumeStore;
    if (!resumeList || (resumeList && resumeList.slice().length === 0)) {
      const { navigation } = this.props;
      navigation.navigate('jobIntensionAdd', {});
      return;
    }
    this.props.navigation.navigate('resumeAdd');
  }

  onAnnex = () => {
    this.props.navigation.navigate('annexResume', {});
  };

  renderHerderLeft() {
    return (
      <TouchableOpacity onPress={() => this.newResume()}>
        <Icon type="material" name="add" size={24} color="#fff" iconStyle={{ marginLeft: 0 }} />
      </TouchableOpacity>
    );
  }

  renderHerderRight() {
    return (
      <TouchableOpacity
        style={{ flexDirection: 'row', alignItems: 'center' }}
        onPress={() => this.onAnnex()}
      >
        <Image style={{ width: 12, height: 13 }} source={res.resumeAnnex} />
        <Text style={[headerStyle.rightBtn, { color: 'white', marginLeft: 7 }]}>
          {I18n.t('page_resume_annex_title')}
        </Text>
      </TouchableOpacity>
    );
  }

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  render() {
    const { navigation } = this.props;
    const { resumeList } = this.props.resumeStore;
    const { currentIndex } = this.state;
    let title = I18n.t('page_resume_title');
    if (resumeList && resumeList.length > 1) {
      title = I18n.t('page_resume_title_more', { current: currentIndex, total: resumeList.length });
    }
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#2089DC" />
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{ text: title, style: headerStyle.center }}
          rightComponent={this.renderHerderRight()}
          leftComponent={resumeList.length < 3 ? this.renderHerderLeft() : null}
        />
        {resumeList && resumeList.length > 0 ? (
          <ScrollView
            ref={(ref) => {
              this.scrollView = ref;
            }}
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            horizontal
            onScroll={this.onScroll}
          >
            {resumeList.map((item, index) => (
              <ResumeItem
                key={`${item.resumeId}`}
                tabLabel={`${I18n.t('page_resume_text_title')}${index + 1}`}
                resume={item}
                isHome
                nav={navigation}
                resumeList={resumeList}
                index={index}
              />
            ))}
          </ScrollView>
        ) : (
          this.renderEmptyComponent()
        )}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}

export default Page;
