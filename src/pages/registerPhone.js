import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, TouchableOpacity, Text, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { Input, But<PERSON>, CheckBox } from 'react-native-elements';
import EvilIcon from 'react-native-vector-icons/EvilIcons';
import Toast from 'react-native-easy-toast';
import CountryCode from '../components/countryCode';
import CommonKeyboardAvoidingView from '../components/commonKeyboardAvoidingView';
import { registerStyle, phColor, subTitleColor } from '../themes';
import I18n from '../i18n';
import res from '../res';
import regExp from '../util/regExp';
import LoadingModal from '../components/loadingModal';
import { normalizePhone } from '../common/special';
import constant from '../store/constant';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';
import validateUtil from '../util/validateUtil';
import InputImageCaptchaModal from '../components/modal/inputImageCaptchaModal';

let timeout = null;
let keyBoardIsShow = false;

@inject('loginAction', 'resumeAction', 'jobAction', 'userAction')
@observer
export default class RegisterPhone extends React.Component {
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      region: constant.defaultRegion,
      regionCode: constant.defaultRegion.code,
      regionCodeLabel: constant.defaultRegion.label,
      phone: '',
      password: '',
      code: '',
      sending: false,
      seconds: 60,
      agreement: true,
      isOpenSelectedCountryCode: false,
      showLoading: false,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    if (timeout) {
      clearInterval(timeout);
    }
  }

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = false;
  }

  onLoadInfo = () => {
    this.props.resumeAction.getResumes();
    this.props.jobAction.getJobStatistics();
    this.props.userAction.getCurrUserInfo();
  };

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  runSendCodeTimeout = () => {
    this.setState({ sending: true });
    timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds === 0) {
        this.setState({ seconds: 60, sending: false });
        clearInterval(timeout);
      }
    }, 1000);
  };

  sendCode = async (imageCaptcha) => {
    this.onLostBlur();
    try {
      const { phone, regionCode, region, sending } = this.state;
      if (sending) return;
      const mobile = validateUtil.validatePhone(phone, region);
      if (!mobile) return;
      const res = await this.inputImageCaptchaModal.sendImPhoneCode(
        {
          param: {
            mobile,
            regionCode,
            imageCaptcha,
          },
          onConfirm: this.sendCode,
        },
        this.props.loginAction.sendLoginCode
      );
      if (res && res.successful) {
        toast.show(I18n.t('page_login_op_sendcode_success'));
        this.runSendCodeTimeout();
      } else {
        if (res.message) {
          toast.show(res.message);
        } else {
          toast.show(I18n.t('page_login_op_sendcode_error'));
        }
      }
    } catch (e) {
      console.log('sendVerifyCode error', err);
      this.setState({ sending: false });
    }

    return;
    const { phone, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    this.setState({ sending: true });
    this.props.loginAction
      .sendRegisterCode(normalizePhone(phone.trim(), regionCode.trim()), regionCode.trim())
      .then(
        (result) => {
          if (result && result.successful) {
            this.toast.show(I18n.t('page_register_op_sendcode_success'));
            // 数秒
            this.runSendCodeTimeout();
          } else {
            this.toast.show(I18n.t('page_register_op_sendcode_error'));
            this.setState({ sending: false });
          }
        },
        () => {
          this.setState({ sending: false });
        }
      );
  };

  onRegist = () => {
    this.onLostBlur();
    const { phone, password, code, agreement, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    if (password.trim() === '' || password.trim().length < 6) {
      this.toast.show(I18n.t('page_register_op_password_format_error'));
      return;
    }
    // 验证密码
    if (!regExp.password.test(password.trim())) {
      this.toast.show(I18n.t('page_register_op_password_format_error'));
      return;
    }
    if (password.trim() === phone.trim()) {
      this.toast.show(I18n.t('page_register_op_password_same_phone_error'));
      return;
    }
    if (code.trim() === '') {
      this.toast.show(I18n.t('page_register_op_code_required'));
      return;
    }
    if (!agreement) {
      this.toast.show(I18n.t('page_register_op_agreement_required'));
      return;
    }
    this.setState({ showLoading: true });
    this.props.loginAction
      .registerByPhone({
        phone: normalizePhone(phone.trim(), regionCode.trim()),
        code: code.trim(),
        password: password.trim(),
        regionCode: regionCode.trim(),
      })
      .then(
        () => {
          this.setState({ showLoading: false });
          this.toast.show(I18n.t('page_register_op_success'));
          this.onLoadInfo();
          this.props.navigation.navigate('registerResult');
        },
        (err) => {
          this.setState({ showLoading: false });
          if (err && err.message) {
            this.toast.show(err.message);
          } else {
            this.toast.show(I18n.t('page_register_op_error'));
          }
        }
      );
  };

  onSelectedCountryCode = (item) => {
    if (item) {
      this.setState({ region: item, regionCodeLabel: item.label, regionCode: item.code });
    }
    this.setState({ isOpenSelectedCountryCode: false });
  };

  openCountryCodeModal = (value) => {
    this.onLostBlur();
    this.setState({ isOpenSelectedCountryCode: value });
  };

  setPhone = (text) => {
    this.setState({
      phone: text,
    });
  };

  setToken = (text) => {
    this.setState({
      password: text,
    });
  };

  setCode = (text) => {
    this.setState({
      code: text,
    });
  };

  setAgreement = () => {
    this.setState({ agreement: !this.state.agreement });
  };

  renderHeader = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.headerSection}>
        <TouchableOpacity
          style={registerStyle.headerBack}
          onPress={() => {
            navigation.navigate('login');
          }}
        >
          <Image source={res.iconBack} style={registerStyle.iconBack} />
        </TouchableOpacity>
        <View>
          <Text style={registerStyle.headerTitle}>
            {I18n.t('page_register_text_phone_header_title')}
          </Text>
        </View>
      </View>
    );
  };

  renderForm = () => (
    <View style={registerStyle.formContent}>
      <View style={[registerStyle.formGroup, registerStyle.formGroupLine]}>
        <View style={registerStyle.formPhoneSection}>
          <View style={registerStyle.formPhoneAreaWrap}>
            <TouchableOpacity
              style={registerStyle.formPhoneAreaTo}
              onPress={() => {
                this.openCountryCodeModal(true);
              }}
            >
              <Text style={registerStyle.formPhoneArea}>
                {this.state.regionCodeLabel}
                <EvilIcon name="chevron-down" size={20} color={subTitleColor} />
              </Text>
            </TouchableOpacity>
          </View>
          <View style={registerStyle.formPhoneInputWrap}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={registerStyle.formControl}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_register_ph_phone')}
              placeholderTextColor={phColor}
              onChangeText={this.setPhone}
              value={this.state.phone}
              maxLength={15}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>
      <View style={[registerStyle.formGroup, registerStyle.formGroupLine]}>
        <View style={registerStyle.formSendCodeSection}>
          <View style={registerStyle.formSendCodeInputWrap}>
            <Input
              inputContainerStyle={registerStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_register_ph_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCode}
              value={this.state.code}
              maxLength={6}
              keyboardType="numeric"
            />
          </View>
          <View style={registerStyle.formSendCodeBtnWrap}>
            <Button
              title={
                this.state.sending
                  ? `${I18n.t('page_register_btn_sendcode')}(${this.state.seconds})`
                  : I18n.t('page_login_btn_sendcode')
              }
              titleStyle={{ color: '#fff', fontSize: 16 }}
              buttonStyle={registerStyle.btnSendCode}
              disabled={this.state.sending}
              onPress={this.sendCode}
            />
          </View>
        </View>
      </View>
      <View style={[registerStyle.formGroup]}>
        <Input
          secureTextEntry
          inputContainerStyle={registerStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_register_ph_phone_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setToken}
          value={this.state.password}
          minLength={6}
          maxLength={16}
        />
      </View>
    </View>
  );

  renderFooter = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.linkSection}>
        <View style={{ flexDirection: 'row', alignItems: 'center', width: '60%', marginRight: 20 }}>
          <CheckBox
            containerStyle={registerStyle.agreementCheckbox}
            textStyle={{ color: '#fff', fontSize: 12, fontWeight: 'normal' }}
            uncheckedIcon="square-o"
            checkedIcon="check-square-o"
            checkedColor="#fff"
            uncheckedColor="#fff"
            size={20}
            checked={this.state.agreement}
            onPress={this.setAgreement}
          />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('agreement');
            }}
          >
            <Text style={registerStyle.agreementLink}>
              {I18n.t('page_register_link_agreement')}
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            flexShrink: 10,
            marginLeft: 20,
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}
        >
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('registerEmail');
            }}
          >
            <Text style={registerStyle.emailRegisterLink}>
              {I18n.t('page_register_link_email_register')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  render() {
    const { isOpenSelectedCountryCode, showLoading } = this.state;
    return (
      // View 用以适配iPhoneX
      <View style={registerStyle.page}>
        <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
          <ImageBackground source={res.bgLogin} style={{ width: '100%', height: '100%' }}>
            <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
              <View style={[registerStyle.container]}>
                {this.renderHeader()}
                <View style={registerStyle.titleSection}>
                  <Text style={registerStyle.title}>{I18n.t('page_register_text_title')}</Text>
                </View>
                <CommonKeyboardAvoidingView behavior="padding" style={registerStyle.formSection}>
                  {this.renderForm()}
                </CommonKeyboardAvoidingView>
                <View style={registerStyle.btnSection}>
                  <TouchableOpacity style={registerStyle.btnRegister} onPress={this.onRegist}>
                    <Text style={registerStyle.btnRegisterText}>
                      {I18n.t('page_register_btn_register')}
                    </Text>
                  </TouchableOpacity>
                </View>
                {this.renderFooter()}
              </View>
            </TouchableWithoutFeedback>
            <CountryCode
              isOpen={isOpenSelectedCountryCode}
              onSelected={this.onSelectedCountryCode}
            />
            <Toast
              ref={(ref) => {
                this.toast = ref;
              }}
              position="center"
            />
            <LoadingModal isOpen={showLoading} loadingTips />
          </ImageBackground>
        </TouchableWithoutFeedback>
        <InputImageCaptchaModal ref={(ref) => (this.inputImageCaptchaModal = ref)} />
      </View>
    );
  }
}
