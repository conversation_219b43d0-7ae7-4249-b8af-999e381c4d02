import React, { Component } from 'react';
import { inject } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, Input } from 'react-native-elements';
import { globalStyle, headerStyle, resumeStyle, titleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import constant from '../store/constant';

@inject('resumeAction', 'resumeStore')
// @observer
export default class Phone extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputText: '',
      name: this.props.navigation.state.params.resumeName,
    };
  }

  onChangeText = (text) => {
    this.setState({
      inputText: text,
    });
  };

  save() {
    const { resumeType } = this.props.navigation.state.params;
    if (resumeType === constant.resumeType.online) {
      this.onSaveOnline();
    } else {
      this.onSaveAnnex();
    }
  }

  onSaveOnline = () => {
    const { navigation } = this.props;
    const { currentResume } = this.props.resumeStore;
    const postData = {};
    postData.name = this.state.inputText;
    postData.hobby = currentResume.hobby ? currentResume.hobby : '';
    postData.training = currentResume.training ? currentResume.training : '';
    postData.completeness = currentResume.completeness;
    postData.description = currentResume.description ? currentResume.description : '';

    this.props.resumeAction.updateResumeAttributes(currentResume.resumeId, postData).then((res) => {
      if (res && res.successful) {
        this.toast.show(res.message);
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
        this.props.resumeAction.getResumes();
      } else {
        this.toast.show(res.message);
      }
    });
  };

  onSaveAnnex = () => {
    const { navigation } = this.props;
    const { currentAnnexResume } = this.props.resumeStore;
    const postData = {};
    postData.name = this.state.inputText;

    this.props.resumeAction
      .updateResumeAttributes(currentAnnexResume.cvId, postData)
      .then((res) => {
        if (res && res.successful) {
          this.toast.show(res.message);
          setTimeout(() => {
            navigation.goBack();
          }, 1000);
          this.props.resumeAction.getAnnexResumes();
        } else {
          this.toast.show(res.message);
        }
      });
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_manager_name'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={() => this.save()}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <Input
          onChangeText={this.onChangeText}
          placeholder={I18n.t('page_resume_label_manager_name')}
          style={[resumeStyle.phoneInput, { paddingHorizontal: 0 }]}
          inputContainerStyle={resumeStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
            paddingLeft: 10,
          }}
          defaultValue={this.state.name}
          maxLength={30}
          underlineColorAndroid="transparent"
          containerStyle={{
            width: '100%',
          }}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
