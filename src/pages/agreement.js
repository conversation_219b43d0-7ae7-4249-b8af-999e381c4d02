import React, { Component } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import { Header } from 'react-native-elements';
import GoBack from '../components/goback';
import { headerStyle, globalStyle } from '../themes';
import I18n from '../i18n';
import Config from '../configs';

export default class Page extends Component {
  static navigationOptions = {
    headerShown: false,
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={globalStyle.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          leftComponent={<GoBack navigation={navigation} />}
          centerComponent={{
            text: I18n.t('page_agreement_text_header_title'),
            style: headerStyle.center,
          }}
        />
        <WebView
          source={{ uri: Config.agreementURl }}
          startInLoadingState
          domStorageEnabled
          javaScriptEnabled
        />
      </View>
    );
  }
}
