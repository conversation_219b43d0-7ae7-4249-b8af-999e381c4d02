import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  Text,
  View,
  FlatList,
  TouchableHighlight,
  Modal,
  InteractionManager,
  Alert,
  Keyboard,
} from 'react-native';
import { Clipboard } from '../components';
import { Header, Icon } from 'react-native-elements';
import ActionSheet from 'react-native-actionsheet';
import Hyperlink from 'react-native-hyperlink';
import {
  dynamicStyle,
  chatListStyle,
  globalStyle,
  headerStyle,
  desColor,
  baseBlueColor,
  subTitleColor,
  dynamicDetailStyle,
  bgColor,
} from '../themes';
import Image from '../components/image';
import DynamicItem from './dynamicItem';
import SeenItem from '../components/seenItem';
import InputBox from './dynamicComInputBox';
import res, { getAvatarSource } from '../res';
import I18n from '../i18n';
import util from '../util';
import Session from '../api/session';
import HeaderCenterButtonGroup from '../components/header/headerCenterButtonGroup';

@inject('dynamicStore', 'dynamicAction', 'userAction', 'personStore', 'resumeStore', 'resumeAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.page = 1; // 谁看过我
    this.isLoadingMoreViewer = false; // 谁看过我
    this.isLoadingMore = false;
    this.isLoadingMoreComments = false;
    this.state = {
      isModalVisible: false,
      isInputModalVisible: false,
      page: 1,
      commentsPage: 1,
      hasMore: false,
      refreshing: true,
      hasMoreViewers: false, // 谁看过我
      viewerRefreshing: true, // 谁看过我
      commentRefresh: false,
      hasMoreComment: false,
      twitterId: '',
      currentDynamic: null,
      favorited: false,
      selectComment: null,
      hideMsgTips: true,
      replyCommentId: 0,
      placeholderComment: I18n.t('page_comment_ph_comment'),
      selectedIndex: 0,
    };
  }

  componentDidMount() {
    if (Platform.OS.toLowerCase() === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.userAction.getUnreadTwitterMessages().then((data) => {
          if (data && data.unreadTwitterNum !== 0) {
            this.setState({ hideMsgTips: false });
          }
        });
      }
    });
    global.emitter.on('msgModalShow', this.onHideMsgTips);
    InteractionManager.runAfterInteractions(() => {
      this.getDynmics();
    });
  }

  componentWillUnmount() {
    global.emitter.off('msgModalShow', this.onHideMsgTips);
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  /**
   * 获取谁看过我
   */
  getResumeViews = async () => {
    await this.props.resumeAction.getResumesViewers({ page: this.page, size: 8 });
    const { resumeViewers, resumeViewersTotalCount } = this.props.resumeStore;
    this.setState({
      hasMoreViewers: resumeViewers.slice().length < parseFloat(resumeViewersTotalCount),
      viewerRefreshing: false,
    });
  };

  onHideMsgTips = () => {
    this.setState({ hideMsgTips: false });
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.setState({ isModalVisible: false });
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onRefresh = async () => {
    if (this.state.selectedIndex === 1) {
      this.setState({ viewerRefreshing: true, hasMoreViewers: false });
      this.isLoadingMoreViewer = false;
      this.page = 1;
      await this.getResumeViews();
    } else {
      Session.isLogin().then((isLogin) => {
        if (isLogin) {
          this.props.userAction.getUnreadTwitterMessages();
        }
      });
      this.setState({ refreshing: true });
      this.state.page = 1;
      await this.getDynmics();
    }
  };

  onCommentRefresh = async (id) => {
    this.setState({ commentRefresh: true });
    this.state.commentsPage = 1;
    await this.getComments(id || this.state.twitterId);
  };

  onBackAndroid = () => {
    if (this.props.navigation.state.routeName !== 'DynamicTab') {
      // this.props.navigation.pop();
    }
    // 禁用返回键
    if (this.props.navigation.isFocused()) {
      // 判断  该页面是否处于聚焦状态
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp(); // 直接退出APP
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT, ToastAndroid.CENTER);
      return true;
    }
    // 回调函数onBackAndroid中的return true是必不可少的 --- 大坑，信你个鬼， 必须为false，不然会有bug
    return false;
  };

  onLoadMore = async () => {
    if (this.state.selectedIndex === 1) {
      if (this.state.hasMoreViewers && !this.isLoadingMoreViewer) {
        this.isLoadingMoreViewer = true;
        this.page += 1;
        await this.getResumeViews();
        setTimeout(() => {
          this.isLoadingMoreViewer = false;
        }, 1000);
      }
    } else if (this.state.hasMore && !this.isLoadingMore) {
      this.isLoadingMore = true;
      this.state.page += 1;
      await this.getDynmics();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  onLoadMoreComments = async () => {
    if (this.state.hasMoreComment && !this.isLoadingMoreComments) {
      this.state.commentsPage += 1;
      this.isLoadingMoreComments = true;
      await this.getComments(this.state.twitterId);
      setTimeout(() => {
        this.isLoadingMoreComments = false;
      }, 0);
    }
  };

  onCommentClick = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.setState({ selectComment: item });
        if (item.mine) {
          this.setState({ isInputModalVisible: false, replyCommentId: 0 });
          this.commentActionSheet.show();
        } else {
          this.setState({
            placeholderComment: `${I18n.t('page_dynamic_reply_text')}${
              item.userName ? item.userName : I18n.t('page_dynamic_title_ay_username')
            }:`,
            replyCommentId: item.id,
            isInputModalVisible: true,
          });
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  onDynamicClick = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.setState({ currentDynamic: item });
        this.props.navigation.navigate('dynamicDetail', {
          info: { id: item.id },
          dynamicId: item.id,
          onRefreshDynamic: (dynamicId, commets, likes, liked) => {
            const { dynamicList } = this.props.dynamicStore;
            const deleteIndex = dynamicList.findIndex((x) => x.id === dynamicId);
            dynamicList[deleteIndex].comments = parseInt(commets, 10);
            dynamicList[deleteIndex].likes = parseInt(likes, 10);
            dynamicList[deleteIndex].liked = liked;
            this.props.dynamicStore.dynamicList = dynamicList;
          },
          onDynamicDelete: (dynamicId) => {
            const { dynamicList } = this.props.dynamicStore;
            const deleteIndex = dynamicList.findIndex((x) => x.id === dynamicId);
            dynamicList.splice(deleteIndex, 1);
            this.props.dynamicStore.dynamicList = dynamicList;
          },
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  onActionSheetSelect = (index) => {
    if (index === 0) {
      this.onDeleteComment();
    }
  };

  onDynamicCopySheetSelect = (index) => {
    if (index === 0) {
      const { currentDynamic } = this.state;
      Clipboard.setString((currentDynamic && currentDynamic.content) || '');
      global.toast.show(I18n.t('page_dynamic_text_copy_success'));
    }
  };

  onCommentCopySheetSelect = (index) => {
    if (index === 0) {
      const { selectComment } = this.state;
      Clipboard.setString((selectComment && selectComment.content) || '');
      global.toast.show(I18n.t('page_dynamic_text_copy_success'));
    }
  };

  onDeleteComment = async () => {
    const { commentList } = this.props.dynamicStore;
    const data = this.state.selectComment;
    const ress = await this.props.dynamicAction.deleteComment(data.twitterId, data.id);
    if (ress) {
      if (ress.message) {
        this.toast.show(ress.message);
      }
      if (ress.successful) {
        this.props.dynamicStore.dynamicList = this.props.dynamicStore.dynamicList.map((x) => {
          if (x.id === data.twitterId) {
            x.comments = x.comments > 0 ? x.comments - 1 : 0;
          }
          return x;
        });
        const deleteIndex = commentList.findIndex((x) => x.id === data.id);
        commentList.splice(deleteIndex, 1);
        this.props.dynamicStore.commentList = commentList;
      }
    }
  };

  onToUserProfile = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        if (item.mine) return;
        navigation.push('userProfile', {
          data: item,
          onShowModal: () => {
            this.setState({ isModalVisible: true });
          },
        });
        this.setState({ isModalVisible: false });
      } else {
        this.onShowAlert();
      }
    });
  };

  getComments = async (id) => {
    try {
      await this.props.dynamicAction.queryComments(id, {
        page: this.state.commentsPage,
        size: 10,
      });
      const { commentTotalCount, commentList } = this.props.dynamicStore;
      this.setState({
        hasMoreComment: commentList && commentList.length < parseFloat(commentTotalCount),
        commentRefresh: false,
      });
    } catch (error) {
      this.setState({ commentRefresh: false });
    }
  };

  getDynmics = async () => {
    try {
      await this.props.dynamicAction.queryDynamics({
        page: this.state.page,
        size: 10,
      });
      const { dynamicTotalCount, dynamicList } = this.props.dynamicStore;
      this.setState({
        hasMore: dynamicList.length < parseFloat(dynamicTotalCount),
        refreshing: false,
      });
    } catch (error) {
      this.setState({ refreshing: false });
    }
  };

  _keyboardDidHide = () => {
    this.setState({ placeholderComment: '', replyCommentId: 0 });
  };

  toggleModal = async () => {
    this.state.commentsPage = 1;
    this.setState({ isModalVisible: !this.state.isModalVisible });
    if (!this.state.isModalVisible) {
      this.props.dynamicStore.commentList = [];
      this.props.dynamicStore.commentTotalCount = 0;
    }
  };

  toggleInputModal = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.setState({ isInputModalVisible: !this.state.isInputModalVisible });
      } else {
        this.onShowAlert();
      }
    });
  };

  commentCreate = async (item) => {
    this.toggleModal();
    this.setState({ twitterId: item.id, currentDynamic: item });
    await this.onCommentRefresh(item.id);
  };

  changeFavorite = (item) => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        if (item.liked) {
          const ress = await this.props.dynamicAction.dislikes(item.id);
          if (ress && ress.successful) {
            this.props.dynamicStore.dynamicList = this.props.dynamicStore.dynamicList.map((x) => {
              if (x.id === item.id) {
                x.liked = false;
                x.likes = x.likes > 0 ? x.likes - 1 : 0;
              }
              return x;
            });
          }
          this.listToast.show(ress.message);
        } else {
          const ress = await this.props.dynamicAction.likes(item.id);
          if (ress && ress.successful) {
            this.props.dynamicStore.dynamicList = this.props.dynamicStore.dynamicList.map((x) => {
              if (x.id === item.id) {
                x.liked = true;
                x.likes += 1;
              }
              return x;
            });
          }
          this.listToast.show(ress.message);
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  hideInputBox = async (result) => {
    this.toast.show(result && result.message ? result.message : I18n.t('page_comment_create_fail'));
    if (result && result.successful) {
      this.props.dynamicStore.dynamicList = this.props.dynamicStore.dynamicList.map((x) => {
        if (x.id === this.state.currentDynamic.id) {
          x.comments += 1;
        }
        return x;
      });
      this.setState({ isInputModalVisible: false });
    }
    this.setState({ commentsPage: 1, replyCommentId: 0, placeholderComment: '' });
    await this.getComments(this.state.twitterId);
  };

  dynamicModal = () => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        const { navigate } = this.props.navigation;
        navigate('dynamicEdit', {
          onSelect: () => {
            this.state.page = 1;
            this.setState({ selectedIndex: 0 });
            this.getDynmics();
          },
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  /**
   * 统计谁看过我
   * 60s一次
   */
  hasNewSeen = () => {
    const { lastSeen, preLastSeen } = this.props.resumeStore;
    return lastSeen !== preLastSeen;
  };

  /**
   * tab切换
   */
  updateIndex = (selectedIndex) => {
    Session.isLogin().then((hasLogin) => {
      if (hasLogin) {
        this.setState({ selectedIndex, hasMoreViewers: false });
        if (selectedIndex === 1) {
          this.page = 1;
          this.isLoadingMoreViewer = false;
          this.state.viewerRefreshing = true;
          InteractionManager.runAfterInteractions(() => {
            this.getResumeViews();
          });
        } else {
          this.setState({ refreshing: true });
          this.state.page = 1;
          InteractionManager.runAfterInteractions(() => {
            this.getDynmics();
          });
        }
      } else {
        this.props.navigation.navigate('login');
      }
    });
  };

  renderDynamicItemView = ({ item }) => {
    return (
      <DynamicItem
        info={item}
        navigation={this.props.navigation}
        favorited={this.state.favorited}
        onPress={() => this.onDynamicClick(item)}
        onLongPress={() => {
          this.setState({ currentDynamic: item });
          this.copyDynamicActionSheet.show();
        }}
        onComment={() => this.commentCreate(item)}
        getDynmics={() => this.getDynmics()}
        onFavorite={() =>
          util.HandlerOnceTap(() => {
            this.changeFavorite(item);
          })
        }
      />
    );
  };

  renderCommentItemView = ({ item }) => {
    return (
      <TouchableHighlight
        underlayColor="transparent"
        key={item.id}
        onPress={() => this.onCommentClick(item)}
      >
        <View style={dynamicStyle.commentItemtContent}>
          <View style={dynamicStyle.commentItemLeft}>
            <TouchableHighlight
              underlayColor="transparent"
              onPress={() => this.onToUserProfile(item)}
            >
              <Image source={getAvatarSource(item.avatar)} style={dynamicStyle.commentIcon} />
            </TouchableHighlight>
          </View>
          <View style={dynamicStyle.commentItemRight}>
            <View style={dynamicStyle.commentItemtTitleContent}>
              <Text style={dynamicStyle.commentName}>
                {item.userName ? item.userName : I18n.t('page_dynamic_title_ay_username')}
              </Text>
              <Text style={dynamicStyle.commentTime}>
                {item.createAt_unixtime ? util.getDiffBetween(item.createAt_unixtime) : ''}
              </Text>
            </View>
            <TouchableHighlight
              underlayColor="#f2f2f2"
              onPress={() => this.onCommentClick(item)}
              onLongPress={() => {
                this.setState({ selectComment: item });
                this.copyCommentActionSheet.show();
              }}
            >
              {parseInt(item.replyCommentId, 10) !== 0 ? (
                <View style={dynamicDetailStyle.commentContent}>
                  <Hyperlink
                    onPress={(url, text) => {
                      if (!url.startsWith('http')) {
                        return;
                      }
                      this.setState({ isModalVisible: false });
                      const { navigation } = this.props;
                      navigation.navigate('previewWeb', { title: url, url });
                    }}
                    linkStyle={{ color: '#2980b9' }}
                  >
                    <Text>
                      {`${I18n.t('page_dynamic_reply_text')} `}
                      <Text style={{ color: baseBlueColor }}>
                        {item.replyUserName
                          ? item.replyUserName
                          : I18n.t('page_dynamic_title_ay_username')}
                      </Text>
                      <Text style={dynamicDetailStyle.commentContent}>{`: ${item.content}`}</Text>
                    </Text>
                  </Hyperlink>
                </View>
              ) : (
                <Hyperlink
                  onPress={(url, text) => {
                    if (!url.startsWith('http')) {
                      return;
                    }
                    this.setState({ isModalVisible: false });
                    const { navigation } = this.props;
                    navigation.navigate('previewWeb', { title: url, url });
                  }}
                  linkStyle={{ color: '#2980b9' }}
                >
                  <Text style={dynamicDetailStyle.commentContent}>{item.content}</Text>
                </Hyperlink>
              )}
            </TouchableHighlight>
          </View>
        </View>
      </TouchableHighlight>
    );
  };

  renderModal = () => {
    const { commentList, commentTotalCount } = this.props.dynamicStore;
    return (
      <Modal
        onRequestClose={() => this.setState({ isModalVisible: false })}
        animationType="slide"
        presentationStyle="overFullScreen"
        transparent
        visible={this.state.isModalVisible}
      >
        <View style={dynamicStyle.commentBackGround}>
          <View style={dynamicStyle.commentBackTop}>
            <TouchableHighlight underlayColor="transparent" onPress={this.toggleModal}>
              <Text style={dynamicStyle.commentBackTopEmpty} />
            </TouchableHighlight>
          </View>
          <View style={dynamicStyle.commentBackContent}>
            <View style={dynamicStyle.commentTitleCon}>
              {I18n.locale === 'zh' ? (
                <Text style={dynamicStyle.commentTitle}>
                  {commentTotalCount}
                  {I18n.t('page_comment_title_strip')}
                  {I18n.t('page_comment_text_title')}
                </Text>
              ) : (
                <Text style={dynamicStyle.commentTitle}>
                  {commentTotalCount} {I18n.t('page_comment_title_strip')}
                  {I18n.t('page_comment_text_title')}
                </Text>
              )}
            </View>
            <FlatList
              data={commentList && commentList.slice()}
              renderItem={this.renderCommentItemView}
              keyExtractor={(item, index) => index + item}
              showsVerticalScrollIndicator={false}
              horizontal={false}
              refreshing={this.state.commentRefresh}
              onRefresh={this.onCommentRefresh}
              ListFooterComponent={this.renderCommentFooter}
              onEndReachedThreshold={0.1}
              onEndReached={() => this.onLoadMoreComments()}
              ItemSeparatorComponent={() => (
                <View style={{ height: 1, backgroundColor: bgColor, marginHorizontal: 15 }} />
              )}
              ListEmptyComponent={
                !this.state.commentRefresh ? (
                  <Text style={[dynamicStyle.noComments, { color: subTitleColor }]}>
                    {I18n.t('page_dynamic_no_comment')}
                  </Text>
                ) : (
                  <Text />
                )
              }
            />
            <View style={dynamicStyle.commentInputBack}>
              <TouchableHighlight underlayColor="transparent" onPress={this.toggleInputModal}>
                <View style={dynamicStyle.commentInputView}>
                  <Text style={dynamicStyle.commentInputText}>
                    {I18n.t('page_comment_ph_comment')}
                  </Text>
                </View>
              </TouchableHighlight>
            </View>
          </View>
        </View>
        <Modal
          onRequestClose={() => this.setState({ isInputModalVisible: false })}
          animationType="none"
          presentationStyle="overFullScreen"
          transparent
          visible={this.state.isInputModalVisible}
        >
          <View style={dynamicStyle.commentBackGround}>
            <View style={dynamicStyle.inputBackTop}>
              <TouchableHighlight underlayColor="transparent" onPress={this.toggleInputModal}>
                <Text style={dynamicStyle.commentBackTopEmpty} />
              </TouchableHighlight>
            </View>
            <InputBox
              onPress={this.hideInputBox}
              info={this.state.currentDynamic}
              ref={(ref) => {
                this.input = ref;
              }}
              navigation={this.props.navigation}
              replyCommentId={this.state.replyCommentId}
              placeholderComment={this.state.placeholderComment}
            />
          </View>
        </Modal>
        <ActionSheet
          title={I18n.t('page_dynamic_delete_comment')}
          ref={(ref) => {
            this.commentActionSheet = ref;
          }}
          options={[I18n.t('page_resume_btn_del'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onActionSheetSelect}
        />
        <ActionSheet
          ref={(ref) => {
            this.copyCommentActionSheet = ref;
          }}
          options={[I18n.t('page_dynamic_text_copy'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onCommentCopySheetSelect}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </Modal>
    );
  };

  renderEmptyComponent = () =>
    this.state.refreshing ? (
      <View />
    ) : (
      <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
        <Image style={{ width: '24%', height: 120 }} source={res.noData} />
      </View>
    );

  renderFooter = () => {
    const {
      dynamicStore: { dynamicList },
    } = this.props;
    return this.state.hasMore && dynamicList && dynamicList.length > 0 ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );
  };

  renderCommentFooter = () => {
    const {
      dynamicStore: { commentList },
    } = this.props;
    return this.state.hasMoreComment && commentList && commentList.length > 0 ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );
  };

  renderHeader = (newTwitterMsgData, dynamicList) => {
    const { navigation } = this.props;
    const isTrue =
      !this.state.hideMsgTips &&
      newTwitterMsgData &&
      newTwitterMsgData.unreadTwitterNum !== 0 &&
      dynamicList &&
      dynamicList.length > 0;
    return isTrue ? (
      <View style={dynamicStyle.msgContainer}>
        <TouchableHighlight
          underlayColor="transparent"
          onPress={() => {
            this.setState({ hideMsgTips: true });
            this.props.userAction.resetTwitterMsgData();
            navigation.navigate('dynamicMsg', {
              onSelect: () => {
                this.state.page = 1;
                this.getDynmics();
              },
            });
          }}
        >
          <View style={dynamicStyle.msgContainerBg}>
            <Image
              style={{ width: 30, height: 30, borderRadius: 5 }}
              source={getAvatarSource(newTwitterMsgData.avatar)}
            />
            <View style={dynamicStyle.msgView}>
              {I18n.locale === 'zh' ? (
                <Text numberOfLines={1} style={{ marginLeft: 14, fontSize: 14, color: '#fff' }}>
                  {newTwitterMsgData.unreadTwitterNum}
                  {I18n.t('page_dynamic_new_msg')}
                </Text>
              ) : (
                <Text numberOfLines={1} style={{ marginLeft: 14, fontSize: 14, color: '#fff' }}>
                  {newTwitterMsgData.unreadTwitterNum} {I18n.t('page_dynamic_new_msg')}
                </Text>
              )}
              <Icon name="chevron-thin-right" type="entypo" size={12} color={subTitleColor} />
            </View>
          </View>
        </TouchableHighlight>
      </View>
    ) : (
      <View style={{ display: 'none' }} />
    );
  };

  /**
   * header按钮组
   */
  renderHeaderButtonGroup = () => {
    const { selectedIndex } = this.state;
    const hasNewSeen = this.hasNewSeen();
    return (
      <HeaderCenterButtonGroup
        onPress={this.updateIndex}
        selectedIndex={selectedIndex}
        items={[
          { text: I18n.t('page_dynamic_text_title') },
          {
            text: I18n.t('page_chatlist_text_header_seen'),
            badgeNum: hasNewSeen,
            showBadgeOnlyUnselected: true,
          },
        ]}
      />
    );
  };

  renderNoCompany = () =>
    this.state.viewerRefreshing ? (
      <View />
    ) : (
      <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
        <Image style={{ width: '24%', height: 120 }} source={res.noData} />
      </View>
    );

  renderViewerFooter = () =>
    this.state.hasMoreViewers ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_chatlist_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  render() {
    const {
      navigation,
      dynamicStore: { dynamicList },
      personStore: { newTwitterMsgData },
      resumeStore: { resumeViewers },
    } = this.props;
    const { selectedIndex } = this.state;

    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          rightComponent={
            <TouchableHighlight underlayColor="transparent" onPress={this.dynamicModal}>
              <Image style={{ width: 20, height: 20 }} source={res.fabu} />
            </TouchableHighlight>
          }
          outerContainerStyles={{ borderBottomWidth: 0 }}
          centerComponent={this.renderHeaderButtonGroup()}
        />
        {selectedIndex === 0 && (
          <FlatList
            data={dynamicList}
            ListEmptyComponent={this.renderEmptyComponent}
            renderItem={this.renderDynamicItemView}
            keyExtractor={(item, index) => index + item}
            showsVerticalScrollIndicator={false}
            horizontal={false}
            ListHeaderComponent={this.renderHeader(newTwitterMsgData, dynamicList)}
            ListFooterComponent={this.renderFooter}
            refreshing={this.state.refreshing}
            onRefresh={this.onRefresh}
            onEndReachedThreshold={0.1}
            onEndReached={() => this.onLoadMore()}
            ItemSeparatorComponent={() => <View style={dynamicStyle.separatorLine} />}
          />
        )}
        {selectedIndex === 1 && (
          <FlatList
            style={chatListStyle.listWrap}
            data={resumeViewers}
            keyExtractor={(item, index) => index + item}
            ListEmptyComponent={this.renderNoCompany}
            ListFooterComponent={this.renderViewerFooter}
            renderItem={({ item }) => (
              <SeenItem key={item.id} item={item} navigation={navigation} />
            )}
            initialNumToRender={8}
            onRefresh={this.onRefresh}
            refreshing={this.state.viewerRefreshing}
            onEndReachedThreshold={0.1}
            onEndReached={() => this.onLoadMore()}
            enableEmptySections
          />
        )}
        {this.renderModal()}
        <ActionSheet
          ref={(ref) => {
            this.copyDynamicActionSheet = ref;
          }}
          options={[I18n.t('page_dynamic_text_copy'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onDynamicCopySheetSelect}
        />
        <Toast
          ref={(ref) => {
            this.listToast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
