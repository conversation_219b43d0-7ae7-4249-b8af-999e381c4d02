import React, { Component } from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { Header } from 'react-native-elements';
import { globalStyle, headerStyle, resumeStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

export default class Phone extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputText: '',
    };
  }

  onChangeText = (text) => {
    this.setState({
      inputText: text,
    });
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_label_phone_edit'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.state.params.editPhone(this.state.inputText);
                navigation.goBack();
              }}
            >
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <TextInput
          placeholder="手机号"
          style={resumeStyle.phoneInput}
          onChangeText={this.onChangeText}
        />
      </View>
    );
  }
}
