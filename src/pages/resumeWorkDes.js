import React, { Component } from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { Header } from 'react-native-elements';
import { globalStyle, headerStyle, dynamicStyle, titleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

export default class Des extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputText: this.props.navigation.state.params.des,
      count: 100,
    };
  }

  componentDidMount() {
    const desc = this.props.navigation.state.params.des;
    this.setState({
      count: 100 - (desc ? desc.length : 0),
    });
  }

  onChangeText = (text) => {
    this.setState({
      count: 100 - text.length,
      inputText: text,
    });
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper, dynamicStyle.editheaderBg]}
          centerComponent={{
            text: I18n.t('page_resume_label_work_des'),
            style: headerStyle.centerBlack,
          }}
          leftComponent={<GoBack iconColor={titleColor} navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.state.params.edit(this.state.inputText);
                navigation.goBack();
              }}
            >
              <Text style={[headerStyle.rightBtn]}>{I18n.t('page_resume_btn_save')}</Text>
            </TouchableOpacity>
          }
        />
        <View style={dynamicStyle.container}>
          <TextInput
            style={[dynamicStyle.inputText, dynamicStyle.editInputText]}
            blurOnSubmit={false}
            defaultValue={this.props.navigation.state.params.des}
            underlineColorAndroid="transparent"
            placeholder={I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_work_des')}
            multiline
            textAlignVertical="top"
            maxLength={100}
            onChangeText={this.onChangeText}
          />
          <Text style={dynamicStyle.editTextCount}>
            {I18n.t('page_dynamicedit_tips_textcount1')}
            <Text style={dynamicStyle.numberCount}>{this.state.count}</Text>
            {I18n.t('page_dynamicedit_tips_textcount2')}
          </Text>
        </View>
      </View>
    );
  }
}
