/**
 * 功能：附件简历
 * 描述：
 * Author:孙宇强
 */
import React, { Component } from 'react';
import { View } from 'react-native';
import { Header } from 'react-native-elements';
import { globalStyle, headerStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import AnnexPreview from '../components/annexPreview';

export default class AnnexResumePreview extends Component {
  componentDidMount() {
    const { params } = this.props.navigation.state;

    this.annexPreview.openFile(params);
  }

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1, backgroundColor: '#fff' }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_previews_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <AnnexPreview
          ref={(ref) => {
            this.annexPreview = ref;
          }}
        />
      </View>
    );
  }
}
