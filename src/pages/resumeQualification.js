import React, { Component } from 'react';
import { ScrollView, View } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Header, ListItem } from 'react-native-elements';
import { headerStyle, titleColor, jobStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('resumeStore')
@observer
export default class Page extends Component {
  onSelectQualification = (item) => {
    if (this.props.navigation.state.params) {
      this.props.navigation.state.params.onSelect(item);
    }
  };

  renderSalaryList = () => {
    const {
      resumeStore: { qualificationList },
      navigation,
    } = this.props;
    return qualificationList.map((item) => (
      <ListItem
        key={item.value}
        containerStyle={{
          borderBottomColor: '#eee',
          borderBottomWidth: 0.5,
          paddingHorizontal: 0,
          marginHorizontal: 12,
          paddingVertical: 16,
        }}
        onPress={() => {
          this.onSelectQualification(item);
          navigation.goBack();
        }}
        title={item.label}
        titleStyle={{
          color: titleColor,
          fontSize: 14,
        }}
      />
    ));
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_resume_label_edu'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          <View style={{ backgroundColor: '#fff' }}>{this.renderSalaryList()}</View>
        </ScrollView>
      </View>
    );
  }
}
