import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Picker from 'react-native-picker';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Header, ListItem, Icon } from 'react-native-elements';
import ImagePicker from 'react-native-image-crop-picker';
import Toast from 'react-native-easy-toast';
import ActionSheet from 'react-native-actionsheet';
import { resumeStyle, titleColor, globalStyle, headerStyle, bgColor } from '../themes';
import { getAvatarSource } from '../res';
import I18n from '../i18n';
import util from '../util';
import Avatar from '../components/avatar';
import moment from 'moment';

const styles = StyleSheet.create({
  line: {
    height: 15,
    backgroundColor: bgColor,
  },
});
@inject('resumeStore', 'resumeAction', 'userAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const { currentResume } = this.props.resumeStore;
    this.state = {
      postData:
        currentResume && currentResume.profile ? Object.assign({}, currentResume.profile) : {},
      imageUri: currentResume && currentResume.profile ? currentResume.profile.avatar : '',
    };
  }

  componentWillUnmount() {
    Picker.hide();
    this.goBack();
  }

  saveProfile = () => {
    const { currentResume } = this.props.resumeStore;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.firstName?.trim()) {
      this.showName();
      toast.show(I18n.t('page_resume_op_first_name_required'));
      return;
    }
    if (!temp.lastName?.trim()) {
      this.showName();
      toast.show(I18n.t('page_resume_last_name_required'));
      return;
    }
    if (temp.sexId && temp.sexId.value === 0) {
      this.showSexActionSheet();
      toast.show(I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_sex'));
      return;
    }
    if (!temp.birthday) {
      this.showPickerPress(2);
      toast.show(I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_birth'));
      return;
    }
    if (!temp.workBeginDate) {
      this.showPickerPress(3);
      toast.show(I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_work_start'));
      return;
    }
    if (temp.locationId && temp.locationId.value === 0) {
      this.showCity();
      toast.show(I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_stay_city'));
      return;
    }
    if (!temp.email) {
      this.onEmail();
      toast.show(I18n.t('page_resume_op_email_required'));
      return;
    }
    temp.locationId = temp.locationId.value;
    temp.sexId = temp.sexId.value;
    temp.workBeginDate += '-1';
    temp.birthday += '-1';
    temp.address = '';
    if (!temp.regionCode) {
      temp.regionCode = '855';
    }
    this.props.resumeAction.updateResumeProfile(currentResume.resumeId, temp).then((res) => {
      if (res) {
        if (res.successful) {
          setTimeout(() => {
            this.goBack();
          }, 1000);
          this.props.resumeAction.getResumes();
          this.props.userAction.getCurrUserInfo();
        }
        if (res.message) {
          this.toast.show(res.message);
        }
      }
    });
  };

  upload(image) {
    const form = new FormData();
    const file = {
      uri: IS_IOS ? `file:///${image.path}` : image.path,
      type: 'multipart/form-data',
      name: 'image.jpg',
    };
    form.append('avatar', file);
    this.props.userAction.uploadImage(form).then((res) => {
      if (res.data) {
        const temp = this.state.postData;
        temp.avatar = res.data.http;
        this.setState({
          postData: temp,
          imageUri: res.data.http,
        });
      }
    });
  }

  showSexActionSheet = () => {
    Picker.hide();
    this.sexActionSheet.show();
  };

  sexActionSheetSelect = (index) => {
    const { sexList } = this.props.resumeStore;
    if (index === sexList.length) {
      console.log('cancel');
    } else {
      const temp = this.state.postData;
      temp.sexId = sexList[index];
      this.setState({
        postData: temp,
      });
    }
  };

  showPhotoActionSheet = () => {
    Picker.hide();
    this.photoActionSheet.show();
  };

  photoActionSheetSelect = (index) => {
    if (index === 0) {
      this.pickSingleWithCamera();
    } else if (index === 1) {
      this.openPicLib();
    }
  };

  // 从相机获取图片
  pickSingleWithCamera = () => {
    ImagePicker.openCamera({
      cropping: true,
      width: 400,
      height: 400,
    })
      .then((image) => {
        this.upload(image);
      })
      .catch((e) => console.log(e));
  };

  openPicLib = () => {
    ImagePicker.openPicker({
      cropping: true,
      width: 400,
      height: 400,
    })
      .then((image) => {
        this.upload(image);
      })
      .catch((e) => console.log(e));
  };

  showCity = () => {
    const { navigation } = this.props;
    Picker.hide();
    navigation.navigate('cityList', {
      needSave: false,
      onSelect: (item) => {
        const temp = this.state.postData;
        temp.locationId = { ...item, value: item.locationId };
        this.setState({
          postData: temp,
        });
      },
    });
  };

  onEmail = () => {
    Picker.hide();
    this.props.navigation.navigate('resumeProfileEmail', {
      email: this.state.postData.email,
      editEmail: (email) => {
        const { postData } = this.state;
        postData.email = email;
        this.setState({ postData });
      },
    });
  };

  showName = () => {
    const { navigation } = this.props;
    Picker.hide();
    navigation.navigate('resumeProfileName', {
      callBack: (data) => {
        const temp = this.state.postData;
        temp.firstName = data.first.trim();
        temp.lastName = data.last.trim();
        this.setState({
          postData: temp,
        });
      },
      firstName: this.state.postData.firstName,
      lastName: this.state.postData.lastName,
    });
  };

  firstNameChange = (text) => {
    const temp = this.state.postData;
    temp.firstName = text;
    this.setState({
      postData: temp,
    });
  };

  lastNameChange = (text) => {
    const temp = this.state.postData;
    temp.lastName = text;
    this.setState({
      postData: temp,
    });
  };

  createDateData() {
    const date = [];
    const currentYear = moment().year() + 1;
    for (let i = 1950; i < currentYear; i += 1) {
      const month = [];
      for (let j = 1; j < 13; j += 1) {
        month.push((Array(2).join('0') + j).slice(-2));
      }
      const _date = {};
      _date[i] = month;
      date.push(_date);
    }
    return date;
  }

  sexData(sexList) {
    const list = [];
    if (sexList) {
      sexList.map((item) => {
        list.push(item.label);
        return item;
      });
    }
    list.push(I18n.t('page_resume_btn_cancel'));
    return list;
  }

  showPickerPress(type) {
    const profile = this.state.postData;

    let title = '';
    let picCata = [];
    let select = [];
    if (type === 2) {
      title = I18n.t('page_resume_label_birth');
      picCata = this.createDateData();
      if (profile.birthday) {
        select = profile.birthday.split('-');
      } else {
        select = ['1990', '1'];
      }
    } else if (type === 3) {
      title = I18n.t('page_resume_label_work_start');
      picCata = this.createDateData();
      if (profile.workBeginDate) {
        select = profile.workBeginDate.split('-');
      } else {
        select = ['1990', '1'];
      }
    }
    Picker.init({
      pickerData: picCata,
      pickerTitleText: title,
      selectedValue: select,
      pickerCancelBtnText: I18n.t('page_resume_btn_cancel'),
      pickerConfirmBtnText: I18n.t('page_resume_btn_sure'),
      pickerToolBarBg: [50, 165, 231, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [255, 255, 255, 1],
      pickerCancelBtnColor: [255, 255, 255, 1],
      pickerTitleColor: [255, 255, 255, 1],
      onPickerConfirm: (data) => {
        if (type === 2) {
          const temp = this.state.postData;
          temp.birthday = data.join('-');
          this.setState({
            postData: temp,
          });
        } else if (type === 3) {
          const temp = this.state.postData;
          temp.workBeginDate = data.join('-');
          this.setState({
            postData: temp,
          });
        }
      },
      onPickerSelect: (data) => {
        console.log('onPickerSelect', data);
      },
    });
    Picker.show();
  }

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onShowModal) {
      await fuc.onShowModal();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  render() {
    const { navigation } = this.props;
    const { sexList } = this.props.resumeStore;
    const profile = this.state.postData;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_resume_label_profile'), style: headerStyle.center }}
          leftComponent={this.renderLeftIcon()}
          rightComponent={
            <TouchableOpacity onPress={this.saveProfile}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_photo')}
          chevron
          onPress={this.showPhotoActionSheet}
          rightAvatar={
            <Avatar size="medium" rounded source={getAvatarSource(this.state.imageUri || '')} />
          }
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={[resumeStyle.listItemContent, { height: 80 }]}
        />
        <ListItem
          title={I18n.t('page_resume_label_name')}
          chevron
          onPress={this.showName}
          rightElement={
            <Text
              numberOfLines={1}
              style={{
                width: 200,
                fontSize: 14,
                color: titleColor,
                textAlign: 'right',
              }}
            >
              {util.getUserDisplayName(profile)}
            </Text>
          }
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_sex')}
          chevron
          onPress={this.showSexActionSheet}
          rightTitle={profile.sexId ? profile.sexId.label : ''}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_birth')}
          chevron
          hideChevron
          rightTitle={profile.birthday ? util.dateToYMString(profile.birthday) : ''}
          onPress={() => this.showPickerPress(2)}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_work_start')}
          chevron
          rightTitle={profile.birthday ? util.dateToYMString(profile.workBeginDate) : ''}
          onPress={() => this.showPickerPress(3)}
          titleStyle={resumeStyle.listItemLeftText}
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_stay_city')}
          chevron
          rightTitle={
            profile.locationId
              ? util.getCityNameWithCityData(profile.locationId) || profile.locationId?.label
              : ''
          }
          onPress={this.showCity}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <View style={styles.line} />
        <ListItem
          title={I18n.t('page_resume_label_phone')}
          chevron
          rightTitle={profile.mobile}
          onPress={() => {
            Picker.hide();
            navigation.navigate('modifyPhone');
          }}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_mail')}
          chevron
          rightTitle={profile.email}
          onPress={this.onEmail}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        {/* <Text style={resumeStyle.tipsText}>{I18n.t('page_resume_tips_phone')}</Text> */}
        <ActionSheet
          ref={(ref) => {
            this.sexActionSheet = ref;
          }}
          title={I18n.t('page_resume_label_sex')}
          options={this.sexData(sexList)}
          cancelButtonIndex={sexList ? sexList.length : 0}
          onPress={this.sexActionSheetSelect}
        />
        <ActionSheet
          ref={(ref) => {
            this.photoActionSheet = ref;
          }}
          title={I18n.t('page_resume_label_photo')}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.photoActionSheetSelect}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
