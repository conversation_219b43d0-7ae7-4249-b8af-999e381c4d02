import React, { Component } from 'react';
import Picker from 'react-native-picker';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { Header, ListItem, Icon } from 'react-native-elements';
import ImagePicker from 'react-native-image-crop-picker';
import ActionSheet from 'react-native-actionsheet';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle, dynamicStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import Image from '../components/image';
import moment from 'moment';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const cer = this.props.navigation.state.params.data;
    this.isEdit = this.props.navigation.state.params.edit;
    this.state = {
      postData: cer
        ? Object.assign({}, cer)
        : {
            issued: '',
            name: '',
            obtained: '',
            photo: '',
          },
      issued: cer.issued,
      name: cer.name,
      imageUri: cer.photo,
    };
  }

  componentWillUnmount() {
    Picker.hide();
  }

  onChangeCompany = (text) => {
    const temp = this.state.postData;
    temp.issued = text;
    this.setState({
      postData: temp,
    });
  };

  onChangeName = (text) => {
    const temp = this.state.postData;
    temp.name = text;
    this.setState({
      postData: temp,
    });
  };

  onFocus = () => {
    Picker.hide();
  };

  upload(image) {
    const { currentResume } = this.props.resumeStore;
    const form = new FormData();
    const file = { uri: image.path, type: 'multipart/form-data', name: 'image.jpg' };
    form.append('file', file);
    this.props.resumeAction.uploadImage(currentResume.resumeId, form).then((res) => {
      if (res.data) {
        const temp = this.state.postData;
        temp.photo = res.data.http;
        this.setState({
          postData: temp,
          imageUri: res.data.http,
        });
      }
    });
  }

  publish = () => {
    Keyboard.dismiss();
    const { currentResume } = this.props.resumeStore;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.name) {
      this.toast.show(
        `${I18n.t('page_resume_tips_input')}${I18n.t('page_resume_label_qualification_name')}`
      );
      return;
    }
    if (!temp.issued) {
      this.toast.show(
        `${I18n.t('page_resume_tips_input')}${I18n.t('page_resume_label_qualification_issued')}`
      );
      return;
    }
    if (!temp.obtained) {
      this.toast.show(
        `${I18n.t('page_resume_tips_select')}${I18n.t('page_resume_label_qualification_obtained')}`
      );
      return;
    }
    temp.resumeId = currentResume.resumeId;

    if (this.isEdit) {
      this.props.resumeAction
        .updateQualification(temp.id, currentResume.resumeId, temp)
        .then((res) => {
          this.showRequestResult(res);
        });
    } else {
      this.props.resumeAction.addQualification(currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    }
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      Picker.hide();
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  show = () => {
    Picker.hide();
    this.ActionSheet.show();
  };

  actionSheetSelect = (index) => {
    if (index === 0) {
      this.pickSingleWithCamera();
    } else if (index === 1) {
      this.openPicLib();
    }
  };

  // 从相机获取图片
  pickSingleWithCamera = () => {
    ImagePicker.openCamera({
      cropping: true,
      width: 300,
      height: 400,
    })
      .then((image) => {
        this.upload(image);
      })
      .catch((e) => console.log(e));
  };

  openPicLib = () => {
    ImagePicker.openPicker({
      cropping: true,

      cropperCircleOverlay: false,
      compressImageMaxWidth: 480,
      compressImageMaxHeight: 640,
      compressImageQuality: 0.5,
      mediaType: 'photo',
    })
      .then((image) => {
        this.upload(image);
      })
      .catch((e) => console.log(e));
  };

  createDateData() {
    const date = [];
    const currentYear = moment().year() + 1;
    for (let i = 1950; i < currentYear; i += 1) {
      date.push(i);
    }
    return date;
  }

  showPickerPress() {
    Keyboard.dismiss();
    const cer = this.state.postData;
    Picker.init({
      pickerData: this.createDateData(),
      pickerTitleText: I18n.t('page_resume_label_qualification_obtained'),
      selectedValue: cer.obtained ? [cer.obtained] : [2018],
      pickerCancelBtnText: I18n.t('page_resume_btn_cancel'),
      pickerConfirmBtnText: I18n.t('page_resume_btn_sure'),
      pickerToolBarBg: [50, 165, 231, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [255, 255, 255, 1],
      pickerCancelBtnColor: [255, 255, 255, 1],
      pickerTitleColor: [255, 255, 255, 1],
      onPickerConfirm: (data) => {
        const temp = this.state.postData;
        temp.obtained = data[0].toString();
        this.setState({
          postData: temp,
        });
      },
      onPickerCancel: () => {},
      onPickerSelect: () => {},
    });
    Picker.show();
  }

  render() {
    const { navigation } = this.props;
    const cer = this.state.postData;

    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_qualification_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_qualification_name')}
          chevron
          input={{
            placeholder:
              I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_qualification_name'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeName,
            onFocus: this.onFocus,
            defaultValue: this.state.name,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_qualification_issued')}
          chevron
          input={{
            placeholder:
              I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_qualification_issued'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeCompany,
            onFocus: this.onFocus,
            defaultValue: this.state.issued,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_qualification_obtained')}
          chevron
          rightTitle={cer.obtained ? cer.obtained.toString() : ''}
          onPress={() => this.showPickerPress()}
          titleStyle={resumeStyle.listItemLeftText}
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_qualification_photo')}
          titleStyle={resumeStyle.listItemLeftText}
          subtitle={
            <TouchableOpacity onPress={this.show}>
              {this.state.imageUri ? (
                <Image
                  source={{ uri: this.state.imageUri }}
                  style={[dynamicStyle.editImage, { marginTop: 10 }]}
                />
              ) : (
                <View style={[dynamicStyle.editImageBackView, { marginTop: 10 }]}>
                  <Icon
                    name="camera"
                    type="feather"
                    iconStyle={dynamicStyle.editImageAdd}
                    size={30}
                  />
                </View>
              )}
            </TouchableOpacity>
          }
        />
        <ActionSheet
          ref={(ref) => {
            this.ActionSheet = ref;
          }}
          title={I18n.t('page_resume_label_qualification_photo')}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.actionSheetSelect}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
