import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text } from 'react-native';
import { Button } from 'react-native-elements';
import { globalStyle, registerStyle } from '../themes';
import I18n from '../i18n';
import res from '../res';
import Image from '../components/image';
import settingsAction from '../store/actions/settings';
import NavigationService from '../navigationService';

let timeout = null;

@inject('globalAction')
@observer
export default class RegisterResult extends React.Component {
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      seconds: 3,
    };
  }

  componentDidMount() {
    this.countdown();
  }

  componentWillUnmount() {
    if (timeout) {
      clearInterval(timeout);
    }
  }

  countdown = () => {
    timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds <= 0) {
        clearInterval(timeout);
        this.handleNavigation();
      }
    }, 1000);
  };

  handleNavigation = () => {
    this.props.globalAction.setRemindCompleteIntension(true);
    settingsAction.selectedTab('Job');
    NavigationService.reset('main');
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.page}>
        <View style={[globalStyle.container, registerStyle.resultContainer]}>
          <View style={registerStyle.resultSection}>
            <View>
              <Text style={registerStyle.successResultTitle}>
                {I18n.t('page_register_text_success_result')}
              </Text>
            </View>
            <View>
              <Image source={res.iconSuccess} style={registerStyle.iconResultSuccess} />
            </View>
            <View>
              <Text style={registerStyle.remainSeconds}>
                {this.state.seconds} {I18n.t('page_register_text_remain_second')}
              </Text>
            </View>
            <View style={registerStyle.btnSection}>
              <Button
                title={I18n.t('page_register_btn_use')}
                titleStyle={{ color: '#fff', fontSize: 18 }}
                onPress={() => {
                  this.handleNavigation();
                }}
                buttonStyle={registerStyle.btnUse}
                containerStyle={registerStyle.btnUseContainer}
              />
            </View>
          </View>
        </View>
      </View>
    );
  }
}
