import React, { Component } from 'react';
import { ScrollView, View, Text, TouchableOpacity } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import Debounce from 'debounce-decorator';
import {
  headerStyle,
  baseBlueColor,
  globalStyle,
  resumeStyle,
  bgColor,
  subTitleColor,
} from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';

@inject('resumeStore', 'resumeAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.name = '';
    this.state = {
      profile: { key: 'profile', postKey: 'profile', select: true },
      careerProfile: {
        key: 'careerProfile',
        postKey: 'careerProfile',
        select: false,
      },
      intention: { key: 'intention', postKey: 'intention', select: false },
      educations: { key: 'educations', postKey: 'educations', select: false },
      languageLevels: {
        key: 'languageLevels',
        postKey: 'languageLevels',
        select: false,
      },
      experiences: {
        key: 'experiences',
        postKey: 'experiences',
        select: false,
      },
      skills: { key: 'skills', postKey: 'skills', select: false },
      training: { key: 'training', postKey: 'training', select: false },
      hobby: { key: 'hobby', postKey: 'hobby', select: false },
      qualifications: {
        key: 'qualifications',
        postKey: 'qualifications',
        select: false,
      },
      description: { key: 'description', select: false },
    };
  }

  onCheckPress(v) {
    this.setState({
      [v.key]: { key: v.key, select: !v.select },
    });
  }

  onChangeName = (text) => {
    this.name = text;
  };

  copyItem(item, resume, crrentResume) {
    if (item.select) {
      resume[item.key] = crrentResume[item.key];
    }
  }

  @Debounce(500)
  save() {
    const { currentResume } = this.props.resumeStore;
    const resume = {};
    resume.name = this.name;
    resume.seekerId = currentResume.seekerId;
    resume.resumeId = currentResume.resumeId;
    this.copyItem(this.state.profile, resume, currentResume);
    this.copyItem(this.state.careerProfile, resume, currentResume);
    this.copyItem(this.state.intention, resume, currentResume);
    this.copyItem(this.state.educations, resume, currentResume);
    this.copyItem(this.state.languageLevels, resume, currentResume);
    this.copyItem(this.state.experiences, resume, currentResume);
    this.copyItem(this.state.skills, resume, currentResume);
    this.copyItem(this.state.qualifications, resume, currentResume);
    if (currentResume.training) {
      this.copyItem(this.state.training, resume, currentResume);
    }
    if (currentResume.hobby) {
      this.copyItem(this.state.hobby, resume, currentResume);
    }
    if (currentResume.description) {
      this.copyItem(this.state.description, resume, currentResume);
    }
    this.props.resumeAction.copyResume(resume).then((res) => {
      this.showRequestResult(res);
    });
  }

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  renderItem = (title, item) => (
    <ListItem
      title={title}
      checkBox={{
        checked: item.key === 'profile' ? true : item.select,
        uncheckedColor: bgColor,
        checkedColor: baseBlueColor,
        iconType: 'ionicon',
        checkedIcon: 'ios-checkmark-circle',
        uncheckedIcon: 'ios-checkmark-circle-outline',
        onPress: () => this.onCheckPress(item),
        containerStyle: {
          height: 40,
          width: 50,
          justifyContent: 'center',
          alignItems: 'flex-end',
        },
      }}
      titleStyle={resumeStyle.listItemLeftText}
      containerStyle={resumeStyle.listItemContent}
      onPress={() => this.onCheckPress(item)}
    />
  );

  render() {
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{
            barStyle: 'light-content',
            backgroundColor: '#2089DC',
          }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: I18n.t('page_resume_btn_manager_add'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={this.props.navigation} />}
          rightComponent={
            <TouchableOpacity onPress={() => this.save()}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ScrollView>
          <ListItem
            title={I18n.t('page_resume_label_manager_name')}
            input={{
              ref: (ref) => (this.input = ref),
              placeholder: I18n.t('page_resume_label_manager_name'),
              inputStyle: resumeStyle.inputText,
              onChangeText: this.onChangeName,
              // defaultValue: this.state.schoolName,
            }}
            chevron
            titleStyle={resumeStyle.listItemLeftText}
            containerStyle={[resumeStyle.listItemContent]}
            onPress={() => this.input.focus()}
          />
          <Text
            style={{
              marginTop: 10,
              marginBottom: 5,
              marginLeft: 15,
              color: subTitleColor,
              fontSize: 14,
            }}
          >
            {I18n.t('page_resume_label_select_other_info')}
          </Text>
          {this.renderItem(I18n.t('page_resume_label_profile'), this.state.profile)}
          {this.renderItem(I18n.t('page_resume_label_career'), this.state.careerProfile)}
          {this.renderItem(I18n.t('page_resume_label_job_like'), this.state.intention)}
          {this.renderItem(I18n.t('page_resume_label_education'), this.state.educations)}
          {this.renderItem(I18n.t('page_resume_label_language'), this.state.languageLevels)}
          {this.renderItem(I18n.t('page_resume_label_work_exp'), this.state.experiences)}
          {this.renderItem(I18n.t('page_resume_label_skill'), this.state.skills)}
          {this.renderItem(I18n.t('page_resume_label_training'), this.state.training)}
          {this.renderItem(I18n.t('page_resume_label_hobby'), this.state.hobby)}
          {this.renderItem(I18n.t('page_resume_label_qualification'), this.state.qualifications)}
          {this.renderItem(I18n.t('page_resume_label_appraise'), this.state.description)}
        </ScrollView>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
