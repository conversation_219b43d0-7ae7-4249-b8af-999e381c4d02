/**
 * 功能：多语言
 * 描述：修改语言
 * Author: sxw
 */
import React from 'react';
import { Text, View, Icon, Touchable, Header, BaseComponent, Image } from '../../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import Constant from '../../../store/constant';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
@inject('settingsAction', 'settingsStore')
@observer
export default class UpdateLanguage extends BaseComponent {
  constructor(props) {
    super(props);

    this.state = {
      selectedLanguage: this.props.settingsStore.language,
    };
  }

  onSave = () => {
    const {
      state: { selectedLanguage },
      props: {
        settingsAction,
        navigation,
        settingsStore: { language },
      },
    } = this;
    if (selectedLanguage === language) {
      navigation.goBack();
      return;
    }
    this.showGlobalLoading();
    const lang = Object.values(Constant.language).find((it) => it.code === selectedLanguage);
    settingsAction.switchLanguage(lang.code).then(
      () => {
        this.showRequestResult(I18n.t('page_setting_text_switch_language_success'));
        NavigationService.reset('main');
      },
      (err) => {
        this.showRequestResult(err?.message);
      }
    );
  };

  onValueChange = (selectedLanguage) => {
    this.setState({ selectedLanguage });
  };

  renderItemRight = () => {
    const { themeStyle } = styles.get(['theme']);
    return <Icon name="check" size={20} type="antdesign" color={themeStyle.primaryColor} />;
  };

  render() {
    const { personnelDetailStyle } = styles.get(['personnelDetail']);
    const { selectedLanguage } = this.state;
    const img = [
      resIcon.cnFlag,
      resIcon.usFlag,
      resIcon.khFlag,
      resIcon.vnFlag,
      resIcon.thFlag,
      resIcon.krFlag,
    ];
    const languageList = Object.values(Constant.language).map((item, index) => {
      return {
        label: item.display,
        code: item.code,
        img: img[index],
      };
    });
    return (
      <View style={personnelDetailStyle.container}>
        <Header
          rightTitle={I18n.t('page_resume_btn_save')}
          rightPress={this.onSave}
          title={I18n.t('page_setting_text_switch_language')}
        />
        {languageList.map((lang) => (
          <Touchable
            style={{
              marginTop: 5,
              height: 50,
              paddingHorizontal: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              backgroundColor: '#fff',
            }}
            key={lang.code}
            onPress={() => this.onValueChange(lang.code)}
          >
            <Image
              source={lang.img}
              resizeMode="cover"
              style={{ width: 24, height: 16, borderRadius: 2, marginRight: 10 }}
            />
            <Text
              style={{
                fontSize: 14,
                color: '#333',
                textAlign: 'left',
                flex: 1,
              }}
            >
              {lang.label}
            </Text>
            {lang.code === selectedLanguage ? this.renderItemRight() : null}
          </Touchable>
        ))}
      </View>
    );
  }
}
