import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Header, View, Text, Button, Input, BaseComponent } from '../../../components';
import { titleColor, settingStyle } from '../../../themes';
import I18n from '../../../i18n';

@inject('personStore', 'companyAction')
@observer
export default class ModifyPwd extends BaseComponent {
  constructor(props) {
    super(props);
    this.state = {
      oldPassword: '',
      newPassword: '',
      newConfirmPassword: '',
      showLoading: false,
    };
  }

  onSubmitPasswordForm = async () => {
    try {
      const pwd = /^([a-z0-9\.\@\!\#\$\%\^\&\*\(\)]){8,20}$/i; //密码长度8-20位,支持数字,字母,特殊字符

      const { oldPassword, newPassword, newConfirmPassword } = this.state;
      if (!oldPassword) {
        toast.show(I18n.t('page_setting_ph_input_old_password'));
        return;
      }
      if (oldPassword === newPassword) {
        toast.show(I18n.t('page_setting_tips_old_equ_new_password'));
        return;
      }
      if (!newPassword || !newPassword.trim() || !pwd.test(newPassword)) {
        toast.show(I18n.t('page_setting_text_password_length'));
        return;
      }
      if (!newConfirmPassword || !newConfirmPassword.trim() || !pwd.test(newConfirmPassword)) {
        toast.show(I18n.t('page_setting_text_password_length'));
        return;
      }
      if (newConfirmPassword !== newPassword) {
        toast.show(I18n.t('page_setting_ph_diff_password'));
        return;
      }
      this.setState({ showLoading: true });
      this.showGlobalLoading();
      const resl = await this.props.companyAction.changePassword({ oldPassword, newPassword });
      if (resl && resl.successful) {
        toast.show(resl.message);
        setTimeout(() => {
          this.props.navigation.goBack();
        }, 1000);
      } else if (resl && resl.fieldErrors) {
        toast.show(I18n.t('page_setting_text_password_length_error'));
      } else {
        toast.show(resl && resl.message);
      }
      this.showRequestResult();
      this.setState({ showLoading: false });
    } catch (error) {
      this.showRequestResult(error?.message);
      this.setState({ showLoading: false });
    }
  };

  onChangeOldText = (val) => {
    this.setState({ oldPassword: val });
  };

  onChangeNewText = (val) => {
    this.setState({ newPassword: val });
  };

  onChangeConfirmNewText = (val) => {
    this.setState({ newConfirmPassword: val });
  };

  renderPasswordInput = () => (
    <View style={settingStyle.passwordContainer}>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_old_password')}
          <Text style={{ color: '#fff' }}>空</Text>
        </Text>
        <Input
          onChangeText={(val) => this.onChangeOldText(val)}
          placeholder={I18n.t('page_resume_tips_input')}
          placeholderTextColor="#ccc"
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={20}
          underlineColorAndroid="transparent"
          selectionColor="#EF3D48"
        />
      </View>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_new_password')}
          <Text style={{ color: '#fff' }}>空</Text>
        </Text>
        <Input
          onChangeText={(val) => this.onChangeNewText(val)}
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          placeholder={I18n.t('page_resume_tips_input')}
          placeholderTextColor="#ccc"
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={20}
          underlineColorAndroid="transparent"
          selectionColor="#EF3D48"
        />
      </View>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_confirm_password')}
        </Text>
        <Input
          onChangeText={(val) => this.onChangeConfirmNewText(val)}
          placeholder={I18n.t('page_resume_tips_input')}
          placeholderTextColor="#ccc"
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={20}
          underlineColorAndroid="transparent"
          selectionColor="#EF3D48"
        />
      </View>
    </View>
  );

  renderSubmitButton = () => (
    <View
      style={{
        width: '100%',
        justifyContent: 'center',
        paddingVertical: 36,
        paddingHorizontal: 20,
      }}
    >
      <Button
        title={I18n.t('page_help_btn_feedback_submit')}
        btnType="login"
        onPress={() => {
          this.onSubmitPasswordForm();
        }}
      />
    </View>
  );

  render() {
    return (
      <View style={settingStyle.settingContainer}>
        <Header title={I18n.t('page_setting_text_modify_password')} />
        {this.renderPasswordInput()}
        {this.renderSubmitButton()}
      </View>
    );
  }
}
