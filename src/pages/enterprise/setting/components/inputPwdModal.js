import React, { Component } from 'react';
import { View, Text, Image, Button } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import InputLinePassword from '../../../../components/input/inputLinePassword';
import I18n from '../../../../i18n';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.primaryBgColor,
      marginTop: 10,
      paddingVertical: 18,
      paddingHorizontal: 20,
      height: 240,
    },
    amountTitle: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    costBox: {
      backgroundColor: 'red',
    },

    line: {
      borderStyle: 'dashed',
      borderWidth: 1,
      borderColor: '#CCCCCC',
      marginTop: 17,
      marginBottom: 4,
    },

    btnBottom: {
      position: 'absolute',
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 18,
      marginLeft: 20,
      bottom: 40,
    },
  };
}
/**
 * 密码确认框组件
 * <AUTHOR>
 */
export default class InputPwdModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      pwd: '',
    };
  }

  show = () => {
    this.setState({ showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = () => {
    const pwd = this.state.pwd.trim();
    if (!pwd) {
      this.modal.onShow(I18n.t('page_login_op_password_required'));
      return;
    } else if (pwd.length < 6) {
      this.modal.onShow(I18n.t('page_settings_unregister_pwd_error'));
      return;
    }

    if (this.props.onConfirm) {
      this.props.onConfirm(this.state.pwd);
    }
    this.close();
  };

  render() {
    const { btnType = 'login', ...rest } = this.props;
    const { showModal } = this.state;
    const { style } = this;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_register_ph_email_confirm_password')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={240}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={true}
        showBottomView={false}
        coverScreen
        style={{ backgroundColor: '#F7F7F7' }}
        positionValue={100}
        {...rest}
      >
        <View style={style.pagerContainer}>
          <Text style={style.amountTitle}>{I18n.t('page_login_op_password_required')}</Text>
          <InputLinePassword
            placeholder={I18n.t('page_login_op_password_required')}
            containerStyle={{ paddingHorizontal: 0 }}
            inputStyle={{ paddingLeft: 2, fontSize: 16 }}
            onChangeText={(text) => {
              this.setState({ pwd: text });
            }}
            maxLength={32}
          />

          <View style={style.btnBottom}>
            <Button
              title={I18n.t('page_setting_confirm_text')}
              btnType={btnType}
              btnSize="s44"
              containerStyle={{ width: '100%' }}
              onPress={this.onConfirm}
            />
          </View>
        </View>
      </BottomModal>
    );
  }
}
