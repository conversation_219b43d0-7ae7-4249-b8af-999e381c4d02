import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { hasEar } from '../../../common';
import RightArrow from '../../../components/rightArrow';
import PickerModal from './components/pickerModal';
import NavigationService from '../../../navigationService';
import regExp from '../../../util/regExp';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    jobText: {
      fontSize: 14,
      color: theme.primaryFontColor,
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
      marginTop: 7,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      marginBottom: 8,
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
    },

    addAddressText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      paddingBottom: 15,
      textAlign: 'center',
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 38,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
  };
}

/**
 * 添加联系人
 */
@inject('jobStore', 'companyAction')
@observer
export default class JobContactAdd extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.item = props.navigation.state.params?.item;
    console.log('this.item', this.item);
    this.positionList = props.jobStore.positionList;
    this.departmentList = props.jobStore.departmentList;
    this.chatStatusList = [
      { value: 'open', label: '开启' },
      { value: 'disabled', label: '未启用' },
    ];

    this.state = {
      form: {
        name: this.item?.name || '',
        telephone: this.item?.telephone || '',
        fax: this.item?.fax || '',
        email: this.item?.email || '',
        chatAccount: this.item?.chatAccount || '',
        chatPassword: this.item?.chatPassword || '',
      },
      department:
        this.departmentList?.find((x) => x.value === this.item?.departmentId.value) || null,
      position: this.positionList?.find((x) => x.value === this.item?.positionId.value) || null,
      chatStatus: this.item?.imEabled ? 'open' : 'disabled',
    };
  }

  componentDidMount() {}

  onConfirm = (type, value) => {
    console.log('onConfirm', type, value);
    if (type === 'chatStatus') {
      this.setState({ chatStatus: value });
    } else {
      const { form } = this.state;
      form[type] = value;
      this.setState({ form });
    }
  };

  onAddSave = async () => {
    try {
      const { form, department, position, chatStatus } = this.state;
      if (!form.name) {
        return toast.show(I18n.t('page_job_text_input_name'));
      } else if (!department) {
        return toast.show(I18n.t('page_job_text_select_department'));
      } else if (!position) {
        return toast.show(I18n.t('page_job_text_select_job'));
      } else if (!form.telephone) {
        return toast.show(I18n.t('page_job_text_input_phone'));
      } else if (!regExp.telephone.test(form.telephone)) {
        return toast.show(I18n.t('page_job_text_input_correct_phone'));
      }
      if (!form.email) {
        return toast.show(I18n.t('page_forgot_op_email_required'));
      } else if (!regExp.email.test(form.email)) {
        return toast.show(I18n.t('page_job_text_input_correct_email'));
      }

      // 验证聊天设置
      if (form.chatAccount && form.chatPassword) {
        if (!/^\d{6}$/.test(form.chatPassword)) {
          return toast.show('聊天密码必须是6位数字');
        }
      }

      this.showGlobalLoading();
      const formData = {
        ...form,
        departmentId: department?.value,
        positionId: position?.value,
        imEabled: chatStatus === 'open',
        hasIM: !!(form.chatAccount && form.chatPassword),
      };
      if (this.item) {
        formData.id = this.item.id;
      }
      const { addContact, editContact } = this.props.companyAction;
      const res = this.item ? await editContact(formData) : await addContact(formData);
      if (res && res.successful) {
        this.showRequestResult(
          this.item ? I18n.t('page_job_text_edit_success') : I18n.t('page_job_text_add_success')
        );
        this.props.navigation.state.params?.callback?.();
        NavigationService.goBack();
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  renderBaseInfo = () => {
    const { style, positionList, departmentList } = this;
    const { department, position } = this.state;
    const Item = this.renderItem;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_resume_label_name')}
          required
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          value={this.state.form?.name}
          onChangeText={(v) => this.onConfirm('name', v)}
        />
        <Item
          label={I18n.t('page_job_text_department')}
          required
          value={department?.label}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_job_text_department'),
              pickerType: 'department',
              selected: department?.value || (departmentList?.length ? departmentList[0].value : 0),
            })
          }
        />
        <Item
          label={I18n.t('page_resume_label_career_pos')}
          required
          value={position?.label}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_resume_label_career_pos'),
              pickerType: 'position',
              selected: position?.value || (positionList?.length ? positionList[0].value : 0),
            })
          }
        />
        <Item
          label={I18n.t('page_job_text_phone')}
          required
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          onChangeText={(v) => this.onConfirm('telephone', v)}
          keyboardType="phone-pad"
          maxLength={20}
          value={this.state.form?.telephone}
        />
        <Item
          label={I18n.t('page_job_text_fax')}
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          onChangeText={(v) => this.onConfirm('fax', v)}
          keyboardType="numeric"
          maxLength={20}
          value={this.state.form?.fax}
        />
        <Item
          label={I18n.t('page_resume_label_mail')}
          required
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          onChangeText={(v) => this.onConfirm('email', v)}
          keyboardType="email-address"
          value={this.state.form?.email}
        />

        {/* 聊天设置分隔线 */}
        <View style={style.separateLine} />

        <Item
          label="聊天账号"
          isInput
          placeholder="请输入聊天账号"
          onChangeText={(v) => this.onConfirm('chatAccount', v)}
          value={this.state.form?.chatAccount}
        />
        <Item
          label="设置聊天密码"
          isInput
          placeholder="请输入6位数字密码"
          onChangeText={(v) => this.onConfirm('chatPassword', v)}
          keyboardType="numeric"
          maxLength={6}
          value={this.state.form?.chatPassword}
        />
        <Item
          label="聊天状态"
          value={this.chatStatusList?.find((x) => x.value === this.state.chatStatus)?.label}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: '聊天状态',
              pickerType: 'chatStatus',
              selected: this.state.chatStatus || 'disabled',
            })
          }
        />
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_select'),
    onPress,
    isInput,
    onChangeText,
    required = false,
    maxLength = 30,
    keyboardType = 'default',
  }) => {
    const { style } = this;
    return (
      <View style={style.itemContainer}>
        <View style={style.itemHeaderContainer}>
          <Text style={style.labelText}>{label} </Text>
          {required ? <Text style={style.labelStarText}>*</Text> : null}
        </View>
        {isInput ? (
          <Input
            placeholder={placeholder}
            placeholderTextColor="#CCCCCC"
            selectionColor="#EF3D48"
            isInput={false}
            style={{ color: '#333', fontSize: 16 }}
            onChangeText={onChangeText}
            returnKeyType="done"
            maxLength={maxLength}
            keyboardType={keyboardType}
            value={value}
          />
        ) : (
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style } = this;
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('page_resume_btn_save')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ width: '100%' }}
          onPress={this.onAddSave}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>
            {!!this.item
              ? I18n.t('page_job_text_edit_contact')
              : I18n.t('page_job_text_add_contact')}
          </Text>
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {this.renderBaseInfo()}

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>

        {this.renderBottom()}

        <PickerModal
          ref={(ref) => (this.pickerModal = ref)}
          onConfirm={({ pickerType, value }) => {
            if (pickerType === 'chatStatus') {
              this.setState({ chatStatus: value });
            } else {
              this.setState({ [pickerType]: value });
            }
          }}
        />
      </View>
    );
  }
}
