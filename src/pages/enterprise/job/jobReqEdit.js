import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  Icon,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { hasEar } from '../../../common';
import RightArrow from '../../../components/rightArrow';
import FlatViewModal from './components/flatViewModal';
import PickerModal from './components/pickerModal';
import NavigationService from '../../../navigationService';
import { computed } from 'mobx';
import moment from 'moment';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
      paddingBottom: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    jobText: {
      fontSize: 14,
      color: theme.primaryFontColor,
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
      marginTop: 7,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
      marginRight: 20,
    },

    addAddressText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      paddingBottom: 15,
      textAlign: 'center',
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 14,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
    bottomItemContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      marginRight: 10,
    },
    bottomItem: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomItemImg: {
      width: 20,
      height: 20,
    },
    bottomItemText: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
      marginTop: 4,
    },

    leftBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    upStepText: {
      fontSize: 14,
      color: '#333',
      marginLeft: 5,
    },

    ageBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  };
}

/**
 * 编辑职位-职位要求
 */
@inject('jobStore', 'resumeStore', 'companyAction')
@observer
export default class JobReqEdit extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { item } = props.navigation.state.params || {};
    this.jobLevelList = props.resumeStore.jobLevelList;
    this.qualificationList = props.resumeStore.qualificationList;
    this.workYearList = props.resumeStore.workYearList;
    this.sexList = props.resumeStore.sexList;
    this.maritalStatusList = props.resumeStore.maritalStatusList;

    this.state = {
      detail: item,
      jobLevel: item?.jobLevelId || null,
      qualification: item?.qualificationId || null,
      workYear: this.workYearList.find((x) => x.value == item?.workyears) || null,
      sex: item?.sex || null,
      maritalStatus: this.maritalStatusList.find((x) => x.value == item?.marital?.value) || null,
      jobLangs:
        item?.jobLangs?.map((x) => {
          return {
            language: x.languageId,
            languageLevel: x.languageLevelId,
          };
        }) || [],
      form: {
        requirement: item?.requirement || '',
        ageFrom: item?.ageFrom || '',
        ageTo: item?.ageTo || '',
        major: item?.major || '',
      },
    };
  }

  componentDidMount() {}

  @computed get workYearLabel() {
    const { workYear } = this.state;
    if (!workYear) {
      return '';
    }
    if (workYear.value == 0) {
      return I18n.t('page_job_text_unlimited');
    }
    return I18n.t('page_job_text_how_many_year', { count: parseInt(workYear.label) });
  }

  onConfirm = (type, value) => {
    console.log('onConfirm', type, value);
    const { form } = this.state;
    form[type] = value;
    this.setState({ form });
  };

  onJobDesc = () => {
    NavigationService.navigate('jobDesc', {
      isBaseInfo: false,
      callback: (requirement) => this.setState({ form: { ...this.state.form, requirement } }),
      description: this.state.form['requirement'],
    });
  };

  onStartAgeChangeText = (text) => {
    console.log('onStartAgeChangeText', text);
    this.onConfirm('ageFrom', text);
  };

  onEndAgeChangeText = (text) => {
    console.log('onEndAgeChangeText', text);
    this.onConfirm('ageTo', text);
  };

  onAddLang = (lang, i) => {
    NavigationService.navigate('jobLanguage', {
      callback: (languageRequire, languageIndex) => {
        const { jobLangs } = this.state;
        const index = jobLangs.findIndex((x) => x.language.value == languageRequire.language.value);
        if (index != -1) {
          jobLangs[index] = languageRequire;
        } else if (languageIndex != null) {
          jobLangs[languageIndex] = languageRequire;
        } else {
          jobLangs.push(languageRequire);
        }
        this.setState({ jobLangs });
      },
      languageRequire: lang,
      languageIndex: i,
    });
  };

  onDelLang = (lang) => {
    const { jobLangs } = this.state;
    const index = jobLangs.findIndex(
      (x) =>
        x.language.value == lang.language.value && x.languageLevel.value == lang.languageLevel.value
    );
    if (index != -1) {
      jobLangs.splice(index, 1);
    }
    this.setState({ jobLangs });
  };

  onSave = () => {
    const { form, jobLevel, qualification, workYear, sex, maritalStatus, jobLangs, detail } =
      this.state;
    if (form['ageFrom'] || form['ageTo']) {
      if (!form['ageFrom'] || !form['ageTo']) {
        return toast.show(I18n.t('page_job_text_input_age_range'));
      } else if (parseInt(form['ageFrom']) < 1) {
        return toast.show(I18n.t('page_job_text_input_start_age_error'));
      } else if (parseFloat(form['ageFrom']) >= parseFloat(form['ageTo'])) {
        return toast.show(I18n.t('page_job_text_input_age_range_error'));
      }
    }
    const formData = {
      ...form,
      jobLevelId: jobLevel, // 职业等级
      qualificationId: qualification, // 学历要求
      workyears: workYear?.value, // 工作年限
      jobLangs: jobLangs.map((x) => {
        return {
          languageId: x.language,
          languageLevelId: x.languageLevel,
        };
      }), // 语言要求
      sex: sex, // 性别
      marital: maritalStatus, // 婚姻状况
      mapImages: detail?.mapImages, // 地图图片
    };
    console.log('formData', formData);
    const { callback } = this.props.navigation.state.params || {};
    callback && callback(formData);
    NavigationService.goBack();
  };

  renderBaseInfo = () => {
    const { style, jobLevelList, qualificationList, workYearList } = this;
    const { form, qualification, jobLevel, workYear, sex, maritalStatus, jobLangs } = this.state;
    const languageRequire = jobLangs?.length ? jobLangs[0] : null;

    const Item = this.renderItem;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_job_text_job_level')}
          value={jobLevel?.label}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_job_text_job_level'),
              pickerType: 'jobLevel',
              selected: jobLevel?.value || (jobLevelList?.length ? jobLevelList[0].value : 0),
            })
          }
        />
        <Item
          label={I18n.t('page_job_text_education_require')}
          value={qualification?.label}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_job_text_education_require'),
              pickerType: 'qualification',
              selected:
                qualification?.value ||
                (qualificationList?.length ? qualificationList[0].value : 0),
            })
          }
        />
        <Item
          label={I18n.t('page_job_text_major')}
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          onChangeText={(v) => this.onConfirm('major', v)}
          value={form['major']}
        />
        <Item
          label={I18n.t('page_job_text_work_years')}
          value={this.workYearLabel}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_job_text_work_years'),
              pickerType: 'workYear',
              selected: workYear?.value || (workYearList?.length ? workYearList[0].value : 0),
            })
          }
        />
        <Item
          label={I18n.t('page_job_text_language_require')}
          isLang
          value={
            languageRequire
              ? `${languageRequire?.language?.label}-${languageRequire?.languageLevel?.label}`
              : null
          }
          onPress={() => this.onAddLang(languageRequire, 0)}
        />
        {jobLangs?.slice(1)?.map((x, index) => (
          <View key={index}>
            <Item
              label={I18n.t('page_job_text_language_require') + (index + 2)}
              showDel
              value={x ? `${x?.language?.label}-${x?.languageLevel?.label}` : null}
              onLangPress={() => this.onDelLang(x)}
              onPress={() => this.onAddLang(x, index + 1)}
            />
          </View>
        ))}
        <Item
          label={I18n.t('page_resume_label_sex')}
          value={sex?.label}
          onPress={() =>
            this.flatViewModal.wrappedInstance.show({
              title: I18n.t('page_job_text_select_sex'),
              type: 'sex',
              selected: sex?.value,
            })
          }
        />

        <Item
          label={I18n.t('page_job_text_age')}
          isInput
          isAge
          placeholder={I18n.t('page_resume_tips_input')}
        />
        <Item
          label={I18n.t('page_job_text_married_status')}
          value={maritalStatus?.label}
          onPress={() =>
            this.flatViewModal.wrappedInstance.show({
              title: I18n.t('page_job_text_married_detail'),
              type: 'maritalStatus',
              selected: maritalStatus?.value,
            })
          }
        />
        <Item
          label={I18n.t('page_job_desc')}
          value={form['requirement'] || ''}
          placeholder={I18n.t('page_resume_tips_input')}
          onPress={this.onJobDesc}
        />
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_select'),
    onPress,
    isInput,
    onChangeText,
    required = false,
    isAge = false,
    maxLength = 30,
    showDel = false,
    isLang = false,
    onLangPress,
  }) => {
    const { style } = this;
    const { form } = this.state;
    return (
      <View style={style.itemContainer}>
        <View style={style.itemHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
            {isLang ? (
              <Touchable onPress={() => this.onAddLang(null, null)}>
                <Icon name="pluscircleo" type="antdesign" size={22} color="#3299FF" />
              </Touchable>
            ) : null}
          </View>
          {showDel ? (
            <Touchable onPress={onLangPress}>
              <Icon name="closecircleo" type="antdesign" size={22} color="#3299FF" />
            </Touchable>
          ) : null}
        </View>
        {isInput ? (
          !isAge ? (
            <Input
              placeholder={placeholder}
              placeholderTextColor="#CCCCCC"
              selectionColor="#EF3D48"
              isInput={false}
              style={{ color: '#333', fontSize: 16 }}
              onChangeText={onChangeText}
              maxLength={maxLength}
              value={value}
            />
          ) : (
            <View style={style.ageBox}>
              <Input
                placeholder={placeholder}
                placeholderTextColor="#CCCCCC"
                selectionColor="#EF3D48"
                isInput={false}
                style={{ color: '#333', fontSize: 16, width: '50%' }}
                onChangeText={this.onStartAgeChangeText}
                keyboardType="number-pad"
                inputType="age"
                value={form['ageFrom'] + ''}
              />
              <Text>{I18n.t('page_job_text_to')}</Text>
              <Input
                placeholder={placeholder}
                placeholderTextColor="#CCCCCC"
                selectionColor="#EF3D48"
                isInput={false}
                style={{ color: '#333', fontSize: 16, width: '50%', marginLeft: 20 }}
                onChangeText={this.onEndAgeChangeText}
                keyboardType="number-pad"
                inputType="age"
                value={form['ageTo'] + ''}
              />
            </View>
          )
        ) : (
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text
              style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]}
              textType="amount"
              numberOfLines={1}
            >
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style } = this;
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('page_resume_btn_save')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ width: '100%' }}
          onPress={this.onSave}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_job_text_edit_job_require')}</Text>
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
        >
          {this.renderBaseInfo()}

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>

        {this.renderBottom()}

        <PickerModal
          ref={(ref) => (this.pickerModal = ref)}
          onConfirm={({ pickerType, value }) => {
            console.log('onConfirm', pickerType, value);
            const data = {};
            data[pickerType] = value;
            this.setState({ ...data });
          }}
        />
        <FlatViewModal
          ref={(ref) => (this.flatViewModal = ref)}
          onConfirm={({ type, value }) => this.setState({ [type]: value })}
        />
      </View>
    );
  }
}
