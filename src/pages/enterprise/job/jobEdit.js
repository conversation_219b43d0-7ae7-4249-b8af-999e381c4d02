import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  Icon,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { hasEar } from '../../../common';
import RightArrow from '../../../components/rightArrow';
import LanguageModal from './components/languageModal';
import JobNatureModal from './components/jobNatureModal';
import DatePickerModal from './components/datePickerModal';
import SalaryPickerModal from './components/salaryPickerModal';
import RecruitmentNumberModal from './components/recruitmentNumberModal';
import NavigationService from '../../../navigationService';
import moment from 'moment';
import constant from '../../../store/constant';
import regExp from '../../../util/regExp';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
      paddingBottom: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    jobText: {
      fontSize: 14,
      color: theme.primaryFontColor,
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
      marginTop: 7,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
    },

    addAddressText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      paddingBottom: 15,
      textAlign: 'center',
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 38,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
  };
}

/**
 * 编辑职位-职位基本信息
 */
@inject('jobStore', 'companyAction', 'jobAction')
@observer
export default class JobEdit extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { item } = props.navigation.state.params || {};
    this.jobTypeList = props.jobStore.jobTypeList;
    this.languages = props.jobStore.languages;

    this.state = {
      detail: item,
      language: this.languages?.find((x) => x.value == item?.language),
      jobNature: this.jobTypeList?.find((x) => x.value == item?.termId?.value),
      jobSalary: item?.salaryId || null,
      jobCategory: item?.categoryId || null,
      contact: item?.contact || null,
      form: {
        title: item?.title || '',
        pubdate: item?.pubdate ? moment(item?.pubdate).format('YYYY/MM/DD') : '',
        hirelings: item?.hirelings || '',
        description: item?.description || '',
      },
      addressList: [],
    };
  }

  componentDidMount() {
    this.initAddressList();
    global.emitter.on(constant.event.changeAddress, this.onChangeAddress);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.changeAddress, this.onChangeAddress);
  }

  onChangeAddress = (v) => {
    const { addressList } = this.state;
    const index = addressList?.findIndex((x) => x.timestamp == v.timestamp);
    if (index != -1) {
      addressList[index].address = v.dAddress;
      addressList[index].dAddress = v.dAddress;
      this.setState({ addressList });
    }
  };

  initAddressList = async () => {
    const { item } = this.props.navigation.state.params || {};
    const res = await this.props.jobAction.getLocalAddressList();
    this.setState({
      addressList:
        item?.locations?.map((x) => {
          return {
            dAddress: x.address || '',
            address: x.address || '',
            placeId: x.addressCode,
            location: x.location,
            locationId: x.locationId?.value,
            timestamp: res?.find((y) => `${y.cityName + ' ' + y.address}` == x.address)?.timestamp,
          };
        }) || [],
    });
  };

  onConfirm = (type, value) => {
    console.log('onConfirm', type, value);
    const { form } = this.state;
    form[type] = value;
    this.setState({ form });
  };

  onAddAddress = (location, i) => {
    NavigationService.navigate('jobAddress', {
      callback: (v, addressIndex) => {
        const { addressList } = this.state;

        const index = addressList.findIndex((x) => x.dAddress == v.dAddress);
        if (index != -1) {
          addressList[index] = v;
        } else if (addressIndex != null) {
          addressList[addressIndex] = v;
        } else {
          addressList.push(v);
        }
        this.setState({ addressList });
      },
      location,
      addressIndex: i,
    });
  };

  onDelAddress = (item) => {
    const { addressList } = this.state;
    const index = addressList?.findIndex((x) => x.timestamp == item.timestamp);
    addressList.splice(index, 1);
    this.setState({ addressList });
  };

  onSelectContact = () => {
    NavigationService.navigate('jobContact', {
      callback: (v) => this.setState({ contact: v }),
    });
  };

  onNext = () => {
    const { detail, form, language, jobNature, jobSalary, jobCategory, contact, addressList } =
      this.state;
    const now = moment().format('YYYY/MM/DD');
    if (!form['title']?.trim()) {
      return toast.show(I18n.t('page_job_text_input_job_name'));
    } else if (!regExp.jobTitle.test(form['title'].trim())) {
      return toast.show(I18n.t('page_job_text_job_name_length'));
    } else if (!jobNature) {
      return toast.show(I18n.t('page_job_text_select_job_type'));
    } else if (!form['pubdate']) {
      return toast.show(I18n.t('page_job_text_select_publish_date'));
    } else if (detail?.status?.value != 1 && moment(form['pubdate']).isBefore(now)) {
      return toast.show(I18n.t('page_job_text_select_publish_date_error'));
    } else if (!jobCategory) {
      return toast.show(I18n.t('page_job_text_select_job_category'));
    } else if (!jobSalary) {
      return toast.show(I18n.t('page_job_text_select_salary'));
    } else if (!form['hirelings']) {
      return toast.show(I18n.t('page_job_text_select_recruit_number'));
    } else if (!contact) {
      return toast.show(I18n.t('page_job_text_select_contact'));
    } else if (!addressList?.length) {
      return toast.show(I18n.t('page_job_tips_add_work_place'));
    }

    const formData = {
      ...form,
      termId: jobNature, // 职位性质
      categoryId: jobCategory, // 职位类别
      salaryId: jobSalary, // 薪资待遇
      hirelings: form['hirelings'], // 招聘人数
      contact, // 联系人
      language: language?.value,
      locations:
        addressList?.map((x) => ({
          address: x.dAddress || '',
          addressCode: x.placeId,
          location: x.location,
          locationId: {
            value: x.locationId,
          },
        })) || [],
    };
    const { callback } = this.props.navigation.state.params || {};
    formData.pubdate = moment(formData.pubdate, 'YYYY/MM/DD').toDate();
    formData.pubdate_unixtime = moment(formData.pubdate).unix();
    callback && callback(formData);
    NavigationService.goBack();
  };

  onJobIntention = () => {
    const { jobCategory } = this.state;
    NavigationService.navigate('jobCategory', {
      jobCategory,
      callback: (v) => this.setState({ jobCategory: v }),
    });
  };

  onJobDesc = () => {
    NavigationService.navigate('jobDesc', {
      isBaseInfo: true,
      callback: (description) => this.setState({ form: { ...this.state.form, description } }),
      description: this.state.form['description'],
    });
  };

  onShowDatePicker = (date) => {
    const dateObj = moment(date, 'YYYY/MM/DD');
    this.datePickerModal.show(dateObj);
  };

  renderBaseInfo = () => {
    const { style } = this;
    const { form, language, jobNature, jobSalary, jobCategory, contact, addressList, detail } =
      this.state;
    const location = addressList?.length ? addressList[0] : null;
    const Item = this.renderItem;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_job_text_publish_language')}
          required
          value={language?.label || ''}
          onPress={() => this.languageModal.wrappedInstance.show(language?.value)}
        />
        <Item
          label={I18n.t('page_resume_label_work_name')}
          required
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          onChangeText={(v) => this.onConfirm('title', v)}
          value={form['title']}
        />
        <Item
          label={I18n.t('page_job_text_type_of_job')}
          required
          value={jobNature?.label || ''}
          onPress={() => this.jobNatureModal.wrappedInstance.show(jobNature?.value)}
        />
        <Item
          label={I18n.t('page_job_text_pubdate')}
          required
          value={form['pubdate']}
          onPress={() => this.onShowDatePicker(form['pubdate'])}
          disabled={detail?.status?.value == 1}
        />
        <Item
          label={I18n.t('page_job_text_job_category')}
          required
          value={jobCategory?.label || ''}
          onPress={this.onJobIntention}
        />
        <Item
          label={I18n.t('page_job_text_salary')}
          required
          value={jobSalary?.label || ''}
          onPress={() => this.salaryPickerModal.wrappedInstance.show(jobSalary?.value || 1)}
        />
        <Item
          label={I18n.t('page_job_text_recruit_number')}
          required
          value={form['hirelings']}
          onPress={() => this.recruitmentNumberModal.show(form['hirelings'] || 1)}
        />
        <Item
          label={I18n.t('page_mine_interview_contact')}
          required
          value={contact?.name || ''}
          onPress={this.onSelectContact}
        />
        <Item
          label={I18n.t('page_resume_label_work_des')}
          value={form['description'] || ''}
          placeholder={I18n.t('page_resume_tips_input')}
          onPress={this.onJobDesc}
        />
        <Item
          label={I18n.t('page_job_text_work_place')}
          required
          value={location ? `${location.dAddress}` : ''}
          onPress={() => this.onAddAddress(location, 0)}
        />
        {addressList?.slice(1)?.map((x, index) => (
          <View key={index}>
            <Item
              label={I18n.t('page_job_text_work_place') + (index + 2)}
              value={`${x.dAddress}`}
              onPress={() => this.onAddAddress(x, i + 1)}
              onAddressPress={() => this.onDelAddress(x)}
              showDel
            />
          </View>
        ))}
        <Touchable onPress={() => this.onAddAddress(null, null)}>
          <Text style={style.addAddressText}>+ {I18n.t('page_job_text_add_place')}</Text>
        </Touchable>
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_select'),
    onPress,
    isInput,
    onChangeText,
    maxLength = 30,
    required = false,
    onAddressPress,
    showDel = false,
    disabled = false,
  }) => {
    const { style } = this;
    return (
      <View style={style.itemContainer}>
        <View style={style.itemHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
          {showDel ? (
            <Touchable onPress={onAddressPress}>
              <Icon name="closecircleo" type="antdesign" size={22} color="#3299FF" />
            </Touchable>
          ) : null}
        </View>
        {isInput ? (
          <Input
            placeholder={placeholder}
            placeholderTextColor="#CCCCCC"
            selectionColor="#EF3D48"
            isInput={false}
            style={{ color: '#333', fontSize: 16 }}
            onChangeText={onChangeText}
            maxLength={maxLength}
            value={value}
          />
        ) : (
          <Touchable style={style.valueTextBox} onPress={!disabled ? onPress : null}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            {!disabled ? <RightArrow useImgArrow /> : null}
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style } = this;
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('page_resume_btn_save')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ width: '100%' }}
          onPress={this.onNext}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_job_text_edit_job_baseinfo')}</Text>
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {this.renderBaseInfo()}

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>

        {this.renderBottom()}

        <LanguageModal
          ref={(ref) => (this.languageModal = ref)}
          onConfirm={(v) => this.setState({ language: v })}
        />
        <JobNatureModal
          ref={(ref) => (this.jobNatureModal = ref)}
          onConfirm={(v) => this.setState({ jobNature: v })}
        />
        <DatePickerModal
          ref={(ref) => (this.datePickerModal = ref)}
          onConfirm={(v) => this.onConfirm('pubdate', v.date)}
        />
        <SalaryPickerModal
          ref={(ref) => (this.salaryPickerModal = ref)}
          onConfirm={(v) => this.setState({ jobSalary: v })}
        />
        <RecruitmentNumberModal
          ref={(ref) => (this.recruitmentNumberModal = ref)}
          onConfirm={(v) => this.onConfirm('hirelings', v)}
        />
      </View>
    );
  }
}
