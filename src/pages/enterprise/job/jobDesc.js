import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, Header, KeyboardAwareScrollView, Image, TextInput } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { dynamicStyle } from '../../../themes';
import NavigationService from '../../../navigationService';

const count = 1300;

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 20,
      marginBottom: 30,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    valueTextBox: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
      marginHorizontal: 12,
      backgroundColor: 'rgba(239,61,72,0.05)',
      padding: 8,
      borderRadius: 5,
    },
    valueText: {
      fontSize: theme.fontSizeM,
      color: '#666666',
      marginLeft: 8,
      flexShrink: 10,
    },
  };
}

/**
 * 职位描述
 */
@inject('jobStore')
@observer
export default class JobDesc extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { isBaseInfo, description } = this.props.navigation.state.params || {};
    this.isBaseInfo = isBaseInfo;
    this.state = {
      count,
      msgText: description || '',
    };
  }

  componentDidMount() {}

  onSave = () => {
    const { msgText } = this.state;
    const { callback } = this.props.navigation.state.params;
    callback?.(msgText);
    NavigationService.goBack();
  };

  render() {
    const { style } = this;
    const { msgText } = this.state;
    return (
      <View style={style.container}>
        <Header
          theme="dark"
          rightTitle={I18n.t('page_resume_btn_save')}
          rightTitleColor="#3299FF"
          rightBtnStyle={{ fontWeight: 'bold' }}
          rightPress={this.onSave}
        />

        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={style.userInfo}>
            <Text style={style.nameText}>
              {this.isBaseInfo ? I18n.t('page_work_desc') : I18n.t('page_job_desc')}
            </Text>
          </View>
          <View style={style.valueTextBox}>
            <Image source={resIcon.jobAlertEnterprise} />
            <Text style={style.valueText} textType="amount">
              {I18n.t('page_job_text_tips')}
            </Text>
          </View>

          <TextInput
            style={[
              dynamicStyle.inputText,
              dynamicStyle.editInputText,
              { height: 200, paddingBottom: 10 },
            ]}
            blurOnSubmit={false}
            placeholder={I18n.t('page_dynamic_ph_dynamic')}
            underlineColorAndroid="transparent"
            multiline
            maxLength={count}
            ref={(ref) => {
              this.inputText = ref;
            }}
            onChangeText={(text) => {
              this.setState({
                count: count - text.length,
                msgText: text,
              });
            }}
            textAlignVertical="top"
            selectionColor="#EF3D48"
            defaultValue={msgText}
          />
          <Text style={dynamicStyle.editTextCount}>
            <Text style={[dynamicStyle.numberCount, { color: '#EF3D48' }]}>{msgText.length}</Text>/
            {count}
          </Text>

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>
      </View>
    );
  }
}
