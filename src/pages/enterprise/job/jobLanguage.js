import React from 'react';
import { inject, observer } from 'mobx-react';
import Debounce from 'debounce-decorator';
import { View, Text, Touchable, Header } from '../../../components';
import { deviceWidth, WIDTH, deviceHeight } from '../../../common';
import PageFlatList from '../../../components/list/pageFlatList';
import styles from '../../../themes/enterprise';
import NavigationService from '../../../navigationService';
import I18n from '../../../i18n';
import SafeView from '../../../components/safeView';
import NoData from '../../../components/empty/noData';

/**
 * 功能：语言要求
 * 描述：
 * Author: sxw
 */
function getComponentStyle() {
  const theme = styles.get('theme');
  return {
    listBgColor: theme.listBgColor,
    fontWeightMedium: theme.fontWeightMedium,
    container: {
      flex: 1,
      backgroundColor: theme.secondaryColor,
    },
    languageContainer: {
      backgroundColor: theme.primaryBgColor,
      position: 'relative',
      flex: 1,
    },
    languageInfoContainer: {
      backgroundColor: theme.primaryBgColor,
    },
    levelInfoContainer: {
      position: 'absolute',
      top: 0,
      right: 18,
      width: WIDTH(deviceWidth * 0.673 - 18),
      height: deviceHeight,
      backgroundColor: theme.primaryBgColor,
      // paddingLeft: 15,
      // paddingRight: 16,
      marginRight: -18,
      shadowColor: '#000000',
      shadowOffset: { width: -4, height: 3 },
      shadowOpacity: 0.05,
      elevation: 0,
      //   marginRight: 10
    },
    placeholderContainer: {
      paddingHorizontal: 18,
      backgroundColor: theme.checkedColor,
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: 12,
      paddingBottom: 8,
    },
    placeholderText: {
      color: theme.checkedFontColor,
      fontSize: theme.fontSizeM,
      lineHeight: 20,
    },
    line: {
      height: 0.5,
      backgroundColor: theme.headerBottomBorderColor,
    },
    searchContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingVertical: 10,
    },
    languageItemMain: {
      paddingLeft: 18,
    },
    levelItemMain: {
      paddingHorizontal: 15,
    },
    levelItemInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      //   paddingRight: 17,
    },
    nameText: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      lineHeight: 22,
      paddingVertical: 20,
    },
    htUserLabelText: {
      color: theme.minorFontColor,
      fontSize: theme.fontSizeS,
      lineHeight: 17,
    },
  };
}

@inject('jobStore', 'resumeStore')
@observer
export default class JobLanguage extends React.Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle();
    const { languageRequire } = this.props.navigation.state.params || {};

    this.languageList = props.resumeStore.languageList;
    this.languageLevelList = props.resumeStore.languageLevelList;

    this.state = {
      currentLanguage: languageRequire?.language,
      currentLanguageLevel: languageRequire?.languageLevel,
      languages: this.languageList,
      levels: this.languageLevelList,
      isShowLevel: !!languageRequire,
    };
  }

  componentDidMount() {}

  onChangeText = (text) => {
    this.searchName = text;
    this.filterName();
  };

  onSubmitEditing = () => {
    this.filterName();
  };

  @Debounce(300)
  filterName() {
    this.pageFlatList?.onRefresh();
  }

  onSelectLanguage = (item) => {
    this.setState({ currentLanguage: item, currentLanguageLevel: null, isShowLevel: true });
  };

  onClickLevel = (item) => {
    const { currentLanguage } = this.state;
    const { callback, languageIndex } = this.props.navigation.state.params || {};
    callback &&
      callback(
        {
          language: currentLanguage,
          languageLevel: item,
        },
        languageIndex
      );
    NavigationService.goBack();
  };

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  renderLanguageItem({ item }) {
    const { style } = this;
    const { currentLanguage } = this.state;
    return (
      <Touchable onPress={() => this.onSelectLanguage(item)} style={style.languageItemMain}>
        <Text
          style={[
            style.nameText,
            currentLanguage?.value === item?.value
              ? { color: '#EF3D48', fontWeight: style.fontWeightMedium }
              : {},
          ]}
        >
          {item?.label}
        </Text>
        <View style={style.line} />
      </Touchable>
    );
  }

  renderLevelItem = ({ item, index }) => {
    const { style } = this;
    const { currentLanguageLevel } = this.state;
    return (
      <Touchable onPress={() => this.onClickLevel(item)} style={style.levelItemMain}>
        {index == 0 ? <View style={style.line} /> : null}
        <View style={style.levelItemInfo}>
          <Text
            style={[
              style.nameText,
              item?.value == currentLanguageLevel?.value
                ? { color: style.htUserLabelText.color }
                : {},
            ]}
          >
            {item?.label}
          </Text>
          {item?.value == currentLanguageLevel?.value ? (
            <Text style={style.htUserLabelText}>{I18n.t('page_job_text_selected')}</Text>
          ) : null}
        </View>
        <View style={style.line} />
      </Touchable>
    );
  };

  keyExtractor = ({ item, index, key }) => {
    return `${item && item[key] ? item[key] : index}`;
  };

  render() {
    const { style } = this;
    const { languages, levels, isShowLevel, currentLanguage, currentLanguageLevel } = this.state;
    return (
      <View style={style.container}>
        <Header title={I18n.t('page_job_text_language_require')} />

        {languages?.length ? (
          <View style={style.placeholderContainer}>
            <Text style={style.placeholderText}>
              {I18n.t('page_job_text_has_selected')}：
              {currentLanguage
                ? `${currentLanguage?.label}-${currentLanguageLevel?.label || ''}`
                : ''}
            </Text>
            <View style={style.line} />
          </View>
        ) : null}

        <View style={style.languageContainer}>
          <View style={style.languageInfoContainer}>
            <PageFlatList
              data={languages}
              keyExtractor={(item, index) => this.keyExtractor({ item, index, key: 'value' })}
              renderItem={(item) => this.renderLanguageItem(item)}
              ListFooterComponent={() => <SafeView bottomHeight={120} />}
              ListEmptyComponent={() => <NoData />}
            />
          </View>
          {isShowLevel ? (
            <View style={style.levelInfoContainer}>
              <PageFlatList
                ref={this.initPageFlatList}
                keyExtractor={(item, index) => this.keyExtractor({ item, index, key: 'value' })}
                data={levels}
                renderItem={this.renderLevelItem}
                ListFooterComponent={() => <SafeView bottomHeight={120} />}
                ListEmptyComponent={() => <NoData imageStyle={{ width: 120, height: 90 }} />}
              />
              <SafeView bottomHeight={80} />
            </View>
          ) : null}
        </View>
      </View>
    );
  }
}
