import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Image,
  Alert,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { hasEar } from '../../../common';
import NavigationService from '../../../navigationService';
import NoData from '../../../components/empty/noData';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 20,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    itemContainer: {
      marginBottom: 15,
      marginHorizontal: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 5,
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      flexShrink: 10,
      marginRight: 10,
    },
    btnBox: {
      minWidth: 74,
      height: 32,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: '#EF3D48',
    },
    btnText: {
      fontSize: theme.fontSizeM,
      color: '#EF3D48',
      lineHeight: 30,
      textAlign: 'center',
    },
    jobText: {
      fontSize: theme.fontSizeM,
      color: '#484848',
      marginBottom: 10,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: '#484848',
      lineHeight: 20,
      marginLeft: 8,
      flexShrink: 10,
    },
    bottomContainer: {
      flex: 0,
      paddingVertical: 20,
      paddingBottom: hasEar ? 34 : 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
    },
    lineH: {
      width: 1,
      height: 23,
      backgroundColor: '#cccccc',
    },
    bottomText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
    },
  };
}

/**
 * 工作地点
 */
@inject('jobStore', 'jobAction')
@observer
export default class JobAddress extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);

    this.state = {
      list: [],
      isManager: false,
    };
  }

  componentDidMount() {
    this.getJobAddress();
  }

  getJobAddress = async () => {
    try {
      this.showGlobalLoading();
      const res = await this.props.jobAction.getLocalAddressList();
      this.setState({
        list: res || [],
      });
      this.showRequestResult();
    } catch (error) {}
  };

  onItemPress = (item) => {
    const { isManager } = this.state;
    if (isManager) {
      Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_job_text_delete_address'), [
        {
          text: I18n.t('page_setting_cancel_text'),
          onPress: () => {},
        },
        {
          text: I18n.t('page_job_btn_coonfirm'),
          onPress: () => this.onDeleteContact(item),
        },
      ]);
      return;
    }
    const { callback, addressIndex } = this.props.navigation.state.params || {};
    callback?.(item, addressIndex);
    NavigationService.goBack();
  };

  onDeleteContact = async (item) => {
    try {
      this.showGlobalLoading();
      await this.props.jobAction.deleteLocalAddress(item);
      this.showRequestResult(I18n.t('page_job_text_delete_address_success'));
      this.getJobAddress();
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onDetail = (item) => {
    NavigationService.navigate('jobAddressAdd', {
      item,
      callback: this.getJobAddress,
    });
  };

  onManagerAddress = () => {
    this.setState({
      isManager: !this.state.isManager,
    });
  };

  onAddAddress = () => {
    NavigationService.navigate('jobAddressAdd', {
      callback: this.getJobAddress,
    });
  };

  renderItem = ({ item }) => {
    const { style } = this;
    const { isManager } = this.state;
    return (
      <Touchable style={style.itemContainer} onPress={() => this.onDetail(item)}>
        <View style={style.itemHeaderContainer}>
          <Text style={style.labelText}>{item.cityName} </Text>
          <Touchable
            style={[style.btnBox, isManager ? { backgroundColor: '#EF3D48' } : {}]}
            onPress={() => this.onItemPress(item)}
          >
            <Text style={[style.btnText, isManager ? { color: '#fff' } : {}]}>
              {this.state.isManager
                ? I18n.t('page_resume_annex_delete')
                : I18n.t('page_job_text_select')}
            </Text>
          </Touchable>
        </View>
        <View style={style.valueTextBox}>
          <Image source={resIcon.resumeInfo4Enterprise} />
          <Text style={style.valueText} textType="amount">
            {item.address}
          </Text>
        </View>
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    const { list } = this.state;
    const Item = this.renderItem;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_job_text_select_work_place')}</Text>
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          <View style={{ flex: 1, marginTop: 20 }}>
            {list?.length ? (
              list?.map((item, index) => (
                <View key={index}>
                  <Item index={index} item={item} />
                </View>
              ))
            ) : (
              <NoData />
            )}
          </View>
        </KeyboardAwareScrollView>
        <View style={style.bottomContainer}>
          <Touchable onPress={this.onManagerAddress}>
            <Text style={style.bottomText}>
              {' '}
              {this.state.isManager ? I18n.t('op_complete_title') : I18n.t('op_manage_title')}
            </Text>
          </Touchable>
          <View style={style.lineH} />
          <Touchable onPress={this.onAddAddress}>
            <Text style={style.bottomText}>+{I18n.t('page_job_text_add_text')}</Text>
          </Touchable>
        </View>
      </View>
    );
  }
}
