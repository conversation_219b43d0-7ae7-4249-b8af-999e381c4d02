import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Picker } from '../../../../components';
import BottomModal from '../../../../components/modal/bottomModal';
import I18n from '../../../../i18n';

@inject('jobStore')
@observer
export default class SalaryPickerModal extends Component {
  constructor(props) {
    super(props);
    this.salaryList = props.jobStore.jobSalaryList;
    this.state = {
      isOpen: false,
      selectedSalary: 1,
    };
  }

  onConfirm = () => {
    const { selectedSalary } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm(this.salaryList.find((x) => x.value == selectedSalary));
    }
    this.hide();
  };

  show = (data) => {
    this.setState({ selectedSalary: data, isOpen: true });
  };

  hide = () => {
    this.setState({ isOpen: false });
  };

  renderSalaryPicker = () => {
    const { selectedSalary } = this.state;
    return (
      <View style={{ flexDirection: 'row' }}>
        <Picker
          selectedValue={selectedSalary}
          style={{ flex: 1 }}
          itemStyle={{ fontSize: 18 }}
          onValueChange={(itemValue) => {
            console.log(itemValue);
            this.setState({ selectedSalary: itemValue });
          }}
        >
          {this.salaryList.map((item, index) => this.renderPickerItem(item, index))}
        </Picker>
      </View>
    );
  };

  renderPickerItem = (item, i) => (
    <Picker.Item key={i} label={item.label} value={item.value} color="#333" />
  );

  render() {
    const { ...rest } = this.props;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_job_text_salary')}
        showCancel={false}
        rightTitle={I18n.t('op_complete_title')}
        rightAction={this.onConfirm}
        contentHeight={200}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={this.state.isOpen}
        btnSize="lg"
        {...rest}
      >
        {this.renderSalaryPicker()}
      </BottomModal>
    );
  }
}
