import React, { Component } from 'react';
import { View, Picker } from '../../../../components';
import moment from 'moment';
import util from '../../../../util';
import BottomModal from '../../../../components/modal/bottomModal';
import I18n from '../../../../i18n';

export default class DatePickerModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      yearsData: [],
      monthsData: [],
      weeksData: [],
      daysData: [],
      selectedYear: util.getNowYear().toString(),
      selectedMonth: util.getNowMonth().toString(),
      selectedDay: util.getNowDay().toString(),
    };
  }

  initData = (date) => {
    const { selectedYear, selectedMonth, selectedDay } = this.state;
    this.getYearsData();
    this.getMonthsData();
    let yearText = '';
    let monthText = '';
    let dayText = '';
    if (date) {
      yearText = moment(date).get('year');
      monthText = moment(date).get('month');
      dayText = moment(date).get('date');
      this.setState({
        selectedYear: yearText.toString(),
        selectedMonth: (monthText + 1).toString(),
        selectedDay: dayText.toString(),
      });
    }
    this.getDaysData(yearText || selectedYear, monthText ? monthText + 1 : selectedMonth);
  };

  getYearsData = () => {
    if (this.state.yearsData.length > 0) {
      return;
    }
    const yearsArray = [];
    const maxYear = moment().year() + 76;
    for (let i = 2016; i <= maxYear; i += 1) {
      yearsArray.push(i.toString());
    }
    this.setState({ yearsData: yearsArray });
  };

  getMonthsData = () => {
    if (this.state.monthsData.length > 0) {
      return;
    }
    const monthsArray = [];
    for (let i = 1; i <= 12; i += 1) {
      monthsArray.push(i.toString());
    }
    this.setState({ monthsData: monthsArray });
  };

  getDaysData = (selectedYear, selectedMonth) => {
    const maxDay = moment(
      `${selectedYear}-${this.formatDig(selectedMonth)}`,
      'YYYY-MM'
    ).daysInMonth();
    const daysArray = [];
    for (let i = 1; i <= maxDay; i += 1) {
      daysArray.push(i.toString());
    }
    this.setState({ daysData: daysArray });
  };

  onSpliceText = ({ year, month, day }) => {
    const newMonth = this.formatDig(month);
    const newDay = this.formatDig(day);
    if (this.props.onConfirm) {
      const params = {
        date: `${year}/${newMonth}/${newDay}`,
      };
      this.props.onConfirm(params);
    }
  };

  onConfirm = () => {
    const { selectedYear, selectedMonth, selectedDay } = this.state;
    this.onSpliceText({
      year: selectedYear,
      month: selectedMonth,
      day: selectedDay,
    });
    this.hide();
  };

  formatDig = (num) => (parseInt(num, 10) > 9 ? num : `0${num}`);

  show = (date) => {
    this.setState({ isOpen: true });
    this.initData(date);
  };

  hide = () => {
    this.setState({ isOpen: false });
  };

  getDefaultValue = () => {
    const date = new Date();
    const y = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    return [y, month, day];
  };

  renderDayPicker = () => {
    const { yearsData, monthsData, daysData, selectedYear, selectedMonth, selectedDay } =
      this.state;
    return (
      <View style={{ flexDirection: 'row' }}>
        <Picker
          selectedValue={selectedYear}
          style={{ flex: 1 }}
          itemStyle={{ fontSize: 18 }}
          onValueChange={(itemValue) => {
            this.setState({ selectedYear: itemValue });
            this.getDaysData(itemValue, selectedMonth);
          }}
        >
          {yearsData.map((item, index) => this.renderPickerItem(item, index))}
        </Picker>
        <Picker
          selectedValue={selectedMonth}
          style={{ flex: 1 }}
          itemStyle={{ fontSize: 18 }}
          onValueChange={(itemValue) => {
            this.setState({ selectedMonth: itemValue });
            this.getDaysData(selectedYear, itemValue);
          }}
        >
          {monthsData.map((item, index) => this.renderPickerItem(item, index))}
        </Picker>
        <Picker
          selectedValue={selectedDay}
          style={{ flex: 1 }}
          itemStyle={{ fontSize: 18 }}
          onValueChange={(itemValue) => {
            this.setState({ selectedDay: itemValue });
          }}
        >
          {daysData.map((item, index) => this.renderPickerItem(item, index))}
        </Picker>
      </View>
    );
  };

  renderPickerItem = (key, i) => <Picker.Item key={i} label={key} value={key} color="#333" />;

  render() {
    const { title = I18n.t('page_job_text_pubdate'), ...rest } = this.props;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={title}
        showCancel={false}
        rightTitle={I18n.t('op_complete_title')}
        rightAction={this.onConfirm}
        contentHeight={200}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={this.state.isOpen}
        btnSize="lg"
        {...rest}
      >
        {this.renderDayPicker()}
      </BottomModal>
    );
  }
}
