import React, { Component } from 'react';
import { View, Touchable, Text, Image } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import { deviceWidth } from '../../../../common';
import resIcon from '../../../../res';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: '#fff',
      flex: 1,
    },
    bottomContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
      paddingHorizontal: 14,
    },
    languageTextBox: {
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
      height: 38,
      minWidth: (deviceWidth - 14 * 2 - 12 * 2) / 3,
      marginRight: 12,
      marginBottom: 12,
    },
    languageText: {
      textAlign: 'center',
      lineHeight: 38,
      fontSize: 13,
      color: '#333',
    },
  };
}
/**
 * 发布语言弹出框
 * <AUTHOR>
 */

@inject('jobStore')
@observer
export default class LanguageModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.languages = props.jobStore.languages;

    this.state = {
      selected: null,
      showModal: false,
      langs: this.languages,
    };
  }

  show = (selected) => {
    console.log('selected', selected);
    this.setState({ selected, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false, selected: null });
  };

  onConfirm = (item) => {
    const { onConfirm } = this.props;
    onConfirm && onConfirm(item);
    this.close();
  };

  render() {
    const { ...rest } = this.props;
    const { showModal, langs, selected } = this.state;
    const { style } = this;
    const contentHeight = 144;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_job_text_select_publish_language')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={contentHeight}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        {...rest}
      >
        <View style={style.pagerContainer}>
          <View style={style.bottomContainer}>
            {langs.map((item, index) => (
              <Touchable
                style={[
                  style.languageTextBox,
                  (index + 1) % 3 == 0 ? { marginRight: 0 } : {},
                  selected == item.value ? { backgroundColor: '#FAECEC' } : {},
                ]}
                key={index}
                onPress={() => this.onConfirm(item)}
              >
                <Text
                  style={[style.languageText, selected == item.value ? { color: '#EF3D48' } : {}]}
                >
                  {item.label}
                </Text>
              </Touchable>
            ))}
          </View>
        </View>
      </BottomModal>
    );
  }
}
