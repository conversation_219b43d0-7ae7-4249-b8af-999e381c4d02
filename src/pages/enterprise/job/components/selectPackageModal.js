import React, { Component } from 'react';
import { View, Text, Image, Touchable, Button } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import PageFlatList from '../../../../components/list/pageFlatList';
import moment from 'moment';
import NavigationService from '../../../../navigationService';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.minorBgColor,
      flex: 1,
    },

    itemBox: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 12,
      marginTop: 10,
      overflow: 'hidden',
      paddingTop: 16,
      paddingBottom: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingRight: 14,
    },
    itemContainer: {
      paddingHorizontal: 14,
      flex: 1,
      flexShrink: 1,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    sencondText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.primaryColor,
      marginRight: 11,
    },
    subTextValue: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      marginRight: 4,
    },
    subTextLabel: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    validDaysText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginTop: 12,
    },
    validDaysNum: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
    },
    saparateLine: {
      height: 1,
      backgroundColor: '#E5E5E5',
      marginVertical: 10,
    },
    itemBottomContainer: {
      paddingHorizontal: 14,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },

    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EF3D48',
      height: 32,
      width: 78,
      borderRadius: 5,
      justifyContent: 'center',
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#fff',
      lineHeight: 32,
    },
    buyBtnBox: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 4,
    },
    buyBtn: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EF3D48',
      height: 32,
      paddingHorizontal: 10,
      borderRadius: 5,
      justifyContent: 'center',
    },
    buyBtnText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryBgColor,
    },
    buyText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryColor,
      textAlign: 'center',
      marginTop: 18,
    },
  };
}
/**
 * 服务包弹出框
 * <AUTHOR>
 */
@inject('jobStore', 'resumeStore', 'companyAction')
@observer
export default class SelectPackageModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
    };
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 10,
      };
      if (this.props.itemIds) {
        param.itemIds = this.props.itemIds;
      }
      const { queryProductItems } = this.props.companyAction;
      const res = await queryProductItems(param);
      return res;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  show = () => {
    this.setState({ showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = (item) => {
    if (this.props.onConfirm) {
      this.props.onConfirm(item);
    }
    this.close();
  };

  onBuy = () => {
    NavigationService.navigate('servicePackage', {
      isFromCompany: true,
      initialPage: 0,
      callback: this.onRefresh,
    });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const ids = [4, 5, 6, 9];
    return (
      <View style={style.itemBox} key={index}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>
            {item?.itemName} {I18n.t('page_recharge_text_day', { count: item?.display })}
          </Text>
          <View key={index} style={style.subContainer}>
            <View style={style.dot} />
            <Text style={style.subTextLabel}>{I18n.t('page_job_text_number')}：</Text>
            <Text style={style.subTextValue} textType="amount">
              {item?.id < 0 || ids.includes(item?.id) ? I18n.t('page_job_text_free') : item.amount}
            </Text>
          </View>
          <Text style={style.validDaysText}>
            {I18n.t('page_recharge_text_validity')}：
            <Text style={style.validDaysNum} textType="amount">
              {item.expdate ? moment(item.expdate).format('YYYY-MM-DD') : '－'}
            </Text>
          </Text>
        </View>
        <Touchable style={style.flexRow} onPress={() => this.onConfirm(item)}>
          <Text style={style.statusText}>{I18n.t('page_job_text_select')}</Text>
        </Touchable>
      </View>
    );
  };

  render() {
    const { ...rest } = this.props;
    const { showModal } = this.state;
    const { style } = this;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_job_text_select_service_package')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={440}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={false}
        showBottomView={false}
        {...rest}
      >
        <View style={style.pagerContainer}>
          <PageFlatList
            ref={this.initPageFlatList}
            loadData={this.loadData}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            noDataExtraChildren={
              <View style={style.buyBtnBox}>
                <Button
                  title={I18n.t('page_job_text_buy_service_package')}
                  onPress={this.onBuy}
                  btnType="primary"
                  btnSize="s32"
                  containerStyle={{ minWidth: 90, marginTop: 20 }}
                />
              </View>
            }
            footerComponent={
              <View style={style.buyBtnBox}>
                <Button
                  title={I18n.t('page_job_text_buy_service_package')}
                  onPress={this.onBuy}
                  btnType="buyServicePackage"
                  btnSize="s32"
                  containerStyle={{ minWidth: 90, marginTop: 18 }}
                />
              </View>
            }
            noShowEmpty
            ListFooterComponentStyle={{ paddingBottom: 36 }}
            noDataStyle={{ paddingVertical: 0, paddingTop: 10 }}
          />
        </View>
      </BottomModal>
    );
  }
}
