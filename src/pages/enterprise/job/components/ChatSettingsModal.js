import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import { Button } from '../../../../components';
import I18n from '../../../../i18n';
import styles from '../../../../themes/enterprise';

function getComponentStyle(theme) {
  return {
    container: {
      paddingHorizontal: 20,
      paddingBottom: 15,
    },
    inputRow: {
      marginBottom: 20,
    },
    label: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
      fontWeight: theme.fontWeightMedium,
    },
    input: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      backgroundColor: '#fff',
    },
    radioRow: {
      marginBottom: 20,
    },
    radioGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    radioOption: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 30,
    },
    radioButton: {
      width: 18,
      height: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: '#E5E5E5',
      marginRight: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.stressColor,
    },
    radioButtonInner: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.stressColor,
    },
    radioText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    button: {
      flex: 1,
      marginHorizontal: 5,
    },
  };
}

/**
 * 聊天设置弹出框组件
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class ChatSettingsModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
      chatAccount: '',
      chatPassword: '',
      chatStatus: 'open', // 'open' 或 'disabled'
    };
  }

  show = (data = {}) => {
    this.setState({
      isVisible: true,
      chatAccount: data.chatAccount || '',
      chatPassword: data.chatPassword || '',
      chatStatus: data.chatStatus || 'open',
    });
  };

  hide = () => {
    this.setState({ isVisible: false });
  };

  onChangeChatAccount = (text) => {
    this.setState({ chatAccount: text });
  };

  onChangeChatPassword = (text) => {
    this.setState({ chatPassword: text });
  };

  onChangeChatStatus = (status) => {
    this.setState({ chatStatus: status });
  };

  onConfirm = () => {
    const { chatAccount, chatPassword, chatStatus } = this.state;

    // 验证聊天账号
    if (!chatAccount.trim()) {
      global.toast.show('聊天账号不能为空');
      return;
    }

    // 验证密码格式（假设需要6位数字）
    if (!chatPassword.trim()) {
      global.toast.show('聊天密码不能为空');
      return;
    }

    if (!/^\d{6}$/.test(chatPassword.trim())) {
      global.toast.show('聊天密码必须是6位数字');
      return;
    }

    // 调用父组件的确认回调
    if (this.props.onConfirm) {
      this.props.onConfirm({
        chatAccount: chatAccount.trim(),
        chatPassword: chatPassword.trim(),
        chatStatus,
      });
    }

    this.hide();
  };

  onCancel = () => {
    if (this.props.onCancel) {
      this.props.onCancel();
    }
    this.hide();
  };

  renderRadioButton = (value, label) => {
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const isSelected = this.state.chatStatus === value;

    return (
      <TouchableOpacity style={style.radioOption} onPress={() => this.onChangeChatStatus(value)}>
        <View style={[style.radioButton, isSelected && style.radioButtonSelected]}>
          {isSelected && <View style={style.radioButtonInner} />}
        </View>
        <Text style={style.radioText}>{label}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    const { isVisible, chatAccount, chatPassword } = this.state;
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const contentHeight = 300;

    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title="聊天设置"
        showCancel={false}
        contentHeight={contentHeight}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={isVisible}
        useScrollContent={false}
        showBottomView={false}
      >
        <View style={style.container}>
          {/* 聊天账号输入 */}
          <View style={style.inputRow}>
            <Text style={style.label}>聊天账号:</Text>
            <TextInput
              ref={(ref) => (this.chatAccountInput = ref)}
              style={style.input}
              placeholder="请输入聊天账号"
              value={chatAccount}
              onChangeText={this.onChangeChatAccount}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="next"
              onSubmitEditing={() => this.chatPasswordInput?.focus()}
            />
          </View>

          {/* 聊天密码输入 */}
          <View style={style.inputRow}>
            <Text style={style.label}>设置聊天密码:</Text>
            <TextInput
              ref={(ref) => (this.chatPasswordInput = ref)}
              style={style.input}
              placeholder="请输入密码"
              value={chatPassword}
              onChangeText={this.onChangeChatPassword}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="done"
              onSubmitEditing={this.onConfirm}
            />
          </View>

          {/* 聊天状态选择 */}
          <View style={style.radioRow}>
            <Text style={style.label}>聊天状态:</Text>
            <View style={style.radioGroup}>
              {this.renderRadioButton('open', '开启')}
              {this.renderRadioButton('disabled', '未启用')}
            </View>
          </View>

          {/* 按钮组 */}
          <View style={style.buttonContainer}>
            <Button
              title={I18n.t('op_cancel_title')}
              onPress={this.onCancel}
              btnSize="md"
              outline
              btnType="reset"
              containerStyle={style.button}
            />
            <Button
              title={I18n.t('op_confirm_title')}
              onPress={this.onConfirm}
              btnSize="md"
              btnType="primary"
              containerStyle={style.button}
            />
          </View>
        </View>
      </BottomModal>
    );
  }
}
