import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Icon } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import constant from '../../../../store/constant';
import NavigationService from '../../../../navigationService';
import moment from 'moment';

function getComponentStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: theme.containerPaddingHorizontal,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    sencondText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    itemBottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    timeText: {
      fontSize: theme.fontSizeM,
      color: theme.mediumFontColor,
      flexShrink: 10,
      // flex: 1,
    },
    timeTextBox: {
      marginTop: 10,
      borderTopColor: '#F7F7F7',
      borderTopWidth: 1,
      paddingTop: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    cityText: {
      fontSize: theme.fontSizeM,
      color: '#4962A9',
      marginLeft: 10,
    },
  };
}

/**
 * 列表
 */
@inject('companyAction')
@observer
export default class RecordList extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.status = props.status;
    this.index = props.index;
  }

  componentDidMount() {
    global.emitter.on(constant.event.jobChanged, this.onRefresh);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.jobChanged, this.onRefresh);
  }

  onRefresh = async () => {
    this.pageFlatList.onRefresh();
    this.props.companyAction.getEmployersJobStatistics();
  };

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 8,
      };
      if (this.status == 0 || this.status == 3) {
        param.status = this.status;
      } else if (this.status == 1) {
        param.online = true;
      } else if (this.status == 2) {
        param.offline = true;
      }

      const data = await this.props.companyAction.queryJobs(param);

      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('epjobDetail', {
      item,
      index: this.index,
      cancelCallback: this.onRefresh,
    });
  };

  onRefreshJob = async (item) => {
    try {
      await this.props.companyAction.refreshJobs(item.id);
      this.onRefresh();
    } catch (error) {}
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item.title}</Text>
          <Text style={style.sencondText}>
            {item.termId?.label}
            {item.salaryId?.label && item.salaryId?.label ? `/` : ''}
            {item.salaryId?.label}
            {item.salaryId?.label || item.salaryId?.label ? `/` : ''}
            {item.workyears == 0
              ? I18n.t('page_job_text_experience_unlimited')
              : I18n.t('page_job_text_how_many_year', { count: item.workyears })}
            {item.qualificationId?.label ? `/` : ''}
            {item.qualificationId?.label}
          </Text>
          <View style={style.itemBottomContainer}>
            <Text style={[style.timeText, { marginBottom: 4 }]}>
              {I18n.t('page_job_text_refresh_time')}：
              {moment(Number(item.updatetime)).format('YYYY/MM/DD')}
            </Text>
            <Text style={style.cityText}>
              {item?.locations?.length
                ? item?.locations?.length > 1
                  ? I18n.t('page_job_text_many_city_job')
                  : item.locations[0]?.locationId.label
                : ''}
            </Text>
          </View>
          <Text style={style.timeText}>
            {I18n.t('page_job_text_job_number')}：{item.id}
          </Text>
          {item?.status?.value == 1 && item?.expiredDays ? (
            <View style={style.timeTextBox}>
              <Text style={[style.timeText, { marginRight: 15 }]}>
                {I18n.t('page_job_text_job_offline_time')}：
                <Text style={{ color: '#EF3D48' }}>{item.expiredDays}</Text>
                {I18n.t('page_job_text_day', { count: item.expiredDays })}
              </Text>
              <Touchable onPress={() => this.onRefreshJob(item)}>
                <Icon type="ionicon" name="refresh-circle" size={28} color="#EF3D48" />
              </Touchable>
            </View>
          ) : null}
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <PageFlatList
        ref={this.initPageFlatList}
        onRefresh={this.onRefresh}
        loadData={this.loadData}
        renderItem={this.renderItem}
        showsVerticalScrollIndicator={false}
      />
    );
  }
}
