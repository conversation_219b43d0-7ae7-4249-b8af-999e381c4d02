import React, { Component } from 'react';
import { View, Touchable, Text, Image } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import { deviceWidth } from '../../../../common';
import resIcon from '../../../../res';
import { computed } from 'mobx';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: '#fff',
      flex: 1,
    },
    bottomContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
      paddingHorizontal: 14,
    },
    languageTextBox: {
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
      height: 38,
      minWidth: (deviceWidth - 14 * 2 - 12 * 2) / 3,
      marginRight: 12,
      marginBottom: 12,
    },
    languageText: {
      textAlign: 'center',
      lineHeight: 38,
      fontSize: 13,
      color: '#333',
    },
  };
}
/**
 * 平面选择弹出框
 * <AUTHOR>
 */
@inject('jobStore', 'resumeStore')
@observer
export default class FlatViewModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));

    this.state = {
      selected: null,
      showModal: false,
      type: null,
      title: '',
    };
  }

  @computed get listData() {
    const { type } = this.state;
    const { sexList, maritalStatusList } = this.props.resumeStore;
    if (type === 'sex') {
      return sexList;
    } else if (type === 'maritalStatus') {
      return maritalStatusList;
    }
    return [];
  }

  show = ({ title, type, selected }) => {
    this.setState({ title, type, selected, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false, selected: null });
  };

  onConfirm = (item) => {
    const { type } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm({ type, value: this.listData?.find((x) => x.value == item.value) });
    }
    this.close();
  };

  render() {
    const { ...rest } = this.props;
    const { showModal, selected } = this.state;
    const { style } = this;
    const { title } = this.state;
    const contentHeight = 88;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={title}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={contentHeight}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        {...rest}
      >
        <View style={style.pagerContainer}>
          <View style={style.bottomContainer}>
            {this.listData.map((item, index) => (
              <Touchable
                style={[
                  style.languageTextBox,
                  (index + 1) % 3 == 0 ? { marginRight: 0 } : {},
                  selected == item.value ? { backgroundColor: '#FAECEC' } : {},
                ]}
                key={index}
                onPress={() => this.onConfirm(item)}
              >
                <Text
                  style={[style.languageText, selected == item.value ? { color: '#EF3D48' } : {}]}
                >
                  {item.label}
                </Text>
              </Touchable>
            ))}
          </View>
        </View>
      </BottomModal>
    );
  }
}
