import React, { Component } from 'react';
import { Picker } from '../../../../components';
import BottomModal from '../../../../components/modal/bottomModal';
import I18n from '../../../../i18n';

export default class RecruitmentNumberModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      num: 1,
    };
  }

  onConfirm = () => {
    const { num } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm(num);
    }
    this.hide();
  };

  show = (data) => {
    this.setState({ num: data, isOpen: true });
  };

  hide = () => {
    this.setState({ isOpen: false });
  };

  renderNumPicker = () => {
    const { num } = this.state;
    const nums = Array.from({ length: 10 }, (_, index) => `${index + 1}`);

    return (
      <Picker
        selectedValue={num}
        style={{ flex: 1 }}
        itemStyle={{ fontSize: 18 }}
        onValueChange={(itemValue) => {
          console.log(itemValue);
          this.setState({ num: itemValue });
        }}
      >
        {nums.map((item, index) => this.renderPickerItem(item, index))}
      </Picker>
    );
  };

  renderPickerItem = (item, i) => <Picker.Item key={i} label={item} value={item} color="#333" />;

  render() {
    const { ...rest } = this.props;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_job_text_recruit_number')}
        showCancel={false}
        rightTitle={I18n.t('op_complete_title')}
        rightAction={this.onConfirm}
        contentHeight={200}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={this.state.isOpen}
        {...rest}
      >
        {this.renderNumPicker()}
      </BottomModal>
    );
  }
}
