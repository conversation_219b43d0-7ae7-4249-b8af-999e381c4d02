import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Header, BaseComponent } from '../../../components';
import { headerStyle, jobStyle } from '../../../themes';
import I18n from '../../../i18n';
import LoadingModal from '../../../components/loadingModal';
import CountryCodePicker from '../../../components/CountryPicker2';
import util from '../../../util';

@inject('jobAction', 'cityAction', 'jobStore')
@observer
export default class JobCityChildren extends BaseComponent {
  constructor(props) {
    super(props);
    const cityData = props.navigation.getParam('cityData', {});
    this.state = {
      showLoading: false,
      allPlaceTreeIndex: 0,
      areaName: util.getCityNameWithCityData(cityData),
      allPlaceTree: [],
      pickerDataFormat: [],
    };
  }

  componentDidMount() {
    this.initData();
  }

  initData = async () => {
    try {
      this.showGlobalLoading();
      const res = await this.props.cityAction.getAllPlaceTree();
      this.setState({ allPlaceTree: res || [] }, () => {
        this.marryCity();
      });
      this.showRequestResult(res?.message);
      await this.props.jobAction.getLocationConstants2();
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  marryCity = () => {
    const { allPlaceTree } = this.state;
    const { cityData } = this.props.navigation.state.params;
    const selectIndex = allPlaceTree.findIndex((x) => x.placeId == cityData.placeId);
    if (cityData.twoLevelCityData && cityData.twoLevelCityData.placeId) {
      this.CountryCodePicker.wrappedInstance.selectHeaderAction2(
        util.getCityNameWithCityData(cityData.twoLevelCityData)
      );
    }
    if (cityData.threeLevelCityData && cityData.threeLevelCityData.placeId) {
      this.CountryCodePicker.wrappedInstance.phoneCodeSelected2(
        cityData.threeLevelCityData.placeId
      );
    }
    this.setState(
      {
        allPlaceTreeIndex: selectIndex < 0 ? 26 : selectIndex,
      },
      () => {
        this.queryFormatPickerData();
      }
    );
  };

  getAllPlace = () => {
    this.props.cityAction.getAllPlaceTree().then(
      (res) => {
        this.setState({ allPlaceTree: res || [] }, () => {
          this.marryCity();
        });
      },
      (err) => {
        console.log(err);
      }
    );
  };

  goBack = async () => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onContinueCheck) {
      await fuc.onContinueCheck();
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  selectItem = async (itemParam) => {
    const { navigation } = this.props;
    const { allPlaceTreeIndex, allPlaceTree } = this.state;
    if (itemParam.parentName) {
      const item = allPlaceTree[allPlaceTreeIndex];
      const itemData = item;
      const index1 = item.children.findIndex(
        (x) => util.getCityNameWithCityData(x) == itemParam.parentName.key
      );
      const index2 =
        item.children[index1].children &&
        item.children[index1].children.findIndex((x) => x.placeId == itemParam.placeId);
      itemData.twoLevelCityData = item.children[index1];
      itemData.threeLevelCityData = item.children[index1].children
        ? item.children[index1].children[index2]
        : null;
      const fuc = this.props.navigation.state.params;
      if (fuc) {
        fuc.onSelect(itemData);
        navigation.goBack();
      }
    } else {
      const item = allPlaceTree[allPlaceTreeIndex];
      const itemData = item;
      const index1 = item.children.findIndex((x) => util.getCityNameWithCityData(x) == itemParam);
      itemData.twoLevelCityData = item.children[index1];
      const fuc = this.props.navigation.state.params;
      if (fuc) {
        fuc.onSelect(itemData);
        navigation.goBack();
      }
    }
  };

  selectedArea = (item) => {
    const { allPlaceTree } = this.state;
    const selectIndex = allPlaceTree.findIndex((x) => x.placeId == item.placeId);
    this.setState(
      {
        areaName: util.getCityNameWithCityData(item),
        allPlaceTreeIndex: selectIndex,
      },
      () => {
        this.queryFormatPickerData();
      }
    );
  };

  queryFormatPickerData = async () => {
    const { allPlaceTreeIndex, allPlaceTree } = this.state;
    const cityData = allPlaceTree[allPlaceTreeIndex || 0];
    const pickerData = JSON.parse(JSON.stringify(cityData)) || [];
    await this.props.jobAction.formatPickerData(pickerData.children).then((res) => {
      this.setState({
        pickerDataFormat: res,
      });
    });
  };

  renderHeaderRight = () => {
    const { navigation } = this.props;
    return (
      <Touchable
        onPress={() => {
          navigation.navigate('jobCity', {
            needSave: true,
            onSelect: (item) => {
              if (item.locationId == 32 || item.locationId == 30) {
                const fuc = this.props.navigation.state.params;
                if (fuc) {
                  fuc.onSelect(item);
                  navigation.goBack();
                }
                return;
              }
              this.selectedArea(item);
            },
          });
        }}
      >
        <View>
          <Text style={{ fontSize: 14, color: '#3299FF' }}>
            {I18n.t('page_select_city_switch_city')}
          </Text>
        </View>
      </Touchable>
    );
  };

  render() {
    const { areaName, pickerDataFormat } = this.state;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          theme="dark"
          barStyle="dark-content"
          title={areaName}
          rightComponent={this.renderHeaderRight()}
        />

        <CountryCodePicker
          pickerData={pickerDataFormat}
          isShow
          ref={(ref) => {
            this.CountryCodePicker = ref;
          }}
          onPick={(res) => {
            this.selectItem(res);
          }}
          iconSelected
          disableHeader
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
