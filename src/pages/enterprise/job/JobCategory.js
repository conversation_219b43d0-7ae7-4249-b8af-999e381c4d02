import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  View,
  Header,
  Icon,
  Input,
  KeyboardAwareScrollView,
  ListItem,
} from '../../../components';
import { titleColor, baseBlueColor } from '../../../themes';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import styles from '../../../themes/enterprise';
import NoData from '../../../components/empty/noData';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
      marginBottom: 15,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
    },
    sectionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: 10,
      paddingBottom: 10,
    },

    commonInputContainer: {
      borderBottomColor: 'transparent',
      backgroundColor: '#F7F7F7',
      borderRadius: 5,
      height: 38,
      paddingLeft: 9,
      paddingHorizontal: 0,
      margin: 0,
    },
    inputStyle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
    },
  };
}

@inject('jobStore')
@observer
export default class JobCategory extends Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { jobCategoryList } = props.jobStore;
    const { jobCategory } = props.navigation.state.params;

    this.state = {
      professionList: jobCategoryList?.map((x) => {
        x.isSelected = x.value == jobCategory?.value;
        return x;
      }),
    };
  }

  componentDidMount() {}

  onSelectIntension = (item) => {
    const { callback } = this.props.navigation.state.params;
    callback?.(item);
    NavigationService.goBack();
  };

  onChangeText = (text) => {
    const {
      jobStore: { jobCategoryList },
    } = this.props;
    let temp = jobCategoryList;
    temp = temp.filter((item) => item.label.indexOf(text) > -1);
    if (!text) {
      this.setState({ professionList: jobCategoryList });
    } else {
      this.setState({ professionList: temp });
    }
  };

  renderIntensionList = () => {
    const { professionList } = this.state;
    return (
      <View style={{ backgroundColor: '#fff' }}>
        {professionList?.length ? (
          professionList.map((item) => (
            <ListItem
              key={item.value}
              containerStyle={{
                borderBottomColor: '#eee',
                borderBottomWidth: 0.5,
                paddingHorizontal: 12,
                paddingVertical: 12,
                backgroundColor: item.isSelected ? '#FAECEC' : '#fff',
              }}
              // eslint-disable-next-line react/jsx-no-bind
              onPress={this.onSelectIntension.bind(this, item)}
              rightIcon={
                item.isSelected ? (
                  <Icon type="ionicon" name="ios-checkmark-circle" size={24} color="#EF3D48" />
                ) : (
                  <Icon type="ionicon" name="ios-checkmark-circle" size={24} color="#fff" />
                )
              }
              title={item.label}
              titleStyle={{
                color: titleColor,
                fontSize: 14,
              }}
            />
          ))
        ) : (
          <NoData />
        )}
      </View>
    );
  };

  render() {
    const { style } = this;

    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_job_text_select_job_category')}</Text>
        </View>
        <View style={style.sectionContent}>
          <Input
            inputContainerStyle={style.commonInputContainer}
            inputStyle={[style.inputStyle, { paddingLeft: 0 }]}
            placeholder={I18n.t('page_job_text_search_job_category')}
            placeholderTextColor="#CCCCCC"
            onChangeText={this.onChangeText}
            selectionColor="#EF3D48"
            returnKeyType="done"
            leftIcon={
              <Icon
                name="search1"
                size={20}
                color="#999"
                type="antdesign"
                style={{ marginRight: 10 }}
              />
            }
            style={{ margin: 0 }}
          />
        </View>

        <KeyboardAwareScrollView
          style={{ flex: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {this.renderIntensionList()}
        </KeyboardAwareScrollView>
      </View>
    );
  }
}
