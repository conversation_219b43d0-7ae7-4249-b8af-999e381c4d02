import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Image,
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  BaseComponent,
  Alert,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import util from '../../../util';
import resIcon from '../../../res';
import { deviceWidth, hasEar } from '../../../common';
import { computed } from 'mobx';
import NavigationService from '../../../navigationService';
import moment from 'moment';
import SelectPackageModal from './components/selectPackageModal';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      paddingHorizontal: 14,
      paddingTop: 10,
      marginBottom: 30,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginRight: 10,
    },
    jobText: {
      fontSize: 14,
      color: '#4962A9',
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
      marginTop: 7,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 14,
      marginTop: 10,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
    },
    valueTextBox: {
      marginLeft: 20,
      flexShrink: 10,
    },
    valueText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      flexShrink: 10,
      textAlign: 'right',
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 14,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
    bottomItemContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      // marginRight: 10,
    },
    bottomItem: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    bottomItemImg: {
      width: 20,
      height: 20,
    },
    bottomItemText: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
      marginTop: 4,
    },
  };
}

const JobSimpleStatusIndex = {
  online: 0,
  pending: 1,
  offline: 2,
  draft: 3,
  removed: 4,
};

/**
 * 职位详情
 */
@inject('companyAction', 'resumeAction')
@observer
export default class JobDetail extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { item } = props.navigation.state.params || {};
    this.setStatusIndex(item);
    this.state = {
      detail: item,
    };
  }

  componentDidMount() {
    this.props.resumeAction.queryConstants();
    this.queryJobDetail();
  }

  setStatusIndex = (detail) => {
    const { index } = this.props.navigation.state.params || {};
    if (index || index === 0) {
      this.statusIndex = index;
      return;
    }
    try {
      // 接口新增jobSimpleStatus来判断，else后续可移除
      if (detail.jobSimpleStatus) {
        console.debug('jobSimpleStatus', detail.jobSimpleStatus, detail.status);
        this.statusIndex =
          JobSimpleStatusIndex[detail.jobSimpleStatus] || JobSimpleStatusIndex.online;
      } else {
        switch (detail.status.code) {
          case 'PUBLISHED':
            this.statusIndex = JobSimpleStatusIndex.online;
            break;
          case 'CLOSED':
            this.statusIndex = JobSimpleStatusIndex.offline;
            break;
          case 'DRAFT':
            this.statusIndex = JobSimpleStatusIndex.draft;
            break;
          case 'PENDING':
            this.statusIndex = JobSimpleStatusIndex.pending;
            break;
          default:
            this.statusIndex = JobSimpleStatusIndex.removed;
            break;
        }
      }
    } catch (e) {
      console.warn('setStatusIndex', e, detail);
    }
  };

  get statusText() {
    switch (parseInt(this.statusIndex)) {
      case 0:
        return I18n.t('page_home_text_online');
      case 1:
        return I18n.t('page_home_text_unpublished');
      case 2:
        return I18n.t('page_home_text_offline');
      case 3:
        return I18n.t('page_home_text_draft');
    }
    return '';
  }

  queryJobDetail = async () => {
    try {
      const { detail } = this.state;
      this.showGlobalLoading();
      const res = await this.props.companyAction.queryJobsByJobId(detail?.id);
      this.setStatusIndex(res);
      this.setState({
        detail: res,
      });
      this.showRequestResult();
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onDelJob = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_job_tips_delete_job'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_job_btn_coonfirm'),
        onPress: async () => {
          try {
            const { detail } = this.state;
            this.showGlobalLoading();
            const res = await this.props.companyAction.deleteJobs(detail?.id);
            this.showRequestResult(res?.message);
            if (res?.successful) {
              const { cancelCallback } = this.props.navigation.state.params || {};
              cancelCallback?.();
              NavigationService.goBack();
            }
          } catch (error) {
            this.showRequestResult(error);
          }
        },
      },
    ]);
  };

  onPreview = () => {
    const { detail } = this.state;
    NavigationService.navigate('jobDetail', {
      isPreview: true,
      detail,
    });
  };

  onOffline = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_job_tips_offline_job'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_job_btn_coonfirm'),
        onPress: async () => {
          try {
            const { detail } = this.state;
            this.showGlobalLoading();
            const res = await this.props.companyAction.closeJobs(detail?.id);
            this.showRequestResult(res?.message);
            if (res?.successful) {
              const { cancelCallback } = this.props.navigation.state.params || {};
              cancelCallback?.();
              global.emitter.emit(constant.event.jobChanged);
              NavigationService.goBack();
            }
          } catch (error) {
            this.showRequestResult(error);
          }
        },
      },
    ]);
  };

  onOnlieAgain = async () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_job_tips_online_job'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_job_btn_coonfirm'),
        onPress: async () => {
          try {
            const { detail } = this.state;
            this.showGlobalLoading();
            const res = await this.props.companyAction.publishJobs(detail?.id, {
              employerProductItemId: -1,
              pubdate: moment().format('YYYY-MM-DD'),
            });
            this.showRequestResult(res?.message);
            if (res?.successful) {
              const { cancelCallback } = this.props.navigation.state.params || {};
              cancelCallback?.();
              global.emitter.emit(constant.event.jobChanged);
              NavigationService.goBack();
            }
          } catch (error) {
            this.showRequestResult(error);
          }
        },
      },
    ]);
  };

  onSaveDraft = async (saveAsDraft) => {
    try {
      const { detail } = this.state;
      const formData = {
        title: detail?.title, // 职位名称
        pubdate: moment(detail?.pubdate).format('YYYY-MM-DD') || '',
        hirelings: detail?.hirelings, // 招聘人数
        termId: detail?.termId?.value, // 职位性质
        categoryId: detail?.categoryId?.value, // 职位类别
        salaryId: detail?.salaryId?.value, // 薪资待遇
        contactId: detail?.contact?.id, // 联系人
        language: detail?.language, // 语言要求
        locations: detail?.locations.map((loc) => {
          return {
            address: loc.address,
            addressCode: loc.addressCode,
            location: loc.location,
            locationId: loc.locationId?.value,
          };
        }), // 工作地点
        description: detail?.description, // 工作描述
        requirement: detail?.requirement, // 职位描述
        major: detail?.major, // 专业
        ageFrom: detail?.ageFrom, // 年龄
        ageTo: detail?.ageTo, // 年龄
        jobLevelId: detail?.jobLevelId?.value, // 职业等级
        qualificationId: detail?.qualificationId?.value, // 学历要求
        workyears: detail?.workyears, // 工作年限
        jobLangs: detail?.jobLangs, // 语言要求
        sex: detail?.sex?.value, // 性别
        marital: detail?.marital?.value, // 婚姻状况
        mapImages: detail?.mapImages, // 地图图片
      };
      if (saveAsDraft) {
        formData.saveAsDraft = true;
      }
      this.showGlobalLoading();
      const res = await this.props.companyAction.editJobs(detail.id, formData);
      this.showRequestResult(res?.message);
      if (res && res.successful) {
        const { callback } = this.props.navigation.state.params || {};
        callback && callback();
        global.emitter.emit(constant.event.jobChanged, res);
        NavigationService.goBack();
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onPrePublish = () => {
    this.selectPackageModal.wrappedInstance.show();
  };

  onPublish = async (item) => {
    try {
      const { detail } = this.state;
      this.showGlobalLoading();
      const res = await this.props.companyAction.publishJobs(detail?.id, {
        employerProductItemId: item?.id,
        pubdate: moment(detail.pubdate).format('YYYY-MM-DD'),
      });
      this.showRequestResult(res?.message);
      if (res?.successful) {
        const { cancelCallback } = this.props.navigation.state.params || {};
        cancelCallback?.();
        global.emitter.emit(constant.event.jobChanged);
        NavigationService.goBack();
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onCopy = () => {
    NavigationService.replace('jobAdd', {
      item: this.state.detail,
    });
  };

  onEditBaseInfo = () => {
    const { detail } = this.state;
    NavigationService.navigate('jobEdit', {
      item: detail,
      callback: (item) => {
        this.setState({
          detail: { ...this.state.detail, ...item },
        });
      },
    });
  };

  onEditRequirements = () => {
    const { detail } = this.state;
    NavigationService.navigate('jobReqEdit', {
      item: detail,
      callback: (item) => {
        console.log('item', item);
        this.setState({
          detail: { ...this.state.detail, ...item },
        });
      },
    });
  };

  renderUserInfo = () => {
    const { style } = this;
    const { detail } = this.state;
    return (
      <View style={style.userInfo}>
        <Text style={style.nameText}>{detail?.title}</Text>
        <Text style={style.jobText}>{this.statusText}</Text>
      </View>
    );
  };

  renderBaseInfo = () => {
    const SectionItem = this.renderSection;
    const Item = this.renderItem;
    const { detail } = this.state;
    return (
      <SectionItem
        headeTitle={I18n.t('page_job_text_baseinfo')}
        onPress={this.statusIndex != 2 ? this.onEditBaseInfo : null}
        children={
          <>
            <Item label={I18n.t('page_job_text_type_of_job')} value={detail?.termId?.label} />
            <Item label={I18n.t('page_job_text_job_category')} value={detail?.categoryId?.label} />
            <Item label={I18n.t('page_job_text_recruit_number')} value={detail?.hirelings} />
            <Item
              label={I18n.t('page_job_text_pubdate')}
              value={
                detail?.pubdate_unixtime
                  ? moment(Number(detail.pubdate_unixtime) * 1000).format('YYYY/MM/DD')
                  : ''
              }
            />
            <Item label={I18n.t('page_job_expected_Salary')} value={detail?.salaryId?.label} />
            <Item label={I18n.t('page_mine_interview_contact')} value={detail?.contact?.name} />
            <Item label={I18n.t('page_work_desc')} value={detail?.description} />
            {detail?.locations?.map((loc, index) => (
              <View key={index}>
                <Item
                  label={
                    index == 0
                      ? I18n.t('page_job_text_work_place')
                      : I18n.t('page_job_text_work_place') + (index + 1)
                  }
                  value={loc.address}
                  subValue=""
                />
              </View>
            ))}
          </>
        }
      />
    );
  };

  renderJobRequirements = () => {
    const SectionItem = this.renderSection;
    const Item = this.renderItem;
    const { detail } = this.state;
    return (
      <SectionItem
        headeTitle={I18n.t('page_job_text_job_require')}
        onPress={this.statusIndex != 2 ? this.onEditRequirements : null}
        children={
          <>
            <Item label={I18n.t('page_job_text_job_level')} value={detail?.jobLevelId?.label} />
            <Item
              label={I18n.t('page_job_text_education_require')}
              value={detail?.qualificationId?.label}
            />
            <Item label={I18n.t('page_job_text_major')} value={detail?.major} />
            <Item
              label={I18n.t('page_job_text_work_years')}
              value={
                detail?.workyears
                  ? I18n.t('page_job_text_how_many_year', { count: detail?.workyears })
                  : I18n.t('page_job_text_experience_unlimited')
              }
            />
            {detail?.jobLangs?.map((lang, index) => (
              <View key={index}>
                <Item
                  label={
                    index == 0
                      ? I18n.t('page_job_text_language_require')
                      : I18n.t('page_job_text_language_require') + (index + 1)
                  }
                  value={`${lang.languageId?.label}-${lang.languageLevelId?.label}`}
                />
              </View>
            ))}
            <Item
              label={I18n.t('page_resume_label_sex')}
              value={detail?.sex?.label || I18n.t('page_job_text_unlimited')}
            />
            {detail?.ageTo ? (
              <Item
                label={I18n.t('page_job_text_age')}
                value={`${detail?.ageFrom}-${I18n.t('page_job_text_years_old', {
                  count: detail?.ageTo,
                })}`}
              />
            ) : null}
            <Item
              label={I18n.t('page_job_text_married_status')}
              value={detail?.marital?.label || I18n.t('page_job_text_unlimited')}
            />
            <Item label={I18n.t('page_job_desc')} value={detail?.requirement} />
          </>
        }
        showLine={false}
      />
    );
  };

  renderSection = ({ headeTitle, children, showLine = true, onPress }) => {
    const { style } = this;
    return (
      <>
        <View style={style.sectionContainer}>
          <View style={style.sectionHeader}>
            <View style={style.languagLevelBox}>
              <Image source={resIcon.homeLine} />
              <Text style={style.sectionHeaderText}>{headeTitle}</Text>
            </View>
            {onPress ? (
              <Touchable onPress={onPress}>
                <Image source={resIcon.jobEditEnterprise} />
              </Touchable>
            ) : null}
          </View>
          <View style={style.contentContainer}>{children}</View>
        </View>
        {showLine ? <View style={style.separateLine} /> : null}
      </>
    );
  };

  renderItem = ({ label, value, subValue }) => {
    const { style } = this;
    return (
      <View style={style.itemContainer}>
        <Text style={style.labelText}>{label}</Text>
        <View style={style.valueTextBox}>
          <Text style={style.valueText} textType="amount">
            {value}
          </Text>
          {subValue ? (
            <Text style={style.valueText} textType="amount">
              {subValue}
            </Text>
          ) : null}
        </View>
      </View>
    );
  };

  rightComponent = () => {
    if (this.statusIndex == 0) {
      return null;
    }
    return (
      <Touchable onPress={this.onDelJob}>
        <Image source={resIcon.jobDelEnterprise} />
      </Touchable>
    );
  };

  renderBottom = () => {
    const { style } = this;
    const { detail } = this.state;
    const showOnlineAgain = moment(detail?.expdate).isAfter(moment(), 'day');

    return (
      <View style={style.bottomContainer}>
        <View style={style.bottomItemContainer}>
          <Touchable style={style.bottomItem} onPress={this.onPreview}>
            <Image
              source={resIcon.jobViewEnterprise}
              style={style.bottomItemImg}
              resizeMode="contain"
            />
            <Text style={style.bottomItemText}>{I18n.t('page_resume_btn_preview')}</Text>
          </Touchable>
          {this.statusIndex == 0 ? (
            <>
              <Touchable style={style.bottomItem} onPress={this.onOffline}>
                <Image
                  source={resIcon.jobOfflineEnterprise}
                  style={style.bottomItemImg}
                  resizeMode="contain"
                />
                <Text style={style.bottomItemText}>{I18n.t('page_job_text_offline')}</Text>
              </Touchable>
              <Button
                title={I18n.t('page_dynamicedit_btn_publishdynamic')}
                btnType="login"
                btnSize="s44"
                style={{ width: 210 }}
                onPress={() => this.onSaveDraft(false)}
              />
            </>
          ) : null}
          {this.statusIndex == 3 ? (
            <Touchable style={style.bottomItem} onPress={() => this.onSaveDraft(true)}>
              <Image
                source={resIcon.jobSaveEnterprise}
                style={style.bottomItemImg}
                resizeMode="contain"
              />
              <Text style={style.bottomItemText}>{I18n.t('page_job_text_save_draft')}</Text>
            </Touchable>
          ) : null}
          {this.statusIndex == 2 ? (
            <>
              <Touchable style={style.bottomItem} onPress={this.onCopy}>
                <Image
                  source={resIcon.jobCopyEnterprise}
                  style={style.bottomItemImg}
                  resizeMode="contain"
                />
                <Text style={style.bottomItemText}>{I18n.t('page_dynamic_text_copy')}</Text>
              </Touchable>
              {showOnlineAgain ? (
                <Touchable style={style.bottomItem} onPress={this.onOnlieAgain}>
                  <Image
                    source={resIcon.jobPublishEnterprise}
                    style={style.bottomItemImg}
                    resizeMode="contain"
                  />
                  <Text style={style.bottomItemText}>{I18n.t('page_job_text_reonline')}</Text>
                </Touchable>
              ) : null}
            </>
          ) : null}
          {this.statusIndex == 1 || this.statusIndex == 3 ? (
            <Button
              title={I18n.t('page_dynamicedit_btn_publishdynamic')}
              btnType="login"
              btnSize="s44"
              style={{ width: 210, marginLeft: 10 }}
              onPress={() =>
                this.statusIndex == 1 ? this.onSaveDraft(false) : this.onPrePublish()
              }
            />
          ) : null}
        </View>
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" title="" rightComponent={this.rightComponent} />

        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {this.renderUserInfo()}
          {this.renderBaseInfo()}
          {this.renderJobRequirements()}

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>

        {this.renderBottom()}

        <SelectPackageModal
          ref={(ref) => (this.selectPackageModal = ref)}
          onConfirm={this.onPublish}
        />
      </View>
    );
  }
}
