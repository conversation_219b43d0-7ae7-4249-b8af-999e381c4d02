import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import { hasEar } from '../../../common';
import RightArrow from '../../../components/rightArrow';
import NavigationService from '../../../navigationService';
import util from '../../../util';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    jobText: {
      fontSize: 14,
      color: theme.primaryFontColor,
    },
    separateLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginHorizontal: 18,
      marginVertical: 20,
      marginTop: 7,
    },
    sectionContainer: {},
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    sectionHeaderText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 5,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    languagLevelBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      marginBottom: 8,
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
    },

    addAddressText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      paddingBottom: 15,
      textAlign: 'center',
    },

    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 38,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
  };
}

/**
 * 添加地点
 */
@inject('jobStore', 'companyAction', 'jobAction')
@observer
export default class JobAddressAdd extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.item = props.navigation.state.params?.item;
    console.log('this.item', this.item);
    const data = {};
    if (this.item) {
      Object.keys(this.item).forEach((key) => {
        if (key !== 'address' && key !== 'timestamp') {
          data[key] = this.item[key];
        }
      });
    }

    this.state = {
      cityData: data?.placeId ? data : null,
      detailAddress: this.item?.address || '',
    };
  }

  getCityName = (item) => {
    let areaNameData = util.getCityNameWithCityData(JSON.parse(JSON.stringify(item)));
    if (I18n.locale == 'zh') {
      const { jobCityList } = this.props.jobStore;
      areaNameData = jobCityList.find((x) => x.value == item.locationId)?.label || areaNameData;
    }
    if (item.twoLevelCityData && item.twoLevelCityData.placeId) {
      areaNameData = `${areaNameData} ${util.getCityNameWithCityData(
        JSON.parse(JSON.stringify(item)).twoLevelCityData
      )}`;
    }

    if (item.threeLevelCityData && item.threeLevelCityData.placeId) {
      areaNameData = `${areaNameData} ${util.getCityNameWithCityData(
        JSON.parse(JSON.stringify(item)).threeLevelCityData
      )}`;
    }
    // areaNameData 如果中间包含多个空格 则保留一个
    areaNameData = areaNameData.replace(/\s+/g, ' ');
    return areaNameData;
  };

  saveArea = (item) => {
    const areaNameData = this.getCityName(item);
    const newCityData = JSON.parse(JSON.stringify(item));
    newCityData.cityName = areaNameData;
    console.log('newCityData', newCityData);
    this.setState({ cityData: newCityData });
    // this.props.jobAction.setLocalAddressList(newCityData);
  };

  getLocationLatLong = (item) => {
    let location = item.location;
    if (item.twoLevelCityData && item.twoLevelCityData.location) {
      location = item.twoLevelCityData.location;
    }
    if (item.threeLevelCityData && item.threeLevelCityData.location) {
      location = item.threeLevelCityData.location;
    }
    return location;
  };

  getPlaceId = (item) => {
    let placeId = item.placeId;
    if (item.twoLevelCityData && item.twoLevelCityData.placeId) {
      placeId = item.twoLevelCityData.placeId;
    }
    if (item.threeLevelCityData && item.threeLevelCityData.placeId) {
      placeId = item.threeLevelCityData.placeId;
    }
    return placeId;
  };

  onCityList = () => {
    let { cityData } = this.state;
    if (!cityData) {
      cityData = this.props.jobStore.allPlaceTree?.find((x) => x.locationId == 1);
    }
    NavigationService.navigate('jobCityChildren', {
      needSave: true,
      cityData,
      onSelect: (item) => {
        this.saveArea(item);
      },
    });
    // if (cityData.locationId) {
    //   NavigationService.navigate('jobCityChildren', {
    //     needSave: true,
    //     cityData,
    //     onSelect: (item) => {
    //       this.saveArea(item);
    //     },
    //   });
    // } else {
    //   NavigationService.navigate('jobCity', {
    //     needSave: true,
    //     onSelect: (item) => {
    //       this.saveArea(item);
    //     },
    //   });
    // }
  };

  onAddSave = async () => {
    try {
      const { cityData, detailAddress } = this.state;
      if (!cityData) {
        return toast.show(I18n.t('page_job_text_select_city'));
      } else if (!detailAddress) {
        return toast.show(I18n.t('page_job_text_input_address'));
      }
      this.showGlobalLoading();
      const formData = {
        ...cityData,
        location: this.getLocationLatLong(cityData),
        // placeId: this.getPlaceId(cityData),
        address: detailAddress,
        dAddress: cityData.cityName + ' ' + detailAddress,
      };
      if (this.item) {
        formData.timestamp = this.item.timestamp;
      } else {
        formData.timestamp = (new Date().getTime() / 1000).toFixed(0);
      }

      const { setLocalAddressList } = this.props.jobAction;
      await setLocalAddressList(formData);
      this.showRequestResult(
        this.item ? I18n.t('page_job_text_edit_success') : I18n.t('page_job_text_add_success')
      );
      this.props.navigation.state.params?.callback?.();
      NavigationService.goBack();
      global.emitter.emit(constant.event.changeAddress, formData);
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  renderBaseInfo = () => {
    const { style } = this;
    const { cityData, detailAddress } = this.state;
    const Item = this.renderItem;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_job_text_city_and_area')}
          required
          value={cityData?.cityName}
          onPress={this.onCityList}
        />
        <Item
          label={I18n.t('page_job_text_detail_address')}
          required
          isInput
          placeholder={I18n.t('page_resume_tips_input')}
          value={detailAddress}
          onChangeText={(v) => this.setState({ detailAddress: v })}
        />
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_select'),
    onPress,
    isInput,
    onChangeText,
    required = false,
    maxLength = 30,
    keyboardType = 'default',
  }) => {
    const { style } = this;
    return (
      <View style={style.itemContainer}>
        <View style={style.itemHeaderContainer}>
          <Text style={style.labelText}>{label} </Text>
          {required ? <Text style={style.labelStarText}>*</Text> : null}
        </View>
        {isInput ? (
          <Input
            placeholder={placeholder}
            placeholderTextColor="#CCCCCC"
            selectionColor="#EF3D48"
            isInput={false}
            style={{ color: '#333', fontSize: 16 }}
            onChangeText={onChangeText}
            returnKeyType="done"
            maxLength={maxLength}
            keyboardType={keyboardType}
            value={value}
          />
        ) : (
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style } = this;
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('page_resume_btn_save')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ width: '100%' }}
          onPress={this.onAddSave}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>
            {!!this.item
              ? I18n.t('page_job_text_edit_address')
              : I18n.t('page_job_text_add_address')}
          </Text>
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {this.renderBaseInfo()}

          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>

        {this.renderBottom()}
      </View>
    );
  }
}
