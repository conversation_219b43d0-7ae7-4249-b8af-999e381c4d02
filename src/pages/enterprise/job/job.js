import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform, Icon, Touchable } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import RecordList from './components/recordList';
import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 1,
      borderColor: theme.minorBgColor,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 0,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 20 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 4 - 20) / 2,
      backgroundColor: '#FF4A55',
    },
    rightIcon: {
      backgroundColor: '#EF3D48',
      width: 50,
      height: 32,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
}

/**
 * 职位
 */
@inject('companyStore')
@observer
export default class Job extends React.Component {
  constructor(props) {
    super(props);
    this.initialPage = this.props.navigation.state.params?.initialPage || 0;
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {};
  }

  onAdd = () => {
    NavigationService.navigate('jobAdd');
  };

  rightComponent = () => {
    return (
      <Touchable onPress={this.onAdd} style={this.style.rightIcon}>
        <Icon name="plus" size={22} color="#fff" type="entypo" />
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    const { statistics } = this.props.companyStore;
    return (
      <View style={style.container}>
        <Header
          title={I18n.t('page_home_job_manage_title')}
          hideBack
          rightComponent={this.rightComponent}
        />
        <ScrollableTabView
          tabBarBackgroundColor={style.tabBarBackgroundColor}
          tabBarInactiveTextColor={style.tabBarInactiveTextColor}
          tabBarActiveTextColor={style.tabBarActiveTextColor}
          underlineColor={style.underlineColor}
          tabBarStyle={style.tabBarStyle}
          tabBarTextStyle={style.tabBarTextStyle}
          activeTabTextStyle={style.activeTabTextStyle}
          tabBarUnderlineStyle={style.tabBarUnderlineStyle}
          tabStyles={style.tabStyles}
          initialPage={this.initialPage}
          isHorizontalScroll
          // scrollableTabBar={I18n.locale != 'zh'}
          locked={IS_ANDROID}
        >
          <RecordList
            key="0"
            index="0"
            status="1"
            tabLabel={`${I18n.t('page_home_text_online')}(${statistics?.online || 0})`}
          />
          <RecordList
            key="1"
            index="1"
            status="3"
            tabLabel={`${I18n.t('page_home_text_unpublished')}(${statistics?.pending || 0})`}
          />
          <RecordList
            key="2"
            index="2"
            status="2"
            tabLabel={`${I18n.t('page_home_text_offline')}(${statistics?.offline || 0})`}
          />
          <RecordList
            key="3"
            index="3"
            status="0"
            tabLabel={`${I18n.t('page_home_text_draft')}(${statistics?.draft || 0})`}
          />
        </ScrollableTabView>
      </View>
    );
  }
}
