import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Touchable, View, Header, BaseComponent } from '../../../components';
import { headerStyle, jobStyle, cityStyle } from '../../../themes';
import I18n from '../../../i18n';
import CountryCodePicker from '../../../components/CountryPicker';
import LoadingModal from '../../../components/loadingModal';
import Geolocation from '@react-native-community/geolocation';
import util from '../../../util';

@inject('cityStore', 'jobAction', 'cityAction')
@observer
export default class JobCity extends BaseComponent {
  constructor(props) {
    super(props);
    this.cityData = props.navigation.getParam('cityData', {});

    this.state = {
      showLoading: false,
      currentLocation: {},
    };
  }

  componentDidMount() {
    this.getAllPlace();
  }

  getAllPlace = async () => {
    try {
      this.showGlobalLoading();
      await this.props.jobAction.getLocationConstants2();
      this.showRequestResult();
    } catch (error) {
      this.showRequestResult();
    }
  };

  goBack = async () => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onContinueCheck) {
      await fuc.onContinueCheck();
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  selectItem = async (item) => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc) {
      item.cityName = util.getCityNameWithCityData(item);
      if (fuc.needSave) {
        await this.props.jobAction.saveCurrentCity(item);
      }
      fuc.onSelect(item);
    }
    navigation.goBack();
    // if (item.locationId != 32 && item.locationId != 30) {
    //   navigation.goBack();
    // } else {
    //   navigation.navigate('main');
    //   global.emitter.emit('switchCity', item);
    // }
  };

  render() {
    return (
      <View style={jobStyle.jobContainer}>
        <Header title={I18n.t('page_job_nav_top_select')} />
        <CountryCodePicker
          cityData={this.cityData}
          isShow
          onPick={(res) => {
            this.selectItem(res);
          }}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
