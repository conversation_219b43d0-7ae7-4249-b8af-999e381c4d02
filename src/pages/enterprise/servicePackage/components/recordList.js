import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, BaseComponent, Alert } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import NavigationService from '../../../../navigationService';
import moment from 'moment';
import SelectPackageModal from './selectPackageModal';
import ConfirmBuyModal from './confirmBuyModal';
import constant from '../../../../store/constant';

function getComponentStyle(theme) {
  return {
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 12,
      marginTop: 10,
      overflow: 'hidden',
      paddingTop: 16,
      paddingBottom: 12,
    },
    itemContainer: {
      paddingHorizontal: 14,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    sencondText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.primaryColor,
      marginRight: 11,
    },
    subTextValue: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      marginRight: 4,
    },
    subTextLabel: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    validDaysText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginTop: 12,
    },
    validDaysNum: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
    },
    saparateLine: {
      height: 1,
      backgroundColor: '#E5E5E5',
      marginVertical: 10,
    },
    itemBottomContainer: {
      paddingHorizontal: 14,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    costBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    costText: {
      fontSize: theme.fontSizeLg,
      color: theme.primaryColor,
      fontWeight: theme.fontWeightBold,
    },
    costUnitText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryColor,
      fontWeight: theme.fontWeightBold,
      marginLeft: 6,
      lineHeight: 28,
    },
    exchangeBtn: {
      minWidth: 78,
      height: 32,
      borderRadius: 5,
      backgroundColor: theme.primaryColor,
      paddingHorizontal: 6,
    },
    exchangeText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryBgColor,
      lineHeight: 32,
      textAlign: 'center',
    },
  };
}

/**
 * 列表
 */
@inject('companyAction', 'companyStore')
@observer
export default class RecordList extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.type = props.type;
    this.page = 1;
  }

  componentDidMount() {
    global.emitter.on(constant.event.servicePackageTabChanged, this.needRefresh);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.servicePackageTabChanged, this.needRefresh);
  }

  needRefresh = (index) => {
    if (index == 1) {
      this.onRefresh();
    }
  };

  onRefresh = () => this.pageFlatList?.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    this.page = page;
    try {
      const param = {
        page,
        size: 10,
      };
      const { queryProductServices, queryProducts } = this.props.companyAction;
      const func = this.type == 0 ? queryProductServices : queryProducts;
      const res = await func(param);
      return res;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    if (item.products?.length == 1) {
      // Alert.alert(
      //   I18n.t('op_remind_title'),
      //   I18n.t('page_servicePackage_text_confirm_exchange'),
      //   [
      //     {
      //       text: I18n.t('op_cancel_title'),
      //       onPress: () => console.log('Cancel Pressed'),
      //       style: 'cancel',
      //     },
      //     {
      //       text: I18n.t('op_confrim_text'),
      //       onPress: async () => {
      //         const product = item.products[0];
      //         if (product) {
      //           this.onExchange(product);
      //         }
      //       },
      //     },
      //   ],
      //   { cancelable: false }
      // );
      this.confirmBuyModal.show(item.products[0]);
      return;
    }
    this.selectPackageModal.show(item.products);
  };

  onExchange = async (item) => {
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.buyProduct({ productId: item.id });
      await this.props.companyAction.getBalance();
      const { callback } = this.props.navigation.state.params || {};
      callback && callback();
      this.showRequestResult(res?.message);
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onDetail = (item) => {
    NavigationService.navigate('recordDetail', {
      item: {
        ...item,
        id: item.accountTradeId,
        amount: item.productPrice,
        type: { label: I18n.t('page_job_text_buy_service_package') },
        description: item.productDescription,
      },
    });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const list = item.products?.length > 0 ? item.products[0].productItems : [];
    return (
      <View style={style.itemBox} key={index}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item.name}</Text>
          {list?.map((x, index) => (
            <View key={index} style={style.subContainer}>
              <View style={style.dot} />
              <Text style={style.subTextValue}>{x.amount}</Text>
              <Text style={style.subTextLabel} textType="amount">
                {x.itemId.label}
              </Text>
            </View>
          ))}
          <Text style={style.validDaysText}>
            {I18n.t('page_recharge_text_validity')}：
            <Text style={style.validDaysNum} textType="amount">
              {list?.length ? I18n.t('page_recharge_text_day', { count: list[0].expdate }) : ''}
            </Text>
          </Text>
        </View>
        <View style={style.saparateLine} />
        <View style={style.itemBottomContainer}>
          <View style={style.costBox}>
            <Text style={style.costText} textType="amount">
              {item.products?.length ? item.products[0].price : 0}
            </Text>
            <Text style={style.costUnitText}>
              {item.products?.length > 1
                ? I18n.t('page_servicePackage_text_coin')
                : I18n.t('page_recharge_text_coin', {
                    count: item.products?.length ? item.products[0].price : 0,
                  })}
            </Text>
          </View>
          <Touchable onPress={() => this.onItem(item)} style={style.exchangeBtn}>
            <Text style={style.exchangeText}>
              {item.products?.length > 1
                ? I18n.t('page_servicePackage_text_select_specification')
                : I18n.t('page_servicePackage_text_exchange')}
            </Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderMyItem = ({ item, index }) => {
    const { style } = this;
    return (
      <Touchable style={style.itemBox} key={index} onPress={() => this.onDetail(item)}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item?.productName}</Text>
          <Text style={style.subTextLabel} textType="amount">
            {I18n.t('page_recharge_text_trade_number')}：{item?.accountTradeId}
          </Text>
          <Text style={style.validDaysText}>
            {I18n.t('page_servicePackage_text_buy_time')}：
            <Text style={style.subTextLabel} textType="amount">
              {moment(item.cdate).format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          </Text>
          <Text style={style.validDaysText}>
            {I18n.t('page_servicePackage_text_expire_time')}：
            <Text style={style.validDaysNum} textType="amount">
              {moment(item.expdate).format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          </Text>
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.type == 0 ? this.renderItem : this.renderMyItem}
          showsVerticalScrollIndicator={false}
        />

        <SelectPackageModal
          ref={(ref) => (this.selectPackageModal = ref)}
          onConfirm={this.onExchange}
          companyStore={this.props.companyStore}
        />
        <ConfirmBuyModal
          ref={(ref) => (this.confirmBuyModal = ref)}
          onConfirm={this.onExchange}
          companyStore={this.props.companyStore}
        />
      </>
    );
  }
}
