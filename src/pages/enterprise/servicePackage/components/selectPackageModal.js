import React, { Component } from 'react';
import { View, Text, Image, Touchable, Alert } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import PageFlatList from '../../../../components/list/pageFlatList';
import ConfirmBuyModal from './confirmBuyModal';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.minorBgColor,
      flex: 1,
    },

    itemBox: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 12,
      marginTop: 10,
      overflow: 'hidden',
      paddingTop: 16,
      paddingBottom: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemContainer: {
      paddingHorizontal: 14,
      flex: 1,
      flexShrink: 1,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    sencondText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.primaryColor,
      marginRight: 11,
    },
    subTextValue: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      marginRight: 4,
    },
    subTextLabel: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    itemBottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 4,
    },
    costBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    },
    costText: {
      fontSize: theme.fontSizeLg,
      color: theme.primaryColor,
      fontWeight: theme.fontWeightBold,
    },
    costUnitText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryColor,
      fontWeight: theme.fontWeightBold,
      marginLeft: 6,
      lineHeight: 28,
    },
    exchangeBtn: {
      minWidth: 78,
      height: 32,
      borderRadius: 5,
      backgroundColor: theme.primaryColor,
    },
    exchangeText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryBgColor,
      lineHeight: 32,
      textAlign: 'center',
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EF3D48',
      height: 32,
      width: 78,
      borderRadius: 5,
      justifyContent: 'center',
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#fff',
      lineHeight: 32,
    },
  };
}
/**
 * 服务包弹出框
 * <AUTHOR>
 */
export default class SelectPackageModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      list: [],
    };
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  show = (list) => {
    this.setState({ list, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = (item) => {
    this.confirmBuyModal.show(item);
  };

  onBuyConfirm = (item) => {
    if (this.props.onConfirm) {
      this.props.onConfirm(item);
    }
    this.close();
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    return (
      <View style={style.itemBox} key={index}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item?.name}</Text>
          {item?.productItems?.map((x, index) => (
            <View key={index} style={style.subContainer}>
              <View style={style.dot} />
              <Text style={style.subTextValue}>{x.amount}</Text>
              <Text style={style.subTextLabel} textType="amount">
                {x.itemId.label}
              </Text>
            </View>
          ))}

          <View style={style.itemBottomContainer}>
            <View style={style.costBox}>
              <Text style={style.costText} textType="amount">
                {item.price || 0}
              </Text>
              <Text style={style.costUnitText}>
                {I18n.t('page_recharge_text_coin', {
                  count: item.price || 0,
                })}
              </Text>
            </View>
            <Touchable style={style.flexRow} onPress={() => this.onConfirm(item)}>
              <Text style={style.statusText}>{I18n.t('page_servicePackage_text_exchange')}</Text>
            </Touchable>
          </View>
        </View>
      </View>
    );
  };

  render() {
    const { companyStore, ...rest } = this.props;
    const { showModal, list } = this.state;
    const { style } = this;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_servicePackage_text_specification')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={460}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={false}
        showBottomView={false}
        coverScreen
        {...rest}
      >
        <View style={style.pagerContainer}>
          <PageFlatList
            ref={this.initPageFlatList}
            data={list}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={() => <View style={{ height: 36 }} />}
          />
        </View>

        <ConfirmBuyModal
          ref={(ref) => (this.confirmBuyModal = ref)}
          onConfirm={this.onBuyConfirm}
          companyStore={companyStore}
          onClose={this.close}
        />
      </BottomModal>
    );
  }
}
