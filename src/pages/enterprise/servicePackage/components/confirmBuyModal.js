import React, { Component } from 'react';
import { View, Text, Image, Touchable, Al<PERSON>, Button } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import { Icon } from 'react-native-elements';
import NavigationService from '../../../../navigationService';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.primaryBgColor,
      marginTop: 10,
      paddingVertical: 18,
      paddingHorizontal: 20,
      height: 420,
    },
    amountTitle: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 12,
      textAlign: 'center',
    },
    costBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    costText: {
      fontSize: theme.fontSizeLg,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
    },
    costUnitText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      lineHeight: 28,
      marginLeft: 6,
    },
    line: {
      borderStyle: 'dashed',
      borderWidth: 1,
      borderColor: '#CCCCCC',
      marginTop: 17,
      marginBottom: 4,
    },
    sectionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    sectionItemLabel: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    btnBottom: {
      position: 'absolute',
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 18,
      marginLeft: 20,
      bottom: 40,
    },
    btnRecharge: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: 44,
      borderRadius: 5,
      backgroundColor: '#FAECEC',
    },
    btnRechargeText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryColor,
      marginRight: -3,
    },
    walletItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    walletText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      marginLeft: 4,
    },
    walletTextNum: {
      fontSize: theme.fontSizeL,
      color: theme.primaryColor,
    },
  };
}
/**
 * 购买服务包确认弹窗
 * <AUTHOR>
 */
export default class ConfirmBuyModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      item: null,
    };
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  show = (item) => {
    console.log('item', item);
    this.setState({ item, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = () => {
    if (this.props.onConfirm) {
      this.props.onConfirm(this.state.item);
    }
    this.close();
  };

  onRecharge = () => {
    this.close();
    this.props.onClose && this.props.onClose();
    NavigationService.navigate('companyRecharge', {});
  };

  render() {
    const { companyStore, ...rest } = this.props;
    const { showModal, item } = this.state;
    const { style } = this;
    const showRecharge = item?.price > companyStore?.balance;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_servicePackage_label_confirm_exchange')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={400}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={true}
        showBottomView={false}
        coverScreen
        style={{ backgroundColor: '#F7F7F7' }}
        {...rest}
      >
        <View style={style.pagerContainer}>
          <Text style={style.amountTitle}>{I18n.t('page_servicePackage_text_pay_amount')}</Text>
          <View style={style.costBox}>
            <Text style={style.costText} textType="amount">
              {item?.price}
            </Text>
            <Text style={style.costUnitText}>
              {I18n.t('page_recharge_text_coin', { count: item?.price || 0 })}
            </Text>
          </View>
          <View style={style.line} />
          <View style={style.sectionItem}>
            <Text style={style.sectionItemLabel}>
              {I18n.t('page_servicePackage_label_transaction_type')}
            </Text>
            <Text style={style.sectionItemLabel}>
              {I18n.t('page_servicePackage_text_buy_service_package')}
            </Text>
          </View>
          <View style={style.sectionItem}>
            <Text style={style.sectionItemLabel}>
              {I18n.t('page_servicePackage_text_pay_method')}
            </Text>
            <Text style={style.sectionItemLabel}>
              {I18n.t('page_servicePackage_text_wallet_balance')}
            </Text>
          </View>
          <View style={style.sectionItem}>
            <Text style={style.sectionItemLabel}></Text>
            <View style={style.walletItem}>
              <Image source={resIcon.iconWalletEnterprise} />
              <Text style={style.walletText} textType="amount">
                {I18n.t('page_home_text_balance')}
                <Text style={style.walletTextNum}> {companyStore?.balance || '0'} </Text>
                {I18n.t('page_recharge_text_coin', { count: companyStore?.balance || 0 })}
              </Text>
            </View>
          </View>
          <View style={style.btnBottom}>
            {showRecharge ? (
              <Touchable style={style.btnRecharge} onPress={this.onRecharge}>
                <Text style={style.btnRechargeText} ellipsizeMode="middle" textType="amount">
                  {I18n.t('page_servicePackage_text_balance_insufficient')}
                </Text>
                <Icon name="chevron-right" type="feather" size={22} color="#EF3D48" />
              </Touchable>
            ) : null}
            <View style={{ height: 24 }} />
            <Button
              title={I18n.t('page_servicePackage_label_confirm_exchange')}
              btnType="login"
              btnSize="s44"
              disabled={showRecharge}
              containerStyle={{ width: '100%' }}
              onPress={this.onConfirm}
            />
          </View>
        </View>
      </BottomModal>
    );
  }
}
