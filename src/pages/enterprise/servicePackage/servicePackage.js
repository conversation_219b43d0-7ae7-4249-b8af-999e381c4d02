import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform, Icon, Touchable } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import RecordList from './components/recordList';
import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 1,
      borderColor: theme.minorBgColor,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 0,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 20 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 2 - 20) / 2,
      backgroundColor: '#FF4A55',
    },
    rightIcon: {
      backgroundColor: '#EF3D48',
      width: 50,
      height: 32,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
}

/**
 * 服务包
 */
@inject('userStore', 'mineStore', 'mineAction', 'settingsStore')
@observer
export default class ServicePackage extends React.Component {
  constructor(props) {
    super(props);
    this.initialPage = props.navigation.state.params?.initialPage || 0;
    this.isFromCompany = props.navigation.state.params?.isFromCompany || false;
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {};
  }

  onChangeTab = ({ i }) => {
    global.emitter.emit(constant.event.servicePackageTabChanged, i);
  };

  render() {
    const { style } = this;

    return (
      <View style={style.container}>
        <Header
          title={I18n.t('page_tabbar_text_service_package_title')}
          hideBack={!this.isFromCompany}
        />
        <ScrollableTabView
          tabBarBackgroundColor={style.tabBarBackgroundColor}
          tabBarInactiveTextColor={style.tabBarInactiveTextColor}
          tabBarActiveTextColor={style.tabBarActiveTextColor}
          underlineColor={style.underlineColor}
          tabBarStyle={style.tabBarStyle}
          tabBarTextStyle={style.tabBarTextStyle}
          activeTabTextStyle={style.activeTabTextStyle}
          tabBarUnderlineStyle={style.tabBarUnderlineStyle}
          tabStyles={style.tabStyles}
          initialPage={this.initialPage}
          isHorizontalScroll={false}
          locked={IS_ANDROID}
          onChangeTab={this.onChangeTab}
        >
          <RecordList
            key="0"
            type="0"
            tabLabel={I18n.t('page_servicePackage_text_buy')}
            navigation={this.props.navigation}
          />
          <RecordList
            key="1"
            type="1"
            tabLabel={I18n.t('page_servicePackage_text_my')}
            navigation={this.props.navigation}
          />
        </ScrollableTabView>
      </View>
    );
  }
}
