import React from 'react';
import { Text, View, Header, ScrollView } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import util from '../../../util';
import moment from 'moment';

function getComponentStyle(theme) {
  return {
    scrollViewContainer: {
      flex: 1,
    },
    container: {
      // backgroundColor: theme.primaryBgColor,
      marginHorizontal: 12,
      marginTop: 10,
    },
    headerText: {
      fontSize: 18,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    itemContainerBox: {
      paddingHorizontal: 14,
      paddingTop: 20,
      paddingBottom: 6,
      borderRadius: 10,
      backgroundColor: theme.primaryBgColor,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 0,
      marginRight: 16,
      minWidth: 80,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 1,
    },
    cellBox: {
      backgroundColor: '#FFF5F5',
      borderRadius: 10,
      marginBottom: 10,
      paddingTop: 18,
      paddingBottom: 14,
      paddingLeft: 11,
      paddingRight: 16,
    },
    dotLineContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      overflow: 'hidden',
      marginBottom: 12,
    },
    dotLine: {
      // width: '100%',
      // borderStyle: 'dashed',
      // borderWidth: 1,
      // borderColor: '#CCCCCC',
      // marginBottom: 12,
      width: 5,
      height: 1,
      backgroundColor: '#CCCCCC',
      marginLeft: 2,
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    subContentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      flex: 1,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.primaryColor,
      marginRight: 11,
    },
    subTextValue: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 0,
    },
    subTextLabel: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 1,
      marginRight: 16,
    },
  };
}

/**
 * 交易记录详情
 */
export default class RecordDetail extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.item = props.navigation.state.params?.item || {};
  }

  componentDidMount() {}

  renderItem = ({ label, value }) => {
    const { style } = this;
    if (!value) {
      return null;
    }
    return (
      <View style={style.itemContainer}>
        <Text style={style.labelText} textType="amount">
          {label}
        </Text>
        <Text style={style.valueText} textType="amount">
          {value}
        </Text>
      </View>
    );
  };

  renderSubItem = ({ label, value, showDot = true }) => {
    const { style } = this;
    if (!value) {
      return null;
    }
    return (
      <View style={style.subContainer}>
        {showDot ? <View style={style.dot} /> : null}
        <View style={style.subContentContainer}>
          <Text style={style.subTextLabel}>{label}</Text>
          <Text style={style.subTextValue} textType="amount">
            {value}
          </Text>
        </View>
      </View>
    );
  };

  renderDashLine = () => {
    const { style } = this;
    const dottedLineDots = Array.from({ length: 100 }, (_, index) => (
      <View key={index} style={style.dotLine}></View>
    ));
    return <View style={style.dotLineContainer}>{dottedLineDots}</View>;
  };

  render() {
    const { style } = this;
    const { item } = this;
    const Item = this.renderItem;
    const SubItem = this.renderSubItem;
    return (
      <>
        <Header theme="dark" title={I18n.t('page_servicePackage_text_transaction_detail')} />

        <ScrollView style={style.scrollViewContainer} showsVerticalScrollIndicator={false}>
          <View style={style.container}>
            <View style={style.itemContainerBox}>
              <Item
                label={I18n.t('page_servicePackage_text_transaction_number')}
                value={item?.id}
              />
              <Item
                label={I18n.t('page_servicePackage_text_transaction_time')}
                value={item?.cdate ? moment(item.cdate).format('YYYY/MM/DD HH:mm') : ''}
              />

              <Item label={I18n.t('page_servicePackage_text_user')} value={item?.username} />
              <Item
                label={I18n.t('page_servicePackage_text_transaction_price')}
                value={
                  item?.amount
                    ? `${I18n.t('page_recharge_text_number_of_coin', { count: item.amount })}`
                    : ''
                }
              />
              <Item
                label={I18n.t('page_servicePackage_text_transaction_type')}
                value={item?.type?.label}
              />
              <Item
                label={I18n.t('page_servicePackage_text_service_package')}
                value={item?.productName}
              />
              <Item label={I18n.t('page_servicePackage_text_discount')} value={item?.discount} />
              <Item label={I18n.t('page_servicePackage_text_remark')} value={item?.description} />
              {item.employerProductItems?.length ? (
                <>
                  {/* <View style={style.dotLine} /> */}
                  {this.renderDashLine()}
                  <Text style={[style.labelText, { marginBottom: 14 }]}>
                    {I18n.t('page_servicePackage_text_service_package')}
                  </Text>
                  {item.employerProductItems.map((x, index) => (
                    <View style={style.cellBox} key={index}>
                      <SubItem
                        label={I18n.t('page_servicePackage_text_service_item')}
                        value={x.itemName}
                      />
                      <SubItem
                        label={I18n.t('page_servicePackage_text_buy_number')}
                        value={x.amount}
                      />
                      <SubItem
                        label={I18n.t('page_servicePackage_text_validity')}
                        value={x.expdate ? moment(x.expdate).format('YYYY/MM/DD HH:mm') : ''}
                      />
                      <SubItem
                        label={I18n.t('page_servicePackage_text_service_validity')}
                        value={I18n.t('page_recharge_text_day', { count: x.display })}
                      />
                    </View>
                  ))}
                </>
              ) : null}
            </View>
          </View>
        </ScrollView>
      </>
    );
  }
}
