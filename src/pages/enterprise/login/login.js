/**
 * 功能：登录
 * 描述：
 * 作者：sxw
 */

import React, { Component } from 'react';
import axios from 'axios';
import {
  BackHandler,
  Button,
  Icon,
  Image,
  StatusBar,
  Text,
  View,
  Touchable,
  Input,
  KeyboardAwareScrollView,
  BaseComponent,
} from '../../../components';
import { inject, observer } from 'mobx-react';
import I18n, { setLanguage } from '../../../i18n';
import styles from '../../../themes/enterprise';
import resIcon from '../../../res';
import TooltipMenu from '../../../components/menu/tooltipMenu';
import constant from '../../../store/constant';
import { reset } from '../../../components/softInputMode';
import { statusBarHeight, hasEar } from '../../../common';
import NavigationService from '../../../navigationService';
import InputPassword from '../../../components/input/inputLinePassword';
import configs from '../../../configs';
import I18nUtil from '../../../util/I18nUtil';
import CountryCode from '../../../components/countryCode';
import regExp from '../../../util/regExp';
import validateUtil from '../../../util/validateUtil';
import InputImageCaptchaModal from '../../../components/modal/inputImageCaptchaModal';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    logoLogin: {
      marginTop: 12,
    },
    loginTitle: {
      color: '#26305F',
      fontSize: 14,
      fontWeight: '500',
      marginTop: -20,
    },
    btnBox: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 38,
      marginVertical: 21,
    },
    btnContainerStyle: {
      flex: 1,
    },
    languageContainer: {
      marginTop: statusBarHeight,
      paddingHorizontal: 28,
      height: 40,
      justifyContent: 'center',
    },
    languageTooltipWrap: {
      flexDirection: 'row-reverse',
    },
    languageWrap: {
      flexDirection: 'row-reverse',
      alignItems: 'center',
    },
    language: {
      paddingHorizontal: 5,
      height: 20,
      justifyContent: 'center',
    },
    languageText: {
      fontSize: 14,
      color: '#333',
      lineHeight: 18,
    },
    languageDropdown: {
      justifyContent: 'center',
    },
    registerBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    registerNormalText: {
      color: '#384A7C',
      fontSize: 15,
      fontWeight: '500',
    },
    registerText: {
      color: theme.primaryColor,
      fontSize: 15,
      fontWeight: '500',
    },
    inputForm: {
      marginTop: 41,
    },
    formInputContainer: {
      flexDirection: 'column',
    },
    formTitle: {
      fontSize: 14,
      fontWeight: '400',
      color: theme.primaryFontColor,
      marginHorizontal: 38,
    },
    inputContainer: {
      height: 48,
      marginHorizontal: 25,
    },
    commonInputContainer: {
      borderBottomColor: '#EEEEEE',
    },
    inputStyle: {
      height: 48,
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
    },
    forgetPwd: {
      color: '#384A7C',
      fontSize: 15,
      fontWeight: '400',
      textAlign: 'right',
      paddingRight: 38,
      marginTop: 8,
    },
    backPersonalBox: {
      flex: 0,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    backPersonal: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingTop: 10,
      paddingBottom: hasEar ? 44 : 20,
      backgroundColor: '#fff',
    },
    backPersonalItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    backPersonalText: {
      fontSize: 15,
      color: '#384A7C',
      fontWeight: '400',
      marginLeft: 9,
    },

    tabSection: {
      marginTop: 25,
      backgroundColor: '#fff',
    },
    tabHeader: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      height: 50,
    },
    tabItem: {
      justifyContent: 'center',
      alignItems: 'center',
      minWidth: 80,
    },
    tabTitle: {
      fontSize: 16,
      color: '#333',
    },
    tabLine: {
      width: 30,
      height: 4,
      backgroundColor: '#fff',
      marginTop: 5,
    },
    tabLineActive: {
      backgroundColor: '#EF3D48',
    },
    tabActiveTitle: {
      color: '#333',
    },
    tabBody: {
      // flex: 1,
    },
    formSendCodeBtnWrap: {
      height: 36,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 10,
      borderColor: '#384A7C',
      borderWidth: 1,
      borderRadius: 2,
    },
    switchText: {
      fontSize: 14,
      color: '#384A7C',
    },
    regionCodeText: {
      fontSize: 16,
      color: '#333',
      marginRight: 5,
    },
  };
}

@inject('loginAction', 'companyAction', 'globalStore', 'globalAction')
@observer
export default class Login extends BaseComponent {
  static navigationOptions = {
    gestureEnabled: false,
  };

  constructor(props) {
    super(props);
    this.isGoBack = props.navigation.getParam('isGoBack', false);
    this.timeout = null;
    this.state = {
      account: '',
      password: '',
      captcha: '',
      signature: '',
      captchaImage: '',
      tabId: 1,
      region: constant.defaultRegion,
      regionCode: constant.defaultRegion.code,
      regionCodeLabel: constant.defaultRegion.label,
      phone: '',
      code: '',
      sending: false,
      seconds: 60,
      isOpenSelectedCountryCode: false,
      usePhonePwd: false,
    };
    this.loginStyle = getComponentStyle(styles.get('theme'));
  }

  componentDidMount() {
    this.getCaptchaImage();
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    reset();
  }

  componentWillUnmount() {
    if (this.timeout) {
      clearInterval(this.timeout);
    }
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => {
    return this.isGoBack;
  };

  getCaptchaImage = () => {
    axios.get(`${configs.serverURL}/captcha/image?format=base64`).then((result) => {
      this.setState({
        captchaImage: result.data.image,
        signature: result.data.signature,
      });
    });
  };

  onRegister = () => {
    this.props.navigation.navigate('webviewDetail', {
      url: configs.registerUrl + '?lang=' + I18n.locale,
      title: 'page_login_title_enterpise_register',
      showShare: false,
      headerBgColor: '#EF3D48',
      callback: () => this.forceUpdate(),
      isLoginPage: true,
    });
  };

  onLogin = () => {
    const { tabId } = this.state;
    if (tabId === 1) {
      this.onAccountLogin();
    } else {
      this.onPhoneCodeLogin();
    }
  };

  onAccountLogin = async () => {
    try {
      const { account, password, captcha, signature, phone, regionCode, usePhonePwd } = this.state;
      if (!usePhonePwd && account.trim() === '') {
        toast.show(I18n.t('page_login_text_enter_account'));
        return;
      }
      if (usePhonePwd && phone.trim() === '') {
        toast.show(I18n.t('page_login_text_input_phone'));
        return;
      }
      if (password.trim() === '') {
        toast.show(I18n.t('page_login_op_password_required'));
        return;
      }
      if (captcha.trim() === '') {
        toast.show(I18n.t('page_login_op_img_code_required'));
        return;
      }
      this.showGlobalLoading();
      const res = await this.props.loginAction.epLoginByAccount(
        usePhonePwd ? `${regionCode}${phone.trim()}` : account.trim(),
        password.trim(),
        captcha.trim(),
        signature
      );
      if (res && res.successful) {
        this.showRequestResult();
        NavigationService.reset('main');
      } else {
        this.getCaptchaImage();
        this.showRequestResult(res.message);
      }
    } catch (error) {
      this.getCaptchaImage();
      this.showRequestResult(error);
    }
  };

  onPhoneCodeLogin = async () => {
    try {
      const { phone, code, captcha, signature, regionCode } = this.state;
      if (phone.trim() === '') {
        toast.show(I18n.t('page_login_text_input_phone'));
        return;
      }
      if (captcha.trim() === '') {
        toast.show(I18n.t('page_login_op_img_code_required'));
        return;
      }
      if (code.trim() === '') {
        toast.show(I18n.t('page_login_text_input_sms_code'));
        return;
      }
      this.showGlobalLoading();
      const res = await this.props.loginAction.authorizeMobile({
        mobile: phone.trim(),
        regionCode,
        verificationCode: code.trim(),
        captcha: captcha.trim(),
        signature,
      });
      if (res && res.successful) {
        this.showRequestResult();
        NavigationService.reset('main');
      } else {
        this.getCaptchaImage();
        this.showRequestResult(res.message);
      }
    } catch (error) {
      this.getCaptchaImage();
      this.showRequestResult(error);
    }
  };

  onSwitchLanguage = (language) => {
    setLanguage(language);
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    global.emitter.emit('languageChange', true);
    this.forceUpdate();
  };

  onChangeUsername = (text) => {
    this.setState({ account: text });
  };

  setPassword = (text) => {
    this.setState({ password: text });
  };

  setCaptcha = (text) => {
    this.setState({ captcha: text });
  };

  setPhone = (text) => {
    this.setState({ phone: text });
  };

  setCode = (text) => {
    this.setState({ code: text });
  };

  sendCode = async (imageCaptcha) => {
    try {
      const { phone, regionCode, region, sending } = this.state;
      if (sending) return;
      const mobile = validateUtil.validatePhone(phone, region);
      if (!mobile) return;
      const res = await this.inputImageCaptchaModal.sendImPhoneCode(
        {
          param: {
            mobile: phone,
            regionCode,
            imageCaptcha,
          },
          onConfirm: this.sendCode,
        },
        this.props.companyAction.sendVerifyCode
      );
      if (res && res.successful) {
        toast.show(I18n.t('page_login_op_sendcode_success'));
        this.runSendCodeTimeout();
      } else {
        if (res.message) {
          toast.show(res.message);
        } else {
          toast.show(I18n.t('page_login_op_sendcode_error'));
        }
      }
    } catch (e) {
      console.log('sendVerifyCode error', err);
      this.setState({ sending: false });
    }
  };

  runSendCodeTimeout = () => {
    this.setState({ sending: true });
    this.timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds === 0) {
        this.setState({ seconds: 60, sending: false });
        clearInterval(this.timeout);
      }
    }, 1000);
  };

  onForgotPwd = () => {
    this.props.navigation.navigate('webviewDetail', {
      url: configs.forgetPwdUrl + '?lang=' + I18n.locale,
      title: 'page_login_link_forgot',
      showShare: false,
      headerBgColor: '#EF3D48',
      callback: () => this.forceUpdate(),
      isLoginPage: true,
    });
  };

  onBackPersonal = async () => {
    await this.props.globalAction.setEnterprise(false);
    this.props.navigation.replace('login');
  };

  onSwitchTab = (tabId) => {
    this.setState({ tabId });
  };

  onSelectedCountryCode = (item) => {
    console.log('onSelectedCountryCode', item);
    if (item) {
      this.setState({ region: item, regionCodeLabel: item.label, regionCode: item.code });
    }
    this.setState({ isOpenSelectedCountryCode: false });
  };

  renderLanguage = () => {
    const { loginStyle } = this;
    return (
      <View style={loginStyle.languageContainer}>
        <TooltipMenu
          componentWrapperStyle={loginStyle.languageTooltipWrap}
          positionStyle={{ top: statusBarHeight + 10 }}
          buttonComponent={
            <View style={loginStyle.languageWrap}>
              <View style={loginStyle.languageDropdown}>
                <Icon type="antdesign" name="down" size={14} color="#666" />
              </View>
              <View style={loginStyle.language}>
                <Text style={loginStyle.languageText}>{I18n.t('language')}</Text>
              </View>
            </View>
          }
          items={[
            {
              label: I18n.t('language', { locale: 'zh' }),
              icon: resIcon.cnFlag,
              onPress: () => this.onSwitchLanguage('zh'),
            },
            {
              label: I18n.t('language', { locale: 'en' }),
              icon: resIcon.usFlag,
              onPress: () => this.onSwitchLanguage('en'),
            },
            {
              label: I18n.t('language', { locale: 'km' }),
              icon: resIcon.khFlag,
              onPress: () => this.onSwitchLanguage('km'),
            },
            {
              label: I18n.t('language', { locale: 'vi' }),
              icon: resIcon.vnFlag,
              onPress: () => this.onSwitchLanguage('vi'),
            },
            {
              label: I18n.t('language', { locale: 'th' }),
              icon: resIcon.thFlag,
              onPress: () => this.onSwitchLanguage('th'),
            },
            {
              label: I18n.t('language', { locale: 'ko' }),
              icon: resIcon.krFlag,
              onPress: () => this.onSwitchLanguage('ko'),
            },
          ]}
        />
      </View>
    );
  };

  renderAccountForm = () => {
    const { loginStyle } = this;
    const { password, account, phone, captcha, usePhonePwd } = this.state;
    return (
      <View style={loginStyle.inputForm}>
        <View style={[loginStyle.formInputContainer, {}]}>
          <Text style={loginStyle.formTitle}>
            {usePhonePwd
              ? I18n.t('page_resume_label_phone')
              : I18n.t('page_login_text_enterprise_account')}
          </Text>
          <View style={loginStyle.inputContainer}>
            {usePhonePwd ? (
              <Input
                inputContainerStyle={loginStyle.commonInputContainer}
                inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
                placeholder={I18n.t('page_login_text_input_phone')}
                placeholderTextColor="#CCCCCC"
                onChangeText={this.setPhone}
                selectionColor="#EF3D48"
                returnKeyType="done"
                value={phone}
                inputType="phone"
                leftIcon={() => (
                  <Touchable
                    onPress={() => this.setState({ isOpenSelectedCountryCode: true })}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                  >
                    <Text style={loginStyle.regionCodeText}>{this.state.regionCodeLabel}</Text>
                    <Icon
                      type="antdesign"
                      name="down"
                      size={14}
                      color="#666"
                      style={{ marginLeft: 5 }}
                    />
                  </Touchable>
                )}
                rightIcon={() => (
                  <Touchable onPress={() => this.setState({ usePhonePwd: false })}>
                    <Text style={loginStyle.switchText}>
                      {I18n.t('page_login_text_use_account')}
                    </Text>
                  </Touchable>
                )}
              />
            ) : (
              <Input
                inputContainerStyle={loginStyle.commonInputContainer}
                inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
                placeholder={I18n.t('page_login_text_input_enterprise_account')}
                placeholderTextColor="#CCCCCC"
                onChangeText={this.onChangeUsername}
                selectionColor="#EF3D48"
                returnKeyType="done"
                value={account}
                rightIcon={() => (
                  <Touchable onPress={() => this.setState({ usePhonePwd: true })}>
                    <Text style={loginStyle.switchText}>{I18n.t('page_login_text_use_phone')}</Text>
                  </Touchable>
                )}
              />
            )}
          </View>
        </View>
        <View style={[loginStyle.formInputContainer, { marginTop: 18 }]}>
          <Text style={loginStyle.formTitle}>{I18n.t('page_login_ph_password')}</Text>
          <View style={loginStyle.inputContainer}>
            <InputPassword
              placeholder={I18n.t('page_login_op_password_required')}
              maxLength={16}
              onChangeText={this.setPassword}
              inputContainerStyle={loginStyle.commonInputContainer}
              placeholderTextColor="#CCCCCC"
              inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
              selectionColor="#EF3D48"
              defaultValue={password}
              returnKeyType="done"
            />
          </View>
        </View>
        <View style={[loginStyle.formInputContainer, { marginTop: 18 }]}>
          <Text style={loginStyle.formTitle}>{I18n.t('page_login_ph_img_code')}</Text>
          <View style={loginStyle.inputContainer}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={loginStyle.commonInputContainer}
              inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
              placeholder={I18n.t('page_register_op_code_required')}
              placeholderTextColor="#CCCCCC"
              onChangeText={this.setCaptcha}
              maxLength={5}
              rightIcon={() => (
                <Touchable onPress={this.getCaptchaImage}>
                  <Image
                    source={{ uri: this.state.captchaImage }}
                    style={{ height: 50, width: 120, marginLeft: 5 }}
                    resizeMode="contain"
                  />
                </Touchable>
              )}
              returnKeyType="done"
              value={captcha}
              selectionColor="#EF3D48"
            />
          </View>
        </View>
      </View>
    );
  };

  renderPhoneForm = () => {
    const { loginStyle } = this;
    const { code, phone, captcha, sending, seconds } = this.state;
    return (
      <View style={loginStyle.inputForm}>
        <View style={[loginStyle.formInputContainer, {}]}>
          <Text style={loginStyle.formTitle}>{I18n.t('page_resume_label_phone')}</Text>
          <View style={loginStyle.inputContainer}>
            <Input
              inputContainerStyle={loginStyle.commonInputContainer}
              inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
              placeholder={I18n.t('page_login_text_input_phone')}
              placeholderTextColor="#CCCCCC"
              onChangeText={this.setPhone}
              selectionColor="#EF3D48"
              returnKeyType="done"
              value={phone}
              inputType="phone"
              leftIcon={() => (
                <Touchable
                  onPress={() => this.setState({ isOpenSelectedCountryCode: true })}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <Text style={loginStyle.regionCodeText}>{this.state.regionCodeLabel}</Text>
                  <Icon
                    type="antdesign"
                    name="down"
                    size={14}
                    color="#666"
                    style={{ marginLeft: 5 }}
                  />
                </Touchable>
              )}
            />
          </View>
        </View>
        <View style={[loginStyle.formInputContainer, { marginTop: 18 }]}>
          <Text style={loginStyle.formTitle}>{I18n.t('page_login_ph_img_code')}</Text>
          <View style={loginStyle.inputContainer}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={loginStyle.commonInputContainer}
              inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
              placeholder={I18n.t('page_login_op_code_required')}
              placeholderTextColor="#CCCCCC"
              onChangeText={this.setCaptcha}
              maxLength={5}
              rightIcon={() => (
                <Touchable onPress={this.getCaptchaImage}>
                  <Image
                    source={{ uri: this.state.captchaImage }}
                    style={{ height: 50, width: 120, marginLeft: 5 }}
                    resizeMode="contain"
                  />
                </Touchable>
              )}
              returnKeyType="done"
              value={captcha}
              selectionColor="#EF3D48"
            />
          </View>
        </View>
        <View style={[loginStyle.formInputContainer, { marginTop: 18 }]}>
          <Text style={loginStyle.formTitle}>{I18n.t('page_login_text_sms_code')}</Text>
          <View style={loginStyle.inputContainer}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={loginStyle.commonInputContainer}
              inputStyle={[loginStyle.inputStyle, { paddingLeft: 0 }]}
              placeholder={I18n.t('page_login_text_input_sms_code')}
              placeholderTextColor="#CCCCCC"
              onChangeText={this.setCode}
              maxLength={5}
              inputType="phone"
              rightIcon={() => (
                <Touchable
                  style={[
                    loginStyle.formSendCodeBtnWrap,
                    sending ? { backgroundColor: '#eee', borderWidth: 0 } : {},
                  ]}
                  disabled={sending}
                  onPress={this.sendCode}
                >
                  <Text style={{ fontSize: 14, color: sending ? '#666' : '#384A7C' }}>
                    {sending
                      ? `${I18n.t('page_login_btn_sendcode')}(${seconds})`
                      : I18n.t('page_login_btn_sendcode')}
                  </Text>
                </Touchable>
                // <Button
                //   title={
                //     sending
                //       ? `${I18n.t('page_login_btn_sendcode')}(${seconds})`
                //       : I18n.t('page_login_btn_sendcode')
                //   }
                //   disabled={sending}
                //   onPress={this.sendCode}
                //   btnSize="s36"
                //   outline
                //   btnType="sendCode"
                //   style={{ minWidth: 90 }}
                //   containerStyle={{ marginHorizontal: 0 }}
                // />
              )}
              returnKeyType="done"
              value={code}
              selectionColor="#EF3D48"
            />
          </View>
        </View>
      </View>
    );
  };

  render() {
    const { loginStyle } = this;
    const { tabId, isOpenSelectedCountryCode } = this.state;
    return (
      <View style={loginStyle.container}>
        <StatusBar containerStyle={{ height: 0 }} barStyle="dark-content" translucent />
        {this.renderLanguage()}
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          <View style={{ flex: 1 }}>
            <View style={{ alignItems: 'center' }}>
              <Image source={resIcon.logo} style={loginStyle.logoLogin} />
            </View>

            <View style={[loginStyle.tabSection]}>
              <View style={loginStyle.tabHeader}>
                <Touchable
                  style={[loginStyle.tabItem, { marginRight: 80 }]}
                  onPress={() => this.onSwitchTab(1)}
                >
                  <Text style={[loginStyle.tabTitle, tabId === 1 && loginStyle.tabActiveTitle]}>
                    {I18n.t('page_login_ph_password')}
                  </Text>
                  <View style={[loginStyle.tabLine, tabId === 1 && loginStyle.tabLineActive]} />
                </Touchable>
                <Touchable style={loginStyle.tabItem} onPress={() => this.onSwitchTab(2)}>
                  <Text style={[loginStyle.tabTitle, tabId === 2 && loginStyle.tabActiveTitle]}>
                    {I18n.t('page_login_ph_code')}
                  </Text>
                  <View style={[loginStyle.tabLine, tabId === 2 && loginStyle.tabLineActive]} />
                </Touchable>
              </View>
              <View style={loginStyle.tabBody}>
                {tabId === 1 && this.renderAccountForm()}
                {tabId === 2 && this.renderPhoneForm()}
              </View>
            </View>

            <Touchable onPress={this.onForgotPwd}>
              <Text style={loginStyle.forgetPwd}>{I18n.t('page_login_link_forgot')}?</Text>
            </Touchable>
            <View style={loginStyle.btnBox}>
              <Button
                title={I18n.t('page_login_btn_login')}
                onPress={this.onLogin}
                containerStyle={loginStyle.btnContainerStyle}
                btnType="login"
              />
            </View>
            <View style={loginStyle.registerBox}>
              <Text style={loginStyle.registerNormalText}>
                {I18n.t('page_login_text_no_account')}
              </Text>
              <Touchable onPress={this.onRegister}>
                <Text style={loginStyle.registerText}> {I18n.t('page_register_btn_register')}</Text>
              </Touchable>
            </View>
          </View>
          <View style={loginStyle.backPersonalBox}>
            <View style={loginStyle.backPersonal}>
              <Touchable onPress={this.onBackPersonal} style={loginStyle.backPersonalItem}>
                <Image source={resIcon.loginChange} />
                <Text style={loginStyle.backPersonalText}>
                  {I18n.t('page_login_text_back_personal')}
                </Text>
              </Touchable>
            </View>
          </View>
        </KeyboardAwareScrollView>
        <CountryCode isOpen={isOpenSelectedCountryCode} onSelected={this.onSelectedCountryCode} />
        <InputImageCaptchaModal ref={(ref) => (this.inputImageCaptchaModal = ref)} isEnterprise />
      </View>
    );
  }
}
