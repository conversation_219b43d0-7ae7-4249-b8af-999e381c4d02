import launch from './launch';
// import home from './home/<USER>';
import resume from './resume/resume';
import chat from './chat/chat';
import job from './job/job';
import servicePackage from './servicePackage/servicePackage';
import recordDetail from './servicePackage/recordDetail';
import login from './login/login';
import setting from './setting/setting';
import company from './company/company';
import companyCert from './company/companyCert';
import companyInfo from './company/companyInfo';
import buyList from './company/buyList';
import rechargeList from './company/rechargeList';
import rechargeDetail from './company/rechargeDetail';
import companyRecharge from './recharge/recharge';
import rechargeMethod from './recharge/rechargeMethod';
import rechargeOrderPay from './recharge/rechargeOrderPay';
import interviewManagement from './interviewManagement/interviewManagement';
import resumeDetail from './resume/resumeDetail';
import resumeSearch from './resume/resumeSearch';
import resumeCollection from './resume/resumeCollection';

import jobAdd from './job/jobAdd';
import jobEdit from './job/jobEdit';
import jobDetail from './job/jobDetail';
import jobContact from './job/jobContact';
import jobAddress from './job/jobAddress';
import jobDesc from './job/jobDesc';
import jobRequirements from './job/jobRequirements';
import jobLanguage from './job/jobLanguage';
import jobCategory from './job/JobCategory';
import jobAddressAdd from './job/jobAddressAdd';
import jobContactAdd from './job/jobContactAdd';
import jobCity from './job/jobCity';
import jobCityChildren from './job/jobCityChildren';
import jobReqEdit from './job/jobReqEdit';
import modifyPwd from './setting/modifyPwd';
import tradeRecord from './home/<USER>';
import tradeFilter from './home/<USER>';
import rechargeFilter from './company/rechargeFilter';
import resumeFilter from './resume/resumeFilter';
import updateLanguage from './setting/updateLanguage';
import rechargeSubmit from './recharge/rechargeSubmit';
import rechargeSuccess from './recharge/rechargeSuccess';
import changeAccount from './home/<USER>';
import messageList from './home/<USER>';

export default {
  launch,
  epChat: chat,
  // home,
  resume,
  epJob: job,
  servicePackage,
  recordDetail,
  epLogin: login,
  setting,
  company,
  companyInfo,
  companyCert,
  buyList,
  rechargeList,
  rechargeDetail,
  companyRecharge,
  rechargeMethod,
  rechargeOrderPay,
  interviewManagement,
  resumeDetail,
  resumeSearch,
  jobAdd,
  jobEdit,
  epjobDetail: jobDetail,
  jobContact,
  jobAddress,
  jobDesc,
  jobRequirements,
  jobLanguage,
  jobCategory,
  jobAddressAdd,
  jobContactAdd,
  jobCity,
  jobCityChildren,
  jobReqEdit,
  modifyPwd,
  resumeCollection,
  tradeRecord,
  tradeFilter,
  rechargeFilter,
  resumeFilter,
  updateLanguage,
  rechargeSubmit,
  rechargeSuccess,
  changeAccount,
  messageList,
};
