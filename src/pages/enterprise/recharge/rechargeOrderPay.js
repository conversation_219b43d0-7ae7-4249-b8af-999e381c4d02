import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Button, View, Header, Image, Touchable, BaseComponent } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
import { computed } from 'mobx';
import util from '../../../util';

function getComponentStyle(theme) {
  return {
    container: {
      marginHorizontal: 12,
      marginTop: 20,
      borderRadius: 10,
    },
    headerContainer: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 10,
      marginBottom: 10,
      overflow: 'hidden',
      paddingBottom: 24,
    },
    headerContainerActive: {
      borderColor: theme.primaryColor,
      borderWidth: 1,
    },
    headerBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: 60,
      backgroundColor: '#FFF5F5',
    },
    headerText: {
      fontSize: 18,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    itemContainerBox: {
      borderRadius: 10,
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 24,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 14,
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    sparateLine: {
      width: '100%',
      borderWidth: 1,
      borderColor: '#CCCCCC',
      borderStyle: 'dashed',
      marginTop: 4,
      marginBottom: 18,
    },
    btnStyle: {
      marginHorizontal: 12,
      marginTop: 60,
    },
    headerContentBox: {
      paddingHorizontal: 14,
      marginTop: 12,
    },
    headerTipsBox: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    headerTipsText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    headerCellTitle: {
      fontSize: theme.fontSizeL,
      color: theme.primaryColor,
      marginBottom: 16,
    },
    btnNormal: {
      width: 108,
      height: 38,
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
      alignItems: 'center',
      justifyContent: 'center',
    },
    btnNormalActive: {
      backgroundColor: theme.primaryColor,
    },
    btnNormalText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
    },
    btnNormalTextActiveText: {
      color: '#fff',
    },
    btnGroup: {
      flexDirection: 'row',
      alignItems: 'center',
      // justifyContent: 'center',
    },
  };
}

/**
 * 充值订单支付
 */
@inject('companyAction')
@observer
export default class RechargeOrderPay extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const data = props.navigation.state.params || {};
    console.log('data', data);
    this.selected = data?.selected;
    this.state = {
      invoiceOpen: 1,
      invoiceValue: 0,
    };
  }

  componentDidMount() {
    this.getInvoice();
  }

  getInvoice = async () => {
    const { companyAction } = this.props;
    const res = await companyAction.queryInvoiceChargeRate();
    let invoiceValue = 0;
    if (Object.prototype.toString.call(res) === '[object Object]') {
      invoiceValue = res?.data || 0;
    } else {
      invoiceValue = res;
    }
    this.setState({
      invoiceValue,
    });
  };

  @computed get finalPaymentAmount() {
    const { invoiceOpen, invoiceValue } = this.state;
    const { selected } = this;
    let amount = 0;
    if (invoiceOpen == 1) {
      amount = selected?.amount * (1 + invoiceValue);
    } else {
      amount = selected?.amount;
    }
    amount = amount.toFixed(2);
    if (amount.toString().split('.')[1] == '00') {
      amount = parseInt(amount);
    }
    return amount;
  }

  onSubmit = async () => {
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.createRecharge({
        amount: this.selected?.amount,
        invoice: this.state.invoiceOpen == 1,
      });
      if (res?.successful) {
        this.showRequestResult(I18n.t('page_recharge_text_create_order_success'));
        const data = this.props.navigation.state.params || {};
        NavigationService.replace('rechargeMethod', {
          ...data,
          invoice: this.state.invoiceOpen == 1,
          orderId: res.orderId,
          amount: this.finalPaymentAmount,
        });
      }
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onCheck = (value) => {
    this.setState({
      invoiceOpen: value,
    });
  };

  renderItem = ({ label, value }) => {
    const { style } = this;
    return (
      <View style={style.itemContainer}>
        <Text style={style.labelText}>{label}</Text>
        <Text style={style.valueText} textType="amount">
          {value}
        </Text>
      </View>
    );
  };

  render() {
    const { style, selected } = this;
    const { invoiceOpen, invoiceValue } = this.state;
    const Item = this.renderItem;
    return (
      <>
        <Header theme="dark" title={I18n.t('page_recharge_text_order_pay')} />

        <View style={style.container}>
          <View style={[style.headerContainer, !invoiceOpen ? style.headerContainerActive : {}]}>
            <View style={style.headerBox}>
              <Text style={style.headerText}>{I18n.t('page_recharge_text_confirm_order')}</Text>
            </View>
            <View style={style.headerContentBox}>
              <View style={style.headerTipsBox}>
                <Image source={resIcon.jobAlertEnterprise} />
                <Text style={style.headerTipsText}>
                  {' '}
                  {I18n.t('page_recharge_text_invoice_tips', {
                    invoice: `${invoiceValue * 100}`,
                  })}{' '}
                </Text>
              </View>
              <Text style={style.headerCellTitle}>{I18n.t('page_recharge_text_invoice')}</Text>
              <View style={style.btnGroup}>
                <Touchable
                  style={[
                    style.btnNormal,
                    invoiceOpen == 1 ? style.btnNormalActive : {},
                    { marginRight: 20 },
                  ]}
                  onPress={() => this.onCheck(1)}
                >
                  <Text
                    style={[
                      style.btnNormalText,
                      invoiceOpen == 1 ? style.btnNormalTextActiveText : {},
                    ]}
                  >
                    {I18n.t('page_recharge_text_yes')}
                  </Text>
                </Touchable>
                {/* <Touchable
                  style={[style.btnNormal, invoiceOpen == 2 ? style.btnNormalActive : {}]}
                  onPress={() => this.onCheck(2)}
                >
                  <Text
                    style={[
                      style.btnNormalText,
                      invoiceOpen == 2 ? style.btnNormalTextActiveText : {},
                    ]}
                  >
                    {I18n.t('page_recharge_text_no')}
                  </Text>
                </Touchable> */}
              </View>
            </View>
          </View>
          <View style={style.itemContainerBox}>
            <Item
              label={I18n.t('page_recharge_text_recharge_amount')}
              value={I18n.t('page_recharge_text_number_of_coin', {
                count: util.formatAmount(selected?.coin),
              })}
            />
            <Item
              label={I18n.t('page_recharge_text_order_pay_amount')}
              value={`$ ${util.formatAmount(selected?.amount)}`}
            />
            <Item
              label={I18n.t('page_recharge_text_service_fee')}
              value={`$ ${
                invoiceOpen == 0
                  ? '-'
                  : invoiceOpen == 2
                  ? 0
                  : util.formatAmount(selected?.amount * parseFloat(invoiceValue))
              }`}
            />
            <View style={style.sparateLine} />
            <Item
              label={I18n.t('page_recharge_text_actual_pay_amount')}
              value={`$ ${util.formatAmount(this.finalPaymentAmount)}`}
            />

            <Button
              title={I18n.t('page_recharge_text_order_submit')}
              btnType="login"
              onPress={this.onSubmit}
              style={style.btnStyle}
              disabled={invoiceOpen == 0}
            />
          </View>
        </View>
      </>
    );
  }
}
