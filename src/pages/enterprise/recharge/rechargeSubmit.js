import React, { Component } from 'react';
import moment from 'moment';
import { inject, observer } from 'mobx-react';
import {
  Text,
  View,
  ScrollView,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er,
  <PERSON>ton,
  ActivityIndicator,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';

/**
 * 充值提交成功
 * <AUTHOR>
 */
@inject('companyAction')
@observer
export default class RechargeSubmit extends Component {
  static navigationOptions = () => ({
    gestureEnabled: false,
  });

  constructor(props) {
    super(props);
    this.style = styles.get(['paySuccess']).paySuccessStyle;
    this.orderId = this.props.navigation.getParam('orderId', 0);
    this.state = {
      statusValue: 0,
    };
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.queryOrderDetail();
    this.startTimer();
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
    this.stopTimer();
  }

  onBackButtonPressAndroid = () => true;

  startTimer = () => {
    this.interval = setInterval(() => {
      const { statusValue } = this.state;
      if (statusValue != 1) {
        this.queryOrderDetail();
      } else {
        this.stopTimer();
      }
    }, 1000);
  };

  stopTimer = () => {
    if (this.interval) {
      clearInterval(this.interval);
    }
  };

  queryOrderDetail = async () => {
    try {
      const orderId = this.orderId;
      if (!orderId) {
        return;
      }
      const res = await this.props.companyAction.queryRechargeStatus(orderId);
      this.setState({ statusValue: res });
      if (res == 1) {
        this.stopTimer();
        toast.show(I18n.t('page_recharge_text_recharge_success'));
        setTimeout(() => {
          NavigationService.replace('rechargeSuccess', {
            orderId: this.orderId,
          });
        }, 300);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  onViewOrder = () => {
    NavigationService.resetMultiple([
      { routeName: 'main', params: null },
      { routeName: 'cleaningOrderList', params: {} },
    ]);
    this.props.pageAction.selectedTab('Mine');
  };

  onBackHome = () => {
    NavigationService.reset('main');
  };

  render() {
    const { style } = this;
    return (
      <View style={style.pagerContainer}>
        <Header hideBack />
        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <View style={style.contentContainer}>
            <View style={style.topContainer}>
              <ActivityIndicator animating color="#FA3F43" size="large" />
              <Text style={[style.pendingText]}>{I18n.t('page_recharge_text_submit_success')}</Text>
              <Text style={[style.subText]}>
                {I18n.t('page_recharge_text_submit_success_tips')}
              </Text>
              <Text style={[style.subText]}>{I18n.t('page_recharge_text_not_wait')}</Text>
            </View>
          </View>
          <View style={[style.btnBoxContainer, { marginTop: 70 }]}>
            <Button
              onPress={this.onBackHome}
              title={I18n.t('page_recharge_text_back_home')}
              btnSize="ml"
              btnType="login"
              style={style.btnBox}
            />
          </View>
        </ScrollView>
      </View>
    );
  }
}
