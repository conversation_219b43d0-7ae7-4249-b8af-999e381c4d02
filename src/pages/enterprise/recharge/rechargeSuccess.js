import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, ScrollView, Image, BackHandler, Header, But<PERSON> } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';

/**
 * 充值成功
 * <AUTHOR>
 */
@inject('companyAction')
@observer
export default class RechargeSuccess extends Component {
  static navigationOptions = () => ({
    gestureEnabled: false,
  });

  constructor(props) {
    super(props);
    this.style = styles.get(['paySuccess']).paySuccessStyle;
    this.state = {};
  }

  componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressAndroid);
  }

  onBackButtonPressAndroid = () => true;

  onViewOrder = () => {
    const routes = [
      { routeName: 'main' },
      {
        routeName: 'rechargeList',
        params: { type: 0 },
      },
    ];
    NavigationService.resetMultiple(routes);
  };

  onBackHome = () => {
    NavigationService.reset('main');
  };

  render() {
    const { style } = this;

    return (
      <View style={style.pagerContainer}>
        <Header hideBack />
        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <View style={style.contentContainer}>
            <View style={style.topContainer}>
              <Image source={resIcon.iconSubmitSuccessEnterprise} />
              <Text style={[style.titleText]}>{I18n.t('page_recharge_text_recharge_success')}</Text>
            </View>
          </View>
          <View style={style.btnBoxContainer}>
            <Button
              onPress={this.onViewOrder}
              title={I18n.t('page_recharge_text_view_recharge_record')}
              btnSize="s44"
              btnType="viewRecord"
              style={[style.btnBox, { marginBottom: 16 }]}
            />
            <Button
              onPress={this.onBackHome}
              title={I18n.t('page_recharge_text_back_home')}
              btnSize="s44"
              btnType="login"
              style={style.btnBox}
            />
          </View>
        </ScrollView>
      </View>
    );
  }
}
