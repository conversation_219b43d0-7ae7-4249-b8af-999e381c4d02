import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Button, Touchable, Text, Image, BaseComponent } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import util from '../../../util';
import payManager from '../../../util/payManager';
import configs from '../../../configs';
import constant from '../../../store/constant';
import promiseUtil from '../../../util/promiseUtil';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    mainBox: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 16,
      margin: 12,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
    },
    amountTitle: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    amountUnit: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      lineHeight: 42,
    },
    amountText: {
      fontSize: 48,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginLeft: 4,
    },
    amountBox: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      marginBottom: 8,
    },
    sparateLine: {
      width: '100%',
      borderWidth: 1,
      borderColor: '#CCCCCC',
      borderStyle: 'dashed',
      marginTop: 4,
      marginBottom: 18,
    },
    goodsInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    },
    infoText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    selectMethodText: {
      marginHorizontal: 12,
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      marginBottom: 8,
    },
    methodBox: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      marginHorizontal: 12,
      borderRadius: 10,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: 60,
      borderBottomWidth: 1,
      borderBottomColor: '#F2F2F2',
    },
    itemFront: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemImg: {
      width: 28,
      height: 28,
      borderRadius: 4,
    },
    itemText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      marginLeft: 12,
    },
    circleImg: {
      width: 24,
      height: 24,
    },
    circle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: '#CCCCCC',
    },
    btnStyle: {
      marginHorizontal: 38,
      marginTop: 28,
    },
  };
}

/**
 * 充值方式
 */
@inject('companyAction', 'companyStore')
@observer
export default class RechargeMethod extends BaseComponent {
  constructor(props) {
    super(props);
    const data = props.navigation.state.params || {};
    console.log('data', data);
    this.selected = data?.selected;
    this.amount = data?.amount;
    this.orderId = data?.orderId;
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      methods: [],
    };
  }

  componentDidMount() {
    this.queryMethods();
    global.emitter.on(constant.event.rechargeSuccess, this.onRechargeSuccess);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.rechargeSuccess, this.onRechargeSuccess);
  }

  onRechargeSuccess = async (res) => {
    if (res?.data?.status == 'ok') {
      await this.props.companyAction.getBalance();
      const data = this.props.navigation.state.params || {};
      const routes = [
        { routeName: 'main' },
        { routeName: 'company' },
        // {
        //   routeName: 'rechargeList',
        //   params: { type: 0 },
        // },
      ];
      if (data?.home) {
        routes.splice(1, 1);
      }
      toast.show(I18n.t('page_recharge_text_recharge_submit_success'));
      if (data?.payCallback) {
        data.payCallback();
        NavigationService.goBack();
        return;
      }
      // NavigationService.resetMultiple(routes);
      await promiseUtil.sleep(500);
      NavigationService.replace('rechargeSubmit', {
        orderId: this.orderId,
      });
    } else if (res?.data?.status == 'cancel') {
      toast.show(I18n.t('page_servicePackage_text_pay_cancel'));
    } else {
      toast.show(I18n.t('page_servicePackage_text_pay_fail'));
    }
  };

  queryMethods = async () => {
    try {
      this.showGlobalLoading();
      const { companyAction } = this.props;
      const res = await companyAction.queryPaymentGateway();
      this.showRequestResult();
      if (res) {
        this.setState({
          methods: res
            ?.sort((a, b) => b?.paymentMethod?.value - a?.paymentMethod?.value)
            ?.filter((k) => k.paymentMethod?.value == 11 || k.paymentMethod?.value == 10)
            ?.map((x) => {
              x.checked = x.paymentMethod?.value == 11;
              x.name = x.paymentMethod?.label;
              if (x.paymentMethod?.value == 10) {
                x.name = 'ABA KHQR';
              }

              switch (x.paymentMethod?.value) {
                case 4:
                  x.icon = resIcon.iconAlipayEnterprise;
                  break;
                case 7:
                  x.icon = resIcon.iconPayPIPAYEnterprise;
                  break;
                case 11:
                  x.icon = resIcon.iconLongpayEnterprise;
                  break;
                case 10:
                  x.icon = resIcon.iconPayKHQREnterprise;
                  break;

                default:
                  break;
              }
              return x;
            }),
        });
      }
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onItemPress = (item) => {
    const { methods } = this.state;
    const newMethods = methods.map((m) => {
      if (m.id === item.id) {
        m.checked = true;
      } else {
        m.checked = false;
      }
      return m;
    });
    this.setState({
      methods: newMethods,
    });
  };

  onSubmit = async () => {
    try {
      const { methods } = this.state;
      const paymentMethod = methods.find((x) => x.checked)?.paymentMethod?.value;
      this.showGlobalLoading();
      const { payRecharge, paywayPrepay } = this.props.companyAction;
      const { companyInfo } = this.props.companyStore;
      if (paymentMethod == 11) {
        const res = await payRecharge(this.orderId, {
          paymentMethod,
        });
        if (!res?.successful) {
          this.showRequestResult(res?.message);
          return;
        }
        this.showRequestResult();
        const data = res?.data || {};
        data.result.callbackUrl = `${configs.appUrl}?deepLinkType=3`;
        data.result.remark = I18n.t('page_recharge_text_company_recharge');
        await payManager.onLongPay(data);
      } else if (paymentMethod == 10) {
        const res = await paywayPrepay(this.orderId, {
          paymentMethod,
          ext:
            (companyInfo?.email || '') +
            (companyInfo?.mobile || '') +
            'abapay_khqr_deeplink' +
            configs.appUrl,
          subPaymentMethod: 'abapay_khqr_deeplink',
        });

        const data = res?.data || {};
        await payManager.onABAPay({
          prePayData: { ...data, phone: companyInfo?.mobile, email: companyInfo?.email },
          route: 'rechargeSubmit',
          routeParam: { orderId: this.orderId },
          callback: () => this.showRequestResult(),
        });
      } else {
        this.showRequestResult(I18n.t('in_developing'));
      }
    } catch (error) {
      console.warn('error', error.message);
      this.showRequestResult(error?.message);
    }
  };

  render() {
    const { style, selected } = this;
    const { methods } = this.state;

    return (
      <View style={style.container}>
        <Header theme="dark" title={I18n.t('page_recharge_text_checkout_counter')} />
        <View style={style.mainBox}>
          <Text style={style.amountTitle}>{I18n.t('page_recharge_text_pay_amount')}</Text>
          <View style={style.amountBox}>
            <Text style={style.amountUnit} textType="amount">
              $
            </Text>
            <Text style={style.amountText} textType="amount">
              {util.formatAmount(this.amount)}
            </Text>
          </View>
          <View style={style.sparateLine} />
          <View style={style.goodsInfo}>
            <Text style={style.infoText}>{I18n.t('page_recharge_text_product_name')}：</Text>
            <Text style={style.infoText} textType="amount">
              {I18n.t('page_recharge_title')}{' '}
              {I18n.t('page_recharge_text_number_of_coin', { count: selected?.coin })}
            </Text>
          </View>
        </View>
        <Text style={style.selectMethodText}>{I18n.t('page_recharge_text_select_pay_method')}</Text>
        <View style={style.methodBox}>
          {methods.map((item, index) => (
            <Touchable
              style={[
                style.itemContainer,
                index !== methods.length - 1 ? {} : { borderBottomWidth: 0 },
              ]}
              key={index}
              onPress={() => this.onItemPress(item)}
            >
              <View style={style.itemFront}>
                <Image source={item.icon} style={style.itemImg} />
                <Text style={style.itemText}>{item.name}</Text>
              </View>
              {item.checked ? (
                <Image source={resIcon.iconMethodCheckedkEnterprise} style={style.circleImg} />
              ) : (
                <View style={style.circle} />
              )}
            </Touchable>
          ))}
        </View>
        <Button
          title={I18n.t('page_recharge_text_pay_now')}
          btnType="login"
          onPress={this.onSubmit}
          style={style.btnStyle}
        />
      </View>
    );
  }
}
