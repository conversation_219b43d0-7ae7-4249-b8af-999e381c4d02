import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Image,
  Text,
  Touchable,
  View,
  Header,
  Button,
  KeyboardAwareScrollView,
  Input,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import { deviceWidth } from '../../../common';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import util from '../../../util';

function getComponentStyle(theme) {
  return {
    headerText: {
      fontSize: 14,
      color: theme.titleFontColor,
      paddingHorizontal: 21,
      marginVertical: 12,
    },
    container: {
      marginHorizontal: 21,
    },
    itemContainerBox: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    itemContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12,
      backgroundColor: theme.primaryBgColor,
      width: (deviceWidth - 21 * 2 - 12) / 2,
      minHeight: 100,
      borderRadius: 10,
      position: 'relative',
    },
    labelText: {
      fontSize: theme.fontSizeXVII,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      marginBottom: 8,
    },
    valueText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    valueDeprecatedText: {
      fontSize: theme.fontSizeM,
      color: '#cccccc',
      textDecorationLine: 'line-through',
    },
    subValueText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      marginLeft: 12,
    },
    subBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkImg: {
      position: 'absolute',
      bottom: -2,
      right: -2,
    },
    sectionContent: {
      backgroundColor: '#fff',
      paddingHorizontal: 16,
      paddingVertical: 16,
      marginHorizontal: 21,
      borderRadius: 10,
      position: 'relative',
    },
    commonInputContainer: {
      borderBottomColor: 'transparent',
      backgroundColor: '#F7F7F7',
      borderRadius: 5,
      paddingLeft: 9,
    },
    inputStyle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeXXL,
      fontWeight: theme.fontWeightBold,
      textAlign: 'center',
    },
    unitStyle: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginRight: 12,
    },
    saleText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      textAlign: 'center',
      marginTop: 13,
    },
  };
}

/**
 * 充值
 */
@inject('companyAction')
@observer
export default class Recharge extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.state = {
      list: [],
      current: null,
      inputAmount: '',
      useInputAmount: false,
    };
  }

  get isAmountAvailable() {
    const { inputAmount, current } = this.state;
    if (this.state.useInputAmount) {
      return inputAmount != 0;
    }
    return !!current;
  }

  componentDidMount() {
    this.getRechargeBags();
  }

  getRechargeBags = async () => {
    try {
      this.showGlobalLoading();
      const { companyAction } = this.props;
      const res = await companyAction.queryRechargeBags();
      this.showRequestResult();
      this.setState({
        list: res || [],
        current: res?.length ? res[0] : null,
        useInputAmount: !res?.length,
      });
    } catch (e) {
      this.showRequestResult(e?.message);
    }
  };

  onItemPress = (item) => {
    this.setState({
      current: item,
      useInputAmount: false,
    });
  };

  onSubmit = () => {
    const { inputAmount, useInputAmount } = this.state;
    if (useInputAmount && !inputAmount) {
      return toast.show(I18n.t('page_recharge_tips_input_amount'));
    }
    const { current } = this.state;
    let selected = current || {};
    if (useInputAmount) {
      selected.coin = parseInt(inputAmount);
      selected.amount = parseInt(inputAmount);
    }
    const data = this.props.navigation.state.params || {};
    NavigationService.navigate('rechargeOrderPay', { selected, ...data });
  };

  onChangeText = (text) => {
    this.setState({
      inputAmount: text,
    });
  };

  onSelectInput = () => {
    this.setState({
      current: null,
      useInputAmount: true,
    });
  };

  renderItem = ({ label, value, subValue, customContainer = {}, showCheck }) => {
    const { style } = this;
    return (
      <View
        style={[
          style.itemContainer,
          showCheck ? { borderWidth: 2, borderColor: '#EF3D48' } : {},
          customContainer,
        ]}
      >
        <Text style={style.labelText} textType="amount">
          {label}
        </Text>
        <View style={style.subBox}>
          <Text style={subValue ? style.valueDeprecatedText : style.valueText} textType="amount">
            $ {value}
          </Text>
          {subValue ? (
            <Text style={style.subValueText} textType="amount">
              $ {subValue}
            </Text>
          ) : null}
        </View>
        {showCheck ? (
          <Image source={resIcon.iconAmountCheckEnterprise} style={style.checkImg} />
        ) : null}
      </View>
    );
  };

  rightComponent = () => {
    return (
      <Touchable onPress={() => NavigationService.navigate('rechargeList')}>
        <Image source={resIcon.iconRecordEnterprise} />
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    const { list, current, inputAmount, useInputAmount } = this.state;
    const Item = this.renderItem;
    return (
      <>
        <Header
          theme="dark"
          title={I18n.t('page_recharge_title')}
          rightComponent={this.rightComponent}
        />
        <KeyboardAwareScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {list?.length ? (
            <Text style={style.headerText}>{I18n.t('page_recharge_text_select_amount')}</Text>
          ) : null}
          <View style={style.container}>
            <View style={style.itemContainerBox}>
              {list?.map((x, index) => (
                <Touchable onPress={() => this.onItemPress(x)} key={index}>
                  <Item
                    label={x.coin}
                    value={x.coin}
                    subValue={x.amount != x.coin ? x.amount : null}
                    customContainer={index % 2 == 0 ? { marginRight: 12 } : {}}
                    showCheck={x.coin === current?.coin}
                  />
                </Touchable>
              ))}
            </View>
          </View>
          <Text style={style.headerText}>{I18n.t('page_recharge_text_other_amount')}</Text>
          <Touchable
            style={[
              style.sectionContent,
              useInputAmount ? { borderColor: '#EF3D48', borderWidth: 1 } : {},
            ]}
            onPress={this.onSelectInput}
          >
            <Input
              inputContainerStyle={style.commonInputContainer}
              inputStyle={[style.inputStyle, { paddingLeft: 0 }]}
              placeholder={'0'}
              placeholderTextColor="#999999"
              onChangeText={this.onChangeText}
              onFocus={this.onSelectInput}
              selectionColor="#EF3D48"
              rightIcon={() => (
                <Text style={style.unitStyle}>
                  {I18n.t('page_recharge_text_coin', { count: parseInt(this.state.inputAmount) })}
                </Text>
              )}
              onClear={this.onClear}
              keyboardType="number-pad"
              inputType="amount"
              returnKeyType="done"
            />
            <Text style={style.saleText} textType="amount">
              {I18n.t('page_recharge_text_price')}：${util.formatAmount(inputAmount) || '-'}
            </Text>

            {useInputAmount ? (
              <Image
                source={resIcon.iconAmountCheckEnterprise}
                style={[style.checkImg, { right: -1, bottom: -1 }]}
              />
            ) : null}
          </Touchable>
          <Button
            title={I18n.t('page_recharge_text_recharge_now')}
            btnType="login"
            onPress={this.onSubmit}
            style={{ marginTop: 18, marginHorizontal: 38 }}
            disabled={!this.isAmountAvailable}
          />
        </KeyboardAwareScrollView>
      </>
    );
  }
}
