import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, <PERSON>er, Button, ScrollView, Alert, BaseComponent } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import util from '../../../util';
import moment from 'moment';
import { hasEar } from '../../../common';
import NavigationService from '../../../navigationService';
import { computed } from 'mobx';

function getComponentStyle(theme) {
  return {
    container: {
      backgroundColor: theme.primaryBgColor,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 10,
    },
    headerBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: 60,
      backgroundColor: '#FFF5F5',
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
    },
    headerText: {
      fontSize: 18,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    itemContainerBox: {
      paddingHorizontal: 14,
      marginTop: 20,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 14,
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 0,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 1,
      marginLeft: 20,
      textAlign: 'right',
    },
    btnGroup: {
      marginHorizontal: 38,
      marginBottom: hasEar ? 44 : 24,
    },
  };
}

/**
 * 充值详情
 */
@inject('companyAction')
@observer
export default class RechargeDetail extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.item = props.navigation.state.params?.item || {};
    this.state = {
      item: this.item,
    };
  }

  componentDidMount() {}

  @computed get showBtnGroup() {
    return this.state.item?.status?.value == 0;
  }

  onCancel = async () => {
    Alert.alert(
      I18n.t('op_remind_title'),
      I18n.t('page_recharge_text_cancel_order'),
      [
        { text: I18n.t('op_cancel_title'), onPress: () => {}, style: 'cancel' },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.showGlobalLoading();
              const { companyAction } = this.props;
              const res = await companyAction.cancelRecharge(this.item.id);
              this.showRequestResult();
              if (res?.successful) {
                const data = this.props.navigation.state.params;
                data?.cancelCallback?.();
                this.setState({
                  item: {
                    ...this.item,
                    status: {
                      ...this.item.status,
                      value: 8,
                      label: I18n.t('page_recharge_text_order_status_canceld'),
                    },
                  },
                });
              }
            } catch (error) {
              this.showRequestResult(error?.message);
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  onPay = () => {
    const { item } = this;
    NavigationService.navigate('rechargeMethod', {
      orderId: item.id,
      amount: item.cashrep,
      selected: {
        coin: item.cash,
      },
      // payCallback: this.onRefresh,
    });
  };

  renderItem = ({ label, value }) => {
    const { style } = this;
    if (!value) {
      return null;
    }
    return (
      <View style={style.itemContainer}>
        <Text style={style.labelText}>{label}</Text>
        <Text style={style.valueText} textType="amount">
          {value}
        </Text>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { item } = this.state;
    const Item = this.renderItem;
    return (
      <>
        <Header theme="dark" title={I18n.t('page_dynamic_header_detail_title')} />

        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          <View style={style.container}>
            {/* <View style={style.headerBox}>
            <Text style={style.headerText}>订单详情</Text>
          </View> */}
            <View style={style.itemContainerBox}>
              {/* <Item label="充值网币数：" value={`${util.formatAmount(item.cash)} 币`} /> */}
              <Item
                label={I18n.t('page_recharge_text_recharge_amount')}
                value={I18n.t('page_recharge_text_number_of_coin', {
                  count: util.formatAmount(item.cash),
                })}
              />
              <Item
                label={I18n.t('page_recharge_text_real_pay_amount')}
                value={`$ ${util.formatAmount(item.cashrep)}`}
              />
              <Item
                label={I18n.t('page_recharge_text_recharge_time')}
                value={moment(item?.cdate_unixtime * 1000).format('YYYY/MM/DD HH:mm')}
              />
              <Item label={I18n.t('page_recharge_text_order_status')} value={item?.status?.label} />
              <Item label={I18n.t('page_recharge_text_payment_method')} value={item?.type?.label} />
              <Item label={I18n.t('page_recharge_text_order_number')} value={item?.id} />
              <Item label={I18n.t('page_recharge_text_VAT')} value={util.formatAmount(item.vat)} />
              <Item
                label={I18n.t('page_recharge_text_invoice_status')}
                value={item?.invoiceStatus?.label}
              />
            </View>
          </View>
        </ScrollView>
        {this.showBtnGroup ? (
          <View style={style.btnGroup}>
            <Button
              title={I18n.t('page_recharge_text_pay_now')}
              btnType="login"
              btnSize="s44"
              style={{ marginBottom: 18 }}
              onPress={this.onPay}
            />
            <Button
              title={I18n.t('page_recharge_btn_cancel_order')}
              outline
              btnType="reset"
              onPress={this.onCancel}
            />
          </View>
        ) : null}
      </>
    );
  }
}
