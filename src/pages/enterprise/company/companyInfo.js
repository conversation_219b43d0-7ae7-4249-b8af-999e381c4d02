import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  Image,
  Icon,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import RightArrow from '../../../components/rightArrow';
import res from '../../../res';
import NavigationService from '../../../navigationService';
import PickerModal from './components/pickerModal';
import UploadImage from './components/uploadImage';
import UploadLogo from './components/uploadLogo';
import BenefitTagModal from './components/benefitTagModal';
import { hasEar, deviceWidth } from '../../../common';

function getComponentStyle(theme) {
  const imageW = (deviceWidth - 18 * 2 - 12 * 3) / 3 - 1;
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
      paddingBottom: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemNoLineContainer: {
      marginBottom: 18,
      paddingBottom: 15,
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueLogoBox: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#F7F7F7',
    },
    labelTextBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 10,
      backgroundColor: '#FAECEC',
      marginRight: 8,
      marginBottom: 8,
      borderRadius: 5,
    },
    labelTextTag: {
      color: '#EF3D48',
      fontSize: 13,
      marginRight: 4,
      paddingLeft: 22,
    },
    deleteColor: '#EF3D48',
    deleteIconStyle: {
      paddingLeft: 4,
      paddingRight: 22,
      paddingVertical: 2,
    },
    labelTextAddBox: {
      backgroundColor: '#F6F6F6',
      borderColor: '#99A3BA',
      borderWidth: 1,
      borderStyle: 'dashed',
    },
    labelTextAddTag: {
      color: theme.devicesSeparatorColor,
      paddingHorizontal: 22,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 38,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
    bottomItem: {
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 54,
      marginLeft: 13,
    },
    bottomItemImg: {
      width: 20,
      height: 20,
    },
    bottomItemText: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
      marginTop: 4,
    },
    itemImageHeaderContainer: {
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    subLabelText: {
      marginTop: 8,
      marginBottom: 5,
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      lineHeight: 17,
    },
    imagesBox: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    imageBox: {
      height: imageW,
      width: imageW,
      backgroundColor: '#F7F7F7',
      marginRight: 12,
      marginBottom: 8,
      overflow: 'hidden', // 超出部分剪切
    },
    imageBoxDeleteBtn: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    imageW: imageW - 12,
    imageStyle: { width: imageW, height: imageW, resizeMode: 'cover' },
  };
}

/**
 * 企业主页信息
 */
@inject('companyStore', 'companyAction')
@observer
export default class CompanyInfo extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { companyInfo } = this.props.companyStore;
    const companyInfoCopy = { ...companyInfo };
    this.companyInfo = companyInfoCopy;
    this.state = {
      enableAutomaticScroll: true,
      form: {
        employerId: companyInfoCopy?.employerId || '',
        company: companyInfoCopy?.company || '',
        logo: companyInfoCopy?.logo || '',
        industrialId: companyInfoCopy?.industrialId?.value || '', // 公司行业
        industrialIdLabel: companyInfoCopy?.industrialId?.label || '', // 公司行业
        scaleId: companyInfoCopy?.scaleId?.value || '', // 公司人数
        scaleIdLabel: companyInfoCopy?.scaleId?.label || '', // 公司人数
        companyType: companyInfoCopy?.companyType?.value || '', // 公司性质
        companyTypeLabel: companyInfoCopy?.companyType?.label || '', // 公司性质
        webUrl: companyInfoCopy?.webUrl || '', // 公司网站
        welfare: companyInfoCopy?.welfare || [], // 公司福利
        locationId: companyInfoCopy?.locationId?.value || '', // 公司区域
        locationIdLabel: companyInfoCopy?.locationId?.label || '', // 公司区域
        address: companyInfoCopy?.address || '', // 公司地址
        images: companyInfoCopy?.images || [], // 公司图片
        description: companyInfoCopy?.description || '', // 公司介绍
      },
    };
  }

  onSave = async () => {
    const { form } = this.state;
    if (!form.industrialId) return toast.show(I18n.t('page_companyInfo_op_company_industry'));
    if (!form.scaleId) return toast.show(I18n.t('page_companyInfo_op_company_number_people'));
    if (!form.companyType) return toast.show(I18n.t('page_companyInfo_op_company_type'));
    if (!form.locationId) return toast.show(I18n.t('page_companyInfo_op_company_area'));
    if (!form.address.trim()) return toast.show(I18n.t('page_companyInfo_op_company_address'));
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.editEmployer(form);
      if (res.message) this.showRequestResult(res.message);
      if (res.status === 1) {
        const onComplete = this.props.navigation.getParam('onComplete');
        if (onComplete) onComplete();
        NavigationService.goBack();
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onCloseBenefitTag = () => {
    this.setState({ enableAutomaticScroll: true });
  };

  addOrEditBenefitTag = (tag, isEdit, editIndex) => {
    if (!tag) return;
    const { form } = this.state;
    // const isStringInArray = form?.welfare?.includes(tag);
    // if (isStringInArray) return;
    isEdit ? (form.welfare[editIndex] = tag) : (form.welfare = [...form?.welfare, tag]);
    this.setState({ form });
  };

  onPickerConfirm = (pickerType, value) => {
    const pickerTypeLabel = `${pickerType}Label`;
    const { form } = this.state;
    form[pickerType] = value.value;
    form[pickerTypeLabel] = value.label;
    this.setState(form);
  };

  componentDidMount() {
    this.props.companyAction.queryConstants();
    const { industrialOptions, employerScaleOptions, etypeOptions, locationOptions } =
      this.props.companyStore;
    this.defaultIndustrialValue = industrialOptions?.length > 0 ? industrialOptions[0]?.value : 0; // 公司行业
    this.defaultEmployerScaleValue =
      employerScaleOptions?.length > 0 ? employerScaleOptions[0]?.value : 0; // 公司规模
    this.defaultEtypeValue = etypeOptions?.length > 0 ? etypeOptions[0]?.value : 0; // 公司性质
    this.defaultLocationValue = locationOptions?.length > 0 ? locationOptions[0].value : 0; // 地址区域
  }

  onConfirm = (type, value) => {
    const { form } = this.state;
    form[type] = value;
    this.setState({ form });
  };

  onPreview = () => {
    const { form } = this.state;
    NavigationService.navigate('companyDetail', {
      employerId: form.employerId,
      isPreview: true,
      form,
    });
  };

  renderBaseInfo = () => {
    const { style } = this;
    const { form } = this.state;
    const {
      renderItem: Item,
      renderImageItem: ImageItem,
      renderLogoItem: LogoItem,
      renderLabelItem: LabelItem,
    } = this;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_companyInfo_label_company_name')}
          isInput
          editable={false}
          value={form['company']}
          maxLength={120}
        />
        <LogoItem label={I18n.t('page_companyInfo_label_company_logo')} />
        <Item
          label={I18n.t('page_companyInfo_label_company_industry')}
          value={form['industrialIdLabel']}
          required
          placeholder={I18n.t('page_resume_tips_select')}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_companyInfo_label_company_industry'),
              pickerType: 'industrialId',
              selected: form['industrialId'] || this.defaultIndustrialValue,
            })
          }
        />
        <Item
          label={I18n.t('page_companyInfo_label_company_number_people')}
          value={form['scaleIdLabel']}
          required
          placeholder={I18n.t('page_resume_tips_select')}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_companyInfo_label_company_number_people'),
              pickerType: 'scaleId',
              selected: form['scaleId'] || this.defaultEmployerScaleValue,
            })
          }
        />
        <Item
          label={I18n.t('page_companyInfo_label_company_type')}
          value={form['companyTypeLabel']}
          required
          placeholder={I18n.t('page_resume_tips_select')}
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_companyInfo_label_company_type'),
              pickerType: 'companyType',
              selected: form['companyType'] || this.defaultEtypeValue,
            })
          }
        />
        <Item
          label={I18n.t('page_companyInfo_label_company_website')}
          isInput
          placeholder={I18n.t('page_resume_tips_select')}
          value={form['webUrl']}
          onChangeText={(v) => this.onConfirm('webUrl', v)}
        />
        <LabelItem label={I18n.t('page_companyInfo_label_company_benefits')} />
        <Item
          label={I18n.t('page_companyInfo_label_company_area')}
          value={form['locationIdLabel']}
          placeholder={I18n.t('page_resume_tips_select')}
          required
          onPress={() =>
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_companyInfo_label_company_area'),
              pickerType: 'locationId',
              selected: form['locationId'] || this.defaultLocationValue,
            })
          }
        />
        <Item
          label={I18n.t('page_companyInfo_label_company_address')}
          isInput
          multiline
          numberOfLines={5}
          onChangeText={(v) => this.onConfirm('address', v)}
          value={form['address']}
          maxLength={100}
          required
        />
        <ImageItem
          label={I18n.t('page_companyInfo_label_company_photo')}
          subLabel={I18n.t('page_company_label_tips')}
          maxLength={5}
        />
        <Item
          label={I18n.t('page_companyInfo_label_companyr_profile')}
          isInput
          multiline
          showLine={false}
          numberOfLines={8}
          onChangeText={(v) => this.onConfirm('description', v)}
          value={form['description']}
          maxLength={200}
        />
      </View>
    );
  };

  renderLabelItem = ({ label, required = false, showLine = true }) => {
    const { style } = this;
    const { form } = this.state;
    return (
      <View style={[showLine ? style.itemContainer : style.itemNoLineContainer]}>
        <View style={style.itemImageHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
        </View>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
          {form.welfare?.map((labelText, index) => (
            <View key={labelText} style={style.labelTextBox}>
              <Touchable
                onPress={() => {
                  this.setState({ enableAutomaticScroll: false }, () =>
                    this.benefitTagModal.open(
                      I18n.t('page_companyInfo_title_company_benefits_edit'),
                      labelText,
                      true,
                      index
                    )
                  );
                }}
              >
                <Text style={style.labelTextTag}>{labelText}</Text>
              </Touchable>
              <Touchable
                onPress={() => {
                  form.welfare = form.welfare?.filter((tagText) => tagText !== labelText);
                  this.setState({ form });
                }}
              >
                <Icon
                  name="close"
                  size={12}
                  color={style.deleteColor}
                  style={style.deleteIconStyle}
                />
              </Touchable>
            </View>
          ))}
          <Touchable
            style={[style.labelTextBox, style.labelTextAddBox]}
            onPress={() => {
              this.setState({ enableAutomaticScroll: false }, () =>
                this.benefitTagModal.open(I18n.t('page_companyInfo_title_company_benefits_add'))
              );
            }}
          >
            <Text style={[style.labelTextTag, style.labelTextAddTag]}>
              {I18n.t('page_companyInfo_btn_company_benefits_add')}
            </Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderLogoItem = ({ label, required = false, showLine = true }) => {
    const { style } = this;
    const {
      form: { employerId },
      form,
    } = this.state;
    return (
      <View style={showLine ? style.itemContainer : style.itemNoLineContainer}>
        <View style={style.itemImageHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
        </View>
        <View style={style.valueLogoBox}>
          <UploadLogo
            employerId={employerId}
            imageUrl={form?.logo}
            onUploadSuccess={(imageUrl) => {
              form.logo = imageUrl;
              this.setState({ form });
            }}
          />
        </View>
      </View>
    );
  };

  renderImageItem = ({ label, subLabel, onPress, required = false, showLine = true }) => {
    const { style } = this;
    const {
      form: { images, employerId },
      form,
    } = this.state;
    return (
      <View style={showLine ? style.itemContainer : style.itemNoLineContainer}>
        <View style={style.itemImageHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
          <Text style={style.subLabelText}>{subLabel}</Text>
        </View>
        <View style={style.imagesBox}>
          {images?.map((imageUrl, index) => (
            <View key={index} style={style.imageBox}>
              <Image
                source={imageUrl ? { uri: imageUrl } : res.iconErrorData}
                defaultSource={res.iconErrorData}
                style={style.imageStyle}
              />
              <Touchable
                style={style.imageBoxDeleteBtn}
                onPress={() => {
                  form.images = form.images?.filter((itemUrl, i) => index !== i);
                  this.setState({ form });
                }}
              >
                <Icon name="delete" size={16} color={style.deleteColor} />
              </Touchable>
            </View>
          ))}
          {images?.length < 5 ? (
            <UploadImage
              imageSize={style.imageW + 12}
              employerId={employerId}
              onUploadSuccess={(imageUrl) => {
                const imagesTmp = images?.concat(imageUrl);
                form.images = imagesTmp;
                this.setState({ form });
              }}
            />
          ) : null}
        </View>
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_input'),
    onPress,
    isInput,
    onChangeText,
    maxLength = 30,
    required = false,
    editable = true,
    multiline = false,
    numberOfLines = 1,
    showLine = true,
  }) => {
    const { style } = this;
    return (
      <View style={showLine ? style.itemContainer : style.itemNoLineContainer}>
        <View style={style.itemHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
        </View>
        {isInput ? (
          <Input
            placeholder={placeholder}
            placeholderTextColor="#CCCCCC"
            selectionColor="#EF3D48"
            isInput={false}
            style={[
              { color: editable ? '#333' : '#CCC', fontSize: 16 },
              multiline
                ? {
                    height: numberOfLines * 20,
                    borderWidth: 1,
                    borderColor: '#EEEEEE',
                    borderRadius: 2,
                    padding: 8,
                  }
                : {},
            ]}
            onChangeText={onChangeText}
            maxLength={maxLength}
            value={value}
            editable={editable}
            multiline={multiline}
            numberOfLines={numberOfLines}
          />
        ) : (
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style } = this;
    return (
      <View style={style.bottomContainer}>
        <Touchable style={style.bottomItem} onPress={this.onPreview}>
          <Image source={res.jobViewEnterprise} style={style.bottomItemImg} />
          <Text style={style.bottomItemText}>{I18n.t('page_job_text_preview')}</Text>
        </Touchable>
        <Button
          title={I18n.t('page_resume_btn_save')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ flex: 1 }}
          onPress={this.onSave}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    const { enableAutomaticScroll } = this.state;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_companyInfo_title_text')}</Text>
        </View>
        <KeyboardAwareScrollView
          reef={(ref) => (this.scrollView = ref)}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          extraScrollHeight={100}
          enableAutomaticScroll={enableAutomaticScroll}
        >
          {this.renderBaseInfo()}
          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>
        {this.renderBottom()}
        <PickerModal
          ref={(ref) => (this.pickerModal = ref)}
          onConfirm={({ pickerType, value }) => this.onPickerConfirm(pickerType, value)}
        />
        <BenefitTagModal
          ref={(ref) => (this.benefitTagModal = ref)}
          onSend={this.addOrEditBenefitTag}
          onClose={this.onCloseBenefitTag}
        />
      </View>
    );
  }
}
