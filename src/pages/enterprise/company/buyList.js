import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, Header } from '../../../components';
import styles from '../../../themes/enterprise';
import PageFlatList from '../../../components/list/pageFlatList';
import I18n from '../../../i18n';
import util from '../../../util';
import moment from 'moment';

function getComponentStyle(theme) {
  return {
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 12,
      marginTop: 10,
      overflow: 'hidden',
      paddingTop: 16,
      paddingBottom: 12,
    },
    itemContainer: {
      paddingHorizontal: 14,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginBottom: 12,
    },
    sencondText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    subContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.primaryColor,
      marginRight: 11,
    },
    subTextValue: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      marginRight: 4,
    },
    subTextLabel: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    validDaysText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginTop: 12,
    },
    validDaysNum: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
    },
    saparateLine: {
      height: 1,
      backgroundColor: '#E5E5E5',
      marginVertical: 10,
    },
    itemBottomContainer: {
      paddingHorizontal: 14,
      paddingBottom: 8,
    },
    normalText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginTop: 8,
    },
  };
}

/**
 * 公司详情 购买列表
 */
@inject('companyAction')
@observer
export default class BuyList extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 5,
      };
      const { queryProducts } = this.props.companyAction;
      const data = await queryProducts(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    return (
      <View style={style.itemBox} key={index}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item.productName}</Text>
          {item?.employerProductItems?.map((x, index) => (
            <View key={index} style={style.subContainer}>
              <View style={style.dot} />
              <Text style={style.subTextValue}>{x.amount}</Text>
              <Text style={style.subTextLabel} textType="amount">
                {x.itemName}
              </Text>
            </View>
          ))}
          <Text style={style.validDaysText} textType="amount">
            {I18n.t('page_recharge_text_validity')}：
            {I18n.t('page_recharge_text_day', { count: item.display })}
          </Text>
        </View>
        <View style={style.saparateLine} />
        <View style={style.itemBottomContainer}>
          <Text style={style.normalText} textType="amount">
            {I18n.t('page_recharge_text_pay_date')}：
            {moment(item?.cdate).format('YYYY/MM/DD HH:mm')}
          </Text>
          <Text style={style.normalText} textType="amount">
            {I18n.t('page_recharge_text_pay_amount')}：
            {I18n.t('page_recharge_text_number_of_coin', {
              count: util.formatAmount(item.productPrice),
            })}
          </Text>
          <Text style={style.normalText} textType="amount">
            {I18n.t('page_recharge_text_trade_number')}：{item?.accountTradeId}
          </Text>
          {item?.productDescription ? (
            <Text style={style.normalText} textType="amount">
              {I18n.t('page_recharge_text_product_description')}：{item.productDescription}
            </Text>
          ) : null}
        </View>
      </View>
    );
  };

  render() {
    return (
      <>
        <Header theme="dark" title={I18n.t('page_recharge_text_purchase_record')} />
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
