import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform, Touchable, Image } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import RechargeItemList from './components/rechargeItemList';
import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 1,
      borderColor: theme.minorBgColor,
      minHeight: 50,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 0,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 8 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 3 - 20) / 2,
      backgroundColor: '#FF4A55',
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      marginRight: 10,
      flexShrink: 10,
    },
    filterBox: {
      position: 'relative',
      width: 50,
      height: 30,
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    filterDot: {
      position: 'absolute',
      top: -3,
      right: 0,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF4A55',
    },
  };
}

/**
 * 充值列表
 */
@inject('companyAction')
@observer
export default class RechargeList extends React.Component {
  constructor(props) {
    super(props);
    this.type = props.navigation.getParam('type', 0);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      filterData: null,
      initType: this.type,
    };
  }

  componentDidMount() {
    this.props.companyAction.queryOrderConstants();
  }

  onFilter = () => {
    NavigationService.navigate('rechargeFilter', {
      filterData: this.state.filterData,
      callback: this.onFilterCallback,
      tabIndex: this.state.initType,
    });
  };

  onFilterCallback = (filterData) => {
    console.log('filterData', filterData);
    this.setState({ filterData });
  };

  onChangeTab = ({ i }) => {
    this.setState({
      initType: i,
    });
    global.emitter.emit(constant.event.rechargeTabChanged, { initType: i });
  };

  rightComponent = () => {
    const { style } = this;

    return (
      <Touchable onPress={this.onFilter} style={style.filterBox}>
        <Image source={resIcon.resumeFilterEnterprise} />
        {this.state.filterData ? <View style={style.filterDot} /> : null}
      </Touchable>
    );
  };

  render() {
    const { style } = this;

    return (
      <View style={style.container}>
        <Header
          theme="dark"
          title={I18n.t('page_company_text_recharge_record')}
          rightComponent={this.rightComponent}
        />
        <ScrollableTabView
          tabBarBackgroundColor={style.tabBarBackgroundColor}
          tabBarInactiveTextColor={style.tabBarInactiveTextColor}
          tabBarActiveTextColor={style.tabBarActiveTextColor}
          underlineColor={style.underlineColor}
          tabBarStyle={style.tabBarStyle}
          tabBarTextStyle={style.tabBarTextStyle}
          activeTabTextStyle={style.activeTabTextStyle}
          tabBarUnderlineStyle={style.tabBarUnderlineStyle}
          tabStyles={style.tabStyles}
          initialPage={this.type || 0}
          isHorizontalScroll={false}
          locked={IS_ANDROID}
          onChangeTab={this.onChangeTab}
        >
          <RechargeItemList
            key="0"
            type="all"
            index="0"
            tabLabel={I18n.t('page_company_text_all')}
          />
          <RechargeItemList
            key="1"
            type="0"
            index="1"
            tabLabel={I18n.t('page_company_text_pending_payment')}
          />
          <RechargeItemList
            key="2"
            type="1"
            index="2"
            tabLabel={I18n.t('page_company_text_paid')}
          />
        </ScrollableTabView>
      </View>
    );
  }
}
