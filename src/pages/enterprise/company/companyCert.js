import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  Button,
  Input,
  Icon,
  Image,
  BaseComponent,
  AlertPro,
} from '../../../components';
import ActionSheet from 'react-native-actionsheet';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import { hasEar, deviceWidth } from '../../../common';
import RightArrow from '../../../components/rightArrow';
import res from '../../../res';
import UploadImage from './components/uploadImage';
import NavigationService from '../../../navigationService';

function getComponentStyle(theme) {
  const imageW = (deviceWidth - 18 * 2 - 12 * 3) / 3 - 1;
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 10,
      paddingBottom: 10,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    verifyFailText: {
      fontSize: theme.fontSizeS,
      color: '#f00',
      flexShrink: 10,
    },
    contentContainer: {
      paddingHorizontal: 18,
      marginTop: 15,
    },
    itemContainer: {
      marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemNoLineContainer: {
      marginBottom: 18,
      paddingBottom: 15,
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeM,
      color: theme.primaryFontColor,
      lineHeight: 20,
    },
    labelStarText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
      paddingTop: 4,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    imagesBox: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    imageBox: {
      height: imageW,
      width: imageW,
      backgroundColor: '#F7F7F7',
      marginRight: 12,
      marginBottom: 8,
      overflow: 'hidden', // 超出部分剪切
    },
    imageBoxDeleteBtn: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    deleteColor: '#EF3D48',
    imageW: imageW - 12,
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 20,
      flexShrink: 10,
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 38,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
    itemImageHeaderContainer: {
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    subLabelText: {
      marginTop: 8,
      marginBottom: 5,
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      lineHeight: 17,
    },
    imageStyle: { width: imageW, height: imageW, resizeMode: 'cover' },
  };
}

/**
 * 企业资质认证
 */
@inject('companyStore', 'companyAction')
@observer
export default class CompanyCert extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { companyInfo } = this.props.companyStore;
    const companyInfoCopy = { ...companyInfo };
    this.companyInfo = companyInfoCopy;
    this.employerId = companyInfo?.employerId || '';
    this.state = {
      alertInfo: null,
      form: {
        company: companyInfoCopy?.company || '',
        corporation: companyInfoCopy?.corporation || '',
        businessLicense: companyInfoCopy?.businessLicense || [],
        taxCertificate: companyInfoCopy?.taxCertificate || [],
      },
    };
  }

  onSave = async () => {
    const { form } = this.state;
    if (!form.company.trim()) {
      return toast.show(I18n.t('page_companyCert_op_company_name'));
    }
    if (!form.corporation.trim()) {
      return toast.show(I18n.t('page_companyCert_op_entity_name'));
    }
    if (form.businessLicense.length <= 0) {
      return toast.show(I18n.t('page_companyCert_op_business_license'));
    }
    if (form.taxCertificate.length <= 0) {
      return toast.show(I18n.t('page_companyCert_op_tax_certificate'));
    }

    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.certification(form);
      if (!res?.status) {
        this.showRequestResult(res);
        return;
      }
      this.showRequestResult(I18n.t('page_companyCert_op_submit_success'));
      const onComplete = this.props.navigation.getParam('onComplete');
      if (onComplete) onComplete();
      NavigationService.goBack();
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onBeforeSubmit = () => {
    this.setState({
      alertInfo: {
        title: I18n.t('op_remind_title'),
        message: I18n.t('page_qualification_confirm_title'),
        showCancel: true,
        onConfirm: () => {
          this.onAlertCancel();
          this.onSave();
        },
      },
    });
  };
  onAlertCancel = () => {
    this.setState({ alertInfo: null });
  };

  onConfirm = (type, value) => {
    console.log('onConfirm', type, value);
    const { form } = this.state;
    form[type] = value;
    this.setState({ form });
  };

  renderBaseInfo = () => {
    const { style } = this;
    const { form } = this.state;
    const { renderItem: Item, renderImageItem: ImageItem } = this;
    return (
      <View style={style.contentContainer}>
        <Item
          label={I18n.t('page_companyCert_label_company_name')}
          required
          isInput
          placeholder={I18n.t('page_companyCert_label_company_name_place')}
          onChangeText={(v) => this.onConfirm('company', v)}
          value={form['company']}
          maxLength={100}
        />
        <Item
          label={I18n.t('page_companyCert_label_corporate_entity_name')}
          required
          isInput
          onChangeText={(v) => this.onConfirm('corporation', v)}
          value={form['corporation']}
          maxLength={100}
        />
        <ImageItem
          label={I18n.t('page_companyCert_label_business_license')}
          subLabel={I18n.t('page_company_label_tips')}
          required
          isBusinessLicense
          maxLength={5}
        />
        <ImageItem
          label={I18n.t('page_companyCert_label_tax_certificate')}
          subLabel={I18n.t('page_company_label_tips')}
          required
          maxLength={5}
          showLine={false}
        />
      </View>
    );
  };

  renderImageItem = ({
    label,
    subLabel,
    required = false,
    showLine = true,
    isBusinessLicense = false,
  }) => {
    const { style, employerId } = this;
    const {
      form: { businessLicense, taxCertificate },
      form,
    } = this.state;
    const images = isBusinessLicense ? businessLicense : taxCertificate;
    return (
      <View style={showLine ? style.itemContainer : style.itemNoLineContainer}>
        <View style={style.itemImageHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
          <Text style={style.subLabelText}>{subLabel}</Text>
        </View>
        <View style={style.imagesBox}>
          {images?.map((imageUrl, index) => (
            <View key={index} style={style.imageBox}>
              <Image
                source={imageUrl ? { uri: imageUrl } : res.iconErrorData}
                defaultSource={res.iconErrorData}
                style={style.imageStyle}
              />
              <Touchable
                style={style.imageBoxDeleteBtn}
                onPress={() => {
                  const imagesTmp = images?.filter((itemUrl) => itemUrl !== imageUrl);
                  isBusinessLicense
                    ? (form.businessLicense = imagesTmp)
                    : (form.taxCertificate = imagesTmp);
                  this.setState({ form });
                }}
              >
                <Icon name="delete" size={16} color={style.deleteColor} />
              </Touchable>
            </View>
          ))}
          {images.length < 5 ? (
            <UploadImage
              imageSize={style.imageW + 12}
              employerId={employerId}
              onUploadSuccess={(imageUrl) => {
                const imagesTmp = images.concat(imageUrl);
                isBusinessLicense
                  ? (form.businessLicense = imagesTmp)
                  : (form.taxCertificate = imagesTmp);
                this.setState({ form });
                console.log(imageUrl);
              }}
            />
          ) : null}
        </View>
      </View>
    );
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_input'),
    onPress,
    isInput,
    onChangeText,
    maxLength = 30,
    required = false,
    showLine = true,
  }) => {
    const { style } = this;
    return (
      <View style={showLine ? style.itemContainer : style.itemNoLineContainer}>
        <View style={style.itemHeaderContainer}>
          <View style={style.labelBox}>
            <Text style={style.labelText}>{label} </Text>
            {required ? <Text style={style.labelStarText}>*</Text> : null}
          </View>
        </View>
        {isInput ? (
          <Input
            placeholder={placeholder}
            placeholderTextColor="#CCCCCC"
            selectionColor="#EF3D48"
            isInput={false}
            style={{ color: '#333', fontSize: 16 }}
            onChangeText={onChangeText}
            maxLength={maxLength}
            value={value}
          />
        ) : (
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        )}
      </View>
    );
  };

  renderBottom = () => {
    const { style, companyInfo } = this;
    const isUnderReview = companyInfo?.qualificationStatus?.value === 0;
    return (
      <View style={style.bottomContainer}>
        <Button
          title={
            isUnderReview
              ? companyInfo?.qualificationStatus?.label
              : I18n.t('page_companyCert_btn_submit')
          }
          btnType="login"
          btnSize="s44"
          containerStyle={{ width: '100%' }}
          onPress={
            companyInfo?.qualificationStatus?.value === 1 ? this.onBeforeSubmit : this.onSave
          }
          disabled={isUnderReview}
        />
      </View>
    );
  };

  render() {
    const { style, companyInfo } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>{I18n.t('page_companyCert_title_text')}</Text>
          {companyInfo?.qualificationMemo ? (
            <Text style={style.verifyFailText}>
              {I18n.t('page_companyCert_text_verify_fail')}
              {companyInfo.qualificationMemo}
            </Text>
          ) : null}
        </View>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {this.renderBaseInfo()}
          <View style={{ height: 100 }} />
        </KeyboardAwareScrollView>
        {this.renderBottom()}
        <ActionSheet
          ref={(ref) => {
            this.photoActionSheet = ref;
          }}
          title={I18n.t('page_resume_label_photo')}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.photoActionSheetSelect}
        />
        <AlertPro
          visible={!!this.state.alertInfo}
          textConfirm={I18n.t('op_confirm_title')}
          textCancel={I18n.t('op_cancel_title')}
          onCancel={this.onAlertCancel}
          {...(this.state.alertInfo || {})}
        />
      </View>
    );
  }
}
