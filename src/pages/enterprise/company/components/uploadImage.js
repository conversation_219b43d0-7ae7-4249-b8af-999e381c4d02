import React from 'react';
import ActionSheet from 'react-native-actionsheet';
import { inject, observer } from 'mobx-react';
import { Touchable, View, Image, BaseComponent } from '../../../../components';
import I18n from '../../../../i18n';
import ImagePickerUtil from '../../../../util/imagePickerUtil';
import res from '../../../../res';

/**
 * 上传图片
 */
@inject('companyAction')
@observer
export default class UploadImage extends BaseComponent {
  upload = (image) => {
    const { employerId, onUploadSuccess } = this.props;
    if (image && image.path && employerId) {
      try {
        this.showGlobalLoading();
        this.props.companyAction
          .uploadCompanyImage(employerId, image.path)
          .then((resImage) => {
            if (resImage.data.http && onUploadSuccess) {
              toast.show(I18n.t('page_company_op_upload_success'));
              onUploadSuccess(resImage.data.http);
            }
          }, this.showRequestResult())
          .finally(() => {
            this.showRequestResult();
          });
      } catch (error) {
        this.showRequestResult(error);
      }
    }
  };

  initActionSheet = (ref) => (this.actionSheet = ref);

  actionSheetSelect = (index) => {
    if (index === 0) {
      this.pickSingleWithCamera();
    } else if (index === 1) {
      this.openPicLib();
    }
  };

  pickSingleWithCamera = () => {
    ImagePickerUtil.openCamera({
      width: 750,
      height: 750,
    })
      .then(this.upload)
      .catch((e) => {
        if (e) console.log(e); // toast.show(e.message);
      });
  };

  openPicLib = () => {
    ImagePickerUtil.openPickerByAvatar({
      width: 750,
      height: 750,
    })
      .then(this.upload)
      .catch((e) => {
        if (e) console.log(e); // toast.show(e.message);
      });
  };

  showActionSheet = () => {
    this.actionSheet.show();
  };

  render() {
    const { imageSize = 50 } = this.props;
    return (
      <View>
        <Touchable onPress={this.showActionSheet}>
          <Image
            source={res.addImage}
            style={{ width: imageSize, height: imageSize, borderRadius: 5 }}
          />
        </Touchable>
        <ActionSheet
          ref={this.initActionSheet}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.actionSheetSelect}
        />
      </View>
    );
  }
}
