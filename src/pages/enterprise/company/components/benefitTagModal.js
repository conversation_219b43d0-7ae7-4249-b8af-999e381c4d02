import React, { Component } from 'react';
import { View, Text, Modal, Button, Touchable, Icon, TextInput } from '../../../../components';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { statusBarHeight, headerHeight } from '../../../../common';
import Toast from 'react-native-easy-toast';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.4)',
    },
    contentContainer: {
      marginTop: 108 + statusBarHeight + headerHeight,
      flex: 1,
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 30,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      height: 60,
    },
    title: {
      flex: 1,
      fontSize: theme.fontSizeXL,
      color: theme.titleFontColor,
      textAlign: 'center',
    },
    rightTitleContainer: {
      position: 'absolute',
      top: 0,
      right: theme.containerPaddingHorizontal,
      height: 60,
      justifyContent: 'center',
    },
    closeColor: theme.timeFontColor,
    contentBox: {
      backgroundColor: '#fff',
      borderRadius: 10,
      width: '100%',
      paddingBottom: 20,
    },

    buttonContainer: {
      marginTop: 20,
      marginHorizontal: 48,
    },
    areaContainer: {
      marginHorizontal: 15,
    },
    inputText: {
      height: 78,
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      lineHeight: 24,
      paddingLeft: 12,
      paddingRight: 8,
      paddingTop: 12,
      paddingBottom: 12,
      backgroundColor: '#F9F9F9',
    },
  };
}

/**
 * 新增或编辑标签
 * <AUTHOR>
 */

export default class BenefitTagModal extends Component {
  constructor(props) {
    super(props);
    const { themeStyle } = styles.get(['theme']);
    this.style = getComponentStyle(themeStyle);

    this.state = {
      isEdit: false,
      aeditIndex: -1,
      isOpen: false,
      title: '',
      content: '',
    };
  }

  open = (title, content = '', isEdit = false, editIndex = -1) => {
    this.setState({ isOpen: true, title, content, isEdit, editIndex });
  };

  close = () => {
    this.setState({ isOpen: false });
    if (this.props.onClose) this.props.onClose();
  };

  onSend = () => {
    const { content, isEdit, editIndex } = this.state;
    if (!content || !content.trim()) {
      return this.toast.show(I18n.t('page_companyInfo_tips_company_benefits__null'));
    }
    if (this.props.onSend) this.props.onSend(content.trim(), isEdit, editIndex);
    this.close();
  };

  onChangeContent = (text) => this.setState({ content: text });

  render() {
    const { style } = this;
    const { title, content } = this.state;
    return (
      <Modal
        animationType="fade"
        transparent
        visible={this.state.isOpen}
        onRequestClose={() => {}}
        presentationStyle="overFullScreen"
        onBackButtonPress={this.close}
      >
        <View style={style.container}>
          <View style={style.contentContainer}>
            <View style={style.contentBox}>
              <View style={style.titleContainer}>
                <Text style={style.title}>{title}</Text>
                <Touchable style={style.rightTitleContainer} onPress={this.close}>
                  <Icon name="close" size={25} type="antdesign" color={style.closeColor} />
                </Touchable>
              </View>
              <View style={style.areaContainer}>
                <TextInput
                  style={style.inputText}
                  borderRadius={10}
                  defaultValue={content}
                  placeholder={I18n.t('page_resume_tips_input')}
                  underlineColorAndroid="transparent"
                  multiline
                  maxLength={50}
                  onChangeText={this.onChangeContent}
                  textAlignVertical="top"
                  autoFocus={true}
                />
              </View>
              <View style={style.buttonContainer}>
                <Button
                  title={I18n.t('im_btn_alert_confirm')}
                  onPress={this.onSend}
                  btnType="login"
                  btnSize="s44"
                />
              </View>
            </View>
          </View>
        </View>
        <Toast ref={(toast) => (this.toast = toast)} position="top" />
      </Modal>
    );
  }
}
