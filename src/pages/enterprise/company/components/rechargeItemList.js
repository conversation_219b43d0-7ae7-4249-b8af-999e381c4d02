import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Image, Button, Alert, BaseComponent } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import constant from '../../../../store/constant';
import NavigationService from '../../../../navigationService';
import util from '../../../../util';
import resIcon from '../../../../res';
import moment from 'moment';

function getComponentStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 12,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemTopRightContainer: {
      marginLeft: 16,
      flex: 1,
    },
    itemAvatar: {
      width: 50,
      height: 50,
    },
    itemNameContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    titleText: {
      fontSize: 16,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginRight: 6,
      flexShrink: 0,
    },
    sencondText: {
      fontSize: 14,
      color: theme.minorFontColor,
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#EF3D48',
      flexShrink: 1,
      textAlign: 'right',
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      marginTop: 22,
      marginBottom: 4,
      flex: 1,
    },
    payAmountText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
    },
    btnGroup: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      marginTop: 12,
      paddingBottom: 6,
    },
  };
}

/**
 * 充值列表
 */
@inject('companyAction')
@observer
export default class RechargeItemList extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.status = props.type;
    this.index = props.index;
    this.filterData = null;
    this.state = {};
  }

  componentDidMount() {
    global.emitter.on(constant.event.rechargeFilterDataChanged, this.onRefreshList);
    global.emitter.on(constant.event.rechargeTabChanged, this.onTabChanged);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.rechargeFilterDataChanged, this.onRefreshList);
    global.emitter.off(constant.event.rechargeTabChanged, this.onTabChanged);
  }

  onRefreshList = ({ data, initType }) => {
    this.filterData = data;
    this.onTabChanged({ initType });
  };

  onTabChanged = ({ initType }) => {
    if (this.index == initType) {
      this.onRefresh();
    }
  };

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 8,
        status: this.status,
      };
      if (this.filterData) {
        param.startTime = this.filterData.startTime;
        param.endTime = this.filterData.endTime;
      }
      if (this.status === 'all') {
        if (this.filterData) {
          param.status = this.filterData.status;
        } else {
          delete param.status;
        }
      }
      const data = await this.props.companyAction.queryRecharges(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('rechargeDetail', {
      item,
      cancelCallback: this.onRefresh,
    });
  };

  onCancel = async (item) => {
    Alert.alert(
      I18n.t('op_remind_title'),
      I18n.t('page_recharge_text_cancel_order'),
      [
        { text: I18n.t('op_cancel_title'), onPress: () => {}, style: 'cancel' },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.showGlobalLoading();
              const { companyAction } = this.props;
              const res = await companyAction.cancelRecharge(item.id);
              this.showRequestResult();
              if (res) {
                this.onRefresh();
              }
            } catch (error) {
              this.showRequestResult(error?.message);
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  onPay = (item) => {
    NavigationService.navigate('rechargeMethod', {
      orderId: item.id,
      amount: item.cashrep,
      selected: {
        coin: item.cash,
      },
      // payCallback: this.onRefresh,
    });
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <Image source={resIcon.iconRechargeEnterprise} style={style.itemAvatar} />
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <Text style={style.titleText} textType="amount">
                  {I18n.t('page_recharge_title')}-
                  {I18n.t('page_recharge_text_number_of_coin', {
                    count: util.formatAmount(item.cash),
                  })}
                </Text>
                <Text
                  style={[
                    style.statusText,
                    item?.status?.value != 0 && item.status?.value != 3 ? { color: '#333' } : {},
                  ]}
                >
                  {item?.status?.label}
                </Text>
              </View>
              <Text style={style.sencondText}>
                {moment(item?.cdate_unixtime * 1000).format('YYYY/MM/DD HH:mm')}
              </Text>
            </View>
          </View>
          <View style={style.bottomContainer}>
            <Text style={style.payAmountText} textType="amount">
              {I18n.t('page_recharge_text_real_pay')}${util.formatAmount(item.cashrep)}
            </Text>
          </View>
          {item?.status?.value == 0 ? (
            <View style={style.btnGroup}>
              <Button
                title={I18n.t('op_cancel_title')}
                onPress={() => this.onCancel(item)}
                btnSize="s32"
                outline
                btnType="line"
                style={{ width: 80, marginRight: 10 }}
                containerStyle={{ marginHorizontal: 0 }}
              />
              <Button
                title={I18n.t('page_recharge_text_pay_now')}
                onPress={() => this.onPay(item)}
                btnSize="s32"
                btnType="login"
                style={{ width: 160, marginRight: 10 }}
                containerStyle={{ marginHorizontal: 0 }}
              />
            </View>
          ) : null}
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <PageFlatList
        ref={this.initPageFlatList}
        loadData={this.loadData}
        renderItem={this.renderItem}
        showsVerticalScrollIndicator={false}
      />
    );
  }
}
