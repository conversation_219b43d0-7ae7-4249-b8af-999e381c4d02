import React from 'react';
import ActionSheet from 'react-native-actionsheet';
import { inject, observer } from 'mobx-react';
import { Touchable, View, Image, BaseComponent, Icon } from '../../../../components';
import I18n from '../../../../i18n';
import ImagePickerUtil from '../../../../util/imagePickerUtil';
import res from '../../../../res';

/**
 * 上传图片
 */
@inject('companyAction')
@observer
export default class UploadLogo extends BaseComponent {
  constructor(props) {
    super(props);
    const { imageUrl = '' } = props;
    this.state = {
      imageUrl: imageUrl || '',
    };
  }

  upload = (image) => {
    const { employerId, onUploadSuccess } = this.props;
    if (image && image.path && employerId) {
      try {
        this.showGlobalLoading();
        this.props.companyAction
          .uploadCompanyLogo(employerId, image.path)
          .then((resImage) => {
            if (resImage.data.http && onUploadSuccess) {
              toast.show(I18n.t('page_company_op_upload_success'));
              this.setState({ imageUrl: resImage.data.http });
              onUploadSuccess(resImage.data.http);
            }
          }, this.showRequestResult())
          .finally(() => {
            this.showRequestResult();
          });
      } catch (error) {
        this.showRequestResult(error);
      }
    }
  };

  initActionSheet = (ref) => (this.actionSheet = ref);

  actionSheetSelect = (index) => {
    if (index === 0) {
      this.pickSingleWithCamera();
    } else if (index === 1) {
      this.openPicLib();
    }
  };

  pickSingleWithCamera = () => {
    ImagePickerUtil.openCamera({
      width: 200,
      height: 200,
      cropping: true,
    })
      .then(this.upload)
      .catch((e) => {
        if (e) console.log(e); // toast.show(e.message);
      });
  };

  openPicLib = () => {
    ImagePickerUtil.openPickerByAvatar({
      width: 200,
      height: 200,
      cropping: true,
    })
      .then(this.upload)
      .catch((e) => {
        if (e) console.log(e); // toast.show(e.message);
      });
  };

  showActionSheet = () => {
    this.actionSheet.show();
  };

  render() {
    const { imageUrl } = this.state;
    const { imageSize = 50 } = this.props;
    return (
      <View>
        <Touchable onPress={this.showActionSheet}>
          <Image
            source={imageUrl ? { uri: imageUrl } : res.editLogo}
            style={{ width: imageSize, height: imageSize, borderRadius: 5 }}
          />
          {imageUrl ? (
            <Icon
              type="antdesign"
              name="camerao"
              color="white"
              size={30}
              style={{
                position: 'absolute',
                top: 10,
                right: 10,
                opacity: 0.8,
              }}
            />
          ) : null}
        </Touchable>
        <ActionSheet
          ref={this.initActionSheet}
          options={[
            I18n.t('page_sheet_label_photo'),
            I18n.t('page_sheet_label_lib'),
            I18n.t('page_sheet_label_cancel'),
          ]}
          cancelButtonIndex={2}
          onPress={this.actionSheetSelect}
        />
      </View>
    );
  }
}
