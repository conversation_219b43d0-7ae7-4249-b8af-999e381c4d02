import React, { Component } from 'react';
import {
  Image,
  Icon,
  Header,
  ScrollView,
  Text,
  Touchable,
  View,
  LinearGradient,
  RefreshControl,
} from '../../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
import Avatar from '../../../components/avatar/avatar';
import RightArrow from '../../../components/rightArrow';
import SettingItem from '../../../components/listItem/settingItem';
import constant from '../../../store/constant';
import util from '../../../util';

/**
 * 企业
 */
@inject('userStore', 'companyStore', 'companyAction', 'pageAction')
@observer
export default class Company extends Component {
  constructor(props) {
    super(props);
    this.mineStyle = styles.get('mine');
    this.state = {
      showAlert: false,
      expandMore: false,
      refreshing: false,
    };
  }

  componentDidMount() {
    this.initData();
  }

  initData = async () => {
    this.setState({ refreshing: true });
    await this.props.companyAction.getEmployer();
    await this.props.companyAction.getBalance();
    await this.props.companyAction.queryProducts({ page: 1, size: 1 });
    await this.props.companyAction.queryContacts({ page: 1, size: 1 });
    this.setState({ refreshing: false });
  };

  goSetting = () => NavigationService.navigate('setting');

  onExpand = () => {
    this.setState({
      expandMore: !this.state.expandMore,
    });
  };

  onBuyList = () => NavigationService.navigate('buyList');

  onRechargeList = (type) => {
    NavigationService.navigate('rechargeList', { type });
  };

  onComplete = () => this.initData();

  onBuyCardPress = () => {
    NavigationService.navigate('servicePackage', { isFromCompany: true, initialPage: 1 });
  };

  onBuy = () => {
    this.props.pageAction.selectedTab(constant.tabs.mine);
    NavigationService.reset('main');
  };

  onRecharge = () => NavigationService.navigate('companyRecharge');

  onEditCompany = () => NavigationService.navigate('companyInfo', { onComplete: this.initData });

  onVerify = () => NavigationService.navigate('companyCert', { onComplete: this.initData });

  render() {
    const { mineStyle } = this;
    const { expandMore } = this.state;
    const { companyInfo, balance, totalPackage, totalContacts } = this.props.companyStore;
    const qualificationStatusValue = companyInfo?.qualificationStatus?.value;
    return (
      <View style={mineStyle.container}>
        <Header
          containerStyle={{ backgroundColor: 'transparent' }}
          leftIconColor="#fff"
          theme="light"
        />
        <LinearGradient colors={['#546DB3', '#394B7E']} style={mineStyle.headerBox} />
        <View style={mineStyle.logoBox}>
          <Image
            source={
              companyInfo?.logo
                ? {
                    uri: companyInfo?.logo,
                  }
                : resIcon.defaultEmployerAvatar
            }
            style={mineStyle.companyLogo}
          />
          {qualificationStatusValue == 1 ? (
            <Image source={resIcon.iconVerify} style={mineStyle.vipIcon} />
          ) : null}
        </View>
        <LinearGradient
          colors={['#FFFFFF', '#F7F7F7', '#F7F7F7', '#F7F7F7', '#F7F7F7', '#F7F7F7']}
          style={mineStyle.contentBox}
        >
          <ScrollView
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={this.state.refreshing} onRefresh={this.initData} />
            }
          >
            <View style={mineStyle.companyInfo}>
              <View style={mineStyle.nameContainer}>
                <Text style={mineStyle.companyName}>{companyInfo?.company}</Text>
                <Touchable onPress={this.onEditCompany} style={mineStyle.editIconBox}>
                  <Image source={resIcon.companyEditBgEnterprise} style={mineStyle.editIcon} />
                </Touchable>
              </View>
              <Text style={mineStyle.companyDesc} numberOfLines={expandMore ? 0 : 2}>
                {I18n.t('page_company_text_company_profile')}：
                {companyInfo?.description || I18n.t('page_job_no_welfare')}
              </Text>
              <Touchable style={mineStyle.moreIcon} onPress={this.onExpand}>
                {companyInfo?.description?.length > 45 ? (
                  <Icon
                    type="entypo"
                    name="chevron-small-down"
                    size={24}
                    color="#333333"
                    style={expandMore ? { transform: [{ rotate: '180deg' }] } : {}}
                  />
                ) : null}
              </Touchable>
            </View>
            <View style={mineStyle.buyGroup}>
              <Touchable
                style={mineStyle.buyGroupItem}
                onPress={() => NavigationService.navigate('tradeRecord')}
              >
                <Text style={mineStyle.buyGroupItemValue}> {util.formatAmount(balance)}</Text>
                <Text style={mineStyle.buyGroupItemLabel}>
                  {I18n.t('page_company_text_balance')}
                </Text>
                <Touchable onPress={this.onRecharge} style={mineStyle.buyBtn}>
                  <Text style={mineStyle.buyBtnText}>{I18n.t('page_home_text_recharge')}</Text>
                </Touchable>
              </Touchable>
              <View style={{ width: 12 }} />
              <Touchable style={mineStyle.buyGroupItem} onPress={this.onBuyCardPress}>
                <Text style={mineStyle.buyGroupItemValue}>{totalPackage}</Text>
                <Text style={mineStyle.buyGroupItemLabel}>
                  {I18n.t('page_tabbar_text_service_package_title')}
                </Text>
                <Touchable onPress={this.onBuy} style={mineStyle.buyBtn}>
                  <Text style={mineStyle.buyBtnText}>{I18n.t('page_company_text_go_to_buy')}</Text>
                </Touchable>
              </Touchable>
            </View>

            <View style={mineStyle.middleContainer}>
              <Touchable style={mineStyle.middleTop} onPress={() => this.onRechargeList(0)}>
                <Text>{I18n.t('page_company_text_recharge_record')}</Text>
                <RightArrow color="#999" />
              </Touchable>
              <View style={mineStyle.middleBottom}>
                <Touchable
                  style={mineStyle.middleBottomItem}
                  onPress={() => this.onRechargeList(0)}
                >
                  <Image source={resIcon.iconRecordAllgEnterprise} />
                  <Text style={mineStyle.middleBottomItemText}>
                    {I18n.t('page_company_text_all')}
                  </Text>
                </Touchable>
                <Touchable
                  style={mineStyle.middleBottomItem}
                  onPress={() => this.onRechargeList(1)}
                >
                  <Image source={resIcon.iconRecordWaitEnterprise} />
                  <Text style={mineStyle.middleBottomItemText}>
                    {I18n.t('page_company_text_pending_payment')}
                  </Text>
                </Touchable>
                <Touchable
                  style={mineStyle.middleBottomItem}
                  onPress={() => this.onRechargeList(2)}
                >
                  <Image source={resIcon.iconRecordPaidEnterprise} />
                  <Text style={mineStyle.middleBottomItemText}>
                    {I18n.t('page_company_text_paid')}
                  </Text>
                </Touchable>
              </View>
            </View>

            <View style={mineStyle.bottomContainer}>
              <SettingItem
                label={I18n.t('page_company_text_purchase_record')}
                valueTextStyle={mineStyle.valueTextStyle}
                onPress={this.onBuyList}
              />
              <SettingItem
                label={I18n.t('page_company_text_set_contact')}
                value={I18n.t('page_company_text_people', { count: totalContacts })}
                valueTextStyle={mineStyle.valueTextStyle}
                onPress={() =>
                  NavigationService.navigate('jobContact', {
                    isCompany: true,
                  })
                }
              />
              <SettingItem
                label={I18n.t('page_company_text_qualification')}
                value={
                  qualificationStatusValue != -1 ? companyInfo?.qualificationStatus?.label : ''
                }
                valueComponent={
                  qualificationStatusValue == -1 ? (
                    <View style={mineStyle.unVerifiedBox}>
                      <Text style={mineStyle.unVerified}>
                        {I18n.t('page_company_text_unverified')}
                      </Text>
                    </View>
                  ) : null
                }
                valueTextStyle={qualificationStatusValue == 0 ? mineStyle.pendingTextStyle : {}}
                onPress={this.onVerify}
              />
            </View>
            <View style={{ height: 64, width: '100%' }} />
          </ScrollView>
        </LinearGradient>
      </View>
    );
  }
}
