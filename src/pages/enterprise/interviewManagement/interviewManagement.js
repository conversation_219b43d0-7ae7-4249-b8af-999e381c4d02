import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import InterviewingList from './components/interviewingList';
import InterviewedList from './components/interviewedList';
import UnInterviewedList from './components/unInterviewedList';
import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 1,
      borderColor: theme.minorBgColor,
      minHeight: 50,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 0,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 8 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 3 - 20) / 2,
      backgroundColor: '#FF4A55',
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      marginRight: 10,
      flexShrink: 10,
    },
  };
}

/**
 * 面试管理
 */
@inject('companyAction')
@observer
export default class InterviewManagement extends React.Component {
  constructor(props) {
    super(props);
    this.initialPage = props.navigation.state.params?.initialPage || 0;

    this.style = getComponentStyle(styles.get('theme'));
    this.state = {};
  }

  componentDidMount() {
    this.props.companyAction.queryConstants();
  }

  render() {
    const { style } = this;

    return (
      <View style={style.container}>
        <Header title={I18n.t('page_home_interview_manage_title')} />

        <ScrollableTabView
          tabBarBackgroundColor={style.tabBarBackgroundColor}
          tabBarInactiveTextColor={style.tabBarInactiveTextColor}
          tabBarActiveTextColor={style.tabBarActiveTextColor}
          underlineColor={style.underlineColor}
          tabBarStyle={style.tabBarStyle}
          tabBarTextStyle={style.tabBarTextStyle}
          activeTabTextStyle={style.activeTabTextStyle}
          tabBarUnderlineStyle={style.tabBarUnderlineStyle}
          tabStyles={style.tabStyles}
          initialPage={this.initialPage}
          isHorizontalScroll={false}
          locked={IS_ANDROID}
        >
          <InterviewingList
            key="0"
            status="-1"
            tabLabel={I18n.t('page_mine_interview_nav_title_interviewing')}
            navigation={this.props.navigation}
          />
          <InterviewedList
            key="1"
            status="1"
            tabLabel={I18n.t('page_mine_interview_nav_title_interviewed')}
            navigation={this.props.navigation}
          />
          <UnInterviewedList
            key="2"
            status="0"
            tabLabel={I18n.t('page_mine_interview_nav_title_not_interview')}
            navigation={this.props.navigation}
          />
        </ScrollableTabView>
      </View>
    );
  }
}
