import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Picker } from '../../../../components';
import BottomModal from '../../../../components/modal/bottomModal';
import { computed } from 'mobx';
import I18n from '../../../../i18n';

@inject('companyStore')
@observer
export default class PickerModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      pickerType: null,
      selected: 1,
      title: '',
    };
  }

  @computed get listData() {
    const { pickerType } = this.state;
    const { commentOptions } = this.props.companyStore;
    if (pickerType === 'commentOptions') {
      return commentOptions;
    }
    return [];
  }

  show = ({ title, pickerType, selected }) => {
    this.setState({ title, pickerType, selected, isOpen: true });
  };

  hide = () => {
    this.setState({ isOpen: false });
  };

  onConfirm = () => {
    const { pickerType, selected } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm({ pickerType, value: this.listData?.find((x) => x.value == selected) });
    }
    this.hide();
  };

  renderPicker = () => {
    const { selected } = this.state;
    return (
      <View style={{ flexDirection: 'row' }}>
        <Picker
          selectedValue={selected}
          style={{ flex: 1 }}
          itemStyle={{ fontSize: 18 }}
          onValueChange={(itemValue) => {
            this.setState({ selected: itemValue });
          }}
        >
          {this.listData?.map((item, index) => this.renderPickerItem(item, index))}
        </Picker>
      </View>
    );
  };

  renderPickerItem = (item, i) => (
    <Picker.Item key={i} label={item.label} value={item.value} color="#333" />
  );

  render() {
    const { ...rest } = this.props;
    const { title } = this.state;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={title}
        showCancel={false}
        rightTitle={I18n.t('op_complete_title')}
        rightAction={this.onConfirm}
        contentHeight={200}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={this.state.isOpen}
        btnSize="lg"
        {...rest}
      >
        {this.renderPicker()}
      </BottomModal>
    );
  }
}
