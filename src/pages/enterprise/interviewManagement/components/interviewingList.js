import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Image, Button, Alert, BaseComponent } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import constant from '../../../../store/constant';
import NavigationService from '../../../../navigationService';
import resIcon from '../../../../res';
import moment from 'moment';

/**
 * 列表
 */
@inject('companyAction', 'companyStore')
@observer
export default class InterviewingList extends BaseComponent {
  style = styles.get('interview');

  constructor(props) {
    super(props);

    this.state = {};
  }

  componentDidMount() {
    global.emitter.on(constant.event.interviewStatusChanged, this.onRefresh);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.interviewStatusChanged, this.onRefresh);
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        interviewStatus: -1,
      };
      const data = await this.props.companyAction.queryInterviews(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item: { ...item, resumeId: item.cvId },
      isSearch: true,
      cancelCallback: this.onRefresh,
    });
  };

  onUpdateStatus = async (item, status) => {
    Alert.alert(
      I18n.t('op_remind_title'),
      status === 0
        ? I18n.t('page_interview_tips_uninterview')
        : I18n.t('page_interview_tips_interviewed'),
      [
        {
          text: I18n.t('op_cancel_title'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.showGlobalLoading();
              const res = await this.props.companyAction.updateInterviewStatus(item.id, status);
              this.showRequestResult(res?.message || I18n.t('op_handle_success_title'));
              global.emitter.emit(constant.event.interviewStatusChanged);
            } catch (error) {
              this.showRequestResult(error?.message || 'updateInterviewStatus:error');
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <View style={style.avatarContainer}>
              <Image
                source={
                  item.intervieweeAvatar ? { uri: item.intervieweeAvatar } : resIcon.defaultAvatar
                }
                defaultSource={resIcon.defaultAvatar}
                style={style.itemAvatar}
              />
            </View>
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <Text style={style.titleText}>{item.intervieweeName}</Text>
                <Text style={style.statusText}>
                  {I18n.t('page_mine_interview_nav_title_interviewing')}
                </Text>
              </View>
              <Text style={style.sencondText}>
                {I18n.t('page_interview_text_interview_position')}：{item.jobTitle}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_mine_interview_time')}：
                {moment(item.actionTime).format('YYYY/MM/DD HH:mm')}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_interview_text_interview_address')}：{item.actionAddress}
              </Text>
            </View>
          </View>
          <View style={style.bottomContainer}>
            <Button
              title={I18n.t('page_mine_interview_nav_title_not_interview')}
              btnSize="s32"
              outline
              style={{ width: 100, marginRight: 10 }}
              titleStyle={{ color: '#EF3D48' }}
              containerStyle={{ marginHorizontal: 0 }}
              onPress={() => this.onUpdateStatus(item, 0)}
            />
            <Button
              title={I18n.t('page_mine_interview_nav_title_interviewed')}
              btnSize="s32"
              btnType="login"
              style={{ width: 100 }}
              containerStyle={{ marginHorizontal: 0 }}
              onPress={() => this.onUpdateStatus(item, 1)}
            />
          </View>
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
