import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Image, Button, Alert, BaseComponent } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import constant from '../../../../store/constant';
import NavigationService from '../../../../navigationService';
import resIcon from '../../../../res';
import moment from 'moment';
import PickerModal from './pickerModal';

/**
 * 列表
 */
@inject('companyAction', 'companyStore')
@observer
export default class InterviewedList extends BaseComponent {
  style = styles.get('interview');

  constructor(props) {
    super(props);
    this.commentOptions = props.companyStore.commentOptions;

    this.state = {};
  }

  componentDidMount() {
    global.emitter.on(constant.event.interviewStatusChanged, this.onRefresh);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.interviewStatusChanged, this.onRefresh);
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        interviewStatus: 0,
      };
      const data = await this.props.companyAction.queryInterviews(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item: { ...item, resumeId: item.cvId },
      isSearch: true,
      cancelCallback: this.onRefresh,
    });
  };

  onCommentConfirm = async (item) => {
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.addCommentsForJobApply(this.item.id, {
        commentId: item.value?.value,
        comments: 'comments',
      });
      this.showRequestResult(res?.message || I18n.t('page_interview_text_evaluation_success'));
      this.onRefresh();
      this.item = null;
    } catch (error) {
      this.showRequestResult(error?.message || 'addCommentsForJobApply:error');
    }
  };

  renderComment = (item) => {
    const { commentOptions, style } = this;
    if (item?.commentId) {
      return (
        <Touchable
          style={[style.bottomContainer, style.commentedBox]}
          onPress={() => {
            this.item = item;
            this.pickerModal.wrappedInstance.show({
              title: I18n.t('page_interview_text_evaluation'),
              pickerType: 'commentOptions',
              selected:
                item?.commentId?.value || (commentOptions?.length ? commentOptions[0].value : 0),
            });
          }}
        >
          <Image source={resIcon.iconFlowerEnterprise} />
          <Text style={style.commentText}>{item.commentId?.label}</Text>
        </Touchable>
      );
    } else {
      return (
        <View style={style.bottomContainer}>
          <Button
            title={I18n.t('page_interview_text_evaluation')}
            btnSize="s32"
            btnType="login"
            style={{ width: 100 }}
            containerStyle={{ marginHorizontal: 0 }}
            onPress={() => {
              this.item = item;
              this.pickerModal.wrappedInstance.show({
                title: I18n.t('page_interview_text_evaluation'),
                pickerType: 'commentOptions',
                selected:
                  item?.commentId?.value || (commentOptions?.length ? commentOptions[0].value : 0),
              });
            }}
          />
        </View>
      );
    }
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <View style={style.avatarContainer}>
              <Image
                source={
                  item.intervieweeAvatar ? { uri: item.intervieweeAvatar } : resIcon.defaultAvatar
                }
                defaultSource={resIcon.defaultAvatar}
                style={style.itemAvatar}
              />
            </View>
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <Text style={style.titleText}>{item.intervieweeName}</Text>
                <Text style={style.statusText}>
                  {I18n.t('page_mine_interview_nav_title_not_interview')}
                </Text>
              </View>
              <Text style={style.sencondText}>
                {I18n.t('page_interview_text_interview_position')}：{item.jobTitle}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_mine_interview_time')}：
                {moment(item.actionTime).format('YYYY/MM/DD HH:mm')}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_interview_text_interview_address')}：{item.actionAddress}
              </Text>
            </View>
          </View>
          {this.renderComment(item)}
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
        <PickerModal ref={(ref) => (this.pickerModal = ref)} onConfirm={this.onCommentConfirm} />
      </>
    );
  }
}
