import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Image, Icon } from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import RightArrow from '../../../../components/rightArrow';
import constant from '../../../../store/constant';
import NavigationService from '../../../../navigationService';
import util from '../../../../util';
import resIcon from '../../../../res';

function getComponentStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 12,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
  };
}

/**
 * 列表
 */
// @inject('companyAction')
@observer
export default class RecordList extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.type = props.type;
    this.state = {};
  }

  componentDidMount() {
    global.emitter.on(constant.event.pendingTxChange, this.onChangeItem);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.pendingTxChange, this.onChangeItem);
  }

  onChangeItem = (txEto) => {
    const index = this.pageFlatList.state?.data?.findIndex((x) => x.id === txEto.id);
    if (index !== -1) {
      this.pageFlatList &&
        this.pageFlatList.changeItem({ ...this.pageFlatList.state?.data[index], ...txEto }, index);
    }
  };

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        type: 3,
      };
      // const data = await this.props.companyAction.queryAssetTradeList(param);

      return { totalCount: 0, result: [] };
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    // NavigationService.navigate('recordDetails', {
    //   item,
    //   cancelCallback: this.onRefresh,
    // });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}></View>
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
