import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform, Icon, Touchable, Text, Image } from '../../../components';
import styles from '../../../themes/enterprise';
import Header from '../../../components/header/header';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import RecordList from './components/recordList';
import { deviceWidth, headerHeight, statusBarHeight } from '../../../common';
import I18n from '../../../i18n';
import resIcon from '../../../res';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 0,
      minHeight: 50,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 8 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 4 - 20) / 2,
      backgroundColor: '#FF4A55',
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      marginRight: 10,
      flexShrink: 10,
    },
  };
}

/**
 * 聊天
 */
@inject('userStore', 'mineStore', 'mineAction', 'settingsStore')
@observer
export default class Chat extends React.Component {
  constructor(props) {
    super(props);

    this.style = getComponentStyle(styles.get('theme'));
    this.state = {};
  }

  render() {
    const { style } = this;

    return (
      <View style={style.container}>
        <Header title={I18n.t('page_tabbar_text_message_title')} hideBack />
        <RecordList />
      </View>
    );
  }
}
