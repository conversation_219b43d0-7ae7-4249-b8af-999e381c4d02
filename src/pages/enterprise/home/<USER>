import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Header, Image } from '../../../components';
import styles from '../../../themes/enterprise';
import PageFlatList from '../../../components/list/pageFlatList';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import util from '../../../util';
import resIcon from '../../../res';
import moment from 'moment';

function getComponentStyle(theme) {
  return {
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      paddingVertical: 16,
      paddingHorizontal: 18,
    },
    itemContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    titleText: {
      fontSize: 16,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    amountText: {
      fontSize: 18,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    normalText: {
      fontSize: theme.fontSizeM,
      color: theme.minorFontColor,
      marginTop: 8,
    },
    saparateBox: {
      height: 1,
      backgroundColor: '#fff',
      paddingHorizontal: 18,
    },
    saparateLine: {
      height: 1,
      backgroundColor: '#F2F2F2',
    },
    topLine: {
      height: 10,
      backgroundColor: '#F2F2F2',
    },
    filterBox: {
      position: 'relative',
      width: 50,
      height: 30,
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    filterDot: {
      position: 'absolute',
      top: -3,
      right: 0,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF4A55',
    },
  };
}

/**
 * 账户流水
 */
@inject('companyAction')
@observer
export default class TradeRecord extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.state = {
      filterData: null,
    };
  }

  componentDidMount() {
    this.props.companyAction.queryTradeTypeConstants();
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 10,
        ...this.state.filterData,
      };
      // 如果param 包含value属性 则删除
      if (param.value) {
        delete param.value;
      }
      const { queryTrades } = this.props.companyAction;
      const data = await queryTrades(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onDetail = (item) => {
    NavigationService.navigate('recordDetail', { item });
  };

  onFilter = () => {
    NavigationService.navigate('tradeFilter', {
      filterData: this.state.filterData,
      callback: this.onFilterCallback,
    });
  };

  onFilterCallback = (filterData) => {
    console.log('filterData', filterData);
    this.setState({ filterData }, () => {
      this.onRefresh();
    });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    return (
      <Touchable style={style.itemBox} key={index} onPress={() => this.onDetail(item)}>
        <View style={style.itemContainer}>
          <Text style={style.titleText}>{item.productName || item?.type?.label}</Text>
          <Text style={style.amountText} textType="amount">
            {util.formatAmount(item.amount)}
          </Text>
        </View>
        <View style={style.itemContainer}>
          <Text style={style.normalText} textType="amount">
            {moment(item?.cdate).format('YYYY/MM/DD HH:mm')}
          </Text>
          <Text style={style.normalText} textType="amount">
            {I18n.t('page_home_text_balance')} {util.formatAmount(item.balance)}
          </Text>
        </View>
      </Touchable>
    );
  };

  rightComponent = () => {
    const { style } = this;

    return (
      <Touchable onPress={this.onFilter} style={style.filterBox}>
        <Image source={resIcon.resumeFilterEnterprise} />
        {this.state.filterData ? <View style={style.filterDot} /> : null}
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <Header
          theme="dark"
          title={I18n.t('page_home_text_record_of_account')}
          rightComponent={this.rightComponent}
        />
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => (
            <View style={this.style.saparateBox}>
              <View style={this.style.saparateLine} />
            </View>
          )}
          ListHeaderComponent={() => <View style={this.style.topLine} />}
        />
      </>
    );
  }
}
