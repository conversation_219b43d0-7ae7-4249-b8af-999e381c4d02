import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Header, Button } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import moment from 'moment';
import { deviceWidth, hasEar } from '../../../common';
import DatePickerModal from '../job/components/datePickerModal';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    contentContainer: {
      paddingHorizontal: 14,
    },
    sectionBox: {
      marginTop: 20,
      flexDirection: 'column',
    },
    sectionTitle: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    itemContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    item: {
      minWidth: (deviceWidth - 28 - 24) / 3,
      height: 38,
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
      marginRight: 12,
      marginBottom: 14,
      paddingHorizontal: 6,
    },
    itemActive: {
      backgroundColor: '#FAECEC',
    },
    itemText: {
      fontSize: 13,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      lineHeight: 38,
      textAlign: 'center',
    },
    itemTextActive: {
      color: '#EF3D48',
    },
    dateBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    dateItem: {
      flex: 1,
      height: 38,
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
    },
    dateText: {
      fontSize: 13,
      color: '#999999',
      lineHeight: 38,
      textAlign: 'center',
    },
    lineBox: {
      height: 38,
      marginHorizontal: 20,
    },
    line: {
      lineHeight: 38,
      textAlign: 'center',
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 30,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
  };
}

/**
 * 账户流水筛选
 */
@inject('companyStore')
@observer
export default class TradeFilter extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { filterData } = this.props.navigation.state.params || {};
    const { tradeTypeList } = this.props.companyStore;
    console.log('tradeTypeList', tradeTypeList);
    const dateList = [
      {
        label: I18n.t('page_recharge_text_recent_month'),
        value: '1',
        startTime: moment().subtract(1, 'months').startOf('day').unix(),
        endTime: moment().endOf('day').unix(),
        selected: false,
      },
      {
        label: I18n.t('page_recharge_text_recent_three_month'),
        value: '3',
        startTime: moment().subtract(3, 'months').startOf('day').unix(),
        endTime: moment().endOf('day').unix(),
        selected: false,
      },
      {
        label: I18n.t('page_recharge_text_recent_six_month'),
        value: '6',
        startTime: moment().subtract(6, 'months').startOf('day').unix(),
        endTime: moment().endOf('day').unix(),
        selected: false,
      },
      { label: I18n.t('page_recharge_text_custom_year'), value: '0', selected: false },
    ];
    this.state = {
      statusList: tradeTypeList?.map((x) => {
        return {
          ...x,
          selected: filterData?.types?.includes(x.value),
        };
      }),
      dateList: dateList.map((x) => {
        return {
          ...x,
          selected: filterData?.value == x.value,
        };
      }),
      form: {
        startTime:
          filterData?.value == 0 ? moment(filterData?.startTime * 1000).format('YYYY-MM-DD') : '',
        endTime:
          filterData?.value == 0 ? moment(filterData?.endTime * 1000).format('YYYY-MM-DD') : '',
      },
      showDatePicker: false,
    };
  }

  onValueChange = (item, type) => {
    if (type == 'type') {
      const { statusList } = this.state;
      const index = statusList.findIndex((x) => x.value == item.value);
      if (index > -1) {
        statusList[index].selected = !statusList[index].selected;
      }
      this.setState({ statusList });
    } else {
      const { dateList } = this.state;
      const data = {};
      dateList.forEach((x) => {
        if (x.value == item.value) {
          x.selected = !x.selected;
        } else {
          x.selected = false;
        }
      });
      data.dateList = dateList;
      if (item.value == 0) {
        data.showDatePicker = true;
        data.form = {
          startTime: '',
          endTime: '',
        };
      }
      this.setState({ ...data });
    }
  };

  onStartDate = (v) => {
    this.setState({
      form: {
        ...this.state.form,
        startTime: v.date,
      },
    });
  };

  onEndDate = (v) => {
    this.setState({
      form: {
        ...this.state.form,
        endTime: v.date,
      },
    });
  };

  onConfirm = () => {
    const { statusList, dateList, form } = this.state;
    const status = statusList?.filter((x) => x.selected);
    const date = dateList?.find((x) => x.selected);
    const param = {};
    if (status?.length) {
      param.types = status?.map((x) => x.value);
    }

    if (date) {
      if (date.value == 0) {
        if (!form['startTime']) {
          toast.show(I18n.t('page_recharge_text_select_start_time'));
          return;
        } else if (!form['endTime']) {
          toast.show(I18n.t('page_recharge_text_select_end_time'));
          return;
        } else if (
          moment(form['startTime'], 'YYYY-MM-DD') > moment(form['endTime'], 'YYYY-MM-DD')
        ) {
          toast.show(I18n.t('page_recharge_text_time_error'));
          return;
        }
        param['startTime'] = moment(form['startTime'], 'YYYY-MM-DD').startOf('day').unix();
        param['endTime'] = moment(form['endTime'], 'YYYY-MM-DD').endOf('day').unix();
      } else {
        param['startTime'] = date.startTime;
        param['endTime'] = date.endTime;
      }
      param.value = date.value;
    }

    const data = this.props.navigation.state.params;
    data?.callback?.(Object.values(param).length > 0 ? param : null);
    NavigationService.goBack();
  };

  onRest = () => {
    const { statusList, dateList } = this.state;
    statusList.forEach((x) => {
      x.selected = false;
    });

    dateList.forEach((x) => {
      x.selected = false;
    });
    this.setState({
      statusList,
      dateList,
      form: {
        startTime: '',
        endTime: '',
      },
      showDatePicker: false,
    });
  };

  render() {
    const { style } = this;
    const { statusList, dateList, form } = this.state;
    const showDatePicker = dateList?.find((x) => x.selected)?.value == 0;
    const startTime = form['startTime'];
    const endTime = form['endTime'];

    return (
      <View style={style.container}>
        <Header theme="dark" title={I18n.t('page_recharge_filter_text_filter')} />
        <View style={style.contentContainer}>
          <View style={style.sectionBox}>
            <Text style={style.sectionTitle}>{I18n.t('page_home_text_record_type')}</Text>
            <View style={style.itemContainer}>
              {statusList?.map((item, index) => (
                <Touchable
                  style={[
                    style.item,
                    (index + 1) % 3 == 0 && I18n.locale == 'zh' ? { marginRight: 0 } : {},
                    item.selected ? style.itemActive : {},
                  ]}
                  key={index}
                  onPress={() => this.onValueChange(item, 'type')}
                >
                  <Text style={[style.itemText, item.selected ? style.itemTextActive : {}]}>
                    {item.label}
                  </Text>
                </Touchable>
              ))}
            </View>
          </View>
          <View style={style.sectionBox}>
            <Text style={style.sectionTitle}>{I18n.t('page_home_text_trade_date')}</Text>
            <View style={style.itemContainer}>
              {dateList?.map((item, index) => (
                <Touchable
                  style={[
                    style.item,
                    (index + 1) % 3 == 0 ? { marginRight: 0 } : {},
                    item.selected ? style.itemActive : {},
                  ]}
                  key={index}
                  onPress={() => this.onValueChange(item, 'date')}
                >
                  <Text style={[style.itemText, item.selected ? style.itemTextActive : {}]}>
                    {item.label}
                  </Text>
                </Touchable>
              ))}
            </View>
            {showDatePicker ? (
              <View style={style.dateBox}>
                <Touchable
                  style={style.dateItem}
                  onPress={() => this.startDatePicker.show(startTime)}
                >
                  <Text style={[style.dateText, startTime ? style.itemText : {}]}>
                    {startTime || I18n.t('page_recharge_text_start_time')}
                  </Text>
                </Touchable>
                <View style={style.lineBox}>
                  <Text style={style.line}>-</Text>
                </View>
                <Touchable style={style.dateItem} onPress={() => this.endDatePicker.show(endTime)}>
                  <Text style={[style.dateText, endTime ? style.itemText : {}]}>
                    {endTime || I18n.t('page_recharge_text_end_time')}
                  </Text>
                </Touchable>
              </View>
            ) : null}
          </View>
        </View>

        <View style={style.bottomContainer}>
          <Button
            title={I18n.t('page_job_btn_reset')}
            btnType="reset"
            btnSize="s44"
            containerStyle={{ flex: 2.5, marginRight: 15 }}
            onPress={this.onRest}
            outline
          />
          <Button
            title={I18n.t('page_msg_confirm_text')}
            btnType="login"
            btnSize="s44"
            containerStyle={{ flex: 5 }}
            onPress={this.onConfirm}
          />
        </View>
        <DatePickerModal
          title={I18n.t('page_recharge_text_start_time')}
          ref={(ref) => (this.startDatePicker = ref)}
          onConfirm={(v) => this.onStartDate(v)}
        />
        <DatePickerModal
          title={I18n.t('page_recharge_text_end_time')}
          ref={(ref) => (this.endDatePicker = ref)}
          onConfirm={(v) => this.onEndDate(v)}
        />
      </View>
    );
  }
}
