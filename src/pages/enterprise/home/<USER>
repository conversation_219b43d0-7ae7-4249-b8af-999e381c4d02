import React, { Component } from 'react';
import { Image, ScrollView, Text, Header, View, Alert, Button } from '../../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    contentContainer: {
      flex: 1,
      paddingHorizontal: 10,
      alignItems: 'center',
      paddingTop: 62,
    },
    titleStyle: {
      fontSize: 17,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      marginTop: 36,
      textAlign: 'center',
    },
    btnStyle: {
      marginHorizontal: 27,
      marginTop: 60,
    },
  };
}

/**
 * 切换个人版
 */
@inject('loginAction', 'companyStore', 'globalAction')
@observer
export default class ChangeAccount extends Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.state = {};
  }

  onChangeAccount = async () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_home_tips_switch_personal'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: async () => {
          NavigationService.navigate('login');
          this.props.loginAction.logoutForEp({ isEnterprise: false });
        },
      },
    ]);
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header />
        <ScrollView style={style.container} showsVerticalScrollIndicator={false}>
          <View style={style.contentContainer}>
            <Image source={resIcon.loginGuide} />
            <Text style={style.titleStyle}>{I18n.t('page_home_text_change_account_tips')}</Text>
            <Button
              title={I18n.t('page_home_text_change_account')}
              btnType="login"
              btnSize="s44"
              containerStyle={{ width: '100%' }}
              style={style.btnStyle}
              titleStyle={{ fontSize: 16 }}
              onPress={this.onChangeAccount}
            />
          </View>
        </ScrollView>
      </View>
    );
  }
}
