import React from 'react';
import { inject, observer } from 'mobx-react';
import { Header, Text, Touchable, View } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import PageFlatList from '../../../components/list/pageFlatList';
import NavigationService from '../../../navigationService';
import constant from '../../../store/constant';
import RightArrow from '../../../components/rightArrow';
import UnreadPoint from '../../../components/unreadPoint';
import moment from 'moment';

/**
 * 消息列表
 * <AUTHOR>
 */
@inject('companyAction')
@observer
export default class MessageList extends React.Component {
  style = styles.get('messageList');

  componentDidMount() {}

  initPageFlatList = (ref) => (this.articlePageFlatList = ref);

  loadData = async (page) => {
    const limit = 20;
    const param = {
      offset: (page - 1) * limit,
      limit,
    };
    return this.props.companyAction.queryInboxMessages(param);
  };

  extractPlainText = (html) => {
    const regex = /(<([^>]+)>)/gi;
    return html.replace(regex, '').trim();
  };

  onItemClick = (item, index) => {
    console.log('onItemClick', item);
    NavigationService.navigate('webviewDetail', {
      messageId: item.messageId,
      url: item.content,
      title: item.subject,
      showShare: false,
      headerBgColor: '#EF3D48',
      isMessage: true,
    });
    if (!item.receiver?.receiveStatus?.value) {
      this.props.companyAction.readMessage(item.messageId).then(() => {
        this.props.companyAction.queryMessagesStatistics();
        item.receiver.receiveStatus.value = 1;
        this.articlePageFlatList.changeItem(item, index);
      });
    }
  };

  renderItem = ({ item, index }) => {
    return (
      <Touchable onPress={() => this.onItemClick(item, index)} style={this.style.itemContainer}>
        <View style={this.style.itemTopContainer}>
          <Text style={this.style.itemDateText}>
            {item.sendAt ? moment(item.sendAt * 1000).format('YYYY-MM-DD HH:mm:ss') : ''}
          </Text>
          <UnreadPoint hide={item.receiver?.receiveStatus?.value != 0} />
          <RightArrow style={{ marginLeft: 5 }} color={this.style.itemDateText.color} />
        </View>
        <View style={this.style.itemLine} />
        <Text style={this.style.itemTitleText}>{item.subject}</Text>
        <Text style={this.style.itemDescText} numberOfLines={1}>
          {this.extractPlainText(item.content)}
        </Text>
      </Touchable>
    );
  };

  render() {
    return (
      <View style={this.style.container}>
        <Header title={I18n.t('page_message_text_message_notice')} />
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          itemId="messageId"
        />
      </View>
    );
  }
}
