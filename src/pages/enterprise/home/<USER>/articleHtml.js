export default `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
 body,html{
   padding: 6px;
   margin: 0;
 }
 img {
  max-width: 100%;
}

</style>
<script type="text/javascript">
  window.onload = function(){
    window.ReactNativeWebView.postMessage(JSON.stringify({ action: 'bodyInfo', data: { offsetHeight: document.body.offsetHeight } }))
  }
</script>
<body>
    <div class="ql-container ql-snow">
        <div class="ql-editor">
          articleDescription
        </div>
    </div>
</body>
</html>`;
