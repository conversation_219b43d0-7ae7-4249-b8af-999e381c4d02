import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Image,
  Icon,
  Popover,
  BaseComponent,
  Alert,
} from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import NavigationService from '../../../../navigationService';
import resIcon from '../../../../res';
import { computed } from 'mobx';
import constant from '../../../../store/constant';
import InviteModal from './inviteModal';

/**
 * 列表
 */
@inject('companyStore', 'companyAction')
@observer
export default class BuyList extends BaseComponent {
  style = styles.get('resumeList');

  constructor(props) {
    super(props);
    this.defaultMenus = [
      {
        icon: null,
        text: I18n.t('page_home_text_pending_communication'),
        style: this.style,
        onPress: this.onCommunicated,
        type: 1,
      },
      {
        icon: null,
        text: I18n.t('page_resume_text_unsuitable'),
        style: this.style,
        onPress: this.onInappropriate,
        type: 2,
      },
      {
        icon: null,
        text: I18n.t('page_resume_text_interview'),
        style: this.style,
        onPress: this.onInvite,
        type: 3,
      },
    ];
    this.menus = this.defaultMenus;
    this.tabIndex = props.tabIndex;
    this.filterData = props.filterData;
    this.selectedJob = props.selectedJob;
    const { buyStatistics } = props.companyStore;
    const actions = [
      {
        label: I18n.t('page_home_text_pending_screening'),
        value: buyStatistics?.chosenTotal || 0,
        type: 0,
      },
      {
        label: I18n.t('page_home_text_pending_communication'),
        value: buyStatistics?.comunicationTotal || 0,
        type: 1,
      },
      {
        label: I18n.t('page_resume_text_invited'),
        value: buyStatistics?.invitedTotal || 0,
        type: 2,
      },
      {
        label: I18n.t('page_resume_text_unsuitable'),
        value: buyStatistics?.incompatibleTotal || 0,
        type: 3,
      },
    ];
    this.state = {
      actions,
      currentAction: actions[0],
    };
  }

  @computed get getStatus() {
    const { currentAction } = this.state;
    switch (currentAction.type) {
      case 0:
        return [1, 2];
      case 1:
        return [0];
      case 2:
        return [3, 4];
      case 3:
        return [-1];
    }
  }

  @computed get formatFilterData() {
    const { filterData } = this;
    if (!filterData) {
      return {};
    }
    const param = {};
    if (this.filterData) {
      const { filterData } = this;
      if (filterData.expectLocation) {
        param.expectLocationId = filterData.expectLocation.locationId;
      }
      param.fromAge = filterData.fromAge;
      param.toAge = filterData.toAge;
      param.sexId = filterData.sexId;
      if (filterData.workYear) {
        param.fromExperienceYear = filterData.workYear.min;
        param.toExperienceYear = filterData.workYear.max;
      }
      param.qualificationId = filterData.qualificationId;
      if (filterData.date) {
        param.deliverRecentDays = filterData.date.value;
      }
      if (filterData.liveLocation) {
        param.liveLocationId = filterData.liveLocation.locationId;
      }
    }
    return param;
  }

  componentDidMount() {
    global.emitter.on(constant.event.resumeJobChanged, this.onRefreshList);
    global.emitter.on(constant.event.resumeFilterDataChanged, this.onFilterRefresh);
    global.emitter.on(constant.event.resumeTabChanged, this.onTabChanged);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.resumeJobChanged, this.onRefreshList);
    global.emitter.off(constant.event.resumeFilterDataChanged, this.onFilterRefresh);
    global.emitter.on(constant.event.resumeTabChanged, this.onTabChanged);
  }

  onRefreshList = ({ item, tabIndex }) => {
    this.selectedJob = item;
    if (this.tabIndex == tabIndex) {
      this.onRefresh();
    }
  };

  onFilterRefresh = ({ data, tabIndex }) => {
    console.log('onFilterRefresh', data);
    this.filterData = data;
    this.onTabChanged({ tabIndex });
  };

  onTabChanged = ({ tabIndex }) => {
    if (this.tabIndex == tabIndex) {
      this.onRefresh();
    }
  };

  initData = async () => {
    this.pageFlatList.onRefresh();
    const res = await this.props.companyAction.getResumeStatistics({
      jobApplyType: 1,
      jobId: this.selectedJob?.id,
      ...this.formatFilterData,
    });
    const actions = [
      {
        label: I18n.t('page_home_text_pending_screening'),
        value: res?.chosenTotal || 0,
        type: 0,
      },
      {
        label: I18n.t('page_home_text_pending_communication'),
        value: res?.comunicationTotal || 0,
        type: 1,
      },
      {
        label: I18n.t('page_resume_text_invited'),
        value: res?.invitedTotal || 0,
        type: 2,
      },
      {
        label: I18n.t('page_resume_text_unsuitable'),
        value: res?.incompatibleTotal || 0,
        type: 3,
      },
    ];
    this.setState({ actions }, () => {});
  };

  onRefresh = async () => {
    await this.initData();
  };

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        status: this.getStatus,
        jobApplyType: 1,
        jobId: this.selectedJob?.id,
        ...this.formatFilterData,
      };
      const { queryApplications } = this.props.companyAction;
      const data = await queryApplications(param);
      if (data.fieldErrors) {
        return { totalCount: 0, result: [] };
      }
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item: { ...item, resumeId: item.cvId },
      isSearch: false,
      cancelCallback: this.onRefresh,
      filterType: this.state.currentAction.type,
      tabIndex: this.tabIndex,
    });
  };

  onTypePress = (item) => {
    this.setState({ currentAction: item }, () => {
      this.pageFlatList.onRefresh();
      if (item.type == 0) {
        this.menus = this.defaultMenus;
      } else if (item.type == 1) {
        this.menus = this.defaultMenus.filter((x) => x.type != 1);
      } else if (item.type == 2) {
        this.menus = this.defaultMenus.filter((x) => x.type == 2);
      }
    });
  };

  onCommunicated = async (item) => {
    await this.onUpdateStatus(item, 0);
  };

  onInappropriate = async (item) => {
    await this.onUpdateStatus(item, -1);
  };

  onInvite = (item) => {
    this.hideMenu(item, true);
  };

  hideMenu = (item, isInvite) => {
    const name = `popoverFun_${item?.id}_${item?.cvId}`;
    if (this[name] && this[name].setState) {
      this[name].setState({ isVisible: false }, () => {
        if (isInvite) {
          const { statistics } = this.props.companyStore;
          if (!statistics?.online) {
            return toast.show(I18n.t('page_resume_text_no_online_job'));
          }
          setTimeout(() => {
            this.inviteModal.wrappedInstance.show(item);
          }, 400);
        } else {
          global.emitter.emit(constant.event.resumeStatusChanged);
        }
      });
    }
  };

  onInviteConfirm = () => {
    global.emitter.emit(constant.event.resumeStatusChanged);
    this.onRefresh();
  };

  onUpdateStatus = async (item, status) => {
    Alert.alert(
      I18n.t('op_remind_title'),
      status === 0
        ? I18n.t('page_resume_text_confirm_pending_communication')
        : I18n.t('page_resume_text_confirm_unsuitable'),
      [
        {
          text: I18n.t('op_cancel_title'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.hideMenu(item);
              this.showGlobalLoading();
              const res = await this.props.companyAction.updateJobApplyStatusById(item?.id, status);
              this.showRequestResult(res?.message);
              this.onRefresh();
            } catch (error) {
              this.showRequestResult(error?.message);
            }
          },
        },
      ]
    );
  };

  renderItem = ({ item }) => {
    const { style } = this;
    const { currentAction } = this.state;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <View style={style.avatarContainer}>
              <Image
                source={
                  item.intervieweeAvatar ? { uri: item.intervieweeAvatar } : resIcon.defaultAvatar
                }
                style={style.itemAvatar}
                resizeMode="cover"
                onError={() => {
                  item.intervieweeAvatar = '';
                  this.forceUpdate();
                }}
              />
            </View>
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <View style={[style.flexRow, { flexShrink: 1 }]}>
                  <Text style={style.titleText}>{item.intervieweeName}</Text>
                  {item.fav ? (
                    <Icon name="star" size={16} color="#F4B022" type="antdesign" />
                  ) : null}
                </View>
                {item?.status?.value != -1 ? (
                  <Popover
                    ref={(refs) => (this[`popoverFun_${item.id}_${item.cvId}`] = refs)}
                    from={
                      <Touchable style={style.flexRowPopver}>
                        <Text style={style.statusText}>{currentAction?.label}</Text>
                        <Image source={resIcon.iconDownEnterprise} style={style.statusImg} />
                      </Touchable>
                    }
                    menus={this.menus}
                    data={item}
                  />
                ) : (
                  <Text style={style.statusText}>{item?.status?.label}</Text>
                )}
              </View>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_work_experience')}：
                {I18n.t('page_resume_text_years', { count: item.careerExperience || 0 })}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_label_career_edu')}：{item?.qualificationId?.label || '-'}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_school')}：{item.schoolName || '-'}
              </Text>
            </View>
          </View>
          {item?.applyType?.value == 0 ? (
            <>
              <View style={style.sapartLine} />
              <View style={style.bottomContainer}>
                <View style={style.filterStatusBox}>
                  <Text style={style.filterStatusText}>{I18n.t('page_mine_label_has_send')}</Text>
                </View>
                <Text style={style.filterStatusTextMain}>{item.jobTitle}</Text>
              </View>
            </>
          ) : null}
        </View>
      </Touchable>
    );
  };

  renderFilter = () => {
    const { actions, currentAction } = this.state;
    const { style } = this;
    return (
      <View style={style.filterContainer}>
        <View style={style.sectionContent}>
          {actions?.map((item, index) => (
            <Touchable
              style={[
                style.sectionContentItem,
                index == actions.length - 1 ? style.sectionContentItemLast : {},
                item.type == currentAction.type ? style.sectionContentItemActive : {},
                I18n.locale != 'en' ? { justifyContent: 'center' } : {},
              ]}
              key={index}
              onPress={() => this.onTypePress(item)}
            >
              <Text
                style={[
                  style.sectionContentItemValue,
                  item.type == currentAction.type ? style.sectionContentItemLabelActive : {},
                ]}
              >
                {item.value > 9999 ? '9999+' : item.value}
              </Text>
              <Text
                style={[
                  style.sectionContentItemLabel,
                  item.type == currentAction.type ? style.sectionContentItemLabelActive : {},
                ]}
              >
                {item.label}
              </Text>
            </Touchable>
          ))}
        </View>
        {/* <Text style={style.filterText}>
          已选：<Text style={style.filterTextMain}>{`广州  |  本科`}</Text>
        </Text> */}
      </View>
    );
  };

  render() {
    return (
      <>
        {this.renderFilter()}
        <PageFlatList
          ref={this.initPageFlatList}
          onRefresh={this.onRefresh}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
        <InviteModal ref={(ref) => (this.inviteModal = ref)} onConfirm={this.onInviteConfirm} />
      </>
    );
  }
}
