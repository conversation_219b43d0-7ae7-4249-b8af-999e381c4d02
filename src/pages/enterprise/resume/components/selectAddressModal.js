import React, { Component } from 'react';
import { View, Text, Image, Touchable } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import PageFlatList from '../../../../components/list/pageFlatList';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 20,
    },
    itemContainer: {
      flex: 1,
      flexShrink: 1,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
      paddingVertical: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    titleText: {
      fontSize: 15,
      color: theme.titleFontColor,
      flexShrink: 1,
      marginRight: 10,
    },
  };
}
/**
 * 选择地址弹出框
 * <AUTHOR>
 */
export default class SelectAddressModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      selectedAddress: null,
    };
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  show = (selectedAddress) => {
    this.setState({ selectedAddress, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = async (item) => {
    if (this.props.onConfirm) {
      this.props.onConfirm(item);
    }
    this.close();
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const { selectedAddress } = this.state;
    const checked = selectedAddress?.address == item.address;
    return (
      <View style={style.itemBox} key={index}>
        <Touchable style={style.itemContainer} onPress={() => this.onConfirm(item)}>
          <Text style={[style.titleText, checked ? { color: '#EF3D48' } : {}]}>{item.address}</Text>
          {checked ? <Image source={resIcon.resumeCheckedEnterprise} /> : null}
        </Touchable>
      </View>
    );
  };

  render() {
    const { list, ...rest } = this.props;
    const { showModal } = this.state;
    const { style } = this;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_resume_title_select_interview_address')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={440}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={false}
        showBottomView={false}
        coverScreen
        {...rest}
      >
        <View style={style.pagerContainer}>
          <PageFlatList
            ref={this.initPageFlatList}
            data={list}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={() => <View style={{ height: 36 }} />}
          />
        </View>
      </BottomModal>
    );
  }
}
