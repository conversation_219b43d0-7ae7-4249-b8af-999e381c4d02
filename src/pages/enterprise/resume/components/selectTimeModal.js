import React, { Component } from 'react';
import { View, Picker } from '../../../../components';
import moment from 'moment';
import util from '../../../../util';
import BottomModal from '../../../../components/modal/bottomModal';
import I18n from '../../../../i18n';

/**
 * 选择日期时间弹出框
 * author: sxw
 */
export default class SelectTimeModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      yearsData: [],
      monthsData: [],
      daysData: [],
      hoursData: [],
      minutesData: [],
      selectedYear: util.getNowYear().toString(),
      selectedMonth: util.getNowMonth().toString(),
      selectedDay: util.getNowDay().toString(),
      selectedHour: util.getNowHour().toString(),
      selectedMinute: util.getNowMinute().toString(),
      confirmTimes: 0,
    };
  }

  initData = (date) => {
    const { selectedYear, selectedMonth, selectedDay } = this.state;
    this.getYearsData();
    this.getMonthsData();
    this.getHoursData();
    this.getMinutesData();
    let yearText = '';
    let monthText = '';
    let dayText = '';
    let hourText = '';
    let minuteText = '';

    const m = date ? moment(date, 'YYYY-MM-DD HH:mm:ss') : moment();
    yearText = m.get('year');
    monthText = m.get('month');
    dayText = m.get('date');
    hourText = this.formatDig(m.get('hour'));
    minuteText = this.formatDig(m.get('minute'));

    this.setState({
      selectedYear: yearText.toString(),
      selectedMonth: (monthText + 1).toString(),
      selectedDay: dayText.toString(),
      selectedHour: hourText.toString(),
      selectedMinute: minuteText.toString(),
    });
    this.getDaysData(yearText || selectedYear, monthText ? monthText + 1 : selectedMonth);
  };

  getYearsData = () => {
    if (this.state.yearsData.length > 0) {
      return;
    }
    const yearsArray = [];
    const maxYear = moment().year() + 76;
    for (let i = moment().year(); i <= maxYear; i += 1) {
      yearsArray.push(i.toString());
    }
    this.setState({ yearsData: yearsArray });
  };

  getMonthsData = () => {
    if (this.state.monthsData.length > 0) {
      return;
    }
    const monthsArray = [];
    for (let i = 1; i <= 12; i += 1) {
      monthsArray.push(i.toString());
    }
    this.setState({ monthsData: monthsArray });
  };

  getDaysData = (selectedYear, selectedMonth) => {
    const maxDay = moment(
      `${selectedYear}-${this.formatDig(selectedMonth)}`,
      'YYYY-MM'
    ).daysInMonth();
    const daysArray = [];
    for (let i = 1; i <= maxDay; i += 1) {
      daysArray.push(i.toString());
    }
    this.setState({ daysData: daysArray });
  };

  getHoursData = () => {
    if (this.state.hoursData.length > 0) {
      return;
    }
    const hoursArray = [];
    for (let i = 0; i <= 23; i += 1) {
      hoursArray.push(this.formatDig(i));
    }
    this.setState({ hoursData: hoursArray });
  };

  getMinutesData = () => {
    if (this.state.minutesData.length > 0) {
      return;
    }
    const minutesArray = [];
    for (let i = 0; i <= 59; i += 1) {
      minutesArray.push(this.formatDig(i));
    }
    this.setState({ minutesData: minutesArray });
  };

  onSpliceText = ({ year, month, day, hour, minute }) => {
    const newMonth = this.formatDig(month);
    const newDay = this.formatDig(day);
    if (this.props.onConfirm) {
      const params = {
        date: `${year}/${newMonth}/${newDay} ${hour}:${minute}`,
      };
      this.props.onConfirm(params);
    }
  };

  onConfirm = () => {
    const { selectedYear, selectedMonth, selectedDay, selectedHour, selectedMinute } = this.state;
    this.setState({ confirmTimes: this.state.confirmTimes + 1 }, () => {
      if (this.state.confirmTimes > 1) {
        this.onSpliceText({
          year: selectedYear,
          month: selectedMonth,
          day: selectedDay,
          hour: selectedHour,
          minute: selectedMinute,
        });
        this.hide();
      }
    });
  };

  formatDig = (num) => (parseInt(num, 10) > 9 ? `${num}` : `0${num}`);

  show = (date) => {
    this.setState({ isOpen: true });
    this.initData(date);
  };

  hide = () => {
    this.setState({ isOpen: false, confirmTimes: 0 });
  };

  getDefaultValue = () => {
    const date = new Date();
    const y = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    return [y, month, day];
  };

  renderDayPicker = () => {
    const {
      yearsData,
      monthsData,
      daysData,
      hoursData,
      minutesData,
      selectedYear,
      selectedMonth,
      selectedDay,
      selectedHour,
      selectedMinute,
      confirmTimes,
    } = this.state;
    return (
      <View style={{ flexDirection: 'row' }}>
        {confirmTimes == 0 ? (
          <>
            <Picker
              selectedValue={selectedYear}
              style={{ flex: 1 }}
              itemStyle={{ fontSize: 18 }}
              onValueChange={(itemValue) => {
                this.setState({ selectedYear: itemValue });
                this.getDaysData(itemValue, selectedMonth);
              }}
            >
              {yearsData.map((item, index) => this.renderPickerItem(item, index))}
            </Picker>
            <Picker
              selectedValue={selectedMonth}
              style={{ flex: 1 }}
              itemStyle={{ fontSize: 18 }}
              onValueChange={(itemValue) => {
                this.setState({ selectedMonth: itemValue });
                this.getDaysData(selectedYear, itemValue);
              }}
            >
              {monthsData.map((item, index) => this.renderPickerItem(item, index))}
            </Picker>
            <Picker
              selectedValue={selectedDay}
              style={{ flex: 1 }}
              itemStyle={{ fontSize: 18 }}
              onValueChange={(itemValue) => {
                this.setState({ selectedDay: itemValue });
              }}
            >
              {daysData.map((item, index) => this.renderPickerItem(item, index))}
            </Picker>
          </>
        ) : null}
        {confirmTimes > 0 ? (
          <>
            <Picker
              selectedValue={selectedHour}
              style={{ flex: 1 }}
              itemStyle={{ fontSize: 18 }}
              onValueChange={(itemValue) => {
                this.setState({ selectedHour: itemValue });
              }}
            >
              {hoursData.map((item, index) => this.renderPickerItem(item, index))}
            </Picker>
            <Picker
              selectedValue={selectedMinute}
              style={{ flex: 1 }}
              itemStyle={{ fontSize: 18 }}
              onValueChange={(itemValue) => {
                this.setState({ selectedMinute: itemValue });
              }}
            >
              {minutesData.map((item, index) => this.renderPickerItem(item, index))}
            </Picker>
          </>
        ) : null}
      </View>
    );
  };

  renderPickerItem = (key, i) => <Picker.Item key={i} label={key} value={key} color="#333" />;

  render() {
    const { title = I18n.t('page_mine_interview_time'), ...rest } = this.props;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={title}
        showCancel={false}
        rightTitle={I18n.t('op_complete_title')}
        rightAction={this.onConfirm}
        contentHeight={200}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={this.state.isOpen}
        btnSize="lg"
        coverScreen
        {...rest}
      >
        {this.renderDayPicker()}
      </BottomModal>
    );
  }
}
