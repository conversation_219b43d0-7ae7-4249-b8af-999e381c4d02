import React, { Component } from 'react';
import { View, Text, Image, Touchable } from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import PageFlatList from '../../../../components/list/pageFlatList';
import constant from '../../../../store/constant';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 20,
    },
    itemContainer: {
      // flex: 1,
      // flexShrink: 1,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
      paddingVertical: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    titleText: {
      fontSize: 15,
      color: theme.titleFontColor,
      flexShrink: 1,
      marginRight: 10,
    },
    allText: {
      fontSize: 15,
      color: theme.primaryColor,
    },
  };
}
/**
 * 简历筛选职位弹出框
 * <AUTHOR>
 */
@inject('jobStore', 'resumeStore', 'companyAction')
@observer
export default class SelectJobModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      selected: null,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.showSelectJobModal, this.onShow);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.showSelectJobModal, this.onShow);
  }

  onShow = (param) => {
    if (!param || this.props.page !== param.page) return;
    this.param = param;
    this.show(param.data);
  };

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 10,
        online: true,
      };
      const { queryJobs } = this.props.companyAction;
      const res = await queryJobs(param);
      // if (!(this.param || this.props).noNeedAll) {
      //   res.result.unshift({ id: 0, title: I18n.t('page_job_company_all_job') });
      // }
      return res;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  show = (selected) => {
    this.setState({ selected, showModal: true });
  };

  close = () => {
    this.setState({ showModal: false });
  };

  onConfirm = (item) => {
    if (this.param?.onConfirm) {
      this.param.onConfirm(item);
      this.param = null;
    } else {
      this.props.onConfirm?.(item);
    }
    this.close();
  };

  onJobSelected = (item) => {
    this.setState({ selected: item });
    this.onConfirm(item);
  };

  onRightAction = () => {
    this.onConfirm({ id: 0, title: I18n.t('page_job_company_all_job') });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const { selected } = this.state;
    const checked = selected?.id == item.id;
    return (
      <View style={style.itemBox} key={index}>
        <Touchable style={style.itemContainer} onPress={() => this.onJobSelected(item)}>
          <Text style={[style.titleText, checked ? { color: '#EF3D48' } : {}]}>{item.title}</Text>
          {checked ? <Image source={resIcon.resumeCheckedEnterprise} /> : null}
        </Touchable>
      </View>
    );
  };

  itemSeparatorComponent = () => <View style={this.style.saparateLine} />;

  render() {
    const { title = I18n.t('page_resume_text_has_published_jobs'), ...rest } = this.props;
    const { showModal } = this.state;
    const { style } = this;
    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={this.param?.title || title}
        showCancel={true}
        // rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightComponent={<Text style={style.allText}>{I18n.t('page_job_company_all_job')}</Text>}
        rightAction={this.onRightAction}
        contentHeight={440}
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        useScrollContent={false}
        showBottomView={false}
        useNativeDriver={true}
        coverScreen
        {...rest}
      >
        <View style={style.pagerContainer}>
          <PageFlatList
            ref={this.initPageFlatList}
            loadData={this.loadData}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            // ItemSeparatorComponent={this.itemSeparatorComponent}
          />
        </View>
      </BottomModal>
    );
  }
}
