import React from 'react';
import {
  View,
  Text,
  Image,
  Touchable,
  BaseComponent,
  Button,
  Input,
  InteractionManager,
  KeyboardAwareScrollView,
} from '../../../../components/index';
import styles from '../../../../themes/enterprise';
import I18n from '../../../../i18n';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import resIcon from '../../../../res';
import RightArrow from '../../../../components/rightArrow';
import SelectJobModal from './selectJobModal';
import SelectAddressModal from './selectAddressModal';
import SelectTimeModal from './selectTimeModal';
import moment from 'moment';
import LoadingModal from '../../../../components/loadingModal';
import promiseUtil from '../../../../util/promiseUtil';
import constant from '../../../../store/constant';
import companyAction from '../../../../store/actions/company';

function getComponentStyle(theme) {
  return {
    contentContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 18,
    },
    sectionBox: {
      marginBottom: 20,
      flexDirection: 'column',
    },
    itemCellContainer: {
      // marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 10,
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 30,
      width: '100%',
    },
  };
}
/**
 * 邀请面试
 * <AUTHOR>
 */
@inject('jobStore', 'resumeStore', 'companyStore')
@observer
export default class InviteModal extends BaseComponent {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    const { companyInfo } = props.companyStore;
    this.state = {
      showModal: false,
      selectedJob: null,
      timeData: null,
      contact: {
        name: companyInfo?.contact?.name,
        telephone: companyInfo?.mobile || companyInfo?.contact?.telephone,
      },
      selectedAddress: null,
      showLoading: false,
      item: null,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.showInviteModal, this.onShow);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.showInviteModal, this.onShow);
  }

  onShow = (param) => {
    if (!param || this.props.page !== param.page) return;
    this.param = param;
    this.show(param.data);
  };

  show = (item) => {
    console.debug('InviteModal show', item.id, item.employerId, item);
    InteractionManager.runAfterInteractions(() => {
      this.setState({ item });
      this.getRelateJob(item);
    });
  };

  close = () => {
    const { companyInfo } = this.props.companyStore;
    this.setState({
      selectedJob: null,
      timeData: null,
      contact: {
        name: companyInfo?.contact?.name,
        telephone: companyInfo?.mobile || companyInfo?.contact?.telephone,
      },
      selectedAddress: null,
      showModal: false,
    });
  };

  getRelateJob = async (item) => {
    try {
      let defaultJob = this.param?.job;
      if (!defaultJob && this.param?.jobId && this.param.jobId !== '0') {
        defaultJob = await companyAction.queryJobsByJobId(this.param.jobId);
      }
      if (!defaultJob) {
        const param = {
          page: 1,
          size: 1,
          online: true,
          categoryIds: item?.categoryId?.code
            ? [item.categoryId.value]
            : item?.intention?.categoryIds?.map((x) => x.value) || null,
        };
        if (!param.categoryIds) {
          delete param.categoryIds;
        }
        const res = await companyAction.queryJobs(param);
        defaultJob = res?.result?.length ? res.result[0] : null;
      }
      this.setState({
        selectedJob: defaultJob || null,
        timeData: null,
        // contact: defaultJob
        //   ? {
        //       name: defaultJob.contact.name,
        //       telephone: defaultJob.contact.telephone,
        //     }
        //   : null,
        selectedAddress: defaultJob?.locations?.length ? defaultJob.locations[0] : null,
        showModal: true,
      });
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onConfirm = async () => {
    try {
      const { selectedJob, timeData, contact, selectedAddress, item } = this.state;
      const { companyInfo } = this.props.companyStore;
      const cvId = item?.resumeId || item?.cvId;

      if (!selectedJob) {
        return this.modal.onShow(I18n.t('page_resume_text_select_interview_position'));
      }
      if (!timeData) {
        return this.modal.onShow(I18n.t('page_resume_text_select_interview_time'));
      }
      const actionTime = moment(timeData.date, 'YYYY/MM/DD HH:mm');
      if (actionTime.isBefore(moment())) {
        return this.modal.onShow(I18n.t('page_resume_text_select_interview_time_error'));
      } else if (!selectedAddress) {
        return this.modal.onShow(I18n.t('page_resume_text_select_interview_address'));
      } else if (!contact?.telephone?.trim()) {
        return this.modal.onShow(I18n.t('page_resume_text_input_contact_phone'));
      } else if (!contact?.name?.trim()) {
        return this.modal.onShow(I18n.t('page_resume_text_input_contact_name'));
      }

      const param = {
        actionAddress: selectedAddress.address,
        actionConact: contact.name,
        actionMobile: contact.telephone,
        actionTime: actionTime.format(),
        cvId,
        employerId: item?.employerId || companyInfo?.employerId,
        jobId: selectedJob.id,
        jobTitle: selectedJob.title,
        seekerId: item?.seekerId,
        id: item?.id,
      };
      this.setState({ showLoading: true });
      const res = await companyAction.inviteInterview(param);
      if (!item?.id) {
        await companyAction.updateResumeTags({
          cvId,
          tag: 2,
          tagType: 2,
        });
      } else {
        await companyAction.updateJobApplyStatusById(item?.id, 3);
      }
      this.setState({ showLoading: false });
      this.modal.onShow(res?.message);
      await promiseUtil.sleep(800);
      if (res?.successful) {
        if (this.param?.onConfirm) {
          this.param?.onConfirm(param, this.param);
          this.param = null;
        } else {
          this.props.onConfirm?.(param);
        }
        this.close();
      }
    } catch (error) {
      this.modal.onShow(error?.message);
      this.setState({ showLoading: false });
    }
  };

  onSelectJob = (item) => {
    this.setState({
      selectedJob: item,
      contact: item.contact,
      selectedAddress: item?.locations?.length ? item.locations[0] : null,
    });
  };

  onSelectAddress = (item) => {
    this.setState({ selectedAddress: item });
  };

  onSelectTime = (item) => {
    this.setState({ timeData: item });
  };

  onChangeText = (key, value) => {
    const { contact } = this.state;
    this.setState({ contact: { ...contact, [key]: value } });
  };

  renderItem = ({
    label,
    value,
    placeholder = I18n.t('page_resume_tips_select'),
    onPress,
    showArrow = true,
    isInput,
    onChangeText,
    maxLength = 30,
    inputType,
  }) => {
    const { style } = this;
    return (
      <View style={style.sectionBox}>
        <View style={style.itemCellContainer}>
          <View style={style.itemHeaderContainer}>
            <View style={style.labelBox}>
              <Text style={style.labelText}>{label} </Text>
            </View>
          </View>
          {isInput ? (
            <Input
              placeholder={placeholder}
              placeholderTextColor="#CCCCCC"
              selectionColor="#EF3D48"
              isInput={false}
              style={{ color: '#333', fontSize: 16 }}
              onChangeText={onChangeText}
              maxLength={maxLength}
              value={value}
              inputType={inputType}
              returnKeyType="done"
            />
          ) : (
            <Touchable style={style.valueTextBox} onPress={onPress}>
              <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
                {value || placeholder}
              </Text>
              {showArrow ? <RightArrow useImgArrow /> : null}
            </Touchable>
          )}
        </View>
      </View>
    );
  };

  renderButtonGroup = ({ style }) => {
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('op_cancel_title')}
          btnType="reset"
          btnSize="s44"
          containerStyle={{ flex: 2.5, marginRight: 15 }}
          onPress={this.close}
          outline
        />
        <Button
          title={I18n.t('page_msg_confirm_text')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ flex: 5 }}
          onPress={this.onConfirm}
        />
      </View>
    );
  };

  render() {
    const { ...rest } = this.props;
    const { showModal, selectedJob, timeData, selectedAddress, contact, showLoading } = this.state;
    const { style } = this;
    const Item = this.renderItem;
    const ButtonGroup = this.renderButtonGroup;

    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_resume_text_interview')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={500}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        btnSize="lg"
        iconCancel
        showBottomView={false}
        useScrollContent={false}
        coverScreen
        {...rest}
      >
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always"
          style={{ flex: 1 }}
        >
          <View style={style.contentContainer}>
            <Item
              label={I18n.t('page_interview_text_interview_position')}
              value={selectedJob?.title || ''}
              onPress={() => this.selectJobModal.wrappedInstance.show(selectedJob)}
            />
            <Item
              label={I18n.t('page_mine_interview_time')}
              value={timeData?.date || ''}
              onPress={() => this.selectTimeModal.show(timeData?.date)}
            />
            <Item
              label={I18n.t('page_mine_interview_address')}
              value={selectedAddress?.address || ''}
              onPress={() => this.selectAddressModal.show(selectedAddress)}
            />
            <Item
              label={I18n.t('page_job_text_phone')}
              value={contact?.telephone || ''}
              showArrow={false}
              isInput
              inputType="phone"
              maxLength={16}
              placeholder={I18n.t('page_resume_tips_input')}
              onChangeText={(v) => this.onChangeText('telephone', v)}
            />
            <Item
              label={I18n.t('page_mine_interview_contact')}
              value={contact?.name || ''}
              showArrow={false}
              isInput
              placeholder={I18n.t('page_resume_tips_input')}
              onChangeText={(v) => this.onChangeText('name', v)}
            />
          </View>
          <ButtonGroup style={style} />
          <View style={{ height: 30 }} />
        </KeyboardAwareScrollView>

        <SelectJobModal
          ref={(ref) => (this.selectJobModal = ref)}
          onConfirm={this.onSelectJob}
          noNeedAll
          title={I18n.t('page_resume_title_select_interview_position')}
        />
        <SelectAddressModal
          ref={(ref) => (this.selectAddressModal = ref)}
          onConfirm={this.onSelectAddress}
          list={selectedJob?.locations || []}
        />
        <SelectTimeModal
          ref={(ref) => (this.selectTimeModal = ref)}
          onConfirm={this.onSelectTime}
          title={I18n.t('page_resume_title_select_interview_time')}
        />
        <LoadingModal isOpen={showLoading} />
      </BottomModal>
    );
  }
}
