import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Text,
  Touchable,
  View,
  Image,
  Icon,
  Popover,
  BaseComponent,
  Alert,
} from '../../../../components';
import styles from '../../../../themes/enterprise';
import PageFlatList from '../../../../components/list/pageFlatList';
import I18n from '../../../../i18n';
import NavigationService from '../../../../navigationService';
import resIcon from '../../../../res';
import { computed } from 'mobx';
import constant from '../../../../store/constant';
import InviteModal from './inviteModal';
import ResumeItem from '../../../../components/listItem/resumeItem';

/**
 * 列表
 */
@inject('companyStore', 'companyAction')
@observer
export default class AllList extends BaseComponent {
  style = styles.get('resumeList');

  constructor(props) {
    super(props);
    this.defaultMenus = [
      {
        icon: null,
        text: I18n.t('page_home_text_pending_communication'),
        style: this.style,
        onPress: this.onCommunicated,
        type: 1,
      },
      {
        icon: null,
        text: I18n.t('page_resume_text_unsuitable'),
        style: this.style,
        onPress: this.onInappropriate,
        type: 2,
      },
      {
        icon: null,
        text: I18n.t('page_resume_text_interview'),
        style: this.style,
        onPress: this.onInvite,
        type: 3,
      },
    ];
    this.menus = this.defaultMenus;
    this.initType = props.navigation.state.params?.initType || 0;
    this.tabIndex = props.tabIndex;
    this.filterData = props.filterData;
    this.selectedJob = props.selectedJob;
    const { applicationsStatistics } = props.companyStore;
    const actions = [
      {
        label: I18n.t('page_home_text_pending_screening'),
        value: applicationsStatistics?.chosenTotal || 0,
        type: 0,
      },
      {
        label: I18n.t('page_home_text_pending_communication'),
        value: applicationsStatistics?.comunicationTotal || 0,
        type: 1,
      },
      {
        label: I18n.t('page_resume_text_invited'),
        value: applicationsStatistics?.invitedTotal || 0,
        type: 2,
      },
      {
        label: I18n.t('page_resume_text_unsuitable'),
        value: applicationsStatistics?.incompatibleTotal || 0,
        type: 3,
      },
    ];
    this.state = {
      actions,
      currentAction: actions[this.initType],
    };
  }

  @computed get getStatus() {
    const { currentAction } = this.state;
    switch (currentAction.type) {
      case 0:
        return [1, 2];
      case 1:
        return [0];
      case 2:
        return [3, 4];
      case 3:
        return [-1];
    }
  }

  @computed get formatFilterData() {
    const { filterData } = this;
    if (!filterData) {
      return {};
    }
    const param = {};
    if (this.filterData) {
      const { filterData } = this;
      if (filterData.expectLocation) {
        param.expectLocationId = filterData.expectLocation.locationId;
      }
      param.fromAge = filterData.fromAge;
      param.toAge = filterData.toAge;
      param.sexId = filterData.sexId;
      if (filterData.workYear) {
        param.fromExperienceYear = filterData.workYear.min;
        param.toExperienceYear = filterData.workYear.max;
      }
      param.qualificationId = filterData.qualificationId;
      if (filterData.date) {
        param.deliverRecentDays = filterData.date.value;
      }
      if (filterData.liveLocation) {
        param.liveLocationId = filterData.liveLocation.locationId;
      }
    }
    return param;
  }

  componentDidMount() {
    global.emitter.on(constant.event.resumeJobChanged, this.onRefreshList);
    global.emitter.on(constant.event.homeResumeFilterChanged, this.onHomeFilterRefresh);

    global.emitter.on(constant.event.resumeFilterDataChanged, this.onFilterRefresh);
    global.emitter.on(constant.event.resumeTabChanged, this.onTabChanged);
    global.emitter.on(constant.event.resumeStatusChanged, this.onRefresh);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.resumeJobChanged, this.onRefreshList);
    global.emitter.off(constant.event.homeResumeFilterChanged, this.onHomeFilterRefresh);

    global.emitter.off(constant.event.resumeFilterDataChanged, this.onFilterRefresh);
    global.emitter.on(constant.event.resumeTabChanged, this.onTabChanged);
    global.emitter.off(constant.event.resumeStatusChanged, this.onRefresh);
  }

  onRefreshList = ({ item, tabIndex }) => {
    this.selectedJob = item;
    if (this.tabIndex == tabIndex) {
      this.onRefresh();
    }
  };

  onHomeFilterRefresh = ({ initType }) => {
    this.setState({ currentAction: this.state.actions[initType] }, () => {
      this.filterData = null;
      this.selectedJob = null;
      this.onRefresh();
    });
  };

  onFilterRefresh = ({ data, tabIndex }) => {
    this.filterData = data;
    this.onTabChanged({ tabIndex });
  };

  onTabChanged = ({ tabIndex }) => {
    if (this.tabIndex == tabIndex) {
      this.onRefresh();
    }
  };

  initData = async () => {
    await this.props.companyAction.getApplicationsStatistics({
      jobId: this.selectedJob?.id,
      ...this.formatFilterData,
    });
    const { applicationsStatistics } = this.props.companyStore;
    const actions = [
      {
        label: I18n.t('page_home_text_pending_screening'),
        value: applicationsStatistics?.chosenTotal || 0,
        type: 0,
      },
      {
        label: I18n.t('page_home_text_pending_communication'),
        value: applicationsStatistics?.comunicationTotal || 0,
        type: 1,
      },
      {
        label: I18n.t('page_resume_text_invited'),
        value: applicationsStatistics?.invitedTotal || 0,
        type: 2,
      },
      {
        label: I18n.t('page_resume_text_unsuitable'),
        value: applicationsStatistics?.incompatibleTotal || 0,
        type: 3,
      },
    ];
    this.setState({ actions });
  };

  onRefresh = async () => {
    await this.initData();
    this.pageFlatList.onRefresh();
  };

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        status: this.getStatus,
        jobId: this.selectedJob?.id,
        ...this.formatFilterData,
      };
      const { queryApplications } = this.props.companyAction;
      const data = await queryApplications(param);
      if (data.fieldErrors) {
        return { totalCount: 0, result: [] };
      }
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item: { ...item, resumeId: item.cvId },
      isSearch: false,
      cancelCallback: this.onRefresh,
      filterType: this.state.currentAction.type,
      tabIndex: this.tabIndex,
    });
  };

  onTypePress = (item) => {
    this.setState({ currentAction: item }, () => {
      this.pageFlatList.onRefresh();
      if (item.type == 0) {
        this.menus = this.defaultMenus;
      } else if (item.type == 1) {
        this.menus = this.defaultMenus.filter((x) => x.type != 1);
      } else if (item.type == 2) {
        this.menus = this.defaultMenus.filter((x) => x.type == 2);
      }
    });
  };

  onCommunicated = async (item) => {
    await this.onUpdateStatus(item, 0);
  };

  onInappropriate = async (item) => {
    await this.onUpdateStatus(item, -1);
  };

  onInvite = (item) => {
    this.hideMenu(item, true);
  };

  hideMenu = (item, isInvite) => {
    const name = `popoverFun_${item?.id}_${item?.cvId}`;
    if (this[name] && this[name].setState) {
      this[name].setState({ isVisible: false }, () => {
        if (isInvite) {
          const { statistics } = this.props.companyStore;
          if (!statistics?.online) {
            return toast.show(I18n.t('page_resume_text_no_online_job'));
          }
          setTimeout(() => {
            this.inviteModal.wrappedInstance.show(item);
          }, 400);
        } else {
          global.emitter.emit(constant.event.resumeStatusChanged);
        }
      });
    }
  };

  onInviteConfirm = () => {
    global.emitter.emit(constant.event.resumeStatusChanged);
    this.onRefresh();
  };

  onUpdateStatus = async (item, status) => {
    Alert.alert(
      I18n.t('op_remind_title'),
      status === 0
        ? I18n.t('page_resume_text_confirm_pending_communication')
        : I18n.t('page_resume_text_confirm_unsuitable'),
      [
        {
          text: I18n.t('op_cancel_title'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.hideMenu(item);
              this.showGlobalLoading();
              const res = await this.props.companyAction.updateJobApplyStatusById(item?.id, status);
              this.showRequestResult(res?.message);
              this.onRefresh();
            } catch (error) {
              this.showRequestResult(error?.message);
            }
          },
        },
      ]
    );
  };

  renderPopover = (item) => {
    const { style } = this;
    const { currentAction } = this.state;
    return item.status?.value !== -1 ? (
      <Popover
        ref={(refs) => (this[`popoverFun_${item.id}_${item.cvId}`] = refs)}
        from={
          <Touchable style={style.flexRowPopver}>
            <Text style={style.statusText}>{currentAction?.label}</Text>
            <Image source={resIcon.iconDownEnterprise} style={style.statusImg} />
          </Touchable>
        }
        menus={this.menus}
        data={item}
      />
    ) : (
      <Text style={style.statusText}>{item.status?.label}</Text>
    );
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable key={item.id} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <ResumeItem style={style} item={item} renderPopover={this.renderPopover} />
          {item?.applyType?.value == 0 ? (
            <>
              <View style={style.sapartLine} />
              <View style={style.bottomContainer}>
                <View style={style.filterStatusBox}>
                  <Text style={style.filterStatusText}>{I18n.t('page_mine_label_has_send')}</Text>
                </View>
                <Text style={style.filterStatusTextMain}>{item.jobTitle}</Text>
              </View>
            </>
          ) : null}
        </View>
      </Touchable>
    );
  };

  renderFilter = () => {
    const { actions, currentAction } = this.state;
    const { style } = this;
    return (
      <View style={style.filterContainer}>
        <View style={style.sectionContent}>
          {actions?.map((item, index) => (
            <Touchable
              style={[
                style.sectionContentItem,
                index == actions.length - 1 ? style.sectionContentItemLast : {},
                item.type == currentAction.type ? style.sectionContentItemActive : {},
                I18n.locale != 'en' ? { justifyContent: 'center' } : {},
              ]}
              key={index}
              onPress={() => this.onTypePress(item)}
            >
              <Text
                style={[
                  style.sectionContentItemValue,
                  item.type == currentAction.type ? style.sectionContentItemLabelActive : {},
                ]}
              >
                {item.value > 9999 ? '9999+' : item.value}
              </Text>
              <Text
                style={[
                  style.sectionContentItemLabel,
                  item.type == currentAction.type ? style.sectionContentItemLabelActive : {},
                ]}
              >
                {item.label}
              </Text>
            </Touchable>
          ))}
        </View>
        {/* <Text style={style.filterText}>
          已选：<Text style={style.filterTextMain}>{`广州  |  本科`}</Text>
        </Text> */}
      </View>
    );
  };

  render() {
    return (
      <>
        {this.renderFilter()}
        <PageFlatList
          ref={this.initPageFlatList}
          onRefresh={this.onRefresh}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
        <InviteModal ref={(ref) => (this.inviteModal = ref)} onConfirm={this.onInviteConfirm} />
      </>
    );
  }
}
