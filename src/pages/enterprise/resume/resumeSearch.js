import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Header,
  Text,
  Touchable,
  View,
  Image,
  Icon,
  Input,
  Popover,
  BaseComponent,
} from '../../../components';
import styles from '../../../themes/enterprise';
import PageFlatList from '../../../components/list/pageFlatList';
import I18n from '../../../i18n';
import constant from '../../../store/constant';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
import util from '../../../util';

function getComponentStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 12,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    itemTopRightContainer: {
      marginLeft: 16,
      marginTop: 10,
      flex: 1,
    },
    itemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    itemNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      paddingLeft: 16,
      paddingVertical: 10,
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginRight: 10,
      flexShrink: 1,
    },
    sencondText: {
      fontSize: 13,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
    },
    filterContainer: {
      backgroundColor: '#fff',
      paddingBottom: 10,
    },
    sectionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      paddingHorizontal: 8,
      paddingTop: 5,
    },
    filterText: {
      fontSize: theme.fontSizeS,
      color: theme.minorFontColor,
      paddingHorizontal: 12,
      paddingTop: 10,
    },
    filterTextMain: {
      fontSize: theme.fontSizeS,
      color: theme.titleFontColor,
    },
    sapartLine: {
      height: 1,
      backgroundColor: '#EEEEEE',
      marginTop: 10,
      marginBottom: 10,
    },
    commonInputContainer: {
      borderBottomColor: 'transparent',
      backgroundColor: '#F7F7F7',
      borderRadius: 5,
      height: 38,
      paddingLeft: 9,
      borderBottomWidth: 0,
    },
    inputStyle: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeM,
    },
    filterBox: {
      position: 'relative',
      width: 50,
      height: 30,
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    filterDot: {
      position: 'absolute',
      top: -3,
      right: 0,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF4A55',
    },
  };
}

/**
 * 搜索人才
 */
@inject('companyAction')
@observer
export default class ResumeSearch extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.menus = [
      {
        icon: null,
        text: I18n.t('page_home_text_pending_communication'),
        style: this.style,
        onPress: this.onCommunicated,
      },
      {
        icon: null,
        text: I18n.t('page_resume_text_unsuitable'),
        style: this.style,
        onPress: this.onInappropriate,
      },
    ];
    this.state = {
      filterText: '',
      filterData: null,
    };
  }

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 10;
      const param = {
        offset: (page - 1) * limit,
        limit,
        keyWord: this.state.filterText,
        searchLevel: true,
      };
      const { filterData } = this.state;
      if (filterData) {
        if (filterData.expectLocation) {
          param.expectLocationId = filterData.expectLocation.locationId;
        }
        param.fromAge = filterData.fromAge;
        param.toAge = filterData.toAge;
        param.sexId = filterData.sexId;
        if (filterData.workYear) {
          param.fromExperienceYear = filterData.workYear.min;
          param.toExperienceYear = filterData.workYear.max;
        }
        param.qualificationId = filterData.qualificationId;
        if (filterData.date) {
          param.minTagCreateDays = filterData.date.value;
        }
        if (filterData.liveLocation) {
          param.liveLocationId = filterData.liveLocation.locationId;
        }
      }
      const data = await this.props.companyAction.searchResumes(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item,
      isSearch: true,
      showBuy: true,
      cancelCallback: this.onRefresh,
    });
  };

  onCommunicated = async (item) => {
    this.hideMenu(item);
    await this.updateResumeStatus(item, 1);
  };

  onInappropriate = async (item) => {
    this.hideMenu(item);
    await this.updateResumeStatus(item, 3);
  };

  hideMenu = (item) => {
    const name = `popoverFun_${item?.resumeId}`;
    if (this[name] && this[name].setState) {
      this[name].setState({ isVisible: false });
    }
  };

  updateResumeStatus = async (item, tag) => {
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.updateResumeTags({
        cvId: item.resumeId,
        tag,
        tagType: 2,
      });
      this.showRequestResult(res?.message);
      this.onRefresh();
      global.emitter.emit(constant.event.downloadResumeAdd);
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onChangeText = (v) => {
    this.setState({ filterText: v });
  };

  onClear = () => {
    this.setState({ filterText: '' }, () => {
      this.onRefresh();
    });
  };

  onFilter = () => {
    NavigationService.navigate('resumeFilter', {
      filterData: this.state.filterData,
      callback: this.onFilterCallback,
    });
  };

  onFilterCallback = (filterData) => {
    this.setState({ filterData }, () => {
      this.onRefresh();
    });
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const name = util.getUserDisplayName(item);
    return (
      <Touchable key={index} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <Image
              source={item.avatar ? { uri: item.avatar } : resIcon.defaultAvatar}
              defaultSource={resIcon.defaultAvatar}
              style={style.itemAvatar}
              resizeMode="cover"
            />
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <Text style={style.titleText}>{name}</Text>
                <Popover
                  ref={(refs) => (this[`popoverFun_${item.resumeId}`] = refs)}
                  from={
                    <Touchable style={style.flexRow}>
                      <Text style={style.statusText}>{I18n.t('page_resume_text_download_to')}</Text>
                    </Touchable>
                  }
                  menus={this.menus}
                  data={item}
                />
              </View>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_work_experience')}：
                {I18n.t('page_resume_text_years', { count: item.careerExperience || 0 })}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_label_career_edu')}：{item?.qualificationId?.label || '-'}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_expect_salary')}：{item?.salaryId?.label || '-'}
              </Text>
            </View>
          </View>
        </View>
      </Touchable>
    );
  };

  renderFilter = () => {
    const { filterText } = this.state;
    const { style } = this;
    return (
      <View style={style.filterContainer}>
        <View style={style.sectionContent}>
          <Input
            inputContainerStyle={style.commonInputContainer}
            inputStyle={[style.inputStyle, { paddingLeft: 0 }]}
            placeholder={I18n.t('page_resume_ph_search')}
            placeholderTextColor="#CCCCCC"
            onChangeText={this.onChangeText}
            selectionColor="#EF3D48"
            returnKeyType="search"
            leftIcon={
              <Icon
                name="search1"
                size={20}
                color="#999"
                type="antdesign"
                style={{ marginRight: 10 }}
              />
            }
            onSubmitEditing={this.onRefresh}
            onClear={this.onClear}
            value={filterText}
          />
        </View>
        {/* <Text style={style.filterText}>
          已选：
          <Text style={style.filterTextMain}>
            {`广州  |  本科  | 1年以上 | 资深Java开发工程师`}
          </Text>
        </Text> */}
      </View>
    );
  };

  rightComponent = () => {
    const { style } = this;

    return (
      <Touchable onPress={this.onFilter} style={style.filterBox}>
        <Image source={resIcon.resumeFilterEnterprise} />
        {this.state.filterData ? <View style={style.filterDot} /> : null}
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <Header
          theme="dark"
          title={I18n.t('page_resume_text_search_people')}
          rightComponent={this.rightComponent}
        />

        {this.renderFilter()}
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
