import React from 'react';
import { inject, observer } from 'mobx-react';
import { Header, Text, Touchable, View, Image, Alert } from '../../../components';
import styles from '../../../themes/enterprise';
import PageFlatList from '../../../components/list/pageFlatList';
import I18n from '../../../i18n';
import constant from '../../../store/constant';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
import moment from 'moment';
import util from '../../../util';

function getComponentStyle(theme) {
  return {
    itemContainer: {
      backgroundColor: theme.primaryBgColor,
      paddingHorizontal: 14,
      paddingVertical: 12,
      marginHorizontal: 12,
      marginTop: 10,
      borderRadius: 5,
    },
    itemTopContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    itemTopRightContainer: {
      // marginLeft: 16,
      marginTop: 10,
      flex: 1,
    },
    itemNameContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    flexRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EF3D48',
      height: 32,
      width: 78,
      borderRadius: 5,
      justifyContent: 'center',
    },
    titleText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      marginRight: 5,
      flexShrink: 1,
    },
    sencondText: {
      fontSize: 13,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    statusText: {
      fontSize: theme.fontSizeM,
      color: '#fff',
      lineHeight: 32,
    },
    sexImgFlat: {
      width: 16,
      height: 16,
      borderRadius: 8,
      marginTop: 2,
      backgroundColor: '#fff',
    },
    nameImgBox: {
      flexDirection: 'row',
      flexShrink: 1,
      marginRight: 20,
    },
    favImg: {
      width: 20,
      height: 20,
    },
  };
}

/**
 * 简历收藏
 */
@inject('companyAction')
@observer
export default class ResumeCollection extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.type = props.type;
    this.initType = props.navigation.state.params?.initType || 0;

    this.state = {};
  }

  componentDidMount() {
    global.emitter.on(constant.event.pendingTxChange, this.onChangeItem);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.pendingTxChange, this.onChangeItem);
  }

  onChangeItem = (txEto) => {
    const index = this.pageFlatList.state?.data?.findIndex((x) => x.id === txEto.id);
    if (index !== -1) {
      this.pageFlatList &&
        this.pageFlatList.changeItem({ ...this.pageFlatList.state?.data[index], ...txEto }, index);
    }
  };

  onRefresh = () => this.pageFlatList.onRefresh();

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  loadData = async (page) => {
    try {
      const limit = 8;
      const param = {
        offset: (page - 1) * limit,
        limit,
      };
      const data = await this.props.companyAction.queryFollowResumes(param);
      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItem = (item) => {
    NavigationService.navigate('resumeDetail', {
      item: { ...item, resumeId: item.cvId },
      isSearch: true,
      cancelCallback: this.onRefresh,
    });
  };

  onItemClick = async (item) => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_resume_text_cancel_collect'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: async () => {
          const res = await this.props.companyAction.deleteFollowResume({
            resumeId: item.cvId,
            followCvId: item.id,
          });
          if (res) {
            toast.show(res?.message || I18n.t('page_resume_text_cancel_collect_success'));
            this.onRefresh();
          }
        },
      },
    ]);
  };

  getSexImg = (item) => {
    switch (item?.sexId?.value) {
      case 1:
        return resIcon.resumeMaleEnterprise;

      case 2:
        return resIcon.resumeFemaleEnterprise;

      default:
        return '';
    }
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const img = this.getSexImg(item);
    const name = util.getUserDisplayName(item);

    const date = moment(item.birthday, 'YYYY-MM-DD');
    const yearsDiff = moment().diff(date, 'years');
    return (
      <Touchable key={index} onPress={() => this.onItem(item)}>
        <View style={style.itemContainer}>
          <View style={style.itemTopContainer}>
            <View style={style.itemTopRightContainer}>
              <View style={style.itemNameContainer}>
                <View style={style.nameImgBox}>
                  <Text style={style.titleText}>{name}</Text>
                  {img ? <Image source={img} style={[style.sexImgFlat]} /> : null}
                </View>
                <Touchable onPress={() => this.onItemClick(item)}>
                  <Image
                    source={resIcon.resumeFavEnterprise}
                    resizeMode="contain"
                    style={style.favImg}
                  />
                </Touchable>
              </View>
              {item?.birthday ? (
                <Text style={style.sencondText}>
                  {I18n.t('page_job_text_age')}：
                  {I18n.t('page_job_text_years_old', { count: yearsDiff })}
                </Text>
              ) : null}
              {item?.qualificationId?.label ? (
                <Text style={style.sencondText}>
                  {I18n.t('page_resume_label_career_edu')}：{item?.qualificationId?.label || ''}
                </Text>
              ) : null}
              {item?.careerLevel?.label ? (
                <Text style={style.sencondText}>
                  {I18n.t('page_job_text_job_level')}：{item?.careerLevel?.label || ''}
                </Text>
              ) : null}
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_work_experience')}：
                {I18n.t('page_resume_text_years', { count: item.carrerExperience || 0 })}
              </Text>
              <Text style={style.sencondText}>
                {I18n.t('page_resume_text_collect_time')}：
                {moment(item.followDate).format('YYYY/MM/DD HH:mm:ss')}
              </Text>
            </View>
          </View>
        </View>
      </Touchable>
    );
  };

  render() {
    return (
      <>
        <Header theme="dark" title={I18n.t('page_resume_text_collect_record')} />
        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  }
}
