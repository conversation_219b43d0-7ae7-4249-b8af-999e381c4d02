import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Platform, Touchable, Text, Image, Header } from '../../../components';
import styles from '../../../themes/enterprise';
import ScrollableTabView from '../../../components/tab/scrollableTabView';
import AllList from './components/allList';
import DeliveryList from './components/deliveryList';
import BuyList from './components/buyList';
import DownloadList from './components/downloadList';

import { deviceWidth } from '../../../common';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import NavigationService from '../../../navigationService';
import SelectJobModal from './components/selectJobModal';
import constant from '../../../store/constant';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.minorBgColor,
    },
    tabBarBackgroundColor: theme.primaryBgColor,
    tabBarInactiveTextColor: '#8E96A3',
    tabBarActiveTextColor: '#333333',
    underlineColor: theme.primaryColor,
    tabBarStyle: {
      borderWidth: 0,
      marginBottom: -6,
      ...Platform.select({
        ios: {
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 1,
          shadowRadius: 2,
        },
        android: {
          elevation: 0,
        },
      }),
    },
    tabBarTextStyle: { fontWeight: theme.fontWeightMedium },
    activeTabTextStyle: { fontWeight: theme.fontWeightMedium },
    tabStyles: { paddingTop: 10 },
    tabBarUnderlineStyle: {
      width: 20,
      height: 3,
      borderRadius: 2,
      marginLeft: ((deviceWidth - 0) / 4 - 20) / 2,
      backgroundColor: '#FF4A55',
      marginBottom: 6,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      minWidth: 150,
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      marginRight: 4,
      flexShrink: 10,
    },
    filterBox: {
      position: 'relative',
      marginLeft: 20,
    },
    filterDot: {
      position: 'absolute',
      top: -3,
      right: 0,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FF4A55',
    },
  };
}

/**
 * 职位
 */
@inject('userStore', 'companyAction')
@observer
export default class Resume extends React.Component {
  constructor(props) {
    super(props);

    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      selectedJob: { id: 0, title: I18n.t('page_job_company_all_job') },
      filterData: null,
      tabIndex: 0,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.homeResumeFilterChanged, this.onHomeResumeFilterChanged);
    this.initData();
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.homeResumeFilterChanged, this.onHomeResumeFilterChanged);
  }

  onHomeResumeFilterChanged = () => {
    console.log('onHomeResumeFilterChanged');
    this.setState({
      selectedJob: { id: 0, title: I18n.t('page_job_company_all_job') },
    });
  };

  initData = async () => {
    const jobData = { jobId: this.state.selectedJob?.id };
    await this.props.companyAction.getApplicationsStatistics({ ...jobData });
    await this.props.companyAction.getResumeStatistics({ jobApplyType: 0, ...jobData });
    await this.props.companyAction.getResumeStatistics({ jobApplyType: 1, ...jobData });
    await this.props.companyAction.getResumeTagsStatistics(2);
  };

  onFilter = () => {
    NavigationService.navigate('resumeFilter', {
      filterData: this.state.filterData,
      callback: this.onFilterCallback,
    });
  };

  onFilterCallback = (filterData) => {
    this.setState({ filterData });
    global.emitter.emit(constant.event.resumeFilterDataChanged, {
      data: filterData,
      tabIndex: this.state.tabIndex,
    });
  };

  onFav = () => {
    NavigationService.navigate('resumeCollection');
  };

  onSearch = () => {
    NavigationService.navigate('resumeSearch');
  };

  onSelectJob = (item) => {
    this.setState({ selectedJob: item }, () => {
      global.emitter.emit(constant.event.resumeJobChanged, {
        item,
        tabIndex: this.state.tabIndex,
      });
    });
  };

  onChangeTab = async ({ i }) => {
    this.setState({ tabIndex: i });
    global.emitter.emit(constant.event.resumeTabChanged, { tabIndex: i });
  };

  leftComponent = () => {
    const { style } = this;
    return (
      <Touchable onPress={() => this.selectJobModal.wrappedInstance.show(this.state.selectedJob)}>
        <View style={style.headerContainer}>
          <Text numberOfLines={1} style={style.headerTitle}>
            {this.state.selectedJob?.title}
          </Text>
          <Image source={resIcon.iconDownEnterprise} />
        </View>
      </Touchable>
    );
  };

  rightComponent = () => {
    const { style } = this;
    return (
      <View style={{ flexDirection: 'row' }}>
        <Touchable onPress={this.onFilter} style={style.filterBox}>
          <Image source={resIcon.resumeFilterEnterprise} resizeMode="contain" />
          {this.state.filterData ? <View style={style.filterDot} /> : null}
        </Touchable>
        <Touchable onPress={this.onFav} style={{ marginLeft: 20 }}>
          <Image source={resIcon.resumeUnfavEnterprise} resizeMode="contain" />
        </Touchable>
        <Touchable onPress={this.onSearch} style={{ marginLeft: 20 }}>
          <Image source={resIcon.resumeSearchEnterprise} resizeMode="contain" />
        </Touchable>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { selectedJob, tabIndex, filterData } = this.state;
    const tabParams = {
      jobId: selectedJob?.id,
      navigation: this.props.navigation,
      filterData,
      selectedJob,
    };
    return (
      <View style={style.container}>
        <Header
          leftContainerStyle={{ flex: 5 }}
          leftComponent={this.leftComponent}
          rightComponent={this.rightComponent}
          statusBarProps={{ barStyle: 'dark-content' }}
        />
        <ScrollableTabView
          tabBarBackgroundColor={style.tabBarBackgroundColor}
          tabBarInactiveTextColor={style.tabBarInactiveTextColor}
          tabBarActiveTextColor={style.tabBarActiveTextColor}
          underlineColor={style.underlineColor}
          tabBarStyle={style.tabBarStyle}
          tabBarTextStyle={style.tabBarTextStyle}
          activeTabTextStyle={style.activeTabTextStyle}
          tabBarUnderlineStyle={style.tabBarUnderlineStyle}
          tabStyles={style.tabStyles}
          initialPage={tabIndex}
          isHorizontalScroll={false}
          locked={IS_ANDROID}
          onChangeTab={this.onChangeTab}
        >
          <AllList key="0" tabIndex="0" tabLabel={I18n.t('page_company_text_all')} {...tabParams} />
          <DeliveryList
            key="1"
            tabIndex="1"
            tabLabel={I18n.t('page_resume_text_delivered')}
            {...tabParams}
          />
          <BuyList
            key="2"
            tabIndex="2"
            tabLabel={I18n.t('page_resume_text_buyed')}
            {...tabParams}
          />
          <DownloadList
            key="3"
            tabIndex="3"
            tabLabel={I18n.t('page_resume_text_download')}
            {...tabParams}
          />
        </ScrollableTabView>

        <SelectJobModal ref={(ref) => (this.selectJobModal = ref)} onConfirm={this.onSelectJob} />
      </View>
    );
  }
}
