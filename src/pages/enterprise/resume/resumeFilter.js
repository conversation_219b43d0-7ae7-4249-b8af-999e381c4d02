import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Header, Button, ScrollView } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import { deviceWidth, hasEar } from '../../../common';
import constant from '../../../store/constant';
import RightArrow from '../../../components/rightArrow';
import PickerModal from '../job/components/pickerModal';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    contentContainer: {
      paddingHorizontal: 14,
      flex: 1,
      paddingBottom: 120,
    },
    sectionBox: {
      marginTop: 20,
      flexDirection: 'column',
    },
    sectionTitle: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      marginBottom: 8,
    },
    itemContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    item: {
      width: (deviceWidth - 28 - 24) / 3,
      minHeight: 38,
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
      marginRight: 12,
      marginBottom: 14,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 4,
    },
    itemActive: {
      backgroundColor: '#FAECEC',
    },
    itemText: {
      fontSize: 13,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      // lineHeight: 38,
      textAlign: 'center',
    },
    itemTextActive: {
      color: '#EF3D48',
    },
    dateBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    dateItem: {
      flex: 1,
      height: 38,
      borderRadius: 5,
      backgroundColor: '#F6F6F6',
    },
    dateText: {
      fontSize: 13,
      color: '#999999',
      lineHeight: 38,
      textAlign: 'center',
    },
    lineBox: {
      height: 38,
      marginHorizontal: 20,
    },
    line: {
      lineHeight: 38,
      textAlign: 'center',
    },
    bottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      paddingHorizontal: 30,
      paddingBottom: hasEar ? 34 : 10,
      paddingTop: 10,
      position: 'absolute',
      bottom: 0,
      width: '100%',
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 4,
      shadowOpacity: 1,
    },
    itemCellContainer: {
      // marginBottom: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    labelBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      flexShrink: 10,
    },
  };
}

/**
 * 简历筛选
 */
@inject('companyStore', 'resumeStore')
@observer
export default class ResumeFilter extends React.Component {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    const { filterData } = this.props.navigation.state.params || {};
    const { sexList, filterWorkYears, qualificationList } = this.props.resumeStore;
    const dateList = [
      {
        label: I18n.t('page_resume_text_three_days'),
        value: '3',
        selected: false,
      },
      {
        label: I18n.t('page_resume_text_one_week'),
        value: '7',
        selected: false,
      },
    ];
    this.state = {
      dateList: dateList.map((x) => {
        return {
          ...x,
          selected: filterData?.date?.value == x.value,
        };
      }),
      sexList: sexList.map((x) => {
        return {
          ...x,
          selected: filterData?.sexId == x.value,
        };
      }),
      workYearList: filterWorkYears.map((x) => {
        return {
          ...x,
          selected: filterData?.workYear?.value == x.value,
        };
      }),
      qualificationList: qualificationList.map((x) => {
        return {
          ...x,
          selected: filterData?.qualificationId == x.value,
        };
      }),
      form: {
        fromAge: filterData?.fromAge,
        toAge: filterData?.toAge,
      },
      expectLocation: filterData?.expectLocation,
      liveLocation: filterData?.liveLocation,
    };
  }

  onExpectAddress = () => {
    NavigationService.navigate('jobCity', {
      cityData: this.state.expectLocation,
      onSelect: (item) => {
        console.log('onSelect', item);
        this.setState({
          expectLocation: {
            locationId: item.locationId,
            cityName: item.cityName,
          },
        });
      },
    });
  };

  onLiveAddress = () => {
    NavigationService.navigate('jobCity', {
      cityData: this.state.liveLocation,
      onSelect: (item) => {
        console.log('onSelect', item);
        this.setState({
          liveLocation: {
            locationId: item.locationId,
            cityName: item.cityName,
          },
        });
      },
    });
  };

  onValueChange = (item, type) => {
    const list = this.state[type];
    list.forEach((x) => {
      if (x.value == item.value) {
        x.selected = !x.selected;
      } else {
        x.selected = false;
      }
    });
    this.setState({
      [type]: list,
    });
  };

  onRest = () => {
    const { sexList, workYearList, qualificationList, dateList } = this.state;
    sexList.forEach((x) => {
      x.selected = false;
    });
    workYearList.forEach((x) => {
      x.selected = false;
    });
    qualificationList.forEach((x) => {
      x.selected = false;
    });
    dateList.forEach((x) => {
      x.selected = false;
    });
    this.setState({
      sexList,
      workYearList,
      qualificationList,
      dateList,
      form: {
        fromAge: null,
        toAge: null,
      },
      expectLocation: null,
      liveLocation: null,
    });
  };

  onConfirm = () => {
    const {
      expectLocation,
      form,
      sexList,
      workYearList,
      qualificationList,
      dateList,
      liveLocation,
    } = this.state;
    const param = {};
    if (expectLocation) {
      param.expectLocation = expectLocation;
    }
    if (liveLocation) {
      param.liveLocation = liveLocation;
    }
    if (form.fromAge) {
      param.fromAge = form.fromAge;
    }
    if (form.toAge) {
      param.toAge = form.toAge;
    }
    const sex = sexList?.find((x) => x.selected);
    if (sex) {
      param.sexId = sex.value;
    }
    const workYear = workYearList?.find((x) => x.selected);
    if (workYear) {
      param.workYear = workYear;
    }
    const qualification = qualificationList?.find((x) => x.selected);
    if (qualification) {
      param.qualificationId = qualification.value;
    }
    const date = dateList?.find((x) => x.selected);
    if (date) {
      param.date = date;
    }
    const data = this.props.navigation.state.params;
    data?.callback?.(Object.values(param).length > 0 ? param : null);
    NavigationService.goBack();
  };

  renderItem = ({ label, value, placeholder = I18n.t('page_resume_tips_select'), onPress }) => {
    const { style } = this;
    return (
      <View style={style.sectionBox}>
        <View style={style.itemCellContainer}>
          <View style={style.itemHeaderContainer}>
            <View style={style.labelBox}>
              <Text style={style.labelText}>{label} </Text>
            </View>
          </View>
          <Touchable style={style.valueTextBox} onPress={onPress}>
            <Text style={[style.valueText, !value ? { color: '#CCCCCC' } : {}]} textType="amount">
              {value || placeholder}
            </Text>
            <RightArrow useImgArrow />
          </Touchable>
        </View>
      </View>
    );
  };

  renderAgeItem = ({ style }) => {
    const { form } = this.state;
    const fromAge = form['fromAge'];
    const toAge = form['toAge'];

    return (
      <View style={style.sectionBox}>
        <Text style={style.sectionTitle}>{I18n.t('page_job_text_age')}</Text>
        <View style={style.dateBox}>
          <Touchable
            style={style.dateItem}
            onPress={() =>
              this.pickerModal.wrappedInstance.show({
                title: I18n.t('page_resume_text_min_age'),
                pickerType: 'fromAge',
                selected: form['fromAge'] || 1,
              })
            }
          >
            <Text style={[style.dateText, fromAge ? style.itemText : {}]}>
              {fromAge || I18n.t('page_resume_text_min_age')}
            </Text>
          </Touchable>
          <View style={style.lineBox}>
            <Text style={style.line}>-</Text>
          </View>
          <Touchable
            style={style.dateItem}
            onPress={() =>
              this.pickerModal.wrappedInstance.show({
                title: I18n.t('page_resume_text_max_age'),
                pickerType: 'toAge',
                selected: form['toAge'] || 1,
              })
            }
          >
            <Text style={[style.dateText, toAge ? style.itemText : {}]}>
              {toAge || I18n.t('page_resume_text_max_age')}
            </Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderCommonItem = ({ style, title, list, type }) => {
    return (
      <View style={style.sectionBox}>
        <Text style={style.sectionTitle}>{title}</Text>
        <View style={style.itemContainer}>
          {list?.map((item, index) => (
            <Touchable
              style={[
                style.item,
                (index + 1) % 3 == 0 ? { marginRight: 0 } : {},
                item.selected ? style.itemActive : {},
              ]}
              key={index}
              onPress={() => this.onValueChange(item, type)}
            >
              <Text style={[style.itemText, item.selected ? style.itemTextActive : {}]}>
                {item.label}
              </Text>
            </Touchable>
          ))}
        </View>
      </View>
    );
  };

  renderButtonGroup = ({ style }) => {
    return (
      <View style={style.bottomContainer}>
        <Button
          title={I18n.t('page_job_btn_reset')}
          btnType="reset"
          btnSize="s44"
          containerStyle={{ flex: 2.5, marginRight: 15 }}
          onPress={this.onRest}
          outline
        />
        <Button
          title={I18n.t('page_setting_confirm_text')}
          btnType="login"
          btnSize="s44"
          containerStyle={{ flex: 5 }}
          onPress={this.onConfirm}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    const { sexList, workYearList, qualificationList, dateList, expectLocation, liveLocation } =
      this.state;
    const Item = this.renderItem;
    const AgeItem = this.renderAgeItem;
    const CommonItem = this.renderCommonItem;
    const ButtonGroup = this.renderButtonGroup;

    return (
      <View style={style.container}>
        <Header theme="dark" title={I18n.t('page_recharge_filter_text_filter')} />
        <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
          <View style={style.contentContainer}>
            <Item
              label={I18n.t('page_resume_text_expect_work_place')}
              required
              value={expectLocation?.cityName || ''}
              onPress={this.onExpectAddress}
            />
            <AgeItem style={style} />
            <CommonItem
              style={style}
              title={I18n.t('page_resume_label_sex')}
              list={sexList}
              type="sexList"
            />
            <CommonItem
              style={style}
              title={I18n.t('page_resume_text_simple_work_experience')}
              list={workYearList}
              type="workYearList"
            />
            <CommonItem
              style={style}
              title={I18n.t('page_resume_label_career_edu')}
              list={qualificationList}
              type="qualificationList"
            />
            <CommonItem
              style={style}
              title={I18n.t('page_resume_text_delivery_time')}
              list={dateList}
              type="dateList"
            />
            <Item
              label={I18n.t('page_resume_label_now_city')}
              required
              value={liveLocation?.cityName || ''}
              onPress={this.onLiveAddress}
            />
          </View>
        </ScrollView>
        <ButtonGroup style={style} />

        <PickerModal
          ref={(ref) => (this.pickerModal = ref)}
          onConfirm={({ pickerType, value }) => {
            const { form } = this.state;
            form[pickerType] = value.value;
            this.setState({ form });
          }}
        />
      </View>
    );
  }
}
