import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Image,
  Text,
  Touchable,
  View,
  Header,
  KeyboardAwareScrollView,
  BaseComponent,
  Icon,
  Alert,
} from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import { deviceWidth } from '../../../common';
import moment from 'moment';
import NavigationService from '../../../navigationService';
import NoData from '../../../components/empty/noData';
import SelectPackageModal from '../job/components/selectPackageModal';
import InviteModal from './components/inviteModal';
import constant from '../../../store/constant';
import ActionSheet from '../../../components/modal/actionSheet';
import userAction from '../../../store/actions/user';
import util from '../../../util';
import sendMessageUtil from '../../../database/sendMessageUtil';
import chatSessionDao from '../../../database/dao/chatSessionDao';
import _ from 'lodash';

const filterTypeLabels = [
  'page_home_text_pending_screening',
  'page_home_text_pending_communication',
  'page_resume_text_invited',
  'page_resume_text_unsuitable',
];

/**
 * 简历详情
 */
@inject('companyAction', 'companyStore')
@observer
export default class ResumeDetail extends BaseComponent {
  style = styles.get('resumeDetail');

  constructor(props) {
    super(props);
    const { item, isSearch, showBuy, filterType, tabIndex } =
      this.props.navigation.state.params || {};
    this.item = item;
    this.isSearch = isSearch;
    this.showBuy = showBuy;
    this.tabIndex = tabIndex;
    this.state = {
      detail: null,
      loading: true,
      defaultJob: null,
      imInfo: null,
    };
    this.initActionSheetOption(filterType);
  }

  componentDidMount() {
    this.getDetai();
    this.checkImAccount();
  }

  getSexImg = () => {
    const { detail } = this.state;
    switch (detail?.profile?.sexId?.value) {
      case 1:
        return resIcon.resumeMaleEnterprise;
      case 2:
        return resIcon.resumeFemaleEnterprise;
      default:
        return '';
    }
  };

  getDetai = async () => {
    try {
      this.showGlobalLoading();
      const data = await this.props.companyAction.getResumeDetail(this.item?.resumeId);
      // await this.getRelateJob(data);
      this.setState({ detail: data, loading: false });
      this.showRequestResult(data?.message);
    } catch (error) {
      this.setState({ loading: false });
      this.showRequestResult(error?.message);
    }
  };

  checkImAccount = async () => {
    try {
      if (this.isSearch || !this.item?.seekerId) return;
      const imInfo = await userAction.getImInfo({
        accountType: 1,
        userId: this.item.seekerId,
      });
      this.setState({ imInfo });
    } catch (e) {
      console.warn('resumeDetail checkImAccount', e);
    }
  };

  onIm = async () => {
    try {
      const { imInfo, detail } = this.state;
      if (__DEV__) {
        console.debug('onIm imInfo', imInfo);
        console.debug('onIm item', this.item);
        console.debug('onIm detail', detail);
      }
      let session = {
        sessionId: imInfo.im_id,
        title: imInfo.nickname,
        avatar: imInfo.avatar,
        company: imInfo.nickname,
        resumeId: detail.resumeId,
      };
      const hasJob = this.item.jobId && this.item.jobId !== '0';
      if (hasJob) {
        session.jobId = this.item.jobId;
        session.jobTitle = this.item.jobTitle;
      }
      const localSession = await chatSessionDao.getChatSession({
        sessionId: session.sessionId,
      });
      const reqSendMsg =
        !localSession ||
        localSession.resumeId !== session.resumeId ||
        (localSession.jobId ? localSession.jobId !== session.jobId : session.jobId);
      /*console.debug('jobDetail reqSendMsg', reqSendMsg, {
        lJobId: localSession?.jobId,
        jobId: session.jobId,
        lResumeId: localSession?.resumeId,
        resumeId: session.resumeId,
      });*/
      session = localSession ? _.merge(localSession, session) : session;
      if (reqSendMsg) {
        await sendMessageUtil.sendMessage({
          type: constant.messageType.resume,
          content: JSON.stringify({
            jobApplyId: this.item.id,
          }),
          sessionId: session.sessionId,
          session,
          localExtra: JSON.stringify({
            resume: detail,
            job: session.jobTitle ? { title: session.jobTitle } : undefined,
            jobApply: this.item,
          }),
          jobTitle: session.jobTitle,
        });
      }

      NavigationService.navigate('chatMessage', {
        session,
        sessionId: session.sessionId,
      });
    } catch (e) {
      console.warn('resumeDetail onIm', e);
    }
  };

  getRelateJob = async (data) => {
    if (!data?.intention?.categoryIds?.length && !this.item?.categoryId?.code) {
      return;
    }
    try {
      const param = {
        page: 1,
        size: 1,
        online: true,
        categoryIds: this.item?.categoryId?.code
          ? [this.item.categoryId.value]
          : data?.intention?.categoryIds?.map((x) => x.value) || null,
      };
      if (!param.categoryIds) {
        delete param.categoryIds;
      }
      const { queryJobs } = this.props.companyAction;
      const res = await queryJobs(param);
      this.setState({ defaultJob: res?.result?.length ? res.result[0] : null });
      return res;
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onUpdateStatus = async (status) => {
    Alert.alert(
      I18n.t('op_remind_title'),
      status === 0
        ? I18n.t('page_resume_text_confirm_pending_communication')
        : I18n.t('page_resume_text_confirm_unsuitable'),
      [
        {
          text: I18n.t('op_cancel_title'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('op_confrim_text'),
          onPress: async () => {
            try {
              this.showGlobalLoading();
              let res = null;
              if (!this.item?.id) {
                const tag = status == 0 ? 1 : 3;
                res = await this.props.companyAction.updateResumeTags({
                  cvId: this.item?.resumeId,
                  tag,
                  tagType: 2,
                });
              } else {
                res = await this.props.companyAction.updateJobApplyStatusById(
                  this.item?.id,
                  status
                );
              }
              const { cancelCallback } = this.props.navigation.state.params || {};
              cancelCallback && cancelCallback();
              this.showRequestResult(res?.message);
              NavigationService.goBack();
            } catch (error) {
              this.showRequestResult(error?.message);
            }
          },
        },
      ]
    );
  };

  onInviteInterview = async () => {
    const { statistics } = this.props.companyStore;
    if (!statistics?.online) {
      return toast.show(I18n.t('page_resume_text_no_online_job'));
    }
    console.debug('onInviteInterview', this.state.detail);
    this.inviteModal.wrappedInstance.show({
      ...this.state.detail,
      id: this.item?.id,
      employerId: this.item?.employerId,
    });
  };

  onBuy = async () => {
    this.selectPackageModal.wrappedInstance.show();
  };

  onBuyResume = async (item) => {
    try {
      const { detail } = this.state;
      this.showGlobalLoading();
      const res = await this.props.companyAction.buyResume({
        resumeId: detail?.resumeId,
        employerProductItemId: item?.id,
      });
      global.emitter.emit(constant.event.resumeStatusChanged);
      this.showRequestResult(res?.message);
      this.getDetai();
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onFavorite = async () => {
    try {
      const { detail } = this.state;
      const { followResume } = this.props.companyAction;
      if (!detail?.followed) {
        this.showGlobalLoading();
        const res = await followResume(detail?.resumeId);
        this.showRequestResult(res?.message);
        this.getDetai();
      }
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onInviteConfirm = () => {
    global.emitter.emit(constant.event.resumeTabChanged, { tabIndex: this.tabIndex });
    this.initActionSheetOption(2);
    this.getDetai();
  };

  renderUserInfo = () => {
    const { style } = this;
    let { detail } = this.state;
    let user = detail?.profile || {};
    const img = this.getSexImg();
    const name = util.getUserDisplayName(user);
    return (
      <>
        <View style={style.userInfo}>
          <View style={style.nameContainer}>
            <Text style={style.nameText}>{name}</Text>
            <Text style={style.jobText}>
              {detail?.careerProfile?.careerPosition || detail?.reqJobTitle}
            </Text>
          </View>
          <View style={style.avatarContainer}>
            <Image
              source={user?.avatar ? { uri: user.avatar } : resIcon.defaultAvatar}
              defaultSource={resIcon.defaultAvatar}
              resizeMode="cover"
              style={style.avatarImg}
            />
            {img ? <Image source={img} style={[style.sexImg]} /> : null}
          </View>
        </View>
        <View style={style.separateLine} />
      </>
    );
  };

  renderBaseInfo = () => {
    const { style } = this;
    const SectionItem = this.renderSection;
    const { detail } = this.state;

    const date = moment(detail?.profile?.birthday, 'YYYY-MM-DD');
    const yearsDiff = moment().diff(date, 'years');

    const workBeginDate = moment(detail?.profile?.workBeginDate, 'YYYY-MM-DD');
    const workAge = moment().diff(workBeginDate, 'years');

    return (
      <SectionItem
        headeTitle={I18n.t('page_job_text_baseinfo')}
        showBuy={!detail?.applied}
        children={
          <>
            <View style={style.topItemContainer}>
              <View style={style.topItem}>
                <Image source={resIcon.resumeInfo1Enterprise} />
                <Text style={style.topItemText}>
                  {I18n.t('page_resume_text_years', { count: workAge || 0 })}
                </Text>
              </View>
              {detail?.careerProfile?.qualificationId?.label ? (
                <View style={style.topItem}>
                  <Image source={resIcon.resumeInfo2Enterprise} />
                  <Text style={style.topItemText}>
                    {detail?.careerProfile?.qualificationId?.label || ''}
                  </Text>
                </View>
              ) : null}
              <View style={style.topItem}>
                <Image source={resIcon.resumeInfo3Enterprise} />
                <Text style={style.topItemText}>
                  {I18n.t('page_job_text_years_old', { count: yearsDiff || 0 })}
                </Text>
              </View>
            </View>
            <View style={style.topItemContainer}>
              <View style={style.topItem}>
                <Image source={resIcon.resumeInfo4Enterprise} />
                <Text style={style.topItemText}>{`${I18n.t(
                  'page_resume_text_current_residence'
                )}：${detail?.profile?.locationId.label || ''}`}</Text>
                {detail?.profile?.nationalityId.label ? (
                  <>
                    <Text style={style.topItemLineText}>|</Text>

                    <Text style={style.topItemText}>{`${I18n.t('page_resume_text_native_place')}：${
                      detail?.profile?.nationalityId.label || ''
                    }`}</Text>
                  </>
                ) : null}
              </View>
            </View>
            {detail?.profile?.mobile ? (
              <View style={style.topItemContainer}>
                <View style={style.topItem}>
                  <Image source={resIcon.resumeInfo5Enterprise} />
                  <Text style={style.topItemText}>{detail.profile.mobile}</Text>
                </View>
              </View>
            ) : null}
            {detail?.profile?.email ? (
              <View style={style.topItem}>
                <Image source={resIcon.resumeInfo6Enterprise} />
                <Text style={style.topItemText}>{detail.profile.email}</Text>
              </View>
            ) : null}
          </>
        }
      />
    );
  };

  renderCareerObj = () => {
    const { style } = this;
    const { detail } = this.state;
    const SectionItem = this.renderSection;
    return !detail?.intention?.reqJobTitle && !detail?.reqJobTitle ? null : (
      <SectionItem
        headeTitle={I18n.t('page_resume_label_job_like')}
        children={
          <>
            <View style={style.careerObJContainer}>
              <Text style={style.careerObJTitleText}>
                {detail?.intention?.reqJobTitle}
                {'  '}
                {detail?.intention?.jobTerm.label}
                {'  '}
                {detail?.intention?.locationIds?.map((x) => x.label).join(' | ')}
              </Text>
              <Text style={style.careerObJAmountText} textType="amount">
                {detail?.reqSalaryId.label}
              </Text>
            </View>
            <View>
              <Text style={style.careerObJText}>
                {detail?.intention?.categoryIds?.map((x) => x.label).join('/')}
              </Text>
              <Text style={style.careerObJText}>
                {detail?.intention?.industryIds?.map((x) => x.label).join(' | ')}
              </Text>
            </View>
          </>
        }
      />
    );
  };

  renderEducation = () => {
    const { style } = this;
    const { detail } = this.state;
    const SectionItem = this.renderSection;
    return detail?.educations?.length ? (
      <SectionItem
        headeTitle={I18n.t('page_resume_label_education')}
        children={detail?.educations?.map((x, index) => (
          <View
            key={index}
            style={index == detail?.educations?.length - 1 ? {} : { marginBottom: 15 }}
          >
            <View style={style.careerObJContainer}>
              <Text style={style.careerObJTitleText}>{x.name}</Text>
              <Text style={style.educationDateText} textType="amount">
                {x?.fromDate?.split('-')[0] || ''}-{x?.toDate?.split('-')[0] || ''}
              </Text>
            </View>
            <Text style={style.careerObJText}>
              {x?.qualificationId?.label}
              {'  '}
              {x.major}
            </Text>
          </View>
        ))}
      />
    ) : null;
  };

  renderLanguage = () => {
    const { style } = this;
    const { detail } = this.state;
    const SectionItem = this.renderSection;
    return detail?.languageLevels?.length ? (
      <SectionItem
        headeTitle={I18n.t('page_resume_label_language')}
        children={
          <>
            {detail?.languageLevels?.map((item, index) => (
              <View
                style={[
                  style.languageContainer,
                  index == detail?.languageLevels.length - 1 ? { marginBottom: 0 } : {},
                ]}
                key={index}
              >
                <Text style={style.languageTitle}>{item?.languageId?.label}</Text>
                <View style={style.languagLevelBox}>
                  <View
                    style={[
                      style.languagLevel,
                      item?.languageLevelId?.value
                        ? { width: ((deviceWidth - 72) * item.languageLevelId.value) / 5 }
                        : {},
                    ]}
                  />
                  <Text style={style.languagLevelText} textType="amount">
                    {item?.languageLevelId?.label}
                  </Text>
                </View>
              </View>
            ))}
          </>
        }
      />
    ) : null;
  };

  renderExperience = () => {
    const { style } = this;
    const { detail } = this.state;
    const SectionItem = this.renderSection;
    const format = (date) => {
      if (!date) {
        return '';
      }
      return date.split('-').slice(0, 2).join('.');
    };
    return detail?.experiences?.length ? (
      <SectionItem
        headeTitle={I18n.t('page_resume_text_work_experience')}
        children={detail?.experiences?.map((x, index) => (
          <View key={index}>
            <View style={style.careerObJContainer}>
              <Text style={style.careerObJTitleText}>{x.company}</Text>
              <Text style={style.educationDateText} textType="amount">
                {format(x.fromDate) +
                  ' - ' +
                  (x.toDate ? format(x.toDate) : I18n.t('page_resume_work_exprience_up_to_now'))}
              </Text>
            </View>
            <Text style={style.careerObJTitleText}>{x.title}</Text>
            {x.description ? (
              <Text style={[style.careerObJText, { marginTop: 18 }]}>{x.description}</Text>
            ) : null}
            {index != detail?.experiences?.length - 1 ? (
              <View style={style.separateInnerLine} />
            ) : null}
          </View>
        ))}
      />
    ) : null;
  };

  renderSkills = () => {
    const { style } = this;
    const { detail } = this.state;
    const SectionItem = this.renderSection;
    return detail?.skills?.length ? (
      <SectionItem
        headeTitle={I18n.t('page_resume_label_skill')}
        children={detail?.skills?.map((x, index) => (
          <View key={index} style={index == detail?.skills?.length - 1 ? {} : { marginBottom: 15 }}>
            <Text style={style.careerObJText}>
              {x?.name}
              {'  |  '}
              {I18n.t('page_resume_text_years', { count: x.years })}
            </Text>
          </View>
        ))}
      />
    ) : null;
  };

  renderSelfvaluation = (desc, title) => {
    const { style } = this;
    const SectionItem = this.renderSection;
    return desc ? (
      <SectionItem headeTitle={title} children={<Text style={style.careerObJText}>{desc}</Text>} />
    ) : null;
  };

  renderQualifacations = () => {
    const { style } = this;
    const { detail } = this.state;
    const images = detail?.qualifications?.map((x) => {
      return { url: x.photo };
    });
    const SectionItem = this.renderSection;
    return detail?.qualifications?.length ? (
      <SectionItem
        headeTitle={I18n.t('page_resume_label_qualification')}
        children={detail?.qualifications?.map((x, index) => (
          <Touchable
            key={index}
            onPress={() => {
              NavigationService.navigate('previewImage', {
                images,
                index,
              });
            }}
          >
            <View style={style.qualifacationsContainer}>
              {x.photo ? (
                <Image
                  source={{ uri: x.photo }}
                  style={{ width: 80, height: 100 }}
                  resizeMode="cover"
                />
              ) : (
                <View style={style.photoBox}>
                  <Icon name="file-photo-o" size={24} color="#CCCCCC" type="font-awesome" />
                </View>
              )}
              <View style={style.qualifacationsRight}>
                <Text style={[style.educationDateText, { marginBottom: 8 }]}>{x.obtained}</Text>
                <Text style={[style.careerObJTitleText, { marginBottom: 8 }]} textType="amount">
                  {x.name}
                </Text>
                <Text style={[style.careerObJText]}>{x.issued}</Text>
              </View>
            </View>

            {index != detail?.qualifications?.length - 1 ? (
              <View style={style.separateInnerLine} />
            ) : null}
          </Touchable>
        ))}
      />
    ) : null;
  };

  renderSection = ({ showBuy, headeTitle, children }) => {
    const { style } = this;
    return (
      <>
        <View style={style.sectionContainer}>
          <View style={style.sectionHeaderBox}>
            <View style={style.sectionHeader}>
              <Image source={resIcon.homeLine} />
              <Text style={style.sectionHeaderText}>{headeTitle}</Text>
            </View>
            {showBuy ? (
              <Touchable style={style.flexRow} onPress={this.onBuy}>
                <Text style={style.statusText}>{I18n.t('page_resume_text_buy_resume')}</Text>
              </Touchable>
            ) : null}
          </View>
          <View style={style.contentContainer}>{children}</View>
        </View>
        <View style={style.separateLine} />
      </>
    );
  };

  rightComponent = () => {
    const { detail, loading } = this.state;
    return !loading ? (
      <Touchable onPress={this.onFavorite}>
        <Image
          source={detail?.followed ? resIcon.resumeFavEnterprise : resIcon.resumeUnfavEnterprise}
          resizeMode="contain"
          style={this.style.favImg}
        />
      </Touchable>
    ) : null;
  };

  onShowFilterActionSheet = () => {
    if (!this.actionSheetOption?.length) return;
    global.emitter.emit(constant.event.showActionSheet, {
      page: 'resumeDetail',
      options: this.actionSheetOption,
    });
  };

  initActionSheetOption = (filterType) => {
    if (typeof filterType !== 'number' || filterType === 3) return;
    this.filterType = filterType;
    const data = [];
    if (filterType === 0) {
      data.push({
        onPress: () => this.onUpdateStatus(0),
        title: I18n.t('page_home_text_pending_communication'),
      });
    }
    data.push({
      onPress: () => this.onUpdateStatus(-1),
      title: I18n.t('page_resume_text_unsuitable'),
    });
    if (filterType === 0 || filterType === 1) {
      data.push({
        onPress: this.onInviteInterview,
        title: I18n.t('page_resume_text_invite_interview'),
      });
    }
    this.filterTypeLabel = filterTypeLabels[filterType] && I18n.t(filterTypeLabels[filterType]);
    this.actionSheetOption = data;
  };

  renderBottomButton = () => {
    if (this.isSearch) return null;
    const showLeftBtn = this.actionSheetOption?.length;
    const showRightBth = this.state.imInfo && this.state.detail?.applied;
    return (
      <View style={{ position: 'absolute', bottom: 30, left: 30, right: 30, flexDirection: 'row' }}>
        {showLeftBtn ? (
          <Touchable onPress={this.onShowFilterActionSheet} style={!showRightBth && { flex: 1 }}>
            <View
              style={{
                minHeight: 44,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#FDEEEF',
                borderRadius: 5,
                paddingHorizontal: 15,
              }}
            >
              <Text style={{ color: '#F9415E', paddingRight: 10 }}>
                {this.filterTypeLabel || I18n.t('page_home_text_pending_screening')}
              </Text>
              <Icon name="chevron-down" type="ionicon" size={20} color="#F9415E" />
            </View>
          </Touchable>
        ) : null}
        {showRightBth ? (
          <Touchable style={{ flex: 1, marginLeft: showLeftBtn ? 15 : 0 }} onPress={this.onIm}>
            <View
              style={{
                minHeight: 44,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#EF3D48',
                borderRadius: 5,
              }}
            >
              <Text style={{ color: '#FFFFFF', paddingRight: 10 }}>{I18n.t('page_job_chat')}</Text>
            </View>
          </Touchable>
        ) : null}
        <ActionSheet page="resumeDetail" />
      </View>
    );
  };

  render() {
    const { style } = this;
    const { detail, loading } = this.state;
    return (
      <View style={style.container}>
        <Header theme="dark" title="" rightComponent={this.rightComponent} />
        {loading ? null : !detail?.resumeId ? (
          <NoData text={I18n.t('page_resume_text_resume_not_exist')} />
        ) : (
          <>
            <KeyboardAwareScrollView
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              style={{ flex: 1 }}
              contentContainerStyle={{ flexGrow: 1 }}
            >
              {this.renderUserInfo()}
              {this.renderBaseInfo()}
              {this.renderCareerObj()}
              {this.renderEducation()}
              {this.renderLanguage()}
              {this.renderExperience()}
              {this.renderSkills()}
              {this.renderSelfvaluation(detail?.training, I18n.t('page_resume_label_training'))}
              {this.renderSelfvaluation(detail?.hobby, I18n.t('page_resume_label_hobby'))}
              {this.renderQualifacations()}
              {this.renderSelfvaluation(detail?.description, I18n.t('page_resume_label_appraise'))}

              <View style={{ height: 100 }} />
            </KeyboardAwareScrollView>
            {this.renderBottomButton()}
          </>
        )}

        <SelectPackageModal
          ref={(ref) => (this.selectPackageModal = ref)}
          onConfirm={this.onBuyResume}
          itemIds={[1, 4]}
        />

        <InviteModal
          ref={(ref) => (this.inviteModal = ref)}
          onConfirm={this.onInviteConfirm}
          item={this.item}
          detail={detail}
        />
      </View>
    );
  }
}
