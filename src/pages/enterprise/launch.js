import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity } from 'react-native';
import Toast from 'react-native-easy-toast';
import SplashScreen from 'react-native-splash-screen';
import I18n from '../../i18n';
import res from '../../res';
import Session from '../../api/session';
import Storage from '../../common/storage';
import ImageBackground from '../../components/imageBackground';
import NavigationService from '../../navigationService';
import StatusBar from '../../components/statusBar';

const firstStyle = {
  secondWrap: {
    position: 'absolute',
    top: 30,
    right: 16,
    width: 75,
    height: 30,
    paddingBottom: 5,
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 20,
    borderWidth: 0,
    borderColor: 'rgba(0,0,0,0.5)',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  seconds: {
    color: '#fff',
    paddingTop: 5,
    paddingBottom: 5,
    fontSize: 14,
  },
};

@inject('loginAction', 'personStore', 'userAction')
@observer
export default class Launch extends React.Component {
  constructor(props) {
    super(props);
    this.skiping = false;
    this.state = {
      seconds: 3,
      defaultLanguage: '',
      loading: true,
      adv: res.first,
      hasAdv: false,
    };
    global.emitter.on('clearCountDownTimeout', this.onClearTimer);
  }

  componentDidMount() {
    SplashScreen.hide(); // 关闭启动屏幕
    this.forceUpdate();
    // this.tryLoginIM();
    Promise.all([Session.getLanguage(), Storage.getHasAdv()])
      .then((result) => {
        const lang = result[0];
        const hasAdv = result[1];
        this.setState({ defaultLanguage: lang });
        if (!hasAdv) {
          this.next();
        } else {
          this.getCacheAdvImage().then(
            (base64) => {
              this.setState({ loading: false });
              // 有广告，开始数秒
              if (base64) {
                this.setState({ hasAdv, adv: { uri: `data:image/png;base64,${base64}` } });
                this.runTimer();
              } else {
                // 没有广告，直接跳过
                this.next();
              }
            },
            () => {
              this.next();
            }
          );
        }
      })
      .catch(() => {
        this.setState({ loading: false });
        this.next();
      });
  }

  componentWillUnmount() {
    global.emitter.off('clearCountDownTimeout', this.onClearTimer);
    this.clearCountDownTimeout();
  }

  onClearTimer = () => {
    this.clearCountDownTimeout();
  };

  getCacheAdvImage = () => this.props.userAction.getAdvImage();

  runTimer = () => {
    this.countdown();
    this.timeout2 = setTimeout(() => {
      this.next();
      this.clearCountDownTimeout();
      if (this.timeout2) {
        clearTimeout(this.timeout2);
      }
    }, 3000);
  };

  clearCountDownTimeout() {
    if (this.timeout) {
      clearInterval(this.timeout);
    }
  }

  countdown = () => {
    this.timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
    }, 1000);
  };

  next = () => {
    this.clearCountDownTimeout();
    this.props.userAction.syncCacheAdvImage();
    if (this.skiping) {
      return;
    }

    this.skiping = true;
    NavigationService.replace('main');
  };

  tryLoginIM = () => {
    // Session.isLogin().then((isLogin) => {
    //   if (isLogin) {
    //     this.props.loginAction.autoLoginIM();
    //   }
    // });
  };

  render() {
    const { defaultLanguage, loading, adv, hasAdv } = this.state;
    return (
      <View>
        <StatusBar containerStyle={{ height: 0 }} translucent hidden />
        {!loading && hasAdv ? (
          <ImageBackground
            source={adv}
            style={{ width: '100%', height: '100%', backgroundColor: 'transparent' }}
          >
            <View style={firstStyle.secondWrap}>
              <TouchableOpacity onPress={this.next}>
                <Text style={firstStyle.seconds}>
                  {defaultLanguage ? I18n.t('page_first_skip_text') : 'Skip'} | {this.state.seconds}{' '}
                  s
                </Text>
              </TouchableOpacity>
            </View>
            <Toast
              ref={(ref) => {
                this.toast = ref;
              }}
              position="center"
            />
          </ImageBackground>
        ) : (
          <View style={{ display: 'none' }} />
        )}
      </View>
    );
  }
}
