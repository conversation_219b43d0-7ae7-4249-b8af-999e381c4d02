import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, TouchableOpacity, Text, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { Input, CheckBox } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { registerStyle, phColor } from '../themes';
import I18n from '../i18n';
import res from '../res';
import regExp from '../util/regExp';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

let keyBoardIsShow = false;

@inject('loginAction', 'resumeAction', 'jobAction', 'userAction')
@observer
export default class RegisterEmailPassword extends React.Component {
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      password: '',
      confirmPassword: '',
      agreement: true,
      showLoading: false,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
  }

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = false;
  }

  onLoadInfo = () => {
    this.props.resumeAction.getResumes();
    this.props.jobAction.getJobStatistics();
    this.props.userAction.getCurrUserInfo();
  };

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  onRegist = () => {
    this.onLostBlur();
    const userName = this.props.navigation.getParam('userName');
    const { password, confirmPassword, agreement } = this.state;
    if (password.trim() === '' || password.trim().length < 6) {
      this.toast.show(I18n.t('page_register_op_password_format_error'));
      return;
    }
    // 验证密码
    if (!regExp.password.test(password.trim())) {
      this.toast.show(I18n.t('page_register_op_password_format_error'));
      return;
    }
    if (password.trim() === userName.trim()) {
      this.toast.show(I18n.t('page_register_op_password_same_email_error'));
      return;
    }
    if (confirmPassword.trim() === '') {
      this.toast.show(I18n.t('page_register_op_confirm_password_required'));
      return;
    }
    if (password.trim() !== confirmPassword.trim()) {
      this.toast.show(I18n.t('page_register_op_confirm_password_inconsistent'));
      return;
    }
    if (!agreement) {
      this.toast.show(I18n.t('page_register_op_agreement_required'));
      return;
    }
    this.setState({ showLoading: true });
    this.props.loginAction.registerByEmail(userName.trim(), password.trim()).then(
      () => {
        this.setState({ showLoading: false });
        this.toast.show(I18n.t('page_register_op_success'));
        this.onLoadInfo();
        this.props.navigation.navigate('registerResult');
      },
      (err) => {
        this.setState({ showLoading: false });
        if (err && err.message) {
          this.toast.show(err.message);
        } else {
          this.toast.show(I18n.t('page_register_op_error'));
        }
      }
    );
  };

  setPassword = (text) => {
    this.setState({
      password: text,
    });
  };

  setConfirmPassword = (text) => {
    this.setState({
      confirmPassword: text,
    });
  };

  setAgreement = () => {
    this.setState({ agreement: !this.state.agreement });
  };

  renderHeader = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.headerSection}>
        <TouchableOpacity
          style={registerStyle.headerBack}
          onPress={() => {
            navigation.navigate('registerEmail');
          }}
        >
          <Image source={res.iconBack} style={registerStyle.iconBack} />
        </TouchableOpacity>
        <View>
          <Text style={registerStyle.headerTitle}>
            {I18n.t('page_register_text_email_header_title')}
          </Text>
        </View>
      </View>
    );
  };

  renderForm = () => (
    <View style={registerStyle.formContent}>
      <View style={[registerStyle.formGroup, registerStyle.formGroupLine]}>
        <Input
          secureTextEntry
          inputContainerStyle={registerStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_register_ph_email_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setPassword}
          value={this.state.password}
          minLength={6}
          maxLength={16}
        />
      </View>
      <View style={[registerStyle.formGroup]}>
        <Input
          secureTextEntry
          inputContainerStyle={registerStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_register_ph_email_confirm_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setConfirmPassword}
          value={this.state.confirmPassword}
          minLength={6}
          maxLength={16}
        />
      </View>
    </View>
  );

  renderFooter = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.linkSection}>
        <View style={{ flexDirection: 'row', alignItems: 'center', width: '60%', marginRight: 20 }}>
          <CheckBox
            containerStyle={registerStyle.agreementCheckbox}
            textStyle={{ color: '#fff', fontSize: 12, fontWeight: 'normal' }}
            uncheckedIcon="square-o"
            checkedIcon="check-square-o"
            checkedColor="#fff"
            uncheckedColor="#fff"
            size={20}
            checked={this.state.agreement}
            onPress={this.setAgreement}
          />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('agreement');
            }}
          >
            <Text style={registerStyle.agreementLink}>
              {I18n.t('page_register_link_agreement')}
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            flexShrink: 10,
            marginLeft: 20,
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}
        >
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('registerPhone');
            }}
          >
            <Text style={registerStyle.emailRegisterLink}>
              {I18n.t('page_register_link_phone_register')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  render() {
    return (
      // View 用以适配iPhoneX
      <View style={registerStyle.page}>
        <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
          <ImageBackground source={res.bgLogin} style={{ width: '100%', height: '100%' }}>
            <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
              <View style={[registerStyle.container]}>
                {this.renderHeader()}
                <View style={registerStyle.titleSection}>
                  <Text style={registerStyle.title}>{I18n.t('page_register_text_title')}</Text>
                </View>
                <View behavior="padding" style={registerStyle.emailPasswordFormSection}>
                  {this.renderForm()}
                </View>
                <View style={registerStyle.btnSection}>
                  <TouchableOpacity
                    style={registerStyle.btnRegister}
                    onPress={() => this.onRegist()}
                  >
                    <Text style={registerStyle.btnRegisterText}>
                      {I18n.t('page_register_btn_register')}
                    </Text>
                  </TouchableOpacity>
                </View>
                {this.renderFooter()}
              </View>
            </TouchableWithoutFeedback>
          </ImageBackground>
        </TouchableWithoutFeedback>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
