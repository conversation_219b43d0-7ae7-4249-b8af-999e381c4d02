import React, { Component } from 'react';
import ActionSheet from 'react-native-actionsheet';
import { ScrollView, View, Text, TouchableOpacity, RefreshControl } from 'react-native';
import { Header, Icon, ListItem, Button } from 'react-native-elements';
import { inject, observer } from 'mobx-react';
import {
  headerStyle,
  titleColor,
  desColor,
  jobStyle,
  jobIntensionStyle,
  baseBlueColor,
} from '../themes';
import I18n from '../i18n';

@inject('jobStore', 'jobAction', 'resumeStore', 'resumeAction')
@observer
export default class JobIntention extends Component {
  constructor(props) {
    super(props);
    this.state = {
      refreshing: false,
    };
  }

  componentWillUnmount() {
    this.goBack();
  }

  onRefresh = async () => {
    this.setState({ refreshing: true });
    await this.props.resumeAction.getResumes();
    this.setState({ refreshing: false });
  };

  onSelectIntension = (item) => {
    const { navigation } = this.props;
    navigation.navigate('jobIntensionAdd', {
      data: item.intention,
    });
  };

  onToIntensionAdd = () => {
    const { navigation } = this.props;
    navigation.navigate('jobIntensionAdd', {});
  };

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onShowModal) {
      await fuc.onShowModal();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  formatData(array) {
    const list = array.map((item) => item.label);
    list.push(I18n.t('page_job_actionsheet_cancel'));
    return list;
  }

  showStatuSheet = () => {
    this.statusActionSheet.show();
  };

  statusSheetSelect = (index) => {
    const {
      jobStore: { jobStatusList },
    } = this.props;
    if (index !== jobStatusList.length) {
      const param = jobStatusList[index];
      this.props.jobAction.updateWorkStatus(param ? param.value : 0).then((res) => {
        if (res && res.successful) {
          this.props.resumeAction.getResumes();
        }
      });
    }
  };

  titleFormat = (item) => {
    const title = item.intention.reqJobTitle;
    const arr = item.intention.locationIds.map((l) => l.label);
    let tempCity = null;
    if (arr.length > 1) {
      tempCity = arr.join('/');
    } else if (arr.length === 1) {
      tempCity = arr.join('');
    } else {
      tempCity = '';
    }
    return tempCity ? `（${tempCity}）${title}` : `${title}`;
  };

  renderTopView = () => {
    const { resumeList } = this.props.resumeStore;
    return (
      <View>
        <TouchableOpacity onPress={this.showStatuSheet}>
          <View style={jobIntensionStyle.listItem}>
            <View>
              <Text style={jobIntensionStyle.title}>{I18n.t('page_job_work_status_text')}</Text>
            </View>
            <View style={jobIntensionStyle.subTitleContent}>
              <Text style={jobIntensionStyle.subTitle}>
                {resumeList &&
                resumeList.length > 0 &&
                resumeList[0].intention &&
                resumeList[0].intention.workStatus
                  ? resumeList[0].intention.workStatus.label
                  : ''}
              </Text>
              <Icon
                name="chevron-thin-right"
                type="entypo"
                size={15}
                color={desColor}
                iconStyle={{ marginRight: 10 }}
              />
            </View>
          </View>
          <View style={{ backgroundColor: '#fff' }}>
            <View style={jobIntensionStyle.separateLine} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderIntensionList = () => {
    const {
      resumeStore: { resumeList },
    } = this.props;
    return resumeList.map((item) => (
      <ListItem
        key={item.resumeId}
        onPress={() => this.onSelectIntension(item)}
        containerStyle={{
          borderBottomColor: '#eee',
          borderBottomWidth: 0.5,
          marginHorizontal: 0,
          paddingVertical: 12,
        }}
        title={
          <View style={{ flexDirection: 'row', alignItems: 'center', paddingBottom: 8 }}>
            <Text style={{ color: '#fff', fontSize: 12 }}>（</Text>
            <Text style={{ color: desColor, fontSize: 12 }}>{item.name}</Text>
            <Text style={{ color: '#fff', fontSize: 12 }}>）</Text>
          </View>
        }
        subtitle={
          item.intention.reqJobTitle ? (
            this.titleFormat(item)
          ) : (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ color: '#fff', fontSize: 14 }}>（</Text>
              <Text style={{ color: baseBlueColor, fontSize: 14 }}>
                {I18n.t('page_job_no_intension')}
              </Text>
              <Text style={{ color: '#fff', fontSize: 14 }}>）</Text>
            </View>
          )
        }
        subtitleStyle={{
          color: item.intention.reqJobTitle ? titleColor : baseBlueColor,
          fontSize: 14,
        }}
        rightIcon={<Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />}
      />
    ));
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={15}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  render() {
    const {
      jobStore: { jobStatusList },
      resumeStore: { resumeList },
    } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_job_nav_top_intension_manager'),
            style: headerStyle.center,
          }}
          leftComponent={this.renderLeftIcon()}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={this.state.refreshing} onRefresh={this.onRefresh} />
          }
        >
          {this.renderTopView()}
          <View style={{ marginTop: 4 }}>{this.renderIntensionList()}</View>
        </ScrollView>
        {resumeList.length < 3 ? (
          <View style={jobIntensionStyle.submitButton}>
            <Button
              title={I18n.t('page_job_nav_top_add_intension')}
              buttonStyle={jobIntensionStyle.buttonStyle}
              titleStyle={{ fontSize: 16 }}
              onPress={() => {
                this.onToIntensionAdd();
              }}
            />
          </View>
        ) : (
          <Text />
        )}
        <ActionSheet
          ref={(ref) => {
            this.statusActionSheet = ref;
          }}
          title={I18n.t('page_job_work_status_text')}
          options={this.formatData(jobStatusList)}
          cancelButtonIndex={jobStatusList ? jobStatusList.length : 0}
          onPress={this.statusSheetSelect}
        />
      </View>
    );
  }
}
