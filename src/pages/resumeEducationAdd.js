import React, { Component } from 'react';
import Picker from 'react-native-picker';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import util from '../util';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const education = this.props.navigation.state.params.data;
    this.isEdit = this.props.navigation.state.params.edit;
    this.state = {
      postData: education
        ? Object.assign({}, education)
        : {
            name: '',
            fromDate: '',
            toDate: '',
            qualificationId: 0,
            major: '',
          },
      qualification: null,
      schoolName: education.name,
      eduSubject: education.major,
    };
  }

  componentWillUnmount() {
    Picker.hide();
  }

  onSelectQualification = () => {
    Picker.hide();
    const { navigation } = this.props;
    navigation.navigate('resumeQualification', {
      onSelect: (item) => {
        const temp = this.state.postData;
        temp.qualificationId = item ? item.value : 0;
        this.setState({
          qualification: item,
          postData: temp,
        });
      },
    });
  };

  onSelectMajor = () => {
    Picker.hide();
    const { navigation } = this.props;
    navigation.navigate('resumeMajor', {
      onSelect: (item) => {
        const temp = this.state.postData;
        temp.major = item ? item.label : '';
        this.setState({
          postData: temp,
        });
      },
    });
  };

  onChangeName = (text) => {
    Picker.hide();
    const temp = this.state.postData;
    temp.name = text;
    this.setState({
      postData: temp,
    });
  };

  onChangeEduSubject = (text) => {
    Picker.hide();
    const temp = this.state.postData;
    temp.major = text;
    this.setState({
      postData: temp,
    });
  };

  onFocus = () => {
    Picker.hide();
  };

  publish = () => {
    Keyboard.dismiss();
    const { currentResume } = this.props.resumeStore;
    const { qualification } = this.state;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.name) {
      this.toast.show(`${I18n.t('page_resume_tips_input')}${I18n.t('page_resume_label_school')}`);
      return;
    }
    if (!qualification && !(temp && temp.qualificationId && temp.qualificationId.label)) {
      this.toast.show(`${I18n.t('page_resume_tips_select')}${I18n.t('page_resume_label_edu')}`);
      return;
    }
    if (!temp.fromDate) {
      this.toast.show(
        `${I18n.t('page_resume_tips_select')}${I18n.t('page_resume_label_edu_enter')}`
      );
      return;
    }
    if (!temp.toDate) {
      this.toast.show(
        `${I18n.t('page_resume_tips_select')}${I18n.t('page_resume_label_edu_leave')}`
      );
      return;
    }
    if (
      parseFloat(util.dateToTimestamp(temp.fromDate)) >
      parseFloat(util.dateToTimestamp(temp.toDate))
    ) {
      this.toast.show(I18n.t('page_resume_tips_not_before'));
      return;
    }
    if (!temp.major) {
      this.toast.show(
        `${I18n.t('page_resume_tips_select')}${I18n.t('page_resume_label_edu_subject')}`
      );
      return;
    }
    temp.fromDate += '-1';
    temp.toDate += '-1';
    if (this.isEdit) {
      this.props.resumeAction
        .updateEducation(temp.eduId, currentResume.resumeId, temp)
        .then((res) => {
          this.showRequestResult(res);
        });
    } else {
      this.props.resumeAction.addEducation(currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    }
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      Picker.hide();
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.toast.show(res.message);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  createDateData() {
    const date = [];
    for (let i = 1950; i < 2100; i += 1) {
      const month = [];
      for (let j = 1; j < 13; j += 1) {
        month.push((Array(2).join('0') + j).slice(-2));
      }
      const _date = {};
      _date[i] = month;
      date.push(_date);
    }
    return date;
  }

  showPickerPress(type) {
    Keyboard.dismiss();
    const education = this.state.postData;
    let title = '';
    let picData = [];
    let select = [];
    if (type === 2) {
      title = I18n.t('page_resume_label_work_enter');
      picData = this.createDateData();
      if (education.fromDate) {
        select = education.fromDate.split('-');
      } else {
        select = ['2014', '09'];
      }
    } else if (type === 3) {
      title = I18n.t('page_resume_label_work_leave');
      picData = this.createDateData(1);
      if (education.toDate) {
        select = education.toDate.split('-');
      } else {
        select = ['2018', '06'];
      }
    }
    Picker.init({
      pickerData: picData,
      pickerTitleText: title,
      selectedValue: select,
      pickerCancelBtnText: I18n.t('page_resume_btn_cancel'),
      pickerConfirmBtnText: I18n.t('page_resume_btn_sure'),
      pickerToolBarBg: [50, 165, 231, 1],
      pickerBg: [255, 255, 255, 1],
      pickerConfirmBtnColor: [255, 255, 255, 1],
      pickerCancelBtnColor: [255, 255, 255, 1],
      pickerTitleColor: [255, 255, 255, 1],
      onPickerConfirm: (data) => {
        if (type === 2) {
          const temp = this.state.postData;
          temp.fromDate = data.join('-');
          this.setState({
            postData: temp,
          });
        } else if (type === 3) {
          const temp = this.state.postData;
          temp.toDate = data.join('-');
          this.setState({
            postData: temp,
          });
        }
      },
      onPickerCancel: () => {},
      onPickerSelect: () => {},
    });
    Picker.show();
  }

  render() {
    const { navigation } = this.props;
    const { qualification } = this.state;
    const education = this.state.postData;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_edu_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_school')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_school'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeName,
            onFocus: this.onFocus,
            defaultValue: this.state.schoolName,
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_edu')}
          chevron
          rightTitle={
            qualification
              ? qualification.label
              : education && education.qualificationId
              ? education.qualificationId.label
              : ''
          }
          onPress={() => this.onSelectQualification()}
          titleStyle={resumeStyle.listItemLeftText}
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_edu_enter')}
          chevron
          hideChevron
          rightTitle={education.fromDate ? util.dateToYMString(education.fromDate) : ''}
          onPress={() => this.showPickerPress(2)}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_edu_leave')}
          chevron
          rightTitle={education.toDate ? util.dateToYMString(education.toDate) : ''}
          onPress={() => this.showPickerPress(3)}
          titleStyle={resumeStyle.listItemLeftText}
          rightTitleStyle={resumeStyle.listItemRightText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_edu_subject')}
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_edu_subject'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeEduSubject,
            onFocus: this.onFocus,
            defaultValue: this.state.eduSubject,
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
