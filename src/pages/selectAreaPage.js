import React, { Component } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Header, Icon } from 'react-native-elements';
import { headerStyle, jobStyle } from '../themes';
import I18n from '../i18n';
import LoadingModal from '../components/loadingModal';
import CountryCodePicker from '../components/CountryPicker2';
import util from '../util';

@inject('jobAction', 'cityAction')
@observer
export default class SelectAreaPage extends Component {
  constructor(props) {
    super(props);
    const cityData = props.navigation.getParam('cityData', {});
    this.state = {
      showLoading: false,
      allPlaceTreeIndex: 0,
      areaName: util.getCityNameWithCityData(cityData),
      allPlaceTree: [],
      pickerDataFormat: [],
    };
  }

  componentWillUnmount() {
    this.goBack();
  }

  componentDidMount() {
    this.getAllPlace();
    this.getLocationConstants2();
  }

  getLocationConstants2 = () => {
    this.props.jobAction.getLocationConstants2();
  };

  marryCity = () => {
    const { allPlaceTree } = this.state;
    const { cityData } = this.props.navigation.state.params;
    const selectIndex = allPlaceTree.findIndex((x) => x.placeId == cityData.placeId);
    if (cityData.twoLevelCityData && cityData.twoLevelCityData.placeId) {
      this.CountryCodePicker.wrappedInstance.selectHeaderAction2(
        util.getCityNameWithCityData(cityData.twoLevelCityData)
      );
    }
    if (cityData.threeLevelCityData && cityData.threeLevelCityData.placeId) {
      this.CountryCodePicker.wrappedInstance.phoneCodeSelected2(
        cityData.threeLevelCityData.placeId
      );
    }
    this.setState(
      {
        allPlaceTreeIndex: selectIndex < 0 ? 26 : selectIndex,
      },
      () => {
        this.queryFormatPickerData();
      }
    );
  };

  getAllPlace = () => {
    this.props.cityAction.getAllPlaceTree().then(
      (res) => {
        this.setState({ allPlaceTree: res || [] }, () => {
          this.marryCity();
        });
      },
      (err) => {
        console.log(err);
      }
    );
  };

  goBack = async () => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onContinueCheck) {
      await fuc.onContinueCheck();
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  selectItem = async (itemParam) => {
    const { navigation } = this.props;
    const { allPlaceTreeIndex, allPlaceTree } = this.state;
    if (itemParam.parentName) {
      const item = allPlaceTree[allPlaceTreeIndex];
      const itemData = item;
      const index1 = item.children.findIndex(
        (x) => util.getCityNameWithCityData(x) == itemParam.parentName.key
      );
      const index2 =
        item.children[index1].children &&
        item.children[index1].children.findIndex((x) => x.placeId == itemParam.placeId);
      itemData.twoLevelCityData = item.children[index1];
      itemData.threeLevelCityData = item.children[index1].children
        ? item.children[index1].children[index2]
        : null;
      const fuc = this.props.navigation.state.params;
      if (fuc) {
        fuc.onSelect(itemData);
        navigation.goBack();
      }
    } else {
      const item = allPlaceTree[allPlaceTreeIndex];
      const itemData = item;
      const index1 = item.children.findIndex((x) => util.getCityNameWithCityData(x) == itemParam);
      itemData.twoLevelCityData = item.children[index1];
      const fuc = this.props.navigation.state.params;
      if (fuc) {
        fuc.onSelect(itemData);
        navigation.goBack();
      }
    }
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  renderHeaderRight = () => {
    const { navigation } = this.props;
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('cityList', {
            needSave: true,
            onSelect: (item) => {
              this.selectedArea(item);
            },
          });
        }}
      >
        <View>
          <Text style={{ fontSize: 12, color: '#FFF' }}>
            {I18n.t('page_select_city_switch_city')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  selectedArea = (item) => {
    const { allPlaceTree } = this.state;
    const selectIndex = allPlaceTree.findIndex((x) => x.placeId == item.placeId);
    this.setState(
      {
        areaName: util.getCityNameWithCityData(item),
        allPlaceTreeIndex: selectIndex,
      },
      () => {
        this.queryFormatPickerData();
      }
    );
  };

  queryFormatPickerData = async () => {
    const { allPlaceTreeIndex, allPlaceTree } = this.state;
    const cityData = allPlaceTree[allPlaceTreeIndex || 0];
    const pickerData = JSON.parse(JSON.stringify(cityData)) || [];
    await this.props.jobAction.formatPickerData(pickerData.children).then((res) => {
      this.setState({
        pickerDataFormat: res,
      });
    });
  };

  render() {
    const { areaName, pickerDataFormat } = this.state;

    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          // centerComponent={{ text: I18n.t('page_job_nav_top_select'), style: headerStyle.center }}
          centerComponent={{
            text: areaName,
            style: headerStyle.center,
          }}
          rightComponent={this.renderHeaderRight()}
          leftComponent={this.renderLeftIcon()}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <View style={{ height: 10, backgroundColor: '#2089DC' }} />

        <CountryCodePicker
          pickerData={pickerDataFormat}
          isShow
          ref={(ref) => {
            this.CountryCodePicker = ref;
          }}
          onPick={(res) => {
            this.selectItem(res);
          }}
        />

        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
