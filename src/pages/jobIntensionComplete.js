import React, { Component } from 'react';
import { View, ScrollView, Keyboard } from 'react-native';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import ActionSheet from 'react-native-actionsheet';
import { Header, Icon, ListItem, Button, Input, Text } from 'react-native-elements';
import { headerStyle, desColor, jobIntensionStyle, titleColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import NavigationService from '../navigationService';
import uiUtil from '../util/uiUtil';

@inject('jobStore', 'jobAction', 'resumeAction', 'resumeStore')
@observer
export default class JobIntensionComplete extends Component {
  static navigationOptions = {
    headerShown: false,
    gestureEnabled: false,
  };
  constructor(props) {
    super(props);
    this.keyboardDidShowListener = null;
    this.keyboardDidHideListener = null;
    this.state = {
      jobType: null, // 工作性质
      salary: null, // 薪资
      workStatus: null,
      categoryIds: [], // 职业
      industryIds: [], // 行业
      locationIds: [], // 位置
      reqJobTitle: '', // 期待职位
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this.keyboardDidShowHandler.bind(this)
    );
    this.keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      this.keyboardDidHideHandler.bind(this)
    );
  }

  componentWillUnmount() {
    if (this.keyboardDidShowListener != null) {
      this.keyboardDidShowListener.remove();
    }
    if (this.keyboardDidHideListener != null) {
      this.keyboardDidHideListener.remove();
    }
  }

  onSelectIndustry = () => {
    const { navigation } = this.props;
    navigation.navigate('jobIndustry', {
      industrys: this.state.industryIds,
      onSelect: (item) => {
        this.setState({
          industryIds: item,
        });
      },
    });
  };

  onSelectProfession = () => {
    const { navigation } = this.props;
    navigation.navigate('jobExpected', {
      expects: this.state.categoryIds,
      onSelect: (item) => {
        this.setState({
          categoryIds: item,
        });
      },
    });
  };

  onSelectCity = () => {
    const { navigation } = this.props;
    navigation.navigate('jobCityList', {
      citys: this.state.locationIds,
      onSelect: (item) => {
        this.setState({
          locationIds: item,
        });
      },
    });
  };

  onSelectSalary = () => {
    const { navigation } = this.props;
    navigation.navigate('jobSalary', {
      onSelect: (item) => {
        this.setState({
          salary: item,
        });
      },
    });
  };

  onChangeText = (text) => {
    this.setState({ reqJobTitle: text });
  };

  keyboardDidShowHandler() {}

  keyboardDidHideHandler() {}

  dissmissKeyboard() {
    Keyboard.dismiss();
  }

  typeSheetSelect = (index) => {
    const {
      jobStore: { jobTypeList },
    } = this.props;
    if (index !== jobTypeList.length) {
      const param = jobTypeList[index];
      this.setState({ jobType: param });
    }
  };

  showTypeSheet = () => {
    this.typeActionSheet.show();
  };

  formatData(array) {
    const list = array.map((item) => item.label);
    list.push(I18n.t('page_job_actionsheet_cancel'));
    return list;
  }

  saveIntension = async () => {
    try {
      const { navigation } = this.props;
      const { salary, categoryIds, industryIds, locationIds, reqJobTitle, jobType, workStatus } =
        this.state;
      const { resumeList } = this.props.resumeStore;
      if (!jobType) {
        this.toast.show(I18n.t('page_job_toast_select_work_status'));
        return;
      }
      if (industryIds.length === 0) {
        this.toast.show(I18n.t('page_job_toast_select_expected_industry'));
        return;
      }
      if (categoryIds.length === 0) {
        this.toast.show(I18n.t('page_job_toast_select_expected_profession'));
        return;
      }
      if (!reqJobTitle) {
        this.toast.show(I18n.t('page_job_toast_select_expected_job'));
        return;
      }
      if (locationIds.length === 0) {
        this.toast.show(I18n.t('page_job_toast_select_work_city'));
        return;
      }
      if (!salary) {
        this.toast.show(I18n.t('page_job_toast_select_salary'));
        return;
      }
      const data = {
        jobTerm: jobType ? jobType.value : 0,
        categoryIds: categoryIds.length > 0 ? categoryIds.map((item) => item.value) : [],
        industryIds: industryIds.length > 0 ? industryIds.map((item) => item.value) : [],
        locationIds: locationIds.length > 0 ? locationIds.map((item) => item.value) : [],
        reqJobTitle,
        salary: salary ? salary.value : 0,
        workStatus: workStatus?.value || 1,
      };
      if (resumeList?.length > 0) {
        data.resumeId = resumeList[0].resumeId;
      }
      uiUtil.showGlobalLoading();
      const res = await this.props.jobAction.updateIntentions(data);
      if (res && res.successful) {
        await this.props.resumeAction.getResumes();
        uiUtil.showRequestResult({
          message: res.message || I18n.t('page_job_toast_add_intension_success'),
        });
        NavigationService.replace('resumeComplete');
      } else {
        uiUtil.showRequestResult(res);
      }
    } catch (error) {
      uiUtil.showRequestResult(error);
    }
  };

  get canSubmit() {
    const { salary, categoryIds, industryIds, locationIds, reqJobTitle, jobType } = this.state;
    return (
      !salary ||
      categoryIds.length === 0 ||
      industryIds.length === 0 ||
      !reqJobTitle ||
      locationIds.length === 0 ||
      !jobType
    );
  }

  renderPropsStatus = () => {
    const { salary, categoryIds, industryIds, locationIds, jobType } = this.state;
    return (
      <View style={{ backgroundColor: '#fff' }}>
        <ListItem
          onPress={this.showTypeSheet}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_job_term_text')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={jobType ? jobType.label : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
        />
        <ListItem
          onPress={this.onSelectIndustry}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_expected_industy')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={industryIds.length > 0 ? industryIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          onPress={this.onSelectProfession}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_expected_profession')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={categoryIds.length > 0 ? categoryIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={[
            jobIntensionStyle.rightContainerStyle,
            { flexWrap: 'wrap', overflow: 'hidden' },
          ]}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          containerStyle={[jobIntensionStyle.listContainerStyle, { paddingRight: 3 }]}
          title={I18n.t('page_job_expected_job')}
          titleStyle={{
            color: titleColor,
            fontSize: 14,
            width: 120,
          }}
          rightElement={
            <Input
              maxLength={30}
              underlineColorAndroid="transparent"
              inputContainerStyle={{
                backgroundColor: '#fff',
                borderBottomWidth: 0,
              }}
              onChangeText={(text) => {
                this.onChangeText(text);
              }}
              placeholder={I18n.t('page_job_toast_select_expected_job')}
              placeholderTextColor={desColor}
              returnKeyType="done"
              containerStyle={{
                width: 240,
                marginRight: 0,
                paddingHorizontal: 0,
                height: 0,
              }}
              inputStyle={{
                fontSize: 14,
                color: titleColor,
                height: 40,
                textAlign: 'right',
                paddingRight: 5,
              }}
              rightIcon={
                <Icon size={15} color={desColor} name="chevron-thin-right" type="entypo" />
              }
            />
          }
        />
        <ListItem
          onPress={this.onSelectCity}
          containerStyle={jobIntensionStyle.listContainerStyle}
          title={I18n.t('page_job_work_city')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={locationIds.length > 0 ? locationIds.map((item) => item.label).join(',') : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
          rightTitleProps={{ numberOfLines: 1 }}
        />
        <ListItem
          onPress={this.onSelectSalary}
          containerStyle={[jobIntensionStyle.listContainerStyle, { borderBottomWidth: 0 }]}
          title={I18n.t('page_job_expected_Salary')}
          titleStyle={{ color: titleColor, fontSize: 14 }}
          rightTitle={salary ? salary.label : ''}
          rightTitleStyle={jobIntensionStyle.rightContainerStyle}
          rightIcon={
            <Icon
              size={15}
              color={desColor}
              name="chevron-thin-right"
              type="entypo"
              iconStyle={{ marginRight: -4 }}
            />
          }
        />
      </View>
    );
  };

  render() {
    const {
      navigation,
      jobStore: { jobTypeList },
    } = this.props;
    return (
      <View style={jobIntensionStyle.completeContainer}>
        <Header
          statusBarProps={{
            barStyle: 'dark-content',
          }}
          containerStyle={[headerStyle.wrapper, { backgroundColor: '#fff' }]}
          leftComponent={<GoBack navigation={navigation} iconColor="#000" />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          <View style={jobIntensionStyle.titleContainer}>
            <Text style={jobIntensionStyle.topTitle}>{I18n.t('page_job_interest_question')}</Text>
            <Text style={jobIntensionStyle.topDes}>
              {I18n.t('page_job_fill_for_recommendation')}
            </Text>
          </View>
          {this.renderPropsStatus()}
        </ScrollView>
        <View style={jobIntensionStyle.submitButton}>
          <Button
            title={I18n.t('page_job_btn_next')}
            buttonStyle={jobIntensionStyle.buttonStyle}
            titleStyle={{ fontSize: 16 }}
            onPress={() => {
              this.saveIntension();
            }}
            disabled={this.canSubmit}
          />
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <ActionSheet
          ref={(ref) => {
            this.typeActionSheet = ref;
          }}
          title={I18n.t('page_job_job_term_text')}
          options={this.formatData(jobTypeList)}
          cancelButtonIndex={jobTypeList ? jobTypeList.length : 0}
          onPress={this.typeSheetSelect}
        />
      </View>
    );
  }
}
