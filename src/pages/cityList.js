import React, { Component } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Head<PERSON>, Badge, Icon } from 'react-native-elements';
import { headerStyle, jobStyle, cityStyle } from '../themes';
import I18n from '../i18n';
import CountryCodePicker from '../components/CountryPicker';
import LoadingModal from '../components/loadingModal';
import Geolocation from '@react-native-community/geolocation';
import util from '../util';

@inject('cityStore', 'jobAction', 'cityAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLoading: false,
      currentLocation: {},
    };
  }

  componentWillUnmount() {
    this.goBack();
  }

  componentDidMount() {
    this.getAllPlace();
  }

  getAllPlace = () => {
    this.props.jobAction.getLocationConstants2();
  };

  getPosition = () => {
    this.setState({ showLoading: true });
    Geolocation.getCurrentPosition(
      (position) => {
        const positionData = position.coords;
        if (positionData && positionData.longitude && positionData.latitude) {
          this.props.cityAction.getLocalCity(positionData.longitude, positionData.latitude).then(
            (ress) => {
              this.setState({ currentLocation: ress || {}, showLoading: false });
            },
            (err) => {
              this.setState({ showLoading: false });
              console.log(err);
            }
          );
        }
      },
      (error) => {
        this.setState({ showLoading: false });
        console.log('定位失败', error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 1000,
      }
    );
  };

  goBack = async () => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onContinueCheck) {
      await fuc.onContinueCheck();
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  selectItem = async (item) => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc) {
      fuc.onSelect(item);
      if (fuc.needSave) {
        item.cityName = util.getCityNameWithCityData(item);
        await this.props.jobAction.saveCurrentCity(item);
      }
    }
    if (item.locationId != 32 && item.locationId != 30) {
      navigation.goBack();
    } else {
      navigation.navigate('main');
      global.emitter.emit('switchCity', item);
    }
  };

  reGetLocation = async (item) => {
    const fuc = this.props.navigation.state.params;
    const { navigation } = this.props;
    if (fuc) {
      if (item && Object.keys(item).length > 0) {
        fuc.onSelect(item);
        if (fuc.needSave) {
          item.cityName = util.getCityNameWithCityData(item);
          await this.props.jobAction.saveCurrentCity(item);
        }
        if (item.locationId != 32) {
          navigation.goBack();
        } else {
          navigation.navigate('main');
          global.emitter.emit('switchCity', item);
        }
      } else {
        this.getPosition();
      }
    }
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  renderLocalTag = () => {
    const data = {
      ...this.props.cityStore.locationCity,
      ...this.state.currentLocation,
    };
    const getLocalCityStatus = this.state.showLoading
      ? I18n.t('page_job_get_positioning')
      : I18n.t('page_job_get_location_fail');
    const cityName =
      data && Object.keys(data).length > 0
        ? util.getCityNameWithCityData(data)
        : getLocalCityStatus;
    return (
      <Badge
        value={cityName}
        containerStyle={cityStyle.localTagItem}
        badgeStyle={cityStyle.localTagBadgeStyle}
        textStyle={cityStyle.localTagText}
        textProps={{ numberOfLines: 1 }}
        onPress={() => {
          this.reGetLocation(data);
        }}
      />
    );
  };

  renderTags = () => {
    const {
      cityStore: { hotCityList },
      navigation,
    } = this.props;
    return hotCityList.map((item, index) => (
      <Badge
        key={item.placeId}
        value={util.getCityNameWithCityData(item)}
        containerStyle={cityStyle.tagItem}
        badgeStyle={cityStyle.tagBadgeStyle}
        textStyle={cityStyle.tagText}
        onPress={() => {
          this.selectItem(item);
          navigation.goBack();
        }}
      />
    ));
  };

  render() {
    const {
      cityStore: { hotCityList },
    } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_job_nav_top_select'), style: headerStyle.center }}
          leftComponent={this.renderLeftIcon()}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <View style={{ flexDirection: 'column', backgroundColor: '#fff' }}>
          <View style={cityStyle.localTitle}>
            <Text style={cityStyle.placeholderTitle}>{I18n.t('page_job_local_region')}</Text>
            <View style={cityStyle.localTags}>{this.renderLocalTag()}</View>
          </View>
          <View style={[cityStyle.hotTitle, { marginBottom: hotCityList.length > 0 ? 10 : 26 }]}>
            <Text style={cityStyle.placeholderTitle}>{I18n.t('page_job_hot_city')}</Text>
            <View style={cityStyle.tags}>{this.renderTags()}</View>
          </View>
        </View>

        <CountryCodePicker
          isShow
          onPick={(res) => {
            this.selectItem(res);
          }}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
