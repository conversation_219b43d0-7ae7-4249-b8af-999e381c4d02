import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, TouchableOpacity, Text, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { Input, CheckBox } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { registerStyle, phColor } from '../themes';
import I18n from '../i18n';
import res from '../res';
import regExp from '../util/regExp';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

let keyBoardIsShow = false;

@inject('loginAction')
@observer
export default class RegisterEmail extends React.Component {
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      userName: '',
      agreement: true,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
  }

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = true;
  }

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  setEmail = (text) => {
    this.setState({
      userName: text,
    });
  };

  setAgreement = () => {
    this.setState({ agreement: !this.state.agreement });
  };

  goSetPassword = () => {
    this.onLostBlur();
    const { navigation } = this.props;
    const { userName, agreement } = this.state;
    if (userName.trim() === '' || !regExp.nameExtra.test(userName.trim())) {
      this.toast.show(I18n.t('page_register_op_email_required'));
      return;
    }
    if (!agreement) {
      this.toast.show(I18n.t('page_register_op_agreement_required'));
      return;
    }
    this.props.loginAction.isUsernameExist(userName).then((result) => {
      if (result && result.existsUsername) {
        this.toast.show(I18n.t('page_register_op_user_existed'));
      } else {
        navigation.navigate('registerEmailPassword', { userName: userName.trim() });
      }
    });
  };

  renderHeader = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.headerSection}>
        <TouchableOpacity
          style={registerStyle.headerBack}
          onPress={() => {
            navigation.navigate('registerPhone');
          }}
        >
          <Image source={res.iconBack} style={registerStyle.iconBack} />
        </TouchableOpacity>
        <View>
          <Text style={registerStyle.headerTitle}>
            {I18n.t('page_register_text_email_header_title')}
          </Text>
        </View>
      </View>
    );
  };

  renderForm = () => (
    <View style={registerStyle.formContent}>
      <View style={[registerStyle.formGroup]}>
        <Input
          autoCapitalize="none"
          inputContainerStyle={registerStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_register_ph_email')}
          placeholderTextColor={phColor}
          onChangeText={this.setEmail}
          maxLength={100}
        />
      </View>
    </View>
  );

  renderFooter = () => {
    const { navigation } = this.props;
    return (
      <View style={registerStyle.linkSection}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            width: '60%',
            marginRight: 20,
          }}
        >
          <CheckBox
            containerStyle={registerStyle.agreementCheckbox}
            textStyle={{ color: '#fff', fontSize: 12, fontWeight: 'normal' }}
            uncheckedIcon="square-o"
            checkedIcon="check-square-o"
            checkedColor="#fff"
            uncheckedColor="#fff"
            size={20}
            checked={this.state.agreement}
            onPress={this.setAgreement}
          />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('agreement');
            }}
          >
            <Text style={registerStyle.agreementLink}>
              {I18n.t('page_register_link_agreement')}
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            flexShrink: 10,
            marginLeft: 20,
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}
        >
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('registerPhone');
            }}
          >
            <Text style={registerStyle.emailRegisterLink}>
              {I18n.t('page_register_link_phone_register')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  render() {
    return (
      // View 用以适配iPhoneX
      <View style={registerStyle.page}>
        <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
          <ImageBackground source={res.bgLogin} style={{ width: '100%', height: '100%' }}>
            <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
              <View style={[registerStyle.container]}>
                {this.renderHeader()}
                <View style={registerStyle.titleSection}>
                  <Text style={registerStyle.title}>{I18n.t('page_register_text_title')}</Text>
                </View>
                <View behavior="padding" style={registerStyle.emailFormSection}>
                  {this.renderForm()}
                </View>
                <View style={registerStyle.btnSection}>
                  <TouchableOpacity style={registerStyle.btnRegister} onPress={this.goSetPassword}>
                    <Text style={registerStyle.btnRegisterText}>
                      {I18n.t('page_register_btn_next')}
                    </Text>
                  </TouchableOpacity>
                </View>
                {this.renderFooter()}
              </View>
            </TouchableWithoutFeedback>
          </ImageBackground>
        </TouchableWithoutFeedback>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
