import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, TouchableOpacity, InteractionManager, FlatList } from 'react-native';
import { Header } from 'react-native-elements';
import { headerStyle, deliveredListStyle, desColor } from '../themes';
import I18n from '../i18n';
import GoBack from '../components/goback';
import res, { getEmployerAvatarSource } from '../res';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('jobStore', 'jobAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.state = {
      showLoading: true,
      page: 1,
      hasMore: false,
      refreshing: false,
    };
  }

  async componentDidMount() {
    await InteractionManager.runAfterInteractions(() => {
      this.getJobs();
    });
  }

  onToJobDetail = async (item) => {
    const data = await this.props.jobAction.querySingleJobs(item.jobId);
    this.props.navigation.navigate('jobDetail', { detail: data });
  };

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.state.page = 1;
    await this.getJobs();
    this.setState({ refreshing: false });
  };

  onLoadMore = async () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.state.page += 1;
      this.isLoadingMore = true;
      await this.getJobs();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  getJobs = async () => {
    await this.props.jobAction
      .queryDeliveredJobs({ page: this.state.page, size: 10, jobApplyType: 0 })
      .catch(() => {
        this.setState({ showLoading: false, refreshing: false });
      });
    const { deliveredList, deliveredTotal } = this.props.jobStore;
    this.setState({ hasMore: deliveredList.length < parseFloat(deliveredTotal) });
    this.setState({ showLoading: false });
  };

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderJobList = ({ item }) => (
    <TouchableOpacity key={item.id} onPress={() => this.onToJobDetail(item)}>
      <View style={deliveredListStyle.listContainer}>
        <View style={deliveredListStyle.namePannel}>
          <ImageBackground
            imageStyle={{ borderRadius: 21 }}
            style={deliveredListStyle.companyLogo}
            source={getEmployerAvatarSource(item.employerLogo ? item.employerLogo : '')}
          >
            {item &&
            item.employerQualificationType &&
            item.employerQualificationType.value === 1 ? (
              <Image style={deliveredListStyle.v2} source={res.verify} />
            ) : (
              <View />
            )}
          </ImageBackground>
          <View style={{ flexDirection: 'column', marginLeft: 10, width: '80%' }}>
            <Text numberOfLines={1} style={deliveredListStyle.companySubcribe}>
              {item ? item.company : ''}{' '}
              {item && item.qualificationStatus && item.qualificationStatus.value === 1 ? (
                <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
              ) : null}
            </Text>
            <Text numberOfLines={1} style={deliveredListStyle.nameSubcribe}>
              {item ? item.jobTitle : ''}
            </Text>
            <Text numberOfLines={1} style={deliveredListStyle.jobWages}>
              {item && item.salaryId ? item.salaryId.label : ''}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  render() {
    const { navigation } = this.props;
    const { deliveredList } = this.props.jobStore;
    return (
      <View style={deliveredListStyle.deliveredContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_mine_label_has_send'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <FlatList
          data={deliveredList && deliveredList.length > 0 ? deliveredList.slice() : []}
          ListEmptyComponent={this.renderEmptyComponent}
          renderItem={this.renderJobList}
          keyExtractor={(item, index) => index + item}
          showsVerticalScrollIndicator={false}
          horizontal={false}
          ListFooterComponent={this.renderFooter}
          refreshing={this.state.refreshing}
          onRefresh={this.onRefresh}
          onEndReachedThreshold={0.1}
          onEndReached={() => this.onLoadMore()}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
