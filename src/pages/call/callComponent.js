import React, { Component } from 'react';
import { Keyboard, PanResponder, View } from '../../components';
import { inject, observer } from 'mobx-react';
import constant from '../../store/constant';
import callAction from '../../store/actions/callAction';
import meetingAction from '../../store/actions/meetingAction';
import styles from '../../themes';
import { getCallStyle } from '../../themes/pages/call';
import ReceiveCall from './receiveCall';
import SingleAudioCall from './singleAudioCall';
import SingleVideoCall from './singleVideoCall';
import MeetingCall from './meetingCall';
import { autorun } from 'mobx';
import AppModule from '../../modules/AppModule';
import KeyboardUtil from '../../util/KeyboardUtil';
import PermissionUtilExtra from '../../util/permissionUtilExtra';

/**
 * 音视频通话
 * <AUTHOR>
 */
@inject('store')
@observer
export default class CallComponent extends Component {
  constructor(props) {
    super(props);
    this.style = getCallStyle(styles.get('theme'));
    this.state = {
      marginTop: 0,
      marginLeft: 0,
      loadingText: '',
    };
    this.lastX = this.state.marginLeft;
    this.lastY = this.state.marginTop;
    this.presentState = false;
    this.time = 0;
  }

  componentDidMount() {
    let _showCall = false;
    this.autorunDisposer = autorun(() => {
      const { showCall, callType } = this.props.store.callStore;
      console.debug(`callComponent autorun showCall:${showCall} _showCall:${_showCall}`);
      if (_showCall !== showCall) {
        _showCall = showCall;
        console.debug(`callComponent autorun keepScreenOn:${showCall}`);
        AppModule.keepScreenOn(!!showCall);
        this.changeLoadingText(showCall);
        if (showCall) {
          KeyboardUtil.dismiss();
          if (
            callType === constant.callType.meetingCall ||
            callType === constant.callType.singleVideoCall
          ) {
            PermissionUtilExtra.requestMultiple();
          } else {
            PermissionUtilExtra.requestAudioPermission();
          }
        }
      }
    });
  }

  componentWillUnmount() {
    this.autorunDisposer?.();
    this.changeLoadingText(false);
  }

  changeLoadingText = (showCall) => {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    if (!showCall) return;
    this.timer = setInterval(() => {
      this.time += 1;
      let text = '...';
      const num = this.time % 4;
      if (num === 0) {
        text = '';
      } else if (num === 1) {
        text = '.';
      } else if (num === 2) {
        text = '..';
      }
      this.setState({ loadingText: text });
    }, 500);
  };

  panResponder = PanResponder.create({
    //开启点击手势响应
    onStartShouldSetPanResponder: (evt, gestureState) => true,
    //开启点击手势响应是否劫持 true：不传递给子view false：传递给子view
    onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
    //开启移动手势响应
    onMoveShouldSetPanResponder: (evt, gestureState) => true,
    //开启移动手势响应是否劫持 true：不传递给子view false：传递给子view
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
    //手指触碰屏幕那一刻触发 成为激活状态。
    onPanResponderGrant: (evt, gestureState) => {
      this.presentState = true;
    },
    // 表示手指按下时，成功申请为事件响应者的回调。
    onPanResponderStart: (evt, gestureState) => {
      // 表示申请成功，你成为了事件的响应者，这个时候开始，组件就进入了激活状态。
      // console.log(`手指按下: ${gestureState.dx}   gestureState.dy : ${gestureState.dy}`);
    },
    //手指在屏幕上移动触发
    onPanResponderMove: (evt, gestureState) => {
      // console.log(`移动触发: ${gestureState.dx}   gestureState.dy : ${gestureState.dy}`);
      // 如果累计的移动距离小于5 则表示没移动
      if (
        gestureState.dx < 5 &&
        gestureState.dx > -5 &&
        gestureState.dy < 5 &&
        gestureState.dy > -5
      ) {
        this.presentState = false;
      } else {
        this.presentState = true;
        this.setState({
          marginLeft: this.lastX + gestureState.dx,
          marginTop: this.lastY + gestureState.dy < 0 ? 0 : this.lastY + gestureState.dy,
        });
      }
    },
    //手指离开屏幕触发
    onPanResponderRelease: (evt, gestureState) => {
      // console.log(`手指离开屏幕触发: ${gestureState.dx}   gestureState.dy : ${gestureState.dy}`);
      if (
        gestureState.dx < 5 &&
        gestureState.dx > -5 &&
        gestureState.dy < 5 &&
        gestureState.dy > -5
      ) {
        this.presentState = false;
      }
      // 如果移动了 则进行移动操作
      if (this.presentState) {
        this.lastX = this.state.marginLeft;
        this.lastY = this.state.marginTop;
      } else {
        // 如果判断没移动 则进行返回操作
        this.onZoomIn();
      }
    },
    onPanResponderTerminate: (evt, gestureState) => {},
  });

  refuseCall = () => {
    const { callType } = this.props.store.callStore;
    if (callType === constant.callType.meetingCall) {
      meetingAction.refuseCall();
    } else {
      callAction.refuseCall();
    }
  };

  hungUp = () => {
    const { callState, callType } = this.props.store.callStore;
    if (callState === constant.callState.invite) {
      callAction.cancelCall();
    } else {
      callAction.endCall();
    }
  };

  acceptCall = async () => {
    // const granted = await PermissionUtil.requestMultiple([
    //   PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    //   PermissionsAndroid.PERMISSIONS.CAMERA,
    // ]);
    // if (!granted) return;

    const { callType } = this.props.store.callStore;
    if (callType === constant.callType.meetingCall) {
      meetingAction.acceptCall();
    } else {
      callAction.acceptCall();
    }
  };

  onZoomIn = () => {
    Keyboard.dismiss();
    this.props.store.callStore.zoomIn = !this.props.store.callStore.zoomIn;
  };

  render() {
    const { style } = this;
    const { showCall, callState, callType, zoomIn, localInfo } = this.props.store.callStore;

    const moveStyle = {
      marginTop: this.state.marginTop,
      marginLeft: this.state.marginLeft,
    };

    if (showCall) {
      if (
        callState === constant.callState.receiveInvite ||
        localInfo?.callState === constant.callState.receiveInvite
      ) {
        return (
          <View style={[style.container, zoomIn ? { height: 'auto' } : {}]}>
            <ReceiveCall
              acceptCall={this.acceptCall}
              refuseCall={this.refuseCall}
              loadingText={this.state.loadingText}
              onZoomIn={this.onZoomIn}
            />
          </View>
        );
      } else {
        if (callType === constant.callType.singleAudioCall) {
          return (
            <View
              style={[
                style.container,
                zoomIn ? style.zoomAudioContentStyle : {},
                zoomIn ? moveStyle : {},
              ]}
              {...(zoomIn ? this.panResponder.panHandlers : {})}
            >
              <SingleAudioCall onZoomIn={this.onZoomIn} loadingText={this.state.loadingText} />
            </View>
          );
        } else if (callType === constant.callType.singleVideoCall) {
          return (
            <View
              style={[
                style.container,
                zoomIn ? style.zoomVideoContentStyle : {},
                zoomIn ? moveStyle : {},
              ]}
              {...(zoomIn ? this.panResponder.panHandlers : {})}
            >
              <SingleVideoCall onZoomIn={this.onZoomIn} loadingText={this.state.loadingText} />
            </View>
          );
        } else if (callType === constant.callType.meetingCall) {
          return (
            <View
              style={[
                style.container,
                zoomIn ? style.zoomAudioContentStyle : {},
                zoomIn ? moveStyle : {},
              ]}
              {...(zoomIn ? this.panResponder.panHandlers : {})}
            >
              <MeetingCall onZoomIn={this.onZoomIn} loadingText={this.state.loadingText} />
            </View>
          );
        }
      }
    }
    return null;
  }
}
