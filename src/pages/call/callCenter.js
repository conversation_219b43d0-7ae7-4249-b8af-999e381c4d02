import React from 'react';
import { ImageBackground, Text, View } from '../../components';
import I18n from '../../i18n';
import constant from '../../store/constant';
import callAction from '../../store/actions/callAction';
import styles from '../../themes';
import resIcon from '../../res';
import Avatar from '../../components/avatar/avatar';
import avatarUtil from '../../util/avatarUtil';

function getStyle() {
  const theme = styles.get('theme');
  return {
    centerContainerBG: { flex: 1, backgroundColor: '#000000' },
    centerContainer: {
      width: '100%',
      height: '100%',
      backgroundColor: '#00000020',
    },
    audioCenterContainer: {
      height: 150,
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: 110,
      alignSelf: 'center',
    },
    statusText: {
      marginTop: 16,
      fontSize: 16,
      color: '#ffffff',
      marginBottom: 12,
    },
    timeText: {
      fontSize: 18,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
    largeNameText: {
      marginTop: 14,
      fontSize: 28,
      fontWeight: theme.fontWeightMedium,
      color: '#fff',
    },
  };
}

/**
 * 音视频通话中间组件
 */
export default class CallCenter extends React.PureComponent {
  constructor(props) {
    super(props);
    this.style = getStyle();
  }

  render() {
    const { children, avatar, name, callState, statusText, loadingText, timeCount } = this.props;
    const { style } = this;
    return (
      <ImageBackground
        style={style.centerContainerBG}
        blurRadius={5}
        // source={{ uri: 'https://img.51miz.com/Element/00/95/45/90/07649019_E954590_8c1afdf0.jpg' }}
        source={avatar ? { uri: avatarUtil.handleAvatar(avatar) } : resIcon.defaultAvatar}
        resizeMode="cover"
      >
        <View style={style.centerContainer}>
          <View style={style.audioCenterContainer}>
            <Avatar
              avatar={avatar}
              name={name}
              textStyle={{ fontSize: 50 }}
              size={200}
              borderRadius={60}
            />
            <Text style={style.largeNameText}>{name}</Text>
            {callState === constant.callState.accept ? (
              <Text style={style.statusText}>{I18n.t('page_call_accept')}</Text>
            ) : null}
            {statusText ? (
              <Text style={style.statusText}>
                {statusText}
                {loadingText}
              </Text>
            ) : null}
            {callState === constant.callState.accept ? (
              <Text style={style.timeText}>{callAction.formatHHMMSS(timeCount)}</Text>
            ) : null}
            {children}
          </View>
        </View>
      </ImageBackground>
    );
  }
}
