import React, { Component } from 'react';
import { Image, ScrollView, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import I18n from '../../i18n';
import constant from '../../store/constant';
import styles from '../../themes';
import resIcon from '../../res';
import Avatar from '../../components/avatar/avatar';
import { deviceWidth, footerHeight, statusBarHeight } from '../../common';
import CallCenter from './callCenter';

function getStyle() {
  const theme = styles.get('theme');
  return {
    container: {
      marginTop: statusBarHeight,
      minHeight: 120,
      backgroundColor: '#000000C2',
      marginHorizontal: 10,
      borderRadius: 10,
      paddingHorizontal: 15,
      paddingTop: 20,
    },
    topContainer: {
      // height: 90,
      marginTop: 10,
      flexDirection: 'row',
    },
    leftContainer: {
      flexDirection: 'row',
    },
    nameText: {
      marginLeft: 5,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightMedium,
      lineHeight: 22,
      color: '#fff',
    },
    descBox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    descText: {
      marginLeft: 5,
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 17,
      color: '#fff',
    },
    itemContainer: {
      alignItems: 'center',
      width: 40,
    },
    rightContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      marginVertical: 15,
    },
    usersContainer: {
      flexDirection: 'row',
      marginTop: 5,
    },
    otherText: {
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightRegular,
      lineHeight: 17,
      color: '#FFFFFFB3',
    },
    itemBox: {
      width: deviceWidth / 2 - 30,
      height: 38,
      borderRadius: 8,
      backgroundColor: '#FF4B5A',
    },
    itemBoxAccept: {
      backgroundColor: '#0AB28B',
    },
    itemText: {
      fontSize: 17,
      color: '#fff',
      lineHeight: 38,
      textAlign: 'center',
    },

    controlContainer: {
      height: 140,
      width: deviceWidth,
      position: 'absolute',
      bottom: footerHeight + 20,
    },
    btnsContainer: {
      flex: 1,
      flexDirection: 'row',
      paddingHorizontal: 20,
      justifyContent: 'space-around',
    },
    topActionContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
    },
    zoomContainer: {
      justifyContent: 'center',
    },
    statusText: {
      marginTop: 16,
      fontSize: 16,
      fontWeight: theme.fontWeightRegular,
      color: '#ffffff',
      marginBottom: 12,
    },
    largeTopContainer: {
      height: 50,
      width: '100%',
      position: 'absolute',
      zIndex: 1000,
      top: statusBarHeight,
      flexDirection: 'row',
      alignItems: 'center',
    },
    largeImg: {
      width: 72,
      height: 72,
    },
    largeImgText: {
      fontSize: 12,
      color: '#fff',
      fontWeight: theme.fontWeightMedium,
      lineHeight: 14,
      textAlign: 'center',
      marginTop: 6,
    },
  };
}

/**
 * 音视频通话接收
 * <AUTHOR>
 */
@inject('store')
@observer
export default class receiveCall extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle();
  }

  renderMeeting = () => {
    const { style } = this;
    const { refuseCall, acceptCall, loadingText } = this.props;
    const { otherUsers, hostInfo } = this.props.store.callStore;
    const { imId } = this.props.store.userStore;
    const truncatedArray = otherUsers.filter((x) => x.imId != imId);
    return (
      <View style={style.container}>
        <View style={style.topContainer}>
          <View style={{ flex: 1 }}>
            <Text style={style.nameText}>{hostInfo?.name}</Text>
            <View style={style.descBox}>
              <Image source={resIcon.audioCall} />
              <Text style={style.descText}>
                {I18n.t('page_call_notice_invite_metting')}
                {loadingText}
              </Text>
            </View>
          </View>
          <Avatar avatar={hostInfo?.avatar} name={hostInfo?.name} size={40} borderRadius={16} />
        </View>

        <View style={{ marginTop: 16 }}>
          <Text style={style.otherText}>{I18n.t('page_call_invite_other')}</Text>

          <View style={[style.usersContainer, {}]}>
            <ScrollView style={{ flex: 1 }} horizontal showsHorizontalScrollIndicator={false}>
              {truncatedArray.map((user, index) => (
                <Avatar
                  key={index.toString()}
                  style={{ marginRight: 8 }}
                  avatar={user?.avatar}
                  name={user?.name}
                  textStyle={{ fontSize: 12 }}
                  size={24}
                  borderRadius={9}
                />
              ))}
            </ScrollView>
          </View>
        </View>

        <View style={style.rightContainer}>
          <Touchable onPress={refuseCall} style={style.itemBox}>
            <Text style={style.itemText}>{I18n.t('page_chat_btn_title_reject')}</Text>
          </Touchable>
          <Touchable onPress={acceptCall} style={[style.itemBox, style.itemBoxAccept]}>
            <Text style={style.itemText}>{I18n.t('page_chat_btn_title_accept')}</Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderSingle = () => {
    const { style } = this;

    const { refuseCall, acceptCall, loadingText } = this.props;
    const { callType, user } = this.props.store.callStore;

    return (
      <View style={[style.container]}>
        {/* <TouchableOpacity onPress={this.props.onZoomIn} activeOpacity={1} style={style.leftContainer}> */}
        <View style={style.leftContainer}>
          <View style={{ flex: 1 }}>
            <Text style={style.nameText}>{user?.name}</Text>
            <View style={style.descBox}>
              <Image
                source={
                  callType === constant.callType.singleAudioCall
                    ? resIcon.audioCall
                    : resIcon.videoCall
                }
              />
              <Text style={style.descText}>
                {callType === constant.callType.singleAudioCall
                  ? I18n.t('page_call_receive_audio')
                  : I18n.t('page_call_receive_video')}
                {loadingText}
              </Text>
            </View>
          </View>
          <Avatar avatar={user?.avatar} name={user?.name} size={40} borderRadius={16} />
        </View>
        {/* </TouchableOpacity> */}

        <View style={style.rightContainer}>
          <Touchable onPress={refuseCall} style={style.itemBox}>
            <Text style={style.itemText}>{I18n.t('page_chat_btn_title_reject')}</Text>
          </Touchable>
          <Touchable onPress={acceptCall} style={[style.itemBox, style.itemBoxAccept]}>
            <Text style={style.itemText}>{I18n.t('page_chat_btn_title_accept')}</Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderLarge = () => {
    const { callType } = this.props.store.callStore;
    return (
      <View
        style={{
          width: '100%',
          height: '100%',
        }}
      >
        {this.renderHeader()}
        {callType === constant.callType.meetingCall
          ? this.renderMeetingCenter()
          : this.renderCenter()}
        {this.renderControl()}
      </View>
    );
  };

  renderHeader = () => {
    const { style } = this;
    return (
      <View style={style.largeTopContainer}>
        <View style={style.topActionContainer}>
          <Touchable onPress={this.props.onZoomIn} style={style.zoomContainer}>
            <Image source={resIcon.zoom} />
          </Touchable>
        </View>
      </View>
    );
  };

  renderCenter = () => {
    const { style } = this;
    const { callType, user, timeCount, callState } = this.props.store.callStore;
    const { loadingText } = this.props;
    return (
      <CallCenter
        style={style}
        avatar={user?.avatar}
        name={user?.name}
        callState={callState}
        statusText={
          callType === constant.callType.singleAudioCall
            ? I18n.t('page_call_receive_audio')
            : I18n.t('page_call_receive_video')
        }
        loadingText={loadingText}
        timeCount={timeCount}
      />
    );
  };

  renderMeetingCenter = () => {
    const { style } = this;
    const { timeCount, callState, otherUsers, hostInfo } = this.props.store.callStore;
    const { imId } = this.props.store.userStore;
    const truncatedArray = otherUsers.filter((x) => x.imId != imId);

    const { loadingText } = this.props;
    return (
      <CallCenter
        avatar={hostInfo?.avatar}
        name={hostInfo?.name}
        callState={callState}
        statusText={I18n.t('page_call_notice_invite_metting')}
        loadingText={loadingText}
        timeCount={timeCount}
      >
        <View
          style={{
            marginTop: 16,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text style={[style.otherText, { textAlign: 'center', fontSize: 16 }]}>
            {I18n.t('page_chat_text_participants')}
          </Text>
          <ScrollView
            style={{ flex: 1, marginTop: 8 }}
            horizontal
            showsHorizontalScrollIndicator={false}
          >
            <View
              style={[
                style.usersContainer,
                {
                  flex: 1,
                  paddingHorizontal: 16,
                },
              ]}
            >
              {truncatedArray.map((user, index) => (
                <Avatar
                  key={index.toString()}
                  style={{ marginRight: index === truncatedArray.length - 1 ? 0 : 16 }}
                  avatar={user?.avatar}
                  name={user?.name}
                  textStyle={{ fontSize: 12 }}
                  size={50}
                  borderRadius={20}
                />
              ))}
            </View>
          </ScrollView>
        </View>
      </CallCenter>
    );
  };

  renderControl = () => {
    const { style } = this;
    const { refuseCall, acceptCall } = this.props;
    const { callType } = this.props.store.callStore;
    return (
      <View style={style.controlContainer}>
        <View style={style.btnsContainer}>
          <Touchable onPress={refuseCall} style={style.itemContainer}>
            <Image source={resIcon.rejectCall} style={style.largeImg} />
            <Text style={style.largeImgText}>{I18n.t('page_chat_btn_title_reject')}</Text>
          </Touchable>
          <Touchable onPress={acceptCall} style={style.itemContainer}>
            <Image
              source={
                callType === constant.callType.singleAudioCall
                  ? resIcon.audioAccept
                  : resIcon.videoAccept
              }
              style={style.largeImg}
            />
            <Text style={style.largeImgText}>{I18n.t('page_chat_btn_title_accept')}</Text>
          </Touchable>
        </View>
      </View>
    );
  };

  render() {
    const { callType, zoomIn } = this.props.store.callStore;
    if (zoomIn) {
      return callType === constant.callType.meetingCall
        ? this.renderMeeting()
        : this.renderSingle();
    }
    return this.renderLarge();
  }
}
