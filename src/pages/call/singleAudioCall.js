import React, { Component } from 'react';
import { Image, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import I18n from '../../i18n';
import constant from '../../store/constant';
import meetingAction from '../../store/actions/meetingAction';
import callAction from '../../store/actions/callAction';
import styles from '../../themes';
import resIcon from '../../res';
import AgoraSdk from '../../api/agoraSdk';
import ChatBookModal from '../chat/components/chatBookModal';
import { getCallStyle } from '../../themes/pages/call';
import CallCenter from './callCenter';

/**
 * 私聊音频通话
 * <AUTHOR>
 */
@inject('store')
@observer
export default class SingleAudioCall extends Component {
  constructor(props) {
    super(props);
    this.style = getCallStyle(styles.get('theme'));
  }

  hungUp = () => {
    const { callState } = this.props.store.callStore;
    if (callState === constant.callState.invite) {
      callAction.cancelCall();
    } else {
      callAction.endCall();
    }
  };

  acceptCall = () => {
    callAction.acceptCall();
  };

  // 开启或关闭麦克风。
  switchMicrophone = async () => {
    const { openMicrophone } = this.props.store.callStore;
    AgoraSdk.engine
      ?.enableLocalAudio(!openMicrophone)
      .then(() => {
        this.props.store.callStore.openMicrophone = !openMicrophone;
      })
      .catch((err) => {
        console.warn('enableLocalAudio', err);
      });
  };

  // 选择音频播放设备为扬声器或耳机。
  switchSpeakerphone = async () => {
    callAction.enableSpeakerphone();
  };

  // 邀请人进入
  onAddUser = () => {
    const { peersMap, user, peerId } = this.props.store.callStore;
    this.chatBookModal.open({
      disabledList:
        peersMap && peersMap.size > 0 ? Array.from(peersMap.values()) : [{ ...user, imId: peerId }],
    });
  };

  // 邀请人进入回调
  addCheck = (res) => {
    meetingAction.singleInviteMembers(res);
  };

  renderHeader = () => {
    const { style } = this;
    // const { callState } = this.props.store.callStore;
    return (
      <View style={style.topContainer}>
        <View style={style.topActionContainer}>
          <Touchable onPress={this.props.onZoomIn} style={style.zoomContainer}>
            <Image source={resIcon.zoom} />
          </Touchable>
          {/*{callState === constant.callState.accept ? (
            <Touchable onPress={this.onAddUser} style={style.zoomContainer}>
              <Image source={resIcon.meetingAddUser} />
            </Touchable>
          ) : null}*/}
        </View>
      </View>
    );
  };

  renderCenter = () => {
    const { style } = this;
    const { user, timeoutCount, timeCount, callState } = this.props.store.callStore;
    let text;
    if (timeoutCount % 3 === 0) {
      text = '.';
    } else if (timeoutCount % 3 === 1) {
      text = '..';
    } else {
      text = '...';
    }
    return (
      <CallCenter
        style={style}
        avatar={user?.avatar}
        name={user?.name}
        callState={callState}
        statusText={callState === constant.callState.invite && I18n.t('page_call_watting')}
        loadingText={text}
        timeCount={timeCount}
      />
    );
  };

  renderControl = () => {
    const { style } = this;
    const { callState, timeoutCount, enableSpeakerphone, openMicrophone } =
      this.props.store.callStore;
    return (
      <View style={style.controlContainer}>
        <View style={{ marginHorizontal: 28, height: 50, marginBottom: 20 }}>
          {callState === constant.callState.invite && timeoutCount >= 15 && timeoutCount <= 25 ? (
            <View style={style.tipsContainer}>
              <Text style={style.tipsText}>{I18n.t('page_call_no_response')}</Text>
            </View>
          ) : null}
        </View>
        <View style={style.btnsContainer}>
          <Touchable onPress={this.switchMicrophone} style={style.itemContainer}>
            <Image source={openMicrophone ? resIcon.micOpen : resIcon.micClose} />
            <Text style={style.itemText}>{I18n.t('page_call_btn_mic')}</Text>
          </Touchable>
          <Touchable onPress={this.hungUp} style={style.itemContainer}>
            <Image source={resIcon.hungUp} />
            <Text style={style.itemText}>
              {callState === constant.callState.invite
                ? I18n.t('page_call_btn_cancel')
                : I18n.t('page_call_btn_end')}
            </Text>
          </Touchable>
          <Touchable onPress={this.switchSpeakerphone} style={style.itemContainer}>
            <Image source={enableSpeakerphone ? resIcon.speakOpen : resIcon.speakClose} />
            <Text style={style.itemText}>{I18n.t('page_call_btn_speak')}</Text>
          </Touchable>
        </View>
      </View>
    );
  };

  render() {
    const { style } = this;
    const { callState, timeCount, zoomIn } = this.props.store.callStore;
    if (zoomIn) {
      return (
        <Touchable
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}
          onPress={() => {
            this.props.onZoomIn();
          }}
        >
          <Image style={{ width: 16, height: 16, marginLeft: 14 }} source={resIcon.zoomAudio} />
          {callState === constant.callState.invite ? (
            <Text style={style.zoomStateText}>{I18n.t('page_call_text_wait')}</Text>
          ) : (
            <Text style={style.zoomTimeText}>{callAction.formatHHMMSS(timeCount)}</Text>
          )}
        </Touchable>
      );
    } else {
      return (
        <View
          style={{
            width: '100%',
            height: '100%',
          }}
        >
          {this.renderHeader()}
          {this.renderCenter()}
          {this.renderControl()}
          <ChatBookModal
            fromSingle
            ref={(ref) => (this.chatBookModal = ref)}
            addCheck={this.addCheck}
          />
        </View>
      );
    }
  }
}
