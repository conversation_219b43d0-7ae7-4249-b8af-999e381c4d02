import React, { Component } from 'react';
import { Text, View, Touchable, Image, ImageBackground, Keyboard } from '../../components';
import { inject, observer } from 'mobx-react';
import I18n from '../../i18n';
import constant from '../../store/constant';
import styles from '../../themes';
import resIcon from '../../res';
import { RtcLocalView, RtcRemoteView, VideoRenderMode } from 'react-native-agora';
import AgoraSdk from '../../api/agoraSdk';
import Avatar from '../../components/avatar/avatar';
import LinearGradient from 'react-native-linear-gradient';
import avatarUtil from '../../util/avatarUtil';
import ChatBookModal from '../chat/components/chatBookModal';
import SafeView from '../../components/safeView';
import { getCallStyle } from '../../themes/pages/call';

/**
 * 私聊视频通话
 * <AUTHOR>
 */
@inject('callStore', 'userStore', 'callAction', 'meetingAction')
@observer
export default class SingleVideoCall extends Component {
  constructor(props) {
    super(props);
    this.style = getCallStyle(styles.get('theme'));
  }

  hungUp = () => {
    const { callState } = this.props.callStore;
    if (callState === constant.callState.invite) {
      this.props.callAction.cancelCall();
    } else {
      this.props.callAction.endCall();
    }
  };

  acceptCall = () => {
    this.props.callAction.acceptCall();
  };

  // 开启或关闭麦克风。
  switchMicrophone = async () => {
    this.props.callAction.enableMicrophone();
  };

  // 选择音频播放设备为扬声器或耳机。
  switchSpeakerphone = async () => {
    this.props.callAction.enableSpeakerphone();
  };

  // 打开麦克风
  openCamera = async () => {
    this.props.callAction.enableVideo();
  };

  //切换摄像头
  switchCamera = async () => {
    await AgoraSdk.engine?.switchCamera();
  };

  switchToAudio = () => {
    this.props.callAction.switchToAudio(constant.callType.singleAudioCall);
  };

  // 邀请人进入
  onAddUser = () => {
    const { peersMap, user, peerId } = this.props.callStore;
    this.chatBookModal.open({
      disabledList:
        peersMap && peersMap.size > 0 ? Array.from(peersMap.values()) : [{ ...user, imId: peerId }],
    });
  };

  // 邀请人进入回调
  addCheck = (res) => {
    this.props.meetingAction.singleInviteMembers(res);
  };

  renderHeader = () => {
    const { style } = this;
    const { timeCount, callType, callState } = this.props.callStore;
    return (
      <View style={style.topContainer}>
        <View style={style.topActionContainer}>
          <Touchable onPress={this.props.onZoomIn} style={style.zoomContainer}>
            <Image source={resIcon.zoom} />
          </Touchable>
          {callState === constant.callState.accept ? (
            <Text style={style.titleText}>{this.props.callAction.formatHHMMSS(timeCount)}</Text>
          ) : null}
          {/*<Touchable onPress={this.onAddUser} style={style.zoomContainer}>
            <Image source={resIcon.meetingAddUser} />
          </Touchable>*/}
        </View>
      </View>
    );
  };

  renderCenter = () => {
    const { style } = this;
    const { callState, user, peerId, channel } = this.props.callStore;

    return (
      <View style={style.centerContainer}>
        <ImageBackground
          style={{ flex: 1, backgroundColor: 'black' }}
          blurRadius={10}
          source={
            user?.avatar ? { uri: avatarUtil.handleAvatar(user?.avatar) } : resIcon.defaultAvatar
          }
        >
          <View style={{ flex: 1 }}>
            <View style={style.bigVideoContainer}>
              {callState === constant.callState.invite ? (
                <LinearGradient
                  colors={['#434545', 'transparent']}
                  style={[style.videoUserBox, { paddingRight: 20 }]}
                >
                  <View style={[style.videoUserInfo, { flex: 1 }]}>
                    <Text style={style.videoUserNameText}>{user?.name}</Text>
                    <Text style={style.videoUserStatusText}>
                      {I18n.t('page_call_watting_video')}
                    </Text>
                  </View>
                  <Avatar
                    avatar={user?.avatar}
                    name={user?.name}
                    textStyle={{ fontSize: 30 }}
                    size={60}
                    borderRadius={25}
                  />
                </LinearGradient>
              ) : null}
              {callState === constant.callState.invite
                ? this.renderLocalVideo(channel)
                : this.renderRemoteVideo(peerId, channel)}
            </View>
            {callState === constant.callState.accept ? (
              <View style={style.smallVideoContainer}>{this.renderLocalVideo(channel)}</View>
            ) : null}
          </View>
        </ImageBackground>
      </View>
    );
  };

  renderLocalVideo = (channel) => {
    const { openCamera } = this.props.callStore;
    const { avatar } = this.props.userStore;
    const { style } = this;

    return !openCamera ? (
      <ImageBackground
        style={style.boxVideoM}
        blurRadius={10}
        source={avatar ? { uri: avatarUtil.handleAvatar(avatar) } : resIcon.defaultAvatar}
      >
        <Avatar avatar={avatar} textStyle={{ fontSize: 30 }} size={54} borderRadius={22} />
      </ImageBackground>
    ) : (
      <RtcLocalView.SurfaceView
        style={{ flex: 1 }}
        channelId={channel}
        renderMode={VideoRenderMode.Hidden}
        zOrderMediaOverlay={true}
      />
    );
  };

  renderRemoteVideo = (peerId, channel) => {
    const { remoteVideoMute } = this.props.callStore;
    const { user } = this.props.callStore;
    const { style } = this;

    return remoteVideoMute ? (
      <ImageBackground
        style={style.boxVideoL}
        blurRadius={10}
        source={
          user?.avatar ? { uri: avatarUtil.handleAvatar(user?.avatar) } : resIcon.defaultAvatar
        }
      >
        <Avatar avatar={user?.avatar} textStyle={{ fontSize: 30 }} size={110} borderRadius={42} />
      </ImageBackground>
    ) : (
      <RtcRemoteView.SurfaceView
        style={{ flex: 1 }}
        uid={peerId}
        channelId={channel}
        renderMode={VideoRenderMode.Hidden}
      />
    );
  };

  renderControl = () => {
    const { style } = this;
    const { callState, timeoutCount } = this.props.callStore;

    return (
      <View style={style.controlContainer}>
        <View style={{ marginHorizontal: 28, height: 50, marginBottom: 20 }}>
          {callState === constant.callState.invite && timeoutCount >= 15 && timeoutCount <= 25 ? (
            <View style={style.tipsContainer}>
              <Text style={style.tipsText}>{I18n.t('page_call_no_response')}</Text>
            </View>
          ) : null}
        </View>
        <View style={style.btnsContainer}>
          {callState === constant.callState.invite ? null : (
            <Touchable onPress={this.switchCamera} style={style.itemContainer}>
              <Image source={resIcon.cameraSwitch} />
              <Text style={style.itemText}>{I18n.t('page_call_btn_switch_camera')}</Text>
            </Touchable>
          )}
          <Touchable onPress={this.hungUp} style={style.itemContainer}>
            <Image source={resIcon.hungUp} />
            <Text style={style.itemText}>
              {callState === constant.callState.invite
                ? I18n.t('page_call_btn_cancel')
                : I18n.t('page_call_btn_end')}
            </Text>
          </Touchable>
          <Touchable onPress={this.switchToAudio} style={style.itemContainer}>
            <Image source={resIcon.switchVoice} />
            <Text style={style.itemText}>{I18n.t('page_call_btn_switch_voice')}</Text>
          </Touchable>
        </View>
      </View>
    );
  };

  renderTips = () => {
    const { style } = this;
    const { callState, timeoutCount } = this.props.callStore;
    return callState === constant.callState.invite && timeoutCount >= 15 && timeoutCount <= 25 ? (
      <View style={{ marginHorizontal: 28, minHeight: 50 }}>
        <View style={style.tipsContainer}>
          <Text style={style.tipsText}>{I18n.t('page_call_no_response')}</Text>
        </View>
      </View>
    ) : (
      <View style={{ height: 30 }} />
    );
  };

  renderMiniScreen = () => {
    const { style } = this;
    const { openCamera, enableSpeakerphone, openMicrophone } = this.props.callStore;
    return (
      <View style={style.controlMiniContainer}>
        <View style={style.controlBox}>
          {this.renderTips()}
          <View style={style.btnsMiniContainer}>
            <Touchable onPress={this.openCamera} style={style.itemContainer}>
              <Image
                source={openCamera ? resIcon.meetingVideoOpen : resIcon.meetingVideoClose}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_camera')}</Text>
            </Touchable>
            <Touchable onPress={this.switchMicrophone} style={style.itemContainer}>
              <Image
                source={openMicrophone ? resIcon.micOpen : resIcon.micClose}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_mic')}</Text>
            </Touchable>
            <Touchable onPress={this.hungUp} style={style.itemContainer}>
              <Image source={resIcon.hungUp} style={style.itemMiniIcon} />
              <Text style={style.itemText}>{I18n.t('page_call_btn_cancel')}</Text>
            </Touchable>
            <Touchable onPress={this.switchSpeakerphone} style={style.itemContainer}>
              <Image
                source={enableSpeakerphone ? resIcon.speakOpen : resIcon.speakClose}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_speak')}</Text>
            </Touchable>
            <Touchable onPress={this.switchCamera} style={style.itemContainer}>
              <Image
                source={openCamera ? resIcon.meetingCameraEnable : resIcon.meetingCameraDisable}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_crypto_switch')}</Text>
            </Touchable>
          </View>
        </View>
        <SafeView bottomHeight={10} />
      </View>
    );
  };

  render() {
    const { callState, channel, peerId, zoomIn } = this.props.callStore;
    if (zoomIn) {
      return (
        <Touchable
          style={{ flex: 1, borderRadius: 10, overflow: 'hidden' }}
          onPress={() => {
            this.props.onZoomIn();
          }}
        >
          {callState === constant.callState.invite
            ? this.renderLocalVideo(channel)
            : this.renderRemoteVideo(peerId, channel)}
        </Touchable>
      );
    } else {
      return (
        <View
          style={{
            width: '100%',
            height: '100%',
          }}
        >
          {this.renderHeader()}
          {this.renderCenter()}
          {this.renderMiniScreen()}
          <ChatBookModal
            fromSingle
            ref={(ref) => (this.chatBookModal = ref)}
            addCheck={this.addCheck}
          />
        </View>
      );
    }
  }
}
