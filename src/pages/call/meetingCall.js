import React, { Component } from 'react';
import { Image, ScrollView, Text, Touchable, View } from '../../components';
import { inject, observer } from 'mobx-react';
import I18n from '../../i18n';
import constant from '../../store/constant';
import styles from '../../themes';
import { getMeetingStyle } from '../../themes/pages/meeting';
import resIcon from '../../res';
import AgoraSdk from '../../api/agoraSdk';
import Avatar from '../../components/avatar/avatar';
import { deviceHeight, deviceWidth } from '../../common';
import ChatBookModal from '../chat/components/chatBookModal';
import { RtcLocalView, RtcRemoteView, VideoRenderMode } from 'react-native-agora';
import { Bubbles } from 'react-native-loader';
import SafeView from '../../components/safeView';

/**
 * 私聊音频通话
 * <AUTHOR>
 */
@inject('callStore', 'userStore', 'callAction', 'meetingAction', 'chatAction')
@observer
export default class MeetingCall extends Component {
  constructor(props) {
    super(props);
    this.friendList = [];
    this.style = getMeetingStyle(styles.get('theme'));
  }

  componentDidMount() {
    this.props.chatAction.getFriends(true, false).then((res) => {
      this.friendList = res;
    });
  }

  hungUp = () => {
    this.props.meetingAction.endCall();
  };

  // 开启或关闭麦克风。
  switchMicrophone = async () => {
    this.props.meetingAction.openMicrophone();
  };

  // 选择音频播放设备为扬声器或耳机。
  switchSpeakerphone = async () => {
    this.props.callAction.enableSpeakerphone();
  };

  // 打开麦克风
  openCamera = async () => {
    this.props.meetingAction.openCamera();
  };

  //切换摄像头
  switchCamera = async () => {
    await AgoraSdk.engine?.switchCamera();
  };

  onAddUser = () => {
    const { peersMap } = this.props.callStore;
    this.chatBookModal.open({
      disabledList: Array.from(peersMap.values()),
    });
  };

  addCheck = (res) => {
    this.props.meetingAction.inviteMembers(res);
  };

  onItemPress = (user) => {
    this.props.meetingAction.showBigScreen(user);
  };

  getOtherStatus = (user) => {
    if (!user) {
      return '';
    }
    let status = '';
    if (user.imId !== this.props.userStore.imId) {
      switch (user.callState) {
        case constant.callState.busy:
          status = I18n.t('page_call_status_busy');
          break;
        case constant.callState.reject:
          status = I18n.t('page_friend_text_rejected');
          break;
        case constant.callState.end:
          status = I18n.t('page_call_status_end');
          break;

        default:
          status = '';
          break;
      }
    }
    return status;
  };

  renderHeader = () => {
    const { style } = this;
    const { timeCount } = this.props.callStore;
    return (
      <View style={style.topContainer}>
        <View style={style.topActionContainer}>
          <Touchable
            onPress={() => {
              this.props.onZoomIn();
            }}
          >
            <Image source={resIcon.zoom} style={style.zoomContainer} resizeMode="cover" />
          </Touchable>
          {timeCount === 0 ? (
            <Text style={style.headerText}>
              {I18n.t('page_call_text_wait_member_receive')}
              {this.props.loadingText}
            </Text>
          ) : (
            <Text style={style.zoomTimeText}>
              {this.props.meetingAction.formatHHMMSS(timeCount)}
            </Text>
          )}
          <Touchable onPress={this.onAddUser}>
            <Image source={resIcon.meetingAddUser} style={style.zoomContainer} resizeMode="cover" />
          </Touchable>
        </View>
      </View>
    );
  };

  renderLocalVideo = (channel) => {
    return (
      <RtcLocalView.SurfaceView
        style={{ flex: 1 }}
        channelId={channel}
        renderMode={VideoRenderMode.Hidden}
      />
    );
  };

  renderRemoteVideo = (peerId, channel) => {
    return (
      <RtcRemoteView.SurfaceView
        style={{ flex: 1 }}
        uid={peerId}
        channelId={channel}
        renderMode={VideoRenderMode.Hidden}
      />
    );
  };

  getWindowStyle = (count) => {
    const { style } = this;
    if (count == 2) {
      return style.defaultScale;
    } else if (count == 3 || count == 4) {
      return style.large3Scale;
    } else if (count == 5 || count == 6) {
      return style.large4Scale;
    } else if (count == 7 || count == 8) {
      return style.large6Scale;
    } else if (count == 9 || count == 10) {
      return style.large7Scale;
    } else if (count == 11 || count == 12) {
      return style.large8Scale;
    } else {
      return style.defaultLittleScale;
    }
  };

  // 单个用户的窗口
  renderItem = ({ videoStyle, user, index }) => {
    const { localInfo, channel } = this.props.callStore;
    const { style } = this;
    const { peersMap } = this.props.callStore;
    const totalCount = peersMap?.size; // 会议人数
    const statusText = this.getOtherStatus(user);
    const friend = this.friendList.find((x) => x.imId === user.imId);
    const userName = friend?.memo || user.name;
    return (
      <Touchable
        activeOpacity={1}
        style={videoStyle}
        key={index.toString()}
        onPress={() => this.onItemPress(user)}
      >
        {user.openCamera ? (
          <View style={style.videoStyle}>
            {user.imId === localInfo.imId
              ? this.renderLocalVideo(channel)
              : this.renderRemoteVideo(user.imId, channel)}
          </View>
        ) : (
          <Avatar avatar={user?.avatar} name={userName} style={style.userAvatar} />
        )}
        <View style={style.userName}>
          <Text numberOfLines={1} style={style.userNameText}>
            {userName}
          </Text>
        </View>
        {user?.callState === constant.callState.invite ? (
          <View style={style.inviteState}>
            <Bubbles size={6} color="#FFF" />
          </View>
        ) : null}
        {statusText ? (
          <View style={style.inviteState}>
            <Text style={style.busyStateText}>{statusText}</Text>
          </View>
        ) : null}
        {user?.isSpeaking ? (
          <View style={style.voiceBox}>
            <Image
              source={resIcon.meetingVoiceIcon}
              style={totalCount > 4 ? style.littleVoiceIcon : style.bigVoiceIcon}
            />
          </View>
        ) : null}
      </Touchable>
    );
  };

  renderCenter = () => {
    const { style } = this;
    const { peersMap, timeCount } = this.props.callStore;
    let userView = null;
    const totalCount = peersMap?.size; // 会议人数
    const users = Array.from(peersMap.values()).map((x) => {
      return { ...x };
    }); // 会议人员数组
    const currentUser = users.find((user) => user.imId === this.props.userStore.imId);
    if (currentUser) {
      const currentUserIndex = users.findIndex((user) => user.imId === this.props.userStore.imId);
      users.splice(currentUserIndex, 1);
      users.unshift(currentUser);
    }

    const activeUser = users.find((x) => x.isUserActive); // 点击查看某个人视频
    const videoStyle = totalCount > 4 ? style.defaultLittleScale : style.defaultScale;
    const Item = this.renderItem;
    if (
      totalCount === 2 ||
      totalCount == 4 ||
      totalCount === 6 ||
      totalCount === 8 ||
      totalCount === 9 ||
      totalCount === 12 ||
      totalCount > 12
    ) {
      userView = (
        <View
          style={totalCount === 2 ? style.meetingUserContainer : style.meetingMoreUserContainer}
        >
          {this.getItemViews(users, totalCount)}
        </View>
      );
    } else if (totalCount === 3) {
      userView = (
        <View style={[style.meetingUserContainer, style.meetingUserExtra]}>
          <Item videoStyle={[videoStyle, style.largeScale]} user={users[0]} index={0} />
          <View style={style.mettingSubContainer}>
            {this.getItemViews(users.slice(1), totalCount)}
          </View>
        </View>
      );
    } else if (totalCount === 5) {
      userView = (
        <View style={[style.meetingUserContainer, style.mettingSubContainer]}>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(0, 2), 3)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(2), 5)}</View>
        </View>
      );
    } else if (totalCount === 7) {
      userView = (
        <View style={[style.meetingUserContainer, style.mettingSubContainer]}>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(0, 3), 5)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(3), 7)}</View>
        </View>
      );
    } else if (totalCount === 10) {
      userView = (
        <View style={[style.meetingUserContainer, style.mettingSubContainer]}>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(0, 3), 9)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(3, 6), 9)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(6), 11)}</View>
        </View>
      );
    } else if (totalCount === 11) {
      userView = (
        <View style={[style.meetingUserContainer, style.mettingSubContainer]}>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(0, 3), 9)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(3, 7), 11)}</View>
          <View style={style.meetingUserExtra}>{this.getItemViews(users.slice(7), 11)}</View>
        </View>
      );
    }
    if (activeUser?.isUserActive) {
      userView = (
        <View style={style.meetingUserContainer}>
          <Item videoStyle={style.defaultMaxScale} user={activeUser} index={0} />
        </View>
      );
    }
    return (
      <View style={style.centerContainer}>
        {totalCount > 4 ? (
          <ScrollView scrollEnabled={totalCount > 4} showsVerticalScrollIndicator={false}>
            {userView}
          </ScrollView>
        ) : (
          userView
        )}
      </View>
    );
  };

  getItemViews = (users, totalCount) => {
    const Item = this.renderItem;
    return users.map((user, index) => (
      <Item videoStyle={this.getWindowStyle(totalCount)} user={user} index={index} />
    ));
  };

  // 小屏幕手机
  renderMiniScreen = () => {
    const { style } = this;
    const { localInfo, enableSpeakerphone, peersMap } = this.props.callStore;
    const users = Array.from(peersMap.values())
      .map((x) => {
        return { ...x };
      })
      .filter((y) => y.callState == constant.callState.accept);
    return (
      <View style={style.controlMiniContainer}>
        <View style={style.controlBox}>
          {this.renderTips()}
          <View style={style.btnsMiniContainer}>
            <Touchable onPress={this.openCamera} style={style.itemContainer}>
              <Image
                source={
                  localInfo?.openCamera ? resIcon.meetingVideoOpen : resIcon.meetingVideoClose
                }
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_camera')}</Text>
            </Touchable>
            <Touchable onPress={this.switchMicrophone} style={style.itemContainer}>
              <Image
                source={localInfo?.openMicrophone ? resIcon.micOpen : resIcon.micClose}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_mic')}</Text>
            </Touchable>
            <Touchable onPress={this.hungUp} style={style.itemContainer}>
              <Image source={resIcon.hungUp} style={style.itemMiniIcon} />
              <Text style={style.itemText}>
                {users?.length > 1
                  ? I18n.t('page_call_btn_meeting_end')
                  : I18n.t('page_call_btn_cancel')}
              </Text>
            </Touchable>
            <Touchable onPress={this.switchSpeakerphone} style={style.itemContainer}>
              <Image
                source={enableSpeakerphone ? resIcon.speakOpen : resIcon.speakClose}
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_speak')}</Text>
            </Touchable>
            <Touchable onPress={this.switchCamera} style={style.itemContainer}>
              <Image
                source={
                  localInfo?.openCamera ? resIcon.meetingCameraEnable : resIcon.meetingCameraDisable
                }
                style={style.itemMiniIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_crypto_switch')}</Text>
            </Touchable>
          </View>
        </View>
        <SafeView bottomHeight={10} />
      </View>
    );
  };

  renderNormalScreen = () => {
    const { style } = this;
    const { localInfo, enableSpeakerphone } = this.props.callStore;
    return (
      <View style={style.controlContainer}>
        <View style={style.controlBox}>
          {this.renderTips()}
          <View style={[style.btnsContainer, { marginTop: 30 }]}>
            <Touchable onPress={this.switchMicrophone} style={style.itemContainer}>
              <Image
                source={localInfo?.openMicrophone ? resIcon.micOpen : resIcon.micClose}
                style={style.itemIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_mic')}</Text>
            </Touchable>
            <Touchable onPress={this.switchSpeakerphone} style={style.itemContainer}>
              <Image
                source={enableSpeakerphone ? resIcon.speakOpen : resIcon.speakClose}
                style={style.itemIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_speak')}</Text>
            </Touchable>
            <Touchable onPress={this.openCamera} style={style.itemContainer}>
              <Image
                source={
                  localInfo?.openCamera ? resIcon.meetingVideoOpen : resIcon.meetingVideoClose
                }
                style={style.itemIcon}
              />
              <Text style={style.itemText}>{I18n.t('page_call_btn_camera')}</Text>
            </Touchable>
          </View>
          <View style={[style.btnsContainer, { marginTop: 30 }]}>
            <View style={style.itemContainer} />
            <Touchable onPress={this.hungUp} style={style.itemContainer}>
              <Image source={resIcon.hungUp} style={style.itemCloseIcon} />
            </Touchable>
            <Touchable onPress={this.switchCamera} style={style.itemContainer}>
              <Image
                source={
                  localInfo?.openCamera ? resIcon.meetingCameraEnable : resIcon.meetingCameraDisable
                }
                style={style.itemIcon}
              />
            </Touchable>
          </View>
        </View>
      </View>
    );
  };

  renderControl = () => {
    const isMiniScreen = deviceHeight - deviceWidth < 300;
    return isMiniScreen ? this.renderMiniScreen() : this.renderMiniScreen();
  };

  renderTips = () => {
    const { style } = this;
    const { callState, timeoutCount } = this.props.callStore;
    return callState === constant.callState.invite && timeoutCount >= 15 && timeoutCount <= 25 ? (
      <View style={{ marginHorizontal: 28, minHeight: 50 }}>
        <View style={style.tipsContainer}>
          <Text style={style.tipsText}>{I18n.t('page_call_no_response')}</Text>
        </View>
      </View>
    ) : (
      <View style={{ height: 30 }} />
    );
  };

  render() {
    const { style } = this;
    const { timeCount, zoomIn } = this.props.callStore;
    if (zoomIn) {
      return (
        <Touchable style={style.waitContainer} onPress={this.props.onZoomIn}>
          <Image style={{ width: 16, height: 16, marginLeft: 14 }} source={resIcon.zoomAudio} />
          {timeCount === 0 ? (
            <Text style={style.zoomStateText}>{I18n.t('page_call_text_wait')}</Text>
          ) : (
            <Text style={style.zoomTimeText}>{this.props.callAction.formatHHMMSS(timeCount)}</Text>
          )}
        </Touchable>
      );
    } else {
      return (
        <View style={style.page}>
          {this.renderHeader()}
          {this.renderCenter()}
          {this.renderControl()}
          <ChatBookModal ref={(ref) => (this.chatBookModal = ref)} addCheck={this.addCheck} />
        </View>
      );
    }
  }
}
