import React, { Component } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import { inject, observer } from 'mobx-react';
import { Header } from 'react-native-elements';
import { headerStyle, messageStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import config from '../configs';
import util from '../util';

@inject('userAction')
@observer
export default class PushmessageDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      details: {},
    };
  }

  componentDidMount() {
    this.props.userAction.statsMessage();
    this.getMessageDetail();
  }

  getMessageDetail = () => {
    const {
      params: { messageId },
    } = this.props.navigation.state;
    this.props.userAction.getMessageDetail(messageId).then((res) => {
      const temp = res;
      temp.sendAt = temp && temp.sendAt ? util.timestampToLongString1(temp.sendAt) : '';
      this.setState({ details: temp });
      if (
        res &&
        res.receiver &&
        res.receiver.receiveStatus &&
        res.receiver.receiveStatus.value === 1
      ) {
        return;
      }
      this.props.userAction.markMessageRead(messageId);
    });
  };

  render() {
    const { navigation } = this.props;
    const { details } = this.state;
    const url = `${config.cmsNotifyURl.replace(
      '${id}',
      details.messageId
    )}?title=${encodeURIComponent(details.subject)}&time=${encodeURIComponent(
      details.sendAt
    )}&content=${encodeURIComponent(details.content)}`;
    return (
      <View style={messageStyle.detailsContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_messageDetails_text_header_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <WebView source={{ uri: url }} startInLoadingState domStorageEnabled javaScriptEnabled />
      </View>
    );
  }
}
