import React, { Component } from 'react';
import ExpandableText from 'rn-expandable-text';
import Toast from 'react-native-easy-toast';
import Swiper from 'react-native-swiper';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { inject, observer } from 'mobx-react';
import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  InteractionManager,
  FlatList,
  Alert,
  Linking,
} from 'react-native';
import { Clipboard } from '../components';
import { Header, Icon, Badge } from 'react-native-elements';
import { headerStyle, desColor, companyDetailStyle, jobStyle, baseBlueColor } from '../themes';
import LoadingModal from '../components/loadingModal';
import I18n from '../i18n';
import Session from '../api/session';
import res, { getEmployerAvatarSource } from '../res';
import CommonCustomTabBar from '../components/commonCustomTabBar';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';

@inject('jobStore', 'jobAction', 'userAction', 'personStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    const { form, isPreview } = this.props.navigation.state.params || {};
    this.isPreview = isPreview;
    this.isLoadingMore = false;
    this.state = {
      page: 1,
      currentIndex: 0,
      followed: false,
      detailLoading: !this.isPreview || !form,
      detailParam: form || {},
      hasMore: false,
    };
  }

  async componentDidMount() {
    const {
      params: { employerId },
    } = this.props.navigation.state;
    await InteractionManager.runAfterInteractions(() => {
      this.onGetDeatilOfCompany(employerId);
      this.getCompanyJobs();
    });
  }

  componentWillUnmount() {
    // this.props.jobStore.companyDetail = {};
    this.goBack();
  }

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onGetDeatilOfCompany = (employerId) => {
    if (!this.state.detailLoading) return;

    this.props.jobAction.querySingleCompany(employerId).then(
      (reslut) => {
        console.log('result', reslut);
        this.setState({
          detailParam: reslut,
          detailLoading: false,
          followed: reslut ? reslut.followed : false,
        });
      },
      () => {
        this.setState({ detailLoading: false });
      }
    );
  };

  onToMapDetail = async () => {
    this.props.navigation.navigate('mapDetail');
  };

  onChangeTab = (obj) => {
    requestAnimationFrame(() => {
      this.setState({ currentIndex: obj.i });
    });
  };

  onToJobDetail = (item) => {
    if (this.isPreview) return;
    this.props.navigation.push('jobDetail', { detail: item });
  };

  async onScroll() {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.state.page += 1;
      this.isLoadingMore = true;
      await this.getCompanyJobs();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 0);
    }
  }

  getCompanyJobs = async () => {
    const {
      params: { employerId },
    } = this.props.navigation.state;
    await this.props.jobAction.queryCompanyJobs({
      page: this.state.page,
      size: 10,
      employerId,
    });
    const { companyJobList, companyDetailCount } = this.props.jobStore;
    if (companyJobList) {
      this.setState({ hasMore: companyJobList.length < parseFloat(companyDetailCount) });
    }
  };

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.getFollowed) {
      await fuc.getFollowed();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  followCompany = async () => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        const { detailParam } = this.state;
        if (!this.state.followed) {
          const result = await this.props.userAction.followCompany(detailParam.employerId);
          if (result && result.successful) {
            this.setState({ followed: true });
            this.toast.show(result && result.message);
          }
        } else {
          const result = await this.props.userAction.deleteFollowedCompany({
            employerIds: [detailParam.employerId],
          });
          if (result && result.successful) {
            this.setState({ followed: false });
            this.toast.show(result && result.message);
          }
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  onPhone = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  onCopy = (text) => {
    Clipboard.setString(text);
    global.toast.show(I18n.t('page_dynamic_text_copy_success'));
  };

  renderPhone = (detail) => {
    const {
      personStore: { isLogin },
    } = this.props;
    if (detail && detail.regionCode && detail.mobile && isLogin) {
      return (
        <View style={companyDetailStyle.siteContainer}>
          <Text style={companyDetailStyle.siteTitle}>
            {I18n.t('page_mine_interview_company_phone')}
          </Text>
          <Text
            numberOfLines={1}
            onPress={() => this.onPhone(detail ? detail.regionCode + detail.mobile : '')}
            style={companyDetailStyle.site}
          >
            {detail ? detail.regionCode + detail.mobile : ''}
          </Text>
        </View>
      );
    }
    return null;
  };

  renderEmail = (detail) => {
    const {
      personStore: { isLogin },
    } = this.props;
    if (detail && detail.email && isLogin) {
      return (
        <View style={companyDetailStyle.siteContainer}>
          <Text style={companyDetailStyle.siteTitle}>
            {I18n.t('page_mine_interview_company_eamil')}
          </Text>
          <Text
            numberOfLines={1}
            onPress={() => this.onCopy(detail ? detail.email : '')}
            style={companyDetailStyle.site}
          >
            {detail ? detail.email : ''}
          </Text>
        </View>
      );
    }
    return null;
  };

  renderCompanyInfo = (detail) => {
    const isShow =
      detail &&
      ((detail.scaleId && detail.scaleId.label) ||
        (detail.industrialId && detail.industrialId.label));
    return (
      <View style={companyDetailStyle.companyCardCont}>
        <View style={companyDetailStyle.companySubCard}>
          <ImageBackground
            imageStyle={{ borderRadius: 27 }}
            style={companyDetailStyle.companyLogo}
            source={getEmployerAvatarSource(detail ? detail.logo : '')}
            resizeMode="contain"
          >
            {detail &&
            detail.employerQualificationType &&
            detail.employerQualificationType.value === 1 ? (
              <Image style={companyDetailStyle.v2} source={res.verify} />
            ) : (
              <View />
            )}
          </ImageBackground>
          <View style={companyDetailStyle.companyCardContent}>
            <Text style={companyDetailStyle.cardCompanyName}>
              {detail ? detail.company : ''}{' '}
              {detail && detail.qualificationStatus && detail.qualificationStatus.value === 1 ? (
                <Image source={res.iconVerify} style={{ width: 9, height: 9 }} />
              ) : null}
            </Text>
            {isShow ? (
              <View style={companyDetailStyle.cardCompanyDesc}>
                <Text style={companyDetailStyle.cardTag}>
                  {detail && detail.scaleId && detail.scaleId.label
                    ? `${detail.scaleId.label}${I18n.t('page_job_text_person')}`
                    : ''}
                </Text>
                {detail &&
                detail.industrialId &&
                detail.industrialId.label &&
                detail.scaleId &&
                detail.scaleId.label ? (
                  <Text style={companyDetailStyle.spaceSpaLine} />
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
                <Text style={companyDetailStyle.cardTag}>
                  {detail && detail.industrialId && detail.industrialId.label
                    ? `${detail.industrialId.label}`
                    : ''}
                </Text>
                {detail && detail.companyType && detail.companyType.label ? (
                  <Text style={companyDetailStyle.spaceSpaLine} />
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
                <Text style={companyDetailStyle.cardTag}>
                  {detail && detail.companyType && detail.companyType.label
                    ? `${detail.companyType.label}`
                    : ''}
                </Text>
              </View>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-start',
                flexShrink: 100,
              }}
            >
              <Text style={[companyDetailStyle.cardTag]}>{I18n.t('page_job_company_welfare')}</Text>
              {detail && detail.welfare ? (
                <View style={[companyDetailStyle.welfareContainer]}>
                  {detail.welfare.map((item) => (
                    <View key={item} style={companyDetailStyle.welfareContainerItem}>
                      <Text style={companyDetailStyle.welfareContainerItemText}>{item}</Text>
                      {detail.welfare.length > 1 &&
                      item !== detail.welfare[detail.welfare.length - 1] ? (
                        <Text style={companyDetailStyle.spaceSpaLine} />
                      ) : (
                        <Text style={{ display: 'none' }} />
                      )}
                    </View>
                  ))}
                </View>
              ) : (
                <Text style={[companyDetailStyle.welfareContainerItemText, { marginLeft: 6 }]}>
                  {I18n.t('page_job_no_welfare')}
                </Text>
              )}
            </View>
          </View>
        </View>
        <View style={companyDetailStyle.spaceLine} />
        <View>
          {this.renderPhone(detail)}
          {this.renderEmail(detail)}
          <View style={companyDetailStyle.siteContainer}>
            <Text style={companyDetailStyle.siteTitle}>{I18n.t('page_job_company_site')}</Text>
            <Text
              numberOfLines={1}
              onPress={() => {
                this.props.navigation.navigate('webviewDetail', {
                  url: detail ? detail.webUrl : '',
                  title: I18n.t('page_job_company_site'),
                });
              }}
              style={companyDetailStyle.site}
            >
              {detail ? detail.webUrl : ''}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              this.onToMapDetail();
            }}
          >
            <View style={companyDetailStyle.companyAddressCon}>
              <Text style={companyDetailStyle.siteTitle}>{I18n.t('page_job_company_address')}</Text>
              <View style={companyDetailStyle.addressContainer}>
                <View style={companyDetailStyle.addressContainer2}>
                  {detail && detail.address ? (
                    <Icon type="material" name="location-on" size={16} color={desColor} />
                  ) : (
                    <Icon type="material" name="location-on" size={16} color="#fff" />
                  )}
                  <Text numberOfLines={1} style={{ color: '#333333', fontSize: 12 }}>
                    {detail ? detail.address : ''}
                  </Text>
                </View>
                <View>
                  <Icon type="evilicon" name="chevron-right" size={30} color={desColor} />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderSwiperItem = (images) =>
    images.map((item, i) => (
      <View key={`${i + 1}`} style={jobStyle.swiperimage}>
        <Image style={jobStyle.swiperimage} source={{ uri: item }} />
      </View>
    ));

  renderCompanyPlaceholder = () => (
    <View style={jobStyle.swiperimage}>
      {!this.state.detailLoading ? (
        <Image style={jobStyle.swiperimage} source={res.companyBg} />
      ) : (
        <Image style={jobStyle.swiperimage} />
      )}
    </View>
  );

  renderCompanyIMG = (detail) => {
    const swiperItems =
      detail && detail.images && detail.images.length > 0
        ? this.renderSwiperItem(detail.images)
        : this.renderCompanyPlaceholder();
    return (
      <View style={{ marginHorizontal: 10, marginVertical: 8 }}>
        <View style={jobStyle.swiperimage}>
          <Swiper
            key={detail && detail.images && detail.images.length > 0 ? detail.images.length : 0}
            autoplay
            autoplayTimeout={2}
            horizontal
            showsPagination
            paginationStyle={{ position: 'absolute', bottom: 2 }}
            index={0}
            dotStyle={{ backgroundColor: '#fff', width: 8, height: 8 }}
            activeDotStyle={{ backgroundColor: baseBlueColor, width: 8, height: 8 }}
          >
            {swiperItems}
          </Swiper>
        </View>
      </View>
    );
  };

  renderScorllableTableview = () => (
    <View>
      <ScrollableTabView
        renderTabBar={() => (
          <CommonCustomTabBar
            currentIndex={this.state.currentIndex}
            isCompanyDetail="isCompanyDetail"
          />
        )}
        style={companyDetailStyle.scorllContainer}
        initialPage={0}
        tabBarBackgroundColor="#fff"
        onChangeTab={(obj) => {
          this.onChangeTab(obj);
        }}
      >
        <Text style={companyDetailStyle.textStyle} tabLabel={I18n.t('page_job_company_intro')} />
        <Text style={companyDetailStyle.textStyle} tabLabel={I18n.t('page_job_company_all_job')} />
      </ScrollableTabView>
    </View>
  );

  renderIntroduction = (detail) => (
    <View
      style={[
        companyDetailStyle.jobDescContainer,
        { display: this.state.currentIndex === 0 ? 'flex' : 'none' },
      ]}
    >
      <ExpandableText
        numberOfLines={4}
        style={{
          fontSize: 12,
          marginLeft: 0,
          color: 'gray',
          textAlign: detail && detail.description ? 'left' : 'center',
        }}
        unexpandView={() => null}
        expandView={() =>
          detail && detail.description && detail.description.length > 120 ? (
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
              <View style={companyDetailStyle.lineView} />
              <View style={companyDetailStyle.moreText}>
                <Text style={companyDetailStyle.expandTitle}>
                  {I18n.t('page_resume_label_more')}
                </Text>
                <Icon
                  name="chevron-small-down"
                  type="entypo"
                  size={24}
                  color={baseBlueColor}
                  iconStyle={{ marginTop: 4 }}
                />
              </View>
              <View style={companyDetailStyle.lineView} />
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )
        }
      >
        {detail && detail.description ? detail.description : I18n.t('page_job_company_desc')}
      </ExpandableText>
    </View>
  );

  renderTag = (value, props = {}) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        value={value}
        containerStyle={jobStyle.tagItem}
        badgeStyle={jobStyle.tagItemBadgeStyle}
        textStyle={jobStyle.tagText}
        {...props}
      />
    );
  };

  renderAllJob = ({ item }) => (
    <TouchableOpacity key={item.id} onPress={this.onToJobDetail.bind(this, item)}>
      <View style={[jobStyle.listContainer]}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text numberOfLines={1} style={[jobStyle.jobTitle, { flexGrow: 20, flexShrink: 200 }]}>
            {item.title}
          </Text>
          <Text style={jobStyle.salaryLabel}>
            {item.salaryId && item.salaryId.label ? item.salaryId.label : ''}
          </Text>
        </View>
        <View style={jobStyle.tags}>
          {this.renderTag(item.jobLevelId && item.jobLevelId.label)}
          {this.renderTag(item.workyears > 0 && `${item.workyears}${I18n.t('page_job_text_year')}`)}
          {this.renderTag(item.qualificationId && item.qualificationId.label)}
          {this.renderTag(item.major, { textProps: { numberOfLines: 1 } })}
          {item.jobLangs && item.jobLangs.length > 0 ? (
            item.jobLangs.map((lang, i) =>
              this.renderTag(`${lang.languageId.label}${lang.languageLevelId.label}`, { key: i })
            )
          ) : (
            <Text />
          )}
          {this.renderTag(item.sex && item.sex.label)}
          {this.renderTag(item.ageTo > 0 && `${item.ageFrom}-${item.ageTo}`)}
          {this.renderTag(item.marital && item.marital.label)}
        </View>
        {item.locations && item.locations.length > 0 ? (
          <View style={jobStyle.locationsStyle}>
            {item.locations.map((l) => l.locationId.label).length > 0 ? (
              <Text numberOfLines={1} style={{ color: desColor, fontSize: 12 }}>
                {item.locations.map((l) => l.locationId.label).join(' | ')}
              </Text>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
          </View>
        ) : (
          <Text style={{ display: 'none' }} />
        )}
        <View style={[jobStyle.companyPannel, jobStyle.mb15]}>
          <Image
            style={jobStyle.companyLogo}
            source={getEmployerAvatarSource(item.employer ? item.employer.logo : '')}
            resizeMode="contain"
          />
          <View style={jobStyle.companyDetail}>
            <Text numberOfLines={1} style={[jobStyle.companyName, { width: '98%' }]}>
              {item.employer ? item.employer.company : ''}{' '}
              {/* <Image source={res.iconVerify} style={{ width: 9, height: 9 }} /> */}
            </Text>
            {item &&
            ((item.industrialId && item.industrialId.label) ||
              (item.employer.scaleId && item.employer.scaleId.label)) ? (
              <View style={jobStyle.companyInfo}>
                <Text style={jobStyle.companySubcribe}>
                  {item.employer && item.employer.industrialId
                    ? item.employer.industrialId.label
                    : ''}
                </Text>
                {item.employer &&
                item.employer.scaleId &&
                item.employer.industrialId &&
                item.employer.industrialId.label &&
                item.employer.scaleId.label ? (
                  <Text style={jobStyle.spaceLine} />
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
                {item.employer && item.employer.scaleId && item.employer.scaleId.label ? (
                  <Text style={jobStyle.companySubcribe}>
                    {item.employer && item.employer.scaleId
                      ? `${item.employer.scaleId.label}${I18n.t('page_job_text_person')}`
                      : ''}
                  </Text>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderFavorite = () => (
    <TouchableOpacity
      onPress={() => {
        this.followCompany();
      }}
    >
      <Icon
        type="font-awesome"
        name={this.state.followed ? 'star' : 'star-o'}
        color="#fff"
        size={21}
      />
    </TouchableOpacity>
  );

  renderEmptyComponent = () => (
    <View style={{ marginTop: '10%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  render() {
    const { companyJobList } = this.props.jobStore;
    const { currentIndex, detailParam, detailLoading } = this.state;
    const form = this.props.navigation.state.params?.form || {};
    const data = { ...detailParam, ...form };
    return (
      <View style={companyDetailStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_job_nav_company_home'), style: headerStyle.center }}
          leftComponent={this.renderLeftIcon()}
          rightComponent={this.isPreview ? null : this.renderFavorite}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {!detailLoading ? (
          <ScrollView scrollEventThrottle={200} onScroll={this.onScroll.bind(this)}>
            {this.renderCompanyInfo(data)}
            {this.renderCompanyIMG(data)}
            {this.renderScorllableTableview()}
            {this.renderIntroduction(data)}
            <View style={{ display: currentIndex === 1 ? 'flex' : 'none' }}>
              <FlatList
                data={companyJobList && companyJobList.length > 0 ? companyJobList.slice() : []}
                renderItem={this.renderAllJob}
                keyExtractor={(item, index) => index + item}
                showsVerticalScrollIndicator={false}
                horizontal={false}
                ListEmptyComponent={this.renderEmptyComponent}
                ListFooterComponent={this.renderFooter}
              />
            </View>
          </ScrollView>
        ) : (
          <View />
        )}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.detailLoading} loadingTips={false} />
      </View>
    );
  }
}
