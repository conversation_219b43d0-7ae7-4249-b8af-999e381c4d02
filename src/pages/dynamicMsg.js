import React, { Component } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  InteractionManager,
  Alert,
  ListView,
  TouchableHighlight,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { inject, observer } from 'mobx-react';
import { Header, Icon } from 'react-native-elements';
import { SwipeListView } from 'react-native-swipe-list-view';
import {
  globalStyle,
  headerStyle,
  messageStyle,
  desColor,
  baseBlueColor,
  subTitleColor,
} from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import res, { getAvatarSource } from '../res';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';

@inject('personStore', 'userAction')
@observer
export default class dynamicMsg extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.page = 1;
    this.state = {
      showLoading: true,
      hasMore: false,
      refreshing: false,
    };
  }

  componentDidMount() {
    InteractionManager.runAfterInteractions(() => {
      this.props.userAction.resetTwitterMsgData();
      this.props.userAction.markTwitterMessagesRead();
      this.getMessageInbox();
    });
  }

  componentWillUnmount() {
    this.props.userAction.deleteAllTwitterMessages();
    this.props.userAction.resetTwitterMsgList();
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onSelect) {
      fuc.onSelect();
    }
  }

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.page = 1;
    await this.getMessageInbox();
  };

  onLoadMore = async () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.page += 1;
      this.isLoadingMore = true;
      await this.getMessageInbox();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 1000);
    }
  };

  onDynamicMsgClick = (item) => {
    this.props.navigation.navigate('dynamicDetail', {
      info: { id: item.attributes.twitterId },
    });
  };

  getMessageInbox = async () => {
    try {
      await this.props.userAction.queryTwitterMessage({
        page: this.page,
        size: 10,
        topics: ['like', 'newComment'],
      });
      const { twitterMsgList, totalCount } = this.props.personStore;
      this.setState({
        hasMore: twitterMsgList.length < parseFloat(totalCount),
        showLoading: false,
        refreshing: false,
      });
    } catch (error) {
      this.setState({ showLoading: false, refreshing: false });
    }
  };

  clearAll = () => {
    Alert.alert(I18n.t('page_dynamic_clear_all_msg'), '', [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.setState({ showLoading: true });
          this.props.userAction.deleteAllTwitterMessages().then(
            () => {
              this.page = 1;
              this.getMessageInbox();
              this.setState({ showLoading: false });
            },
            () => {
              this.setState({ showLoading: false });
            }
          );
        },
      },
    ]);
  };

  closeRow = (rowMap, rowKey) => {
    if (rowMap[rowKey]) {
      rowMap[rowKey].closeRow();
    }
  };

  deleteRow = (rowMap, rowKey, data) => {
    this.props.userAction.deleteTwitterMessage(data.messageId);
    const { twitterMsgList } = this.props.personStore;
    const deleteIndex = twitterMsgList.findIndex((x) => x.messageId === data.messageId);
    twitterMsgList.splice(deleteIndex, 1);
    this.props.personStore.twitterMsgList = twitterMsgList;
    this.closeRow(rowMap, rowKey);
  };

  deleteRowForAndroid = (rowMap, rowKey, data) => {
    if (global.IS_ANDROID) {
      this.deleteRow(rowMap, rowKey, data);
    }
  };

  renderEmptyComponent = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderMessageList = (item, secId, rowId, rowMap) => (
    <TouchableHighlight
      onLongPress={() => {
        this.deleteRowForAndroid(rowMap, `${secId}${rowId}`, item);
      }}
      onPress={() => {
        this.onDynamicMsgClick(item);
      }}
    >
      <View style={{ flexDirection: 'column' }}>
        <View
          style={{
            flexDirection: 'row',
            backgroundColor: '#fff',
            paddingHorizontal: 8,
            paddingVertical: 10,
          }}
        >
          <Image
            style={messageStyle.avatar}
            source={getAvatarSource(
              item && item.attributes && item.attributes.avatar ? item.attributes.avatar : ''
            )}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              flexShrink: 100,
              flex: 1,
              marginLeft: 10,
            }}
          >
            <View style={{ flexDirection: 'column', flexShrink: 100 }}>
              <Text style={{ fontSize: 14, color: baseBlueColor, marginBottom: 4 }}>
                {item && item.attributes && item.attributes.userName
                  ? item.attributes.userName
                  : I18n.t('page_dynamic_title_ay_username')}
              </Text>
              {item && item.topic === 'newComment' ? (
                <Text style={{ fontSize: 14, color: subTitleColor, marginBottom: 4 }}>
                  {item && item.content ? item.content : ''}
                </Text>
              ) : (
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    flexShrink: 100,
                  }}
                >
                  <Icon name="heart-outlined" type="entypo" size={20} color={baseBlueColor} />
                  <Text />
                </View>
              )}
              <Text style={messageStyle.commentTime}>{item.sendAt}</Text>
            </View>
            <View style={{ marginLeft: 2 }}>
              {item && item.attributes && item.attributes.twitterImage ? (
                <Image
                  style={messageStyle.twitterImage}
                  source={{ uri: item.attributes.twitterImage }}
                />
              ) : (
                <View
                  style={[
                    messageStyle.twitterImage,
                    { backgroundColor: '#f2f2f2', justifyContent: 'center', alignItems: 'center' },
                  ]}
                >
                  <Text numberOfLines={3} style={{ fontSize: 12, color: subTitleColor }}>
                    {item && item.attributes && item.attributes.twitterContent
                      ? item.attributes.twitterContent
                      : ''}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
        <View
          style={{
            height: 1,
            backgroundColor: '#eee',
            paddingLeft: 60,
            paddingRight: 8,
          }}
        />
      </View>
    </TouchableHighlight>
  );

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: desColor }}>
        {I18n.t('page_message_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  renderNoCompany = () => (
    <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
      <Image style={{ width: '24%', height: 120 }} source={res.noData} />
    </View>
  );

  renderAllView = () => {
    const { twitterMsgList } = this.props.personStore;
    const ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 });
    return (
      <View style={[globalStyle.container]}>
        <View style={messageStyle.list}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            scrollEventThrottle={200}
            // eslint-disable-next-line react/jsx-no-bind
            onScroll={this.onLoadMore.bind(this)}
            refreshControl={
              <RefreshControl refreshing={this.state.refreshing} onRefresh={this.onRefresh} />
            }
          >
            {twitterMsgList && twitterMsgList.length > 0 ? (
              <SwipeListView
                refreshing={this.state.refreshing}
                onRefresh={this.onRefresh}
                closeOnScroll={false}
                closeOnRowPress
                disableLeftSwipe={global.IS_ANDROID}
                disableRightSwipe
                dataSource={ds.cloneWithRows(twitterMsgList.slice())}
                renderRow={(data, secId, rowId, rowMap) =>
                  this.renderMessageList(data, secId, rowId, rowMap)
                }
                renderHiddenRow={(data, secId, rowId, rowMap) => (
                  <View style={messageStyle.rowBack}>
                    <TouchableOpacity
                      style={[messageStyle.backRightBtn, messageStyle.backRightBtnRight]}
                      onPress={() => this.deleteRow(rowMap, `${secId}${rowId}`, data)}
                    >
                      <Text style={messageStyle.backTextWhite}>
                        {I18n.t('page_resume_annex_delete')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
                leftOpenValue={75}
                rightOpenValue={-75}
                enableEmptySections
              />
            ) : (
              this.renderNoCompany()
            )}
          </ScrollView>
        </View>
      </View>
    );
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={[messageStyle.page, { backgroundColor: '#fff' }]}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_dynamic_header_tab_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.clearAll}>
              <Text style={[headerStyle.rightBtn, { color: '#fff' }]}>
                {I18n.t('page_dynamic_new_msg_clear')}
              </Text>
            </TouchableOpacity>
          }
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderAllView()}
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
