import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { inject, observer } from 'mobx-react';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Badge,
  Text,
  ListItem,
  Icon,
  Touchable,
} from '../components';
import { desColor, baseBlueColor, bgColor, generalStyle } from '../themes';
import { getAvatarSource } from '../res';
import I18n from '../i18n';
import TooltipMenu from '../components/tooltipMenu';
import Session from '../api/session';
import I18nUtil from '../util/I18nUtil';
import Avatar from '../components/avatar';
import constant from '../store/constant';
import StatusBar from '../components/statusBar';
import NavigationService from '../navigationService';
import util from '../util';
import promiseUtil from '../util/promiseUtil';
import resIcon from '../res';
import BindPhoneModal from '../components/bindPhoneModal';

@inject(
  'jobStore',
  'cityStore',
  'personStore',
  'jobAction',
  'settingsAction',
  'userAction',
  'resumeAction',
  'messageStore',
  'globalAction',
  'loginAction'
)
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      refreshing: false,
    };
  }

  componentDidMount() {
    this.props.userAction.statsMessage();
    global.emitter.on(constant.event.tabbarChanged, this.onTabBarChanged);
    global.emitter.on('refreshList', this.onGetStatus);
    if (Platform.OS.toLowerCase() === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.tabbarChanged, this.onTabBarChanged);
    global.emitter.off('refreshList', this.onGetStatus);
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  onTabBarChanged = ({ name }) => {
    if (name === 'General') {
      this.onGetStatus();
    }
  };

  onGetStatus = () => {
    this.initData();
  };

  onRefresh = () => {
    this.setState({ refreshing: true });
    this.initData();
    setTimeout(() => {
      this.setState({ refreshing: false });
    }, 1500);
  };

  initData = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.jobAction.getJobStatistics();
        this.props.userAction.getCurrUserInfo();
        this.props.userAction.statsMessage();
      }
    });
  };

  onBackAndroid = () => {
    if (this.props.navigation.state.routeName !== 'MineTab') {
      // this.props.navigation.pop();
    }
    // 禁用返回键
    if (this.props.navigation.isFocused()) {
      // 判断  该页面是否处于聚焦状态
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp(); // 直接退出APP
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT, ToastAndroid.CENTER);
      return true;
    }
    // 回调函数onBackAndroid中的return true是必不可少的 --- 大坑，信你个鬼， 必须为false，不然会有bug
    return false;
  };

  onShowAlert = (content) => {
    Alert.alert(I18n.t('page_setting_remind_text'), content, [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onNavigate = (routeName) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const {
          personStore: { me },
        } = this.props;
        if (!me?.mobile) {
          this.bindPhoneModal.wrappedInstance.show();
          return;
        }
        this.props.navigation.navigate(routeName);
      } else {
        this.onShowAlert(I18n.t('login_first_tips'));
      }
    });
  };

  onSwitchLanguage = (language) => {
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    // this.props.jobAction.clearCurrentCity();
    // this.props.cityStore.locationCity = {};
    this.props.settingsAction.switchLanguage(language);
    this.props.changeSettings();
    global.emitter.emit('languageChange', true);
    this.setDefaultLanguage(language);
  };

  setDefaultLanguage = (language) => {
    let lstr = '';
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        if (language === 'zh') {
          lstr = constant.languageMap.zh;
        } else if (language === 'en') {
          lstr = constant.languageMap.en;
        } else if (language === 'km') {
          lstr = constant.languageMap.km;
        } else if (language === 'vi') {
          lstr = constant.languageMap.vi;
        } else if (language === 'th') {
          lstr = constant.languageMap.th;
        } else if (language === 'ko') {
          lstr = constant.languageMap.ko;
        }
        this.props.userAction.setDefaultLanguage(lstr);
      }
    });
  };

  modifyResume = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.changeTab('Resume');
      } else {
        this.onShowAlert(I18n.t('login_first_tips'));
      }
    });
  };

  showName = () => {
    const {
      personStore: { me, isLogin },
    } = this.props;
    if (isLogin) {
      return util.getUserDisplayName(me);
    }
    return I18n.t('login_first_tips');
  };

  gotoMessage = () => {
    const {
      navigation: { navigate },
    } = this.props;
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        navigate('message');
      } else {
        this.onShowAlert(I18n.t('login_first_tips'));
      }
    });
  };

  onSwitchAccount = async () => {
    NavigationService.replace('epLogin');
    console.log('onSwitchAccount================================');
    await promiseUtil.sleep(1000);
    await this.props.globalAction.setEnterprise(true);
  };

  onExitLogin = async () => {
    const isLogin = await Session.isLogin();
    if (!isLogin) {
      this.onSwitchAccount();
      return;
    }

    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_home_tips_switch_enterprise'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.loginAction.logoutForEp();
        },
      },
    ]);
  };

  render() {
    const {
      personStore: { me, isLogin },
      navigation: { navigate },
      jobStore: { hasCommunicated, totalJobApply, userApply },
      messageStore: { unread, totalUnreadCount },
    } = this.props;
    return (
      <View style={{ flex: 1, backgroundColor: bgColor }}>
        <StatusBar containerStyle={{ height: 0 }} barStyle="light-content" />
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={this.state.refreshing} onRefresh={this.onRefresh} />
          }
          style={{ backgroundColor: bgColor }}
        >
          <View style={generalStyle.titleViewColumn}>
            <View style={generalStyle.languageSection}>
              <TooltipMenu
                isModalOpen
                componentWrapperStyle={generalStyle.languageTooltipWrap}
                modalButtonStyle={{ marginRight: -10 }}
                buttonComponent={
                  <View style={generalStyle.language}>
                    <Text style={generalStyle.languageText}>{I18n.t('language')}</Text>
                  </View>
                }
                items={[
                  {
                    label: I18n.t('language', { locale: 'zh' }),
                    icon: resIcon.cnFlag,
                    onPress: () => this.onSwitchLanguage('zh'),
                  },
                  {
                    label: I18n.t('language', { locale: 'en' }),
                    icon: resIcon.usFlag,
                    onPress: () => this.onSwitchLanguage('en'),
                  },
                  {
                    label: I18n.t('language', { locale: 'km' }),
                    icon: resIcon.khFlag,
                    onPress: () => this.onSwitchLanguage('km'),
                  },
                  {
                    label: I18n.t('language', { locale: 'vi' }),
                    icon: resIcon.vnFlag,
                    onPress: () => this.onSwitchLanguage('vi'),
                  },
                  {
                    label: I18n.t('language', { locale: 'th' }),
                    icon: resIcon.thFlag,
                    onPress: () => this.onSwitchLanguage('th'),
                  },
                  {
                    label: I18n.t('language', { locale: 'ko' }),
                    icon: resIcon.krFlag,
                    onPress: () => this.onSwitchLanguage('ko'),
                  },
                ]}
              />
            </View>
            {isLogin ? (
              <View style={generalStyle.titleView}>
                <View style={[generalStyle.info, { flexShrink: 100, marginRight: 20 }]}>
                  <Text style={generalStyle.nameText}>{this.showName()}</Text>
                  <TouchableOpacity onPress={this.modifyResume}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon type="entypo" name="new-message" size={14} color="#fff" />
                      <Text style={generalStyle.editText}>
                        {I18n.t('page_mine_label_modify_resume')}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View style={[generalStyle.avatar, { flexShrink: 1 }]}>
                  <Avatar
                    size="medium"
                    rounded
                    source={getAvatarSource(me && me.avatar ? me.avatar : '')}
                  />
                </View>
              </View>
            ) : (
              <View style={generalStyle.titleView}>
                <View style={[generalStyle.info, { flexShrink: 100 }]}>
                  <Text style={generalStyle.nameText}>{this.showName()}</Text>
                  <TouchableOpacity
                    onPress={() => {
                      this.onShowAlert(I18n.t('page_general_click_to_login_tips'));
                    }}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon type="entypo" name="new-message" size={14} color="#fff" />
                      <Text style={generalStyle.editText}>
                        {I18n.t('page_general_click_to_login')}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View style={[generalStyle.avatar, { flexShrink: 1 }]}>
                  <Avatar
                    size="medium"
                    rounded
                    source={getAvatarSource(me && me.avatar ? me.avatar : '')}
                  />
                </View>
              </View>
            )}
          </View>
          <View style={generalStyle.itemView}>
            <TouchableOpacity
              onPress={() => {
                this.onNavigate('communicatedList');
              }}
            >
              <View>
                <Text style={[generalStyle.itemText, { fontWeight: 'bold', fontSize: 14 }]}>
                  {hasCommunicated}
                </Text>
                <Text style={[generalStyle.itemText, { fontSize: 12, marginTop: 6 }]}>
                  {I18n.t('page_mine_label_has_chat')}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                this.onNavigate('deliveredList');
              }}
            >
              <View>
                <Text style={[generalStyle.itemText, { fontWeight: 'bold', fontSize: 14 }]}>
                  {userApply}
                </Text>
                <Text style={[generalStyle.itemText, { fontSize: 12, marginTop: 6 }]}>
                  {I18n.t('page_mine_label_has_send')}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                this.onNavigate('interviewList');
              }}
            >
              <View>
                <Text style={[generalStyle.itemText, { fontWeight: 'bold', fontSize: 14 }]}>
                  {totalJobApply}
                </Text>
                <Text style={[generalStyle.itemText, { fontSize: 12, marginTop: 6 }]}>
                  {I18n.t('page_mine_label_Interview')}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          <View style={generalStyle.list}>
            <ListItem
              containerStyle={generalStyle.listItemCon}
              title={I18n.t('page_mine_my_favorite')}
              titleStyle={generalStyle.listItemTitle}
              leftIcon={<Icon type="font-awesome" name="star-o" size={22} color={desColor} />}
              onPress={() => this.onNavigate('favorite')}
              rightIcon={
                <Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />
              }
            />
            <ListItem
              containerStyle={generalStyle.listItemCon}
              title={I18n.t('page_guide_text_consultant')}
              titleStyle={generalStyle.listItemTitle}
              leftIcon={
                <Icon type="font-awesome" name="hand-pointer-o" size={22} color={desColor} />
              }
              onPress={() => this.onNavigate('resources')}
              rightIcon={
                <Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />
              }
            />
            <ListItem
              containerStyle={generalStyle.listItemCon}
              title={I18n.t('page_mine_switch_setting')}
              titleStyle={generalStyle.listItemTitle}
              leftIcon={
                <Icon
                  type="ionicon"
                  name="settings-outline"
                  size={22}
                  color={desColor}
                  style={{ marginRight: -1 }}
                />
              }
              onPress={() => {
                Session.isLogin().then((hasLogin) => {
                  if (hasLogin) {
                    navigate('settings');
                  } else {
                    this.onShowAlert(I18n.t('login_first_tips'));
                  }
                });
              }}
              rightIcon={
                <Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />
              }
            />
            <ListItem
              containerStyle={generalStyle.listItemCon}
              title={I18n.t('page_mine_label_help_feedback')}
              titleStyle={generalStyle.listItemTitle}
              leftIcon={
                <Icon
                  type="ionicon"
                  name="help-circle-outline"
                  size={26}
                  color={desColor}
                  style={{ marginRight: -5, marginLeft: -2 }}
                />
              }
              onPress={() => {
                navigate('help');
              }}
              rightIcon={
                <Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />
              }
            />
            <ListItem
              containerStyle={generalStyle.listItemCon}
              leftElement={
                <TouchableOpacity
                  onPress={this.gotoMessage}
                  style={{ flexDirection: 'row', alignItems: 'center' }}
                >
                  <Icon
                    name="notifications"
                    size={23}
                    color={desColor}
                    style={{ marginRight: 10 }}
                  />
                  <Text style={generalStyle.listItemTitle}>
                    {I18n.t('page_message_text_message_notice')}
                  </Text>
                  {totalUnreadCount ? (
                    <Badge
                      value={totalUnreadCount}
                      containerStyle={{ marginLeft: 10 }}
                      badgeStyle={generalStyle.unreadMsgBadgeBadgeStyle}
                    />
                  ) : null}
                </TouchableOpacity>
              }
              onPress={this.gotoMessage}
              rightIcon={
                <Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />
              }
            />
          </View>
          <Touchable style={generalStyle.switchEnterprise} onPress={this.onExitLogin}>
            <Text style={generalStyle.switchEnterpriseText}>
              {I18n.t('page_home_text_switch_enterprise')}
            </Text>
          </Touchable>
          <Toast
            ref={(ref) => {
              this.toast = ref;
            }}
            position="center"
          />
        </ScrollView>
        <BindPhoneModal
          ref={(ref) => (this.bindPhoneModal = ref)}
          nav={this.props.navigation}
          onCheckIntensions={() => {}}
        />
      </View>
    );
  }
}
