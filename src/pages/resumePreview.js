import React, { Component } from 'react';
import { View } from 'react-native';
import { <PERSON><PERSON>, Icon, Button } from 'react-native-elements';
import ResumeItem from './resumeItem';
import { resumePreStyle, globalStyle, headerStyle, baseBlueColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

class Preview extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // renderCellHeader=title => (
  //   <View style={resumePreStyle.cellHeader}>
  //     <View style={resumePreStyle.cellHeaderContent}>
  //       <View style={resumePreStyle.cellHeaderTag} />
  //       <Text style={resumePreStyle.cellHeaderText}>{title}</Text>
  //     </View>
  //     <View style={resumePreStyle.cellHeaderSep} />
  //   </View>
  // )
  // renderInfoIcon=(name, text) => (
  //   <View style={resumePreStyle.flexDirectionRowNormal}>
  //     <Image source={name} style={resumePreStyle.icon} />
  //     <Text style={resumePreStyle.subTitleText}>{text}</Text>
  //   </View>
  // )

  // renderInfo=() => (
  //   <View style={resumePreStyle.cell} >
  //     {this.renderCellHeader(I18n.t('page_resume_label_profile'))}
  //     <View style={resumePreStyle.cellHeader}>
  //       <View style={resumePreStyle.infoCellTop}>
  //         <Avatar
  //           size={50}
  //           rounded
  //           source={{ uri: user.avatar_url }}
  //         />
  //         <View style={resumePreStyle.infoCellTopRight}>
  //           <View style={resumePreStyle.flexDirectionRowNormal}>
  //             <Text style={[resumePreStyle.titleText, resumePreStyle.infoNameText]}>{user.name}</Text>
  //             <Icon
  //               type="material-community"
  //               name="gender-female"
  //               size={16}
  //               color="red"
  //               reverseColor="black"
  //             />
  //           </View>
  //           <View style={[resumePreStyle.flexDirectionRowNormal, resumePreStyle.infoCellTopRightInfo]} >
  //             {this.renderInfoIcon(Res.iconBirth, user.age)}
  //             {this.renderInfoIcon(Res.iconEdu, user.edu)}
  //             {this.renderInfoIcon(Res.iconEduTime, user.eduTime)}
  //           </View>
  //         </View>
  //       </View>
  //       <View style={[resumePreStyle.flexDirectionRowNormal]} >
  //         <Text style={resumePreStyle.cellHeaderText}>{user.city}<Text style={resumePreStyle.desText}>(出生地)</Text></Text>
  //         <View style={resumePreStyle.separateLine} />
  //         <Text style={resumePreStyle.cellHeaderText}>{user.nowCity}<Text style={resumePreStyle.desText}>(现居地)</Text></Text>
  //       </View>
  //       <Text style={[resumePreStyle.subTitleText, resumePreStyle.phoneText]}>{user.phone}</Text>
  //       <Text style={[resumePreStyle.subTitleText, resumePreStyle.emailText]}>{user.email}</Text>
  //     </View>
  //   </View>
  // )
  // renderLikeJob=() => (
  //   <View style={resumePreStyle.cell} >
  //     {this.renderCellHeader(I18n.t('page_resume_label_job_like'))}
  //     <View style={resumePreStyle.cellHeader}>
  //       <View style={resumePreStyle.likeJobDirectionRowBetween}>
  //         <View >
  //           <Text style={resumePreStyle.titleText}>{likeJob.name}</Text>
  //           <View style={[resumePreStyle.flexDirectionRowNormal, resumePreStyle.likeJobAreaView]}>
  //             <Text style={[resumePreStyle.subTitleText, resumePreStyle.likeJobAreaText]}>{likeJob.area}</Text>
  //             <Text style={resumePreStyle.subTitleText}>{likeJob.type}</Text>
  //           </View>
  //         </View>
  //         <Text style={resumePreStyle.likeJobMoneyText}>{likeJob.money}</Text>
  //       </View>
  //       <Text style={[resumePreStyle.subTitleText, resumePreStyle.likeJobTypeText]}>{likeJob.tips}</Text>
  //     </View>
  //   </View>
  // )
  // renderEducationDetail(rowData, sectionID, rowID) {
  //   return (
  //     <View style={{ marginTop: -15, marginBottom: 30 }}>
  //       <Text style={[resumePreStyle.timelineTimeText]}>{rowData.time}</Text>
  //       <Text style={[resumePreStyle.titleText, resumePreStyle.timelineTitleText]}>{rowData.title}</Text>
  //       <Text style={[resumePreStyle.timelineSubText]}>{rowData.sub}</Text>
  //     </View>
  //   );
  // }
  // renderEducation=() => (
  //   <View style={[resumePreStyle.cell, resumePreStyle.timelineCell]} >
  //     {this.renderCellHeader(I18n.t('page_resume_label_education'))}
  //     {/* <View style={resumePreStyle.cellHeader}> */}
  //     <Timeline
  //       data={education}
  //       showTime={false}
  //       circleSize={8}
  //       circleColor={baseBlueColor}
  //       lineColor={baseBlueColor}
  //       renderDetail={this.renderEducationDetail}
  //       options={{
  //         style: { paddingTop: 15, paddingLeft: 5 },
  //       }}
  //       // innerCircle="dot"
  //     />
  //     {/* </View> */}
  //   </View>
  // )

  // renderJobDetail(rowData, sectionID, rowID) {
  //   return (
  //     <View style={{ marginTop: -15, marginBottom: 30 }}>
  //       <Text style={[resumePreStyle.timelineTimeText]}>{rowData.time}</Text>
  //       <Text style={[resumePreStyle.titleText, resumePreStyle.timelineTitleText]}>{rowData.title}</Text>
  //       <Text style={[resumePreStyle.timelineSubText]}>{rowData.sub}</Text>
  //       <Text style={[resumePreStyle.timelineDesText]}>{rowData.description}</Text>
  //     </View>
  //   );
  // }
  // renderJob=() => (
  //   <View style={[resumePreStyle.cell, resumePreStyle.timelineCell]} >
  //     {this.renderCellHeader(I18n.t('page_resume_label_work_exp'))}
  //     {/* <View style={resumePreStyle.cellHeader}> */}
  //     <Timeline
  //       data={job}
  //       showTime={false}
  //       circleSize={8}
  //       circleColor={baseBlueColor}
  //       lineColor={baseBlueColor}
  //       renderDetail={this.renderJobDetail}
  //       options={{
  //         style: { paddingTop: 15, paddingLeft: 5 },
  //         }}
  //       // innerCircle="dot"
  //     />
  //     {/* </View> */}
  //   </View>
  // )
  // renderAppraise=() => (
  //   <View style={resumePreStyle.cell} >
  //     {this.renderCellHeader(I18n.t('page_resume_label_appraise'))}
  //     <View style={resumePreStyle.cellHeader}>
  //       <Text style={[resumePreStyle.timelineDesText, resumePreStyle.appraiseText]}>执行，执行力强，是合作企业管理，人员管理工作</Text>
  //     </View>
  //   </View>
  // )

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_previews_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
        />
        <ResumeItem nav={navigation} />
        <View style={[resumePreStyle.flexDirectionRowNormal, resumePreStyle.footer]}>
          <Icon
            type="entypo"
            name="share"
            size={35}
            color={baseBlueColor}
            iconStyle={resumePreStyle.shareIcon}
          />
          <Button
            title={I18n.t('page_resume_label_up')}
            buttonStyle={resumePreStyle.footerBtn}
            containerStyle={resumePreStyle.footerBtnContain}
          />
        </View>
      </View>
    );
  }
}

export default Preview;
