import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import regExp from '../util/regExp';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const skill = this.props.navigation.state.params.data;
    this.isEdit = this.props.navigation.state.params.edit;
    this.state = {
      postData: skill
        ? Object.assign({}, skill)
        : {
            name: '',
            years: '',
          },
      name: skill.name,
      years: skill.years,
    };
  }

  onChangeName = (text) => {
    this.updateValue('name', text);
  };

  onChangeYears = (text) => {
    this.updateValue('years', text);
  };

  updateValue(key, value) {
    const temp = this.state.postData;
    temp[key] = value;
    this.setState({
      postData: temp,
    });
  }

  publish = () => {
    const { currentResume } = this.props.resumeStore;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.name) {
      this.toast.show(I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_skill_name'));
      return;
    }
    if (!regExp.numberExtra.test(temp.years)) {
      this.toast.show(I18n.t('page_resume_label_positive_years'));
      return;
    }
    temp.resumeId = currentResume.resumeId;
    if (this.isEdit) {
      this.props.resumeAction.updateSkill(temp.id, currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    } else {
      this.props.resumeAction.addSkill(currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    }
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_skill_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_skill_name')}
          chevron
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_skill_name'),
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeName,
            defaultValue: this.state.name ? this.state.name : '',
            maxLength: 30,
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_skill_years')}
          chevron
          input={{
            placeholder: I18n.t('page_resume_tips_input') + I18n.t('page_resume_label_skill_years'),
            keyboardType: 'numeric',
            maxLength: 2,
            inputStyle: resumeStyle.inputText,
            onChangeText: this.onChangeYears,
            defaultValue: this.state.years ? this.state.years.toString() : '',
          }}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
