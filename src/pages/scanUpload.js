/**
 * 功能：扫码上传
 * 描述：
 * Author:孙宇强
 */
import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, Image } from 'react-native';
import { <PERSON><PERSON>, But<PERSON> } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { scanUploadStyle, globalStyle, headerStyle, baseBlueColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import res from '../res';
import configs from '../configs';

@inject('resumeStore', 'resumeAction')
@observer
export default class UploadAnnex extends Component {
  onScan = () => {
    this.props.navigation.navigate('cameraScan');
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_scan_upload_title'), style: headerStyle.center }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
        />
        <View style={scanUploadStyle.topContainer}>
          <Image style={scanUploadStyle.imageContainer} source={res.resumeComputer} />
          <Text style={scanUploadStyle.titleText}>{I18n.t('page_scan_upload_open')}</Text>
          <Text style={scanUploadStyle.subTitleText}>{configs.authorizedLoginURL}</Text>
          <View style={scanUploadStyle.buttonContainer}>
            <Button
              title={I18n.t('page_scan_upload_button_scan')}
              buttonStyle={{
                backgroundColor: baseBlueColor,
                height: 40,
              }}
              titleStyle={{ fontSize: 16 }}
              onPress={() => this.onScan()}
            />
          </View>
        </View>
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
