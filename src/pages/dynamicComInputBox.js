import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import {
  View,
  TextInput,
  KeyboardAvoidingView,
  Text,
  TouchableOpacity,
  Keyboard,
  Platform,
} from 'react-native';
import { CheckBox } from 'react-native-elements';
import { dynamicStyle } from '../themes';
import I18n from '../i18n';

@inject('dynamicAction')
@observer
export default class Page extends Component {
  componentDidMount() {
    if (Platform.OS === 'android') {
      setTimeout(() => {
        this.inputText && this.inputText.focus();
      }, 200);
    }
  }

  constructor(props) {
    super(props);
    this.state = {
      msgText: '',
      checked: false,
    };
  }

  onCommentPress = async () => {
    const data = this.props.info;
    const result = await this.props.dynamicAction.publishComment(data.id, {
      anonymous: this.state.checked ? 0 : 1,
      content: this.state.msgText,
      replyCommentId: this.props.replyCommentId,
    });
    this.props.onPress(result);
  };

  checkBoxPress = () => {
    this.setState({ checked: !this.state.checked });
  };

  renderBox = () => (
    <View style={dynamicStyle.inputBoxWrapper}>
      <View style={[dynamicStyle.inputBox]}>
        <TextInput
          autoFocus={Platform.OS === 'ios'}
          style={dynamicStyle.inputText}
          blurOnSubmit={false}
          placeholder={
            this.props.placeholderComment
              ? this.props.placeholderComment
              : I18n.t('page_comment_ph_comment')
          }
          underlineColorAndroid="transparent"
          multiline
          maxLength={200}
          ref={(ref) => {
            this.inputText = ref;
          }}
          onChangeText={(text) => {
            this.setState({
              msgText: text,
            });
          }}
          textAlignVertical="top"
        />
        <View style={[dynamicStyle.commentItemtTitleContent, dynamicStyle.inputBtnContent]}>
          <CheckBox
            center
            title={I18n.t('page_comment_text_anonymous')}
            textStyle={
              this.state.checked ? dynamicStyle.inputBtnNiChecked : dynamicStyle.inputBtnNiNormal
            }
            containerStyle={dynamicStyle.inputBtnNiContent}
            checked={this.state.checked}
            onPress={this.checkBoxPress}
          />
          <TouchableOpacity
            onPress={() => {
              this.onCommentPress();
            }}
          >
            <Text style={dynamicStyle.inputBtnSend}>{I18n.t('page_comment_btn_sendcomment')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  render() {
    if (global.IS_IOS) {
      return <KeyboardAvoidingView behavior="padding">{this.renderBox()}</KeyboardAvoidingView>;
    }
    return this.renderBox();
  }
}
