import React from 'react';
import { inject, observer } from 'mobx-react';
import { View, TouchableOpacity, Text, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { Input, Button } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import EvilIcon from 'react-native-vector-icons/EvilIcons';
import CountryCode from '../components/countryCode';
import { forgotStyle, phColor, subTitleColor } from '../themes';
import I18n from '../i18n';

import res from '../res';
import regExp from '../util/regExp';
import LoadingModal from '../components/loadingModal';
import { normalizePhone } from '../common/special';
import constant from '../store/constant';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';
import validateUtil from '../util/validateUtil';
import InputImageCaptchaModal from '../components/modal/inputImageCaptchaModal';

let timeout = null;
let keyBoardIsShow = false;

@inject('loginAction')
@observer
export default class ForgotPassword extends React.Component {
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      region: constant.defaultRegion,
      regionCode: constant.defaultRegion.code,
      regionCodeLabel: constant.defaultRegion.label,
      tabId: 1,
      phone: '',
      code: '',
      email: '',
      password: '',
      sending: false,
      seconds: 60,
      isOpenSelectedCountryCode: false,
      showLoading: false,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    if (timeout) {
      clearInterval(timeout);
    }
  }

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = true;
  }

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  onSwitchTab = (tabId) => {
    this.setState({ tabId });
  };

  setPhone = (text) => {
    this.setState({
      phone: text,
    });
  };

  setCode = (text) => {
    this.setState({
      code: text,
    });
  };

  setEmail = (text) => {
    this.setState({
      email: text,
    });
  };

  setToken = (text) => {
    this.setState({
      password: text,
    });
  };

  runSendCodeTimeout = () => {
    this.setState({ sending: true });
    timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds === 0) {
        this.setState({ seconds: 60, sending: false });
        clearInterval(timeout);
      }
    }, 1000);
  };

  sendPhoneCode = async (imageCaptcha) => {
    try {
      const { phone, regionCode, region, sending } = this.state;
      if (sending) return;
      const mobile = validateUtil.validatePhone(phone, region);
      if (!mobile) return;
      const res = await this.inputImageCaptchaModal.sendImPhoneCode(
        {
          param: {
            mobile,
            regionCode,
            imageCaptcha,
          },
          onConfirm: this.sendPhoneCode,
        },
        this.props.loginAction.sendForgotCodeByPhone
      );
      if (res && res.successful) {
        toast.show(I18n.t('page_login_op_sendcode_success'));
        this.runSendCodeTimeout();
      } else {
        if (res.message) {
          toast.show(res.message);
        } else {
          toast.show(I18n.t('page_login_op_sendcode_error'));
        }
      }
    } catch (e) {
      console.log('sendVerifyCode error', err);
      this.setState({ sending: false });
    }
    return;

    const { phone, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    this.setState({ sending: true });
    this.props.loginAction
      .sendForgotCodeByPhone(normalizePhone(phone.trim(), regionCode.trim()), regionCode.trim())
      .then(
        (result) => {
          if (result && result.successful) {
            this.toast.show(I18n.t('page_forgot_op_sendcode_success'));
            // 数秒
            this.runSendCodeTimeout();
          } else {
            if (result.message) {
              this.toast.show(result.message);
            } else {
              this.toast.show(I18n.t('page_forgot_op_sendcode_error'));
            }
            this.setState({ sending: false });
          }
        },
        () => {
          this.setState({ sending: false });
        }
      );
  };

  sendEmailCode = () => {
    const { email } = this.state;
    if (email.trim() === '') {
      this.toast.show(I18n.t('page_forgot_op_email_required'));
      return;
    }
    this.setState({ sending: true });
    this.props.loginAction.sendForgotCodeByEmail(email.trim()).then((result) => {
      if (result && result.successful) {
        this.toast.show(I18n.t('page_forgot_op_sendcode_success'));
        // 数秒
        this.runSendCodeTimeout();
      } else {
        if (result.message) {
          this.toast.show(result.message);
        } else {
          this.toast.show(I18n.t('page_forgot_op_sendcode_error'));
        }
        this.setState({ sending: false });
      }
    });
  };

  submit = () => {
    if (this.state.tabId === 1) {
      this.doForgotByPhone();
    } else {
      this.doForgotByEmail();
    }
  };

  doForgotByPhone = () => {
    if (!this.verifyPhoneForm()) {
      return;
    }
    const { phone, password, code, regionCode } = this.state;
    this.setState({ showLoading: true });
    this.props.loginAction
      .forgotPasswordByPhone({
        code,
        phone,
        password,
        regionCode,
      })
      .then(
        () => {
          this.setState({ showLoading: false });
          this.toast.show(I18n.t('page_forgot_op_success'));
          setTimeout(() => {
            this.props.navigation.navigate('login');
          }, 2000);
        },
        (err) => {
          this.setState({ showLoading: false });
          if (err && err.message) {
            this.toast.show(err.message);
          } else {
            this.toast.show(I18n.t('page_forgot_op_error'));
          }
        }
      );
  };

  doForgotByEmail = () => {
    if (!this.verifyEmailForm()) {
      return;
    }
    const { email, password, code } = this.state;
    this.setState({ showLoading: true });
    this.props.loginAction.forgotPasswordByEmail({ code, email, password }).then(
      () => {
        this.setState({ showLoading: false });
        this.toast.show(I18n.t('page_forgot_op_success'));
        setTimeout(() => {
          this.props.navigation.navigate('login');
        }, 2000);
      },
      (err) => {
        this.setState({ showLoading: false });
        if (err && err.message) {
          this.toast.show(err.message);
        } else {
          this.toast.show(I18n.t('page_forgot_op_error'));
        }
      }
    );
  };

  verifyPhoneForm = () => {
    const { phone, password, code } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return false;
    }
    // 验证密码
    if (!regExp.password.test(password.trim())) {
      this.toast.show(I18n.t('page_forgot_op_password_format_error'));
      return false;
    }
    if (password.trim() === phone.trim()) {
      this.toast.show(I18n.t('page_forgot_op_password_same_phone_error'));
      return false;
    }
    if (code.trim() === '') {
      this.toast.show(I18n.t('page_forgot_op_code_required'));
      return false;
    }
    return true;
  };

  verifyEmailForm = () => {
    const { email, password, code } = this.state;
    if (email.trim() === '') {
      this.toast.show(I18n.t('page_forgot_op_email_required'));
      return false;
    }
    // 验证密码
    if (!regExp.password.test(password.trim())) {
      this.toast.show(I18n.t('page_forgot_op_password_format_error'));
      return false;
    }
    if (password.trim() === email.trim()) {
      this.toast.show(I18n.t('page_forgot_op_password_same_email_error'));
      return false;
    }
    if (code.trim() === '') {
      this.toast.show(I18n.t('page_forgot_op_code_required'));
      return false;
    }
    return true;
  };

  onSelectedCountryCode = (item) => {
    if (item) {
      this.setState({ region: item, regionCodeLabel: item.label, regionCode: item.code });
    }
    this.setState({ isOpenSelectedCountryCode: false });
  };

  openCountryCodeModal = (value) => {
    this.onLostBlur();
    this.setState({ isOpenSelectedCountryCode: value });
  };

  renderHeader = () => {
    const { navigation } = this.props;
    return (
      <View style={forgotStyle.headerSection}>
        <TouchableOpacity
          style={forgotStyle.headerBack}
          onPress={() => {
            navigation.navigate('login');
          }}
        >
          <Image source={res.iconBack} style={forgotStyle.iconBack} />
        </TouchableOpacity>
        <View>
          <Text style={forgotStyle.headerTitle}>
            {I18n.t('page_forgot_text_email_header_title')}
          </Text>
        </View>
      </View>
    );
  };

  renderForgotPhoneForm = () => (
    <View style={forgotStyle.tabContent}>
      <View style={[forgotStyle.formGroup, forgotStyle.formGroupLine]}>
        <View style={forgotStyle.formPhoneSection}>
          <View style={forgotStyle.formPhoneAreaWrap}>
            <TouchableOpacity
              style={forgotStyle.formPhoneAreaTo}
              onPress={() => {
                this.openCountryCodeModal(true);
              }}
            >
              <Text style={forgotStyle.formPhoneArea}>
                {this.state.regionCodeLabel}
                <EvilIcon name="chevron-down" size={20} color={subTitleColor} />
              </Text>
            </TouchableOpacity>
          </View>
          <View style={forgotStyle.formPhoneInputWrap}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={forgotStyle.formControl}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_forgot_ph_phone')}
              placeholderTextColor={phColor}
              onChangeText={this.setPhone}
              value={this.state.phone}
              maxLength={15}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>
      <View style={[forgotStyle.formGroup, forgotStyle.formGroupLine]}>
        <Input
          secureTextEntry
          inputContainerStyle={forgotStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_forgot_ph_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setToken}
          value={this.state.password}
          maxLength={16}
        />
      </View>
      <View style={forgotStyle.formGroup}>
        <View style={forgotStyle.formSendCodeSection}>
          <View style={forgotStyle.formSendCodeInputWrap}>
            <Input
              inputContainerStyle={forgotStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_forgot_ph_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCode}
              value={this.state.code}
              maxLength={6}
              keyboardType="numeric"
            />
          </View>
          <View style={forgotStyle.formSendCodeBtnWrap}>
            <Button
              title={
                this.state.sending
                  ? `${I18n.t('page_forgot_btn_sendcode')}(${this.state.seconds})`
                  : I18n.t('page_forgot_btn_sendcode')
              }
              titleStyle={{ color: '#fff', fontSize: 16 }}
              buttonStyle={forgotStyle.btnSendCode}
              disabled={this.state.sending}
              onPress={this.sendPhoneCode}
            />
          </View>
        </View>
      </View>
    </View>
  );

  renderForgotEmailForm = () => (
    <View style={forgotStyle.tabContent}>
      <View style={[forgotStyle.formGroup, forgotStyle.formGroupLine]}>
        <Input
          autoCapitalize="none"
          inputContainerStyle={forgotStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_forgot_ph_email')}
          placeholderTextColor={phColor}
          onChangeText={this.setEmail}
          value={this.state.email}
          maxLength={100}
        />
      </View>
      <View style={[forgotStyle.formGroup, forgotStyle.formGroupLine]}>
        <Input
          secureTextEntry
          inputContainerStyle={forgotStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_forgot_ph_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setToken}
          value={this.state.password}
          maxLength={16}
        />
      </View>
      <View style={forgotStyle.formGroup}>
        <View style={forgotStyle.formSendCodeSection}>
          <View style={forgotStyle.formSendCodeInputWrap}>
            <Input
              inputContainerStyle={forgotStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_forgot_ph_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCode}
              value={this.state.code}
              maxLength={6}
              keyboardType="numeric"
            />
          </View>
          <View style={forgotStyle.formSendCodeBtnWrap}>
            <Button
              title={
                this.state.sending
                  ? `${I18n.t('page_forgot_btn_sendcode')}(${this.state.seconds})`
                  : I18n.t('page_forgot_btn_sendcode')
              }
              titleStyle={{ color: '#fff', fontSize: 16 }}
              buttonStyle={forgotStyle.btnSendCode}
              disabled={this.state.sending}
              onPress={this.sendEmailCode}
            />
          </View>
        </View>
      </View>
    </View>
  );

  render() {
    const { tabId, isOpenSelectedCountryCode, showLoading } = this.state;
    return (
      // View 用以适配iPhoneX
      <View style={forgotStyle.page}>
        <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
          <ImageBackground source={res.bgLogin} style={{ width: '100%', height: '100%' }}>
            <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
              <View style={[forgotStyle.container]}>
                {this.renderHeader()}
                <View>
                  <View style={forgotStyle.titleSection}>
                    <Text style={forgotStyle.title}>{I18n.t('page_forgot_text_title')}</Text>
                  </View>
                  <View style={forgotStyle.tabSection}>
                    <View style={forgotStyle.tabHeader}>
                      <View style={tabId === 1 && forgotStyle.tabActive}>
                        <Text
                          style={[forgotStyle.tabTitle, tabId === 1 && forgotStyle.tabActiveTitle]}
                          onPress={() => this.onSwitchTab(1)}
                        >
                          {I18n.t('page_forgot_tab_phone')}
                        </Text>
                      </View>
                      <View style={tabId === 2 && forgotStyle.tabActive}>
                        <Text
                          style={[forgotStyle.tabTitle, tabId === 2 && forgotStyle.tabActiveTitle]}
                          onPress={() => this.onSwitchTab(2)}
                        >
                          {I18n.t('page_forgot_tab_email')}
                        </Text>
                      </View>
                    </View>
                    <View style={forgotStyle.tabBody}>
                      {tabId === 1 && this.renderForgotPhoneForm()}
                      {tabId === 2 && this.renderForgotEmailForm()}
                    </View>
                  </View>
                  <View style={forgotStyle.btnSection}>
                    <TouchableOpacity style={forgotStyle.btnSubmit} onPress={this.submit}>
                      <Text style={forgotStyle.btnSubmitText}>
                        {I18n.t('page_forgot_btn_submit')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ImageBackground>
        </TouchableWithoutFeedback>
        <CountryCode isOpen={isOpenSelectedCountryCode} onSelected={this.onSelectedCountryCode} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={showLoading} loadingTips />
        <InputImageCaptchaModal ref={(ref) => (this.inputImageCaptchaModal = ref)} />
      </View>
    );
  }
}
