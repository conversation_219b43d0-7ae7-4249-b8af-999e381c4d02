// ResumeComplete.js

import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  View,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Text,
} from 'react-native';
import { Icon, Header } from 'react-native-elements';
import { globalStyle, headerStyle } from '../themes';
import ResumeItem from './resumeItem';
import Session from '../api/session';
import I18n from '../i18n';
import res from '../res';
import Image from '../components/image';
import { deviceWidth, footerHeight } from '../common';
import constant from '../store/constant';
import GoBack from '../components/goback';

function getComponentStyle() {
  return {
    container: {
      ...globalStyle.container,
      flex: 1,
    },
    headerText: {
      paddingVertical: 15,
      paddingHorizontal: 15,
      color: '#000',
      fontSize: 12,
      textAlign: 'center',
    },
    emptyContainer: {
      marginTop: '40%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyImage: {
      width: '24%',
      height: 120,
    },
    footerWrapper: {
      position: 'absolute',
      bottom: footerHeight + 10,
      left: 0,
      right: 0,
    },
    footerButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 20,
      height: 40,
      borderRadius: 15,
      backgroundColor: '#2089DC',
    },
    footerText: {
      color: '#fff',
    },
    headerRightBtnContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerRightImage: {
      width: 12,
      height: 13,
    },
    headerRightText: {
      ...headerStyle.rightBtn,
      color: 'white',
      marginLeft: 7,
    },
    headerLeftIcon: {
      marginLeft: 0,
    },
  };
}

@inject('resumeStore', 'resumeAction')
@observer
export default class ResumeComplete extends Component {
  style = getComponentStyle();

  constructor(props) {
    super(props);
    this.state = {
      currentIndex: 1,
    };
  }

  componentDidMount() {
    global.emitter.on('refreshList', this.onRefreshList);
    global.emitter.on(constant.topic.onlineResumeChanged, this.onRefreshList);
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }
    this.isLogin();
  }

  componentWillUnmount() {
    global.emitter.off('refreshList', this.onRefreshList);
    global.emitter.off(constant.topic.onlineResumeChanged, this.onRefreshList);
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  onRefreshList = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.resumeAction.getResumes();
        this.setState({ currentIndex: 1 });
        this.scrollView.scrollTo({ x: 0, y: 0, animated: true });
      }
    });
  };

  onBackAndroid = () => {
    if (this.props.navigation.isFocused()) {
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp();
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT);
      return true;
    }
    return false;
  };

  isLogin() {
    return Session.isLogin().then((isLogin) => {
      if (!isLogin) return Promise.resolve();
      return this.props.resumeAction.getResumes();
    });
  }

  onAnnex = () => {
    this.props.navigation.navigate('annexResume');
  };

  onSkip = () => {
    this.props.navigation.navigate('main');
  };

  renderHerderRight = () => (
    <TouchableOpacity style={this.style.headerRightBtnContainer} onPress={this.onAnnex}>
      <Image style={this.style.headerRightImage} source={res.resumeAnnex} />
      <Text style={this.style.headerRightText}>{I18n.t('page_resume_annex_title')}</Text>
    </TouchableOpacity>
  );

  renderEmptyComponent = () => (
    <View style={this.style.emptyContainer}>
      <Image style={this.style.emptyImage} source={res.noData} />
    </View>
  );

  render() {
    const { navigation } = this.props;
    const { resumeList } = this.props.resumeStore;
    const { currentIndex } = this.state;

    let title = I18n.t('page_resume_title');
    if (resumeList?.length > 1) {
      title = I18n.t('page_resume_title_more', { current: currentIndex, total: resumeList.length });
    }

    return (
      <View style={this.style.container}>
        <StatusBar barStyle="light-content" backgroundColor="#2089DC" />
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          rightComponent={this.renderHerderRight()}
          leftComponent={<GoBack navigation={navigation} />}
        />

        {resumeList?.length > 0 ? (
          <ScrollView
            ref={(ref) => {
              this.scrollView = ref;
            }}
            showsVerticalScrollIndicator={false}
          >
            <Text style={this.style.headerText}>{I18n.t('page_job_resume_prompt')}</Text>

            <ResumeItem resume={resumeList[0]} nav={navigation} resumeList={resumeList} />
            <View style={{ height: 100 }} />
          </ScrollView>
        ) : (
          this.renderEmptyComponent()
        )}

        <Toast ref={(ref) => (this.toast = ref)} position="center" />

        <View style={this.style.footerWrapper}>
          <TouchableOpacity style={this.style.footerButton} onPress={this.onSkip}>
            <Text style={this.style.footerText}>{I18n.t('page_job_skip_later')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}
