import React, { Component } from 'react';
import { View } from 'react-native';
import { Header, Icon, ListItem } from 'react-native-elements';
import { headerStyle, desColor, generalStyle } from '../themes';
import GoBack from '../components/goback';
import Config from '../configs';
import I18n from '../i18n';

export default class Page extends Component {
  onTabItem = (item) => {
    this.props.navigation.navigate('webviewDetail', { url: item.url, title: item.title });
  };

  renderPropsStatus = () => {
    const { tag } = this.props.navigation.state.params;
    const data = tag === 2 ? Config.aboutResume : Config.aboutInterview;
    let array = [];
    if (I18n.locale === 'en') {
      array = data.en;
    } else if (I18n.locale === 'zh') {
      array = data.zh;
    } else {
      array = data.km;
    }
    return array.map((item) => (
      <ListItem
        key={item.id}
        onPress={() => this.onTabItem(item)}
        containerStyle={generalStyle.listItemCon}
        title={item.title}
        titleStyle={generalStyle.listItemTitle}
        rightIcon={<Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />}
      />
    ));
  };

  render() {
    const { navigation } = this.props;
    const { title } = this.props.navigation.state.params;
    return (
      <View style={generalStyle.generalContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: title, style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderPropsStatus()}
      </View>
    );
  }
}
