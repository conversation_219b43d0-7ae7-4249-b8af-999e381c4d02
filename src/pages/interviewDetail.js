import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { View, Text, ScrollView } from 'react-native';
import { inject, observer } from 'mobx-react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-native-elements';
import { headerStyle, interviewDetailStyle, settingStyle, baseBlueColor } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import resIcon, { getEmployerAvatarSource } from '../res';
import ResumeModal from '../components/resumeModal';
import util from '../util';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';
import constant from '../store/constant';

@inject('jobStore', 'userAction', 'jobAction')
@observer
export default class InterviewDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      interviewDetail: null,
      showBtn: true,
      showLoading: true,
    };
  }

  componentDidMount() {
    this.getDetail();
  }

  onReject = async () => {
    const {
      params: { id },
    } = this.props.navigation.state;
    if (id) {
      await this.props.userAction.postInterviewConfirm(id, { accept: false });
      this.toast.show(I18n.t('page_mine_interview_reject_success'));
      this.setState({ showBtn: false });
    }
  };

  onAccept = async (resumeId) => {
    const {
      params: { id },
    } = this.props.navigation.state;
    if (resumeId && id) {
      await this.props.userAction.postInterviewConfirm(id, { accept: true, cvId: resumeId });
      this.props.jobAction.getJobStatistics();
      this.toast.show(I18n.t('page_mine_interview_success'));
      this.setState({ showBtn: false });
      global.emitter.emit(constant.event.showResumeModal, {
        isOpen: false,
        page: 'interviewDetail',
      });
    } else {
      this.toast.show(I18n.t('page_mine_interview_error'));
    }
  };

  getDetail = async () => {
    try {
      const {
        params: { id },
      } = this.props.navigation.state;
      const res = await this.props.userAction.getInterviewDetail(id);
      if (res) {
        this.setState({ interviewDetail: res });
      }
      this.setState({ showLoading: false });
    } catch (error) {
      this.setState({ showLoading: false });
    }
  };

  sendResume = () => {
    global.emitter.emit(constant.event.showResumeModal, {
      isOpen: true,
      page: 'interviewDetail',
    });
  };

  renderTopView = () => {
    const detail = this.state.interviewDetail;
    return (
      <View style={interviewDetailStyle.companyContainer}>
        <ImageBackground
          imageStyle={{ borderRadius: 21 }}
          style={interviewDetailStyle.companyLogo}
          source={getEmployerAvatarSource(detail ? detail.employerLogo : '')}
        >
          {detail &&
          detail.employerQualificationType &&
          detail.employerQualificationType.value === 1 ? (
            <Image style={interviewDetailStyle.v2} source={resIcon.verify} />
          ) : (
            <View />
          )}
        </ImageBackground>
        <View style={{ marginLeft: 10, width: '86%' }}>
          <View style={interviewDetailStyle.companyInfo}>
            <Text style={[interviewDetailStyle.companyName, { flexShrink: 50 }]}>
              {detail ? detail.company : ''}{' '}
              {detail && detail.qualificationStatus && detail.qualificationStatus.value === 1 ? (
                <Image source={resIcon.iconVerify} style={{ width: 9, height: 9 }} />
              ) : null}
            </Text>
            <Text style={[interviewDetailStyle.interviewStatus, { flexShrink: 1 }]}>
              {detail && detail.interviewStatus && detail.interviewStatus.label
                ? detail.interviewStatus.label
                : I18n.t('page_mine_interview_nav_title_interviewing')}
            </Text>
          </View>
          {detail && (detail.jobTitle || (detail.salaryId && detail.salaryId.label)) ? (
            <View style={interviewDetailStyle.otherInfo}>
              <Text style={interviewDetailStyle.companySubcribe}>
                {detail.jobTitle ? detail.jobTitle : ''}
              </Text>
              {detail && (detail.jobTitle || (detail.salaryId && detail.salaryId.label)) ? (
                <Text style={interviewDetailStyle.spaceLine} />
              ) : (
                <Text style={{ display: 'none' }} />
              )}
              {detail.salaryId && detail.salaryId.label ? (
                <Text style={interviewDetailStyle.companySubcribe}>
                  {detail.salaryId ? `${detail.salaryId.label}` : ''}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )}
        </View>
      </View>
    );
  };

  renderListItem = () => {
    const detail = this.state.interviewDetail;
    return (
      <View style={interviewDetailStyle.listItemContainer}>
        <View style={interviewDetailStyle.itemContainer}>
          <Text style={interviewDetailStyle.itemTitle}>{I18n.t('page_mine_interview_time')}</Text>
          <Text style={interviewDetailStyle.itemSubtitle}>
            {detail ? util.dateToLocalString1(detail.actionTime) : ''}
          </Text>
        </View>
        <View style={interviewDetailStyle.itemContainer}>
          <Text style={interviewDetailStyle.itemTitle}>
            {I18n.t('page_mine_interview_address')}
          </Text>
          <Text style={interviewDetailStyle.itemSubtitle}>
            {detail ? detail.actionAddress : ''}
          </Text>
        </View>
        <View style={interviewDetailStyle.itemContainer}>
          <Text style={interviewDetailStyle.itemTitle}>
            {I18n.t('page_mine_interview_contact')}
            <Text style={{ color: '#fff' }}>人</Text>
          </Text>
          <Text style={interviewDetailStyle.itemSubtitle}>{detail ? detail.actionConact : ''}</Text>
        </View>
        <View style={interviewDetailStyle.itemContainer}>
          <Text style={interviewDetailStyle.itemTitle}>
            {I18n.t('page_mine_interview_company_phone')}
          </Text>
          <Text style={interviewDetailStyle.itemSubtitle}>{detail ? detail.actionMobile : ''}</Text>
        </View>
        <View style={[interviewDetailStyle.itemContainer, { alignItems: 'flex-start' }]}>
          <Text style={interviewDetailStyle.itemTitle}>
            {I18n.t('page_mine_interview_company_address')}
          </Text>
          <Text style={interviewDetailStyle.itemSubtitle}>
            {detail ? detail.companyAddress : ''}
          </Text>
        </View>
      </View>
    );
  };

  renderButtonGroup = () => (
    <View style={interviewDetailStyle.btnContainer}>
      <View style={{ width: '50%' }}>
        <Button
          title={I18n.t('page_mine_interview_reject')}
          buttonStyle={interviewDetailStyle.chatBtn}
          titleStyle={{ fontSize: 16, color: baseBlueColor }}
          onPress={() => {
            this.onReject();
          }}
        />
      </View>
      <View style={{ width: '50%' }}>
        <Button
          title={I18n.t('page_mine_interview_accept')}
          buttonStyle={interviewDetailStyle.sendBtn}
          titleStyle={{ fontSize: 16 }}
          onPress={this.sendResume}
        />
      </View>
    </View>
  );

  render() {
    const { navigation } = this.props;
    const {
      params: { isChat, id },
    } = this.props.navigation.state;
    const { showBtn } = this.state;
    const detail = this.state.interviewDetail;
    return (
      <View style={settingStyle.settingContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: isChat
              ? I18n.t('page_mine_interview_invite_info')
              : I18n.t('page_mine_nav_interview_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          {this.renderTopView()}
          {this.renderListItem()}
        </ScrollView>
        {isChat && detail && detail.status && detail.status.value === -2 && showBtn ? (
          this.renderButtonGroup()
        ) : (
          <Text />
        )}
        <ResumeModal
          getResumeId
          nav={navigation}
          jobId={id}
          sendResume={this.onAccept}
          page="interviewDetail"
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
