import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Header, ListItem } from 'react-native-elements';
import Toast from 'react-native-easy-toast';
import { resumeStyle, globalStyle, headerStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('resumeStore', 'resumeAction')
@observer
export default class Profile extends Component {
  constructor(props) {
    super(props);
    const language = this.props.navigation.state.params.data;
    this.isEdit = this.props.navigation.state.params.edit;
    this.state = {
      postData: language
        ? Object.assign({}, language)
        : {
            languageId: {},
            languageLevelId: {},
          },
    };
  }

  componentWillUnmount() {}

  publish = () => {
    const { currentResume } = this.props.resumeStore;
    const temp = Object.assign({}, this.state.postData);
    if (!temp.languageId) {
      this.toast.show(
        I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_language_name')
      );
      return;
    }
    if (!temp.languageLevelId) {
      this.toast.show(
        I18n.t('page_resume_tips_select') + I18n.t('page_resume_label_language_level')
      );
      return;
    }
    temp.resumeId = currentResume.resumeId;
    temp.languageId = temp.languageId.value;
    temp.languageLevelId = temp.languageLevelId.value;

    if (this.isEdit) {
      this.props.resumeAction.updateLanguage(temp.id, currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    } else {
      this.props.resumeAction.addLanguage(currentResume.resumeId, temp).then((res) => {
        this.showRequestResult(res);
      });
    }
  };

  showRequestResult(res) {
    const { navigation } = this.props;
    if (res && res.successful) {
      this.toast.show(res.message);
      setTimeout(() => {
        navigation.goBack();
      }, 1000);
      this.props.resumeAction.getResumes();
    } else {
      this.toast.show(res.message);
    }
  }

  render() {
    const { navigation } = this.props;
    const language = this.state.postData;

    return (
      <View style={{ ...globalStyle.container, flex: 1 }}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_resume_text_language_detail'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack iconColor="white" navigation={navigation} />}
          rightComponent={
            <TouchableOpacity onPress={this.publish}>
              <Text style={[headerStyle.rightBtn, { color: 'white' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ListItem
          title={I18n.t('page_resume_label_language_name')}
          chevron
          onPress={() => {
            navigation.navigate('constantList', {
              constantName: 'languageList',
              title: I18n.t('page_resume_label_language_name'),
              onSelect: (item) => {
                const temp = this.state.postData;
                temp.languageId = item;
                this.setState({
                  postData: temp,
                });
              },
            });
          }}
          rightTitle={language.languageId ? language.languageId.label : ''}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <ListItem
          title={I18n.t('page_resume_label_language_level')}
          chevron
          onPress={() => {
            navigation.navigate('constantList', {
              constantName: 'languageLevelList',
              title: I18n.t('page_resume_label_language_level'),
              onSelect: (item) => {
                const temp = this.state.postData;
                temp.languageLevelId = item;
                this.setState({
                  postData: temp,
                });
              },
            });
          }}
          rightTitle={language.languageLevelId ? language.languageLevelId.label : ''}
          rightTitleStyle={resumeStyle.listItemRightText}
          titleStyle={resumeStyle.listItemLeftText}
          containerStyle={resumeStyle.listItemContent}
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
