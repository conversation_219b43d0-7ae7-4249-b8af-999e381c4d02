import React, { Component } from 'react';
import { FlatList, View } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Input, Header, Icon, ListItem } from 'react-native-elements';
import { headerStyle, titleColor, desColor, jobStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import { RVW } from '../common';

@inject('resumeStore', 'jobStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.constant = this.props.navigation.state.params.constantName;
    this.state = {
      dataList: [],
    };
  }

  componentDidMount() {
    const list = this.props.resumeStore[this.constant];
    this.setState({ dataList: list });
    global.emitter.on('languageChange', this.onUpdate);
  }

  componentWillUnmount() {
    global.emitter.off('languageChange', this.onUpdate);
  }

  onUpdate = () => {
    this.forceUpdate();
  };

  onSelectItem(item) {
    const { navigation } = this.props;
    if (navigation.state.params) {
      navigation.state.params.onSelect(item);
      navigation.goBack();
    }
  }

  onChangeText = (text) => {
    const list = this.props.resumeStore[this.constant];
    let temp = list;
    temp = temp.filter((item) => item.label.indexOf(text) > -1);
    if (!text) {
      this.setState({ dataList: list });
    } else {
      this.setState({ dataList: temp });
    }
  };

  renderRow = ({ item }) => (
    <ListItem
      key={item.value}
      containerStyle={{
        borderBottomColor: '#eee',
        borderBottomWidth: 0.5,
        paddingHorizontal: 12,
        paddingVertical: 12,
        backgroundColor: '#fff',
      }}
      onPress={() => this.onSelectItem(item)}
      title={item.label}
      titleStyle={{
        color: titleColor,
        fontSize: 14,
      }}
    />
  );

  renderSearch = () => (
    <View style={jobStyle.jobExpectedSearch}>
      <Input
        inputContainerStyle={{
          backgroundColor: '#fff',
          marginVertical: 10,
          borderBottomWidth: 0,
          borderRadius: 6,
          marginLeft: 0,
          paddingVertical: 2,
          height: 38,
        }}
        inputStyle={{
          color: titleColor,
          fontSize: 14,
        }}
        onChangeText={(text) => this.onChangeText(text)}
        clearButtonMode="while-editing"
        returnKeyType="search"
        placeholder={I18n.t('page_chatlist_ph_search')}
        placeholderTextColor={desColor}
        leftIcon={<Icon type="evilicon" name="search" size={30} color={desColor} />}
        leftIconContainerStyle={{
          marginLeft: 2,
        }}
      />
    </View>
  );

  render() {
    const { navigation } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: this.props.navigation.state.params.title,
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderSearch()}
        <FlatList
          style={{ backgroundColor: '#fff' }}
          data={this.state.dataList}
          renderItem={this.renderRow}
          keyExtractor={(item, index) => `${index}`}
        />
      </View>
    );
  }
}
