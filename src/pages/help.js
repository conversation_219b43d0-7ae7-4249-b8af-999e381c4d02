import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import Toast from 'react-native-easy-toast';
import { View, Text, TouchableOpacity, Keyboard, Alert } from 'react-native';
import { Header, Icon, ListItem, ButtonGroup, Button } from 'react-native-elements';
import { headerStyle, baseBlueColor, desColor, generalStyle } from '../themes';
import GoBack from '../components/goback';
import MyTextInput from '../components/myTextInput';
import I18n from '../i18n';
import Session from '../api/session';

@inject('userAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedIndex: 0,
      feedbackContent: '',
    };
  }

  onTabItem = (index) => {
    switch (index) {
      case 1:
        break;
      case 2:
        this.props.navigation.navigate('helpList', {
          title: I18n.t('page_help_text_about_resume'),
          tag: 2,
        });
        break;
      case 3:
        this.props.navigation.navigate('helpList', {
          title: I18n.t('page_help_text_about_interview'),
          tag: 3,
        });
        break;
      default:
        break;
    }
  };

  onUpdateIndex = (selectedIndex) => {
    this.setState({ selectedIndex });
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onSubmitFeedback = () => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        const res = await this.props.userAction.userFeedback({
          remark: this.state.feedbackContent,
        });
        if (res.successful) {
          // this.textInput.clear();
          // this.textInput.value = '';
          this.setState({ feedbackContent: '' });
        }
        if (res.message) {
          // this.toast.show(I18n.t('feedback_tips_success'));
          Alert.alert(I18n.t('page_tips_withdraw_title'), I18n.t('feedback_tips_success'), [
            {
              text: I18n.t('page_setting_confirm_text'),
              onPress: () => {},
            },
          ]);
        } else if (res.fieldErrors) {
          this.toast.show(I18n.t('feedback_tips_null'));
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  renderHeaderButtonGroup = () => {
    const { selectedIndex } = this.state;
    const chatTab = () => (
      <Text style={{ color: selectedIndex === 0 ? baseBlueColor : '#fff' }}>
        {I18n.t('page_tab_text_help')}
      </Text>
    );
    const whoTab = () => (
      <Text style={{ color: selectedIndex === 1 ? baseBlueColor : '#fff' }}>
        {I18n.t('page_tab_text_feedback')}
      </Text>
    );
    const buttons = [{ element: chatTab }, { element: whoTab }];
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <ButtonGroup
          onPress={this.onUpdateIndex}
          selectedIndex={selectedIndex}
          buttons={buttons}
          containerStyle={{
            height: 29,
            width: I18n.locale === 'km' ? 220 : 180,
            borderRadius: 5,
            borderColor: '#fff',
            backgroundColor: baseBlueColor,
          }}
          selectedButtonStyle={{
            backgroundColor: '#fff',
          }}
        />
      </View>
    );
  };

  renderPropsStatus = () => (
    <View>
      {/* <ListItem
        onPress={() => this.onTabItem(1)}
        containerStyle={generalStyle.listItemCon}
        title={I18n.t('page_help_text_about_regesiter')}
        titleStyle={generalStyle.listItemTitle}
        rightIcon={<Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />}
      /> */}
      <ListItem
        onPress={() => this.onTabItem(2)}
        containerStyle={generalStyle.listItemCon}
        title={I18n.t('page_help_text_about_resume')}
        titleStyle={generalStyle.listItemTitle}
        rightIcon={<Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />}
      />
      <ListItem
        onPress={() => this.onTabItem(3)}
        containerStyle={generalStyle.listItemCon}
        title={I18n.t('page_help_text_about_interview')}
        titleStyle={generalStyle.listItemTitle}
        rightIcon={<Icon name="chevron-thin-right" type="entypo" size={15} color={desColor} />}
      />
    </View>
  );

  renderFeedback = () => (
    <View style={generalStyle.textInputCon}>
      <MyTextInput
        ref={(ref) => {
          this.textInput = ref;
        }}
        style={{ height: 320, paddingHorizontal: 16, paddingTop: 16 }}
        placeholder={I18n.t('page_help_ph_feedback_text')}
        editable
        returnKeyType="done"
        numberOfLines={0}
        multiline
        placeholderTextColor={desColor}
        underlineColorAndroid="transparent"
        maxLength={200}
        textAlignVertical="top"
        onSubmitEditing={Keyboard.dismiss}
        defaultValue={this.state.feedbackContent}
        // onChange={evt => this.setState({ text: evt.nativeEvent.text })}
        onChangeText={(text) =>
          setTimeout(() => {
            this.setState({ feedbackContent: text });
          })
        }
        // onEndEditing={evt => this.setState({ text: evt.nativeEvent.text })}
      />
    </View>
  );

  renderSubmit = () => (
    <View
      style={{
        width: '100%',
        justifyContent: 'center',
        paddingVertical: 36,
      }}
    >
      <Button
        title={I18n.t('page_help_btn_feedback_submit')}
        buttonStyle={{
          backgroundColor: baseBlueColor,
          width: '90%',
          height: 45,
          marginLeft: '5%',
          borderColor: 'transparent',
          borderWidth: 0,
          borderRadius: 5,
          elevation: 0,
        }}
        titleStyle={{ fontSize: 18 }}
        onPress={() => {
          this.onSubmitFeedback();
        }}
      />
    </View>
  );

  renderLeftComponent = () => {
    const { navigation } = this.props;
    return (
      <View style={{ marginLeft: -90 }}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
        >
          <Icon type="ionicon" name="ios-arrow-back" size={26} color="#fff" />
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    const { selectedIndex } = this.state;
    const { navigation } = this.props;
    return (
      <View style={generalStyle.generalContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={this.renderHeaderButtonGroup()}
          leftComponent={<GoBack navigation={navigation} />}
        />
        {selectedIndex === 0 ? this.renderPropsStatus() : this.renderFeedback()}
        {selectedIndex === 1 ? this.renderSubmit() : []}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
      </View>
    );
  }
}
