import React, { Component } from 'react';
import { ScrollView, View } from 'react-native';
import { inject, observer } from 'mobx-react';
import { Header, ListItem } from 'react-native-elements';
import { headerStyle, titleColor, jobStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';

@inject('jobStore')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onSelectSalary = (item) => {
    if (this.props.navigation.state.params) {
      this.props.navigation.state.params.onSelect(item);
    }
  };

  renderSalaryList = () => {
    const {
      jobStore: { jobSalaryList },
      navigation,
    } = this.props;
    return jobSalaryList.map((item) => (
      <ListItem
        key={item.value}
        containerStyle={{
          borderBottomColor: '#eee',
          borderBottomWidth: 1,
          paddingHorizontal: 12,
          paddingVertical: 16,
        }}
        onPress={() => {
          this.onSelectSalary(item);
          navigation.goBack();
        }}
        title={item.label}
        titleStyle={{
          color: titleColor,
          fontSize: 14,
        }}
      />
    ));
  };

  render() {
    const { navigation } = this.props;
    return (
      <View style={jobStyle.jobContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{ text: I18n.t('page_job_expected_Salary'), style: headerStyle.center }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          <View style={{ backgroundColor: '#fff' }}>{this.renderSalaryList()}</View>
        </ScrollView>
      </View>
    );
  }
}
