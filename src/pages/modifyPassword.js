import React, { Component } from 'react';
import Toast from 'react-native-easy-toast';
import { inject, observer } from 'mobx-react';
import { View, Text } from 'react-native';
import { Head<PERSON>, Button, Input } from 'react-native-elements';
import { headerStyle, baseBlueColor, titleColor, settingStyle } from '../themes';
import GoBack from '../components/goback';
import I18n from '../i18n';
import LoadingModal from '../components/loadingModal';

@inject('personStore', 'userAction')
@observer
export default class Page extends Component {
  constructor(props) {
    super(props);
    this.state = {
      oldPassword: '',
      newPassword: '',
      newConfirmPassword: '',
      showLoading: false,
    };
  }

  onSubmitPasswordForm = async () => {
    try {
      const pwd = /^(?!([a-zA-Z]+|\d+)$)[a-zA-Z\d]{6,16}$/; // 英文字母、数字
      const { oldPassword, newPassword, newConfirmPassword } = this.state;
      if (!oldPassword) {
        this.toast.show(I18n.t('page_setting_ph_input_old_password'));
        return;
      }
      if (oldPassword === newPassword) {
        this.toast.show(I18n.t('page_setting_tips_old_equ_new_password'));
        return;
      }
      if (!newPassword || !newPassword.trim() || !pwd.test(newPassword)) {
        this.toast.show(I18n.t('page_setting_ph_input_new_password'));
        return;
      }
      if (!newConfirmPassword || !newConfirmPassword.trim() || !pwd.test(newConfirmPassword)) {
        this.toast.show(I18n.t('page_setting_ph_input_confirm_password'));
        return;
      }
      if (newConfirmPassword !== newPassword) {
        this.toast.show(I18n.t('page_setting_ph_diff_password'));
        return;
      }
      this.setState({ showLoading: true });
      const resl = await this.props.userAction.generalModifyPassword(newPassword, oldPassword);
      if (resl && resl.successful) {
        this.toast.show(resl.message);
        setTimeout(() => {
          this.props.navigation.goBack();
        }, 1000);
      } else if (resl && resl.fieldErrors) {
        this.toast.show(I18n.t('page_setting_ph_len_password'));
      } else {
        this.toast.show(resl && resl.message);
      }
      this.setState({ showLoading: false });
    } catch (error) {
      this.setState({ showLoading: false });
    }
  };

  onChangeOldText = (val) => {
    this.setState({ oldPassword: val });
  };

  onChangeNewText = (val) => {
    this.setState({ newPassword: val });
  };

  onChangeConfirmNewText = (val) => {
    this.setState({ newConfirmPassword: val });
  };

  renderPasswordInput = () => (
    <View style={settingStyle.passwordContainer}>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_old_password')}
          <Text style={{ color: '#fff' }}>空</Text>
        </Text>
        <Input
          onChangeText={(val) => this.onChangeOldText(val)}
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={16}
          underlineColorAndroid="transparent"
          containerStyle={{
            marginLeft: 24,
          }}
        />
      </View>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_new_password')}
          <Text style={{ color: '#fff' }}>空</Text>
        </Text>
        <Input
          onChangeText={(val) => this.onChangeNewText(val)}
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          placeholder={I18n.t('page_setting_ph_new_password')}
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={16}
          underlineColorAndroid="transparent"
          containerStyle={{
            marginLeft: 24,
            width: 200,
          }}
        />
      </View>
      <View style={settingStyle.inputViewContainer}>
        <Text style={{ fontSize: 14, color: titleColor }}>
          {I18n.t('page_setting_text_confirm_password')}
        </Text>
        <Input
          onChangeText={(val) => this.onChangeConfirmNewText(val)}
          inputContainerStyle={settingStyle.inputContainerStyle}
          inputStyle={{
            color: titleColor,
            fontSize: 14,
          }}
          secureTextEntry
          // clearButtonMode="while-editing"
          maxLength={16}
          underlineColorAndroid="transparent"
          containerStyle={{
            marginLeft: 24,
          }}
        />
      </View>
    </View>
  );

  renderSubmitButton = () => (
    <View style={{ width: '100%', justifyContent: 'center', paddingVertical: 36 }}>
      <Button
        title={I18n.t('page_help_btn_feedback_submit')}
        buttonStyle={[settingStyle.buttonStyle, { backgroundColor: baseBlueColor, borderWidth: 0 }]}
        titleStyle={{ fontSize: 16 }}
        onPress={() => {
          this.onSubmitPasswordForm();
        }}
      />
    </View>
  );

  render() {
    const { navigation } = this.props;
    return (
      <View style={settingStyle.settingContainer}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_setting_text_modify_password'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        {this.renderPasswordInput()}
        {this.renderSubmitButton()}
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
