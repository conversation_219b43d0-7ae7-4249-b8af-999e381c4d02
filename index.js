import { AppRegistry, Platform, Text, TextInput, LogBox } from 'react-native';
import { Input } from 'react-native-elements';
import App from './App';
import { name as appName } from './app.json';
import { ToastManager } from './src/components/toastManager';

Text.defaultProps = {
  ...(Text.defaultProps || {}),
  allowFontScaling: false,
};

TextInput.defaultProps = {
  ...(TextInput.defaultProps || {}),
  allowFontScaling: false,
};

Input.defaultProps = {
  ...(Input.defaultProps || {}),
  renderErrorMessage: false,
  // errorStyle: {
  //   height: 0,
  //   margin: 0,
  //   padding: 0,
  // },
};

delete global.document;

global.toast = ToastManager;
global.IS_IOS = false;
global.IS_ANDROID = false;
global.OS_VERSION = 0;
if (Platform.OS.toLowerCase() === 'android') {
  global.IS_ANDROID = true;
  global.OS_VERSION = Platform.Version;
} else if (Platform.OS.toLowerCase() === 'ios') {
  global.IS_IOS = true;
  global.OS_VERSION = parseInt(Platform.Version, 10);
}
global.logger = console;

// console.disableYellowBox = true;
LogBox.ignoreAllLogs();

AppRegistry.registerComponent(appName, () => App);
