## 开发环境 [参考](https://reactnative.cn/docs/environment-setup)

#### Android
- Java 17.0.11
- Node 18.19.1
- npm 10.2.4
- yarn 1.22.22
- Android SDK：34 (Android 14)
- Android NDK：26.1.10909125
- Android buildToolsVersion：34.0.0

#### iOS

## 安装
1. 执行命令`yarn`
2. 开发工具安装并启用eslint和prettier
3. 代码格式化使用prettier

## 运行

1. IOS 命令
```
yarn ios
```

2. Android 命令
```
build是自定义脚本，会修改js和原生环境配置
编译好的apk会在android/apks目录对应项目下

dev环境
yarn android
生产环境
yarn android-prod
```

## 注意事项
1. 时间相关，优先使用服务器时间，防止用户修改本地时间导致问题
```text
使用 global.now() 代替Date.now()、new Date().getTime()
使用 moment() 时，应该 moment(global.now()) 指定服务器时间

global.elapsedRealtime()用来获取设备运行时间，如果获取处理时间间隔可以使用该方法
```

## 文件结构

```
|--android  （android源码）  
|--ios      （ios源码）  
|--src      （js源代码目录）  
    |--api  （接口对接服务目录）  
    |--common （公共对象目录）  
    |--components （业务组件目录）  
    |--configs  （配置文件）  
    |--i18n （国际化定义目录）  
    |--pages  （页面组件目录）  
    |--res  （资源文件目录，主要是图片）  
    |--store  （mobx状态管理存储store目录，包括action）  
    |--themes （主题样式目录）  
    |--util （util工具方法）  
    |--index.js（路由配置）  
    |--initApp.js （app初始化）  
    |--navigationService.js （路由跳转方法）  
    |--tabbar.js (底部tab)  
|--App.js   （根组件）  
|--index.js （项目注册启动）    
|--app.json  (项目名称配置)    
```

## 编码（规范）
- 注释

  - 文件注释（每个文件开始部分的注释说明）

    ```
     /**
      * 功能描述
      * <AUTHOR>
      */
     class Xxx {
     }
    ```

  - 块注释（大段落描述说明，不要用来注释没用的代码，不允许出现注释掉的代码）

    ```
     /**
      * 注释说明
      */
    ```

  - 方法注释（每个方法都应该要有个注释说明）

    ```
     /**
      * 这个方法注释
      * @param  类型 参数1
      * @param  类型 参数2
      * @return 类型 返回结果
      */
    ```

  - 行注释（变量）

    ```
      //这是一行注释
    ```
- 组件化，不要出现一个非常大的组件或者页面组件。
- 样式编写规范请参照 src/themes/readme.md
- 国际化编写规范，请参照现有的示例
- 资源文件导入导出，请参照现有的示例

## 组件编写原则
1.  组件封装原则，可以分为有状态组件和无状态组件，或者公共类型的组件和业务类型的组件，还可以分，容器组件（pages目录下的都算）和功能组件。
2.  JSX语法，少用三元运算，统一写一个render方法，在里面用if来判断
3.  组件初始state，一定是刚加载的时候应该是什么状态就设置什么状态，避免尽量少setState，典型的例子是 loading
4.  组件里面不能存在数据转换的逻辑，可以考虑放到action或者store里面去做，组件里面应该都是很简单的UI渲染逻辑
5.  一个组件代码尽量不要太大，保持在200-300行左右，一个方法也一样，越短越好
6.  尽量使用组件的思想来巧妙消除过多的if else逻辑
7.  组件初始化，数据获取都应该放在componentDidMount生命周期函数里面，其他的都不能
8.  用到定时器、订阅、注册这些，必须在组件的componentWillUnmount生命周期函数里面移除
9.  组件里面尽量不要出现，字符串常量、数字等类型的常量，需要考虑是否应该迁移到常量配置里面
10. 没有完成的功能，留个 todo 标记，以便回头来处理
11. 对于一些 console.log 调试完成，请一定删除掉
12. 组件样式，对于小组件样式就写在组件里面就好，大的业务组件可以放到themes里面


## 测试
暂时没有用到单元测试、e2e测试，现在主要用到两个测试辅助工具

1. Chrome的开发者工具，可以借助log来调试运行
2. Reactotron工具帮助查看http请求状态
3. Android studio/IDEA的Logcat
4. Sentry跟踪线上运行问题

## 提交（git）
1. 每个功能/bug创建一个分支(有jira也可以使用jira的id), eg:
   > feat-xxx 或 fix-xxx
2. commit内容规范
  1. 前缀为fix/feat开头，括号里面是功能模块，示例如下
     ```text
     fix(user): 修复xxx
     feat(chat): 新增xxx
     feat(version): 版本1.1.0
     ```
  2. 版本号修改单独提交，格式如示例：feat(version): 版本1.1.0
  3. 代码不能直接提交到develop或dev分支，只能把自己的本地分支提交到远程，再创建merge request
  4. 理念和目标是保持dev分支一条直线，对应情形操作方法如下
  - 如果新分支是基于最新dev仅commit一次或第二次commit的是版本号修改，直接发起merge request，不用勾选squash
  - 如果新分支提交了多次且不包含版本号的修改
    1. 将最新dev合并过来
    2. 发起merge request并勾选squash，注意修改commit内容
  - 如果新分支feat-xxx提交了多次修改且包含版本号的修改
    1. 将最新dev合并过来
    2. 基于dev新建分支feat-xxx-merge，并切换到feat-xxx-merge
    3. 执行git merge --squash feat-xxx
    4. 将非版本号的修改全部commit，注意commit内容填写
    5. 将版本号的修改commit
    6. feat-xxx-merge分支发起merge request到dev，不用勾选squash

## 类库
主要是项目用的第三方类库，主要看package.json的dependence就行了

## 工具
项目用到的相关辅助开发的工具

1. Reactotron 查看http请求工具

## 问题
开发过程中遇到比较棘手的问题，可以记录下来，包括解决方案。

## 打包
1. Android
```
每次打包需修改版本号，每次小版本加1，如当前是1.2.1，就改为1.2.2
如果是新功能，改动大，耗时多，就大版本加1，改为1.3.0
单独修改版本号命令：
yarn build vn-1.2.2

打包dev，生成apk（vn-1.2.1会修改版本号，这是可选的）
yarn apk-dev clean vn-1.2.1

打包生产环境，生成apk
yarn apk-prod clean vn-1.2.1

打包生成环境，生成apk和aab
yarn build-prod
```

## GIT 提交规范

* 1、提交格式 (注意冒号后面有空格)

```
<type><(scope)>: <subject>

eg: feat(user): #001 - 完成用户注册
eg: fix(user): #201 - 修复用户注册问题
```

* 2、type 类型说明

```
用于说明 commit 的类别，只允许使用下面7个标识。
 * feat：新功能（feature）
 * fix：修补bug
 * docs：文档（documentation）
 * style： 格式（不影响代码运行的变动）
 * refactor：重构（即不是新增功能，也不是修改bug的代码变动）
 * test：增加测试
 * chore：构建过程或辅助工具的变动
如果type为feat和fix，则该 commit 将肯定出现在 Change log 之中。
```

* 3、scope

```
scope 用于说明 commit 影响的范围，比如数据层、控制层、视图层等等，视项目不同而不同。或者按照Epic，用户模块、订单模块、合同模块。
```

* 4、subject

```
subject是 commit 目的的简短描述，不超过50个字符，最少5个字符，且结尾不加句号（.）。
```
